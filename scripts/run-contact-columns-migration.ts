import { db } from "@/lib/db";
import { sql } from "drizzle-orm";
import { readFileSync } from "node:fs";
import { resolve } from "node:path";

async function main() {
  console.log("Starting migration to add contact columns to locations table...");

  try {
    // Read the SQL file
    const sqlPath = resolve(process.cwd(), "drizzle/migrations/0012_add_contact_columns.sql");
    const sqlContent = readFileSync(sqlPath, "utf8");

    // Execute the SQL
    await db.execute(sql.raw(sqlContent));

    console.log("Migration completed successfully!");
  } catch (error) {
    console.error("Error running migration:", error);
    process.exit(1);
  } finally {
    process.exit(0);
  }
}

main();
