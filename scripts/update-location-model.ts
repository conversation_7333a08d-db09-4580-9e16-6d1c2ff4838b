import { z } from "zod"
import { LOCATION_TYPES, LOCATION_STATUSES } from "../modules/shared/constants"

// Update the location schema to make projectId optional
export const locationSchema = z.object({
  id: z.string().uuid(),
  name: z.string().min(1).max(255),
  description: z.string().optional(),
  address: z.object({
    street: z.string(),
    city: z.string(),
    state: z.string(),
    postalCode: z.string(),
    country: z.string(),
    formatted: z.string().optional(),
  }),
  coordinates: z.object({
    latitude: z.number().min(-90).max(90),
    longitude: z.number().min(-180).max(180),
  }),
  type: z.enum(LOCATION_TYPES as unknown as [string, ...string[]]),
  status: z.enum(LOCATION_STATUSES as unknown as [string, ...string[]]),
  projectId: z.string().uuid().optional(), // Make projectId optional
  organizationId: z.string().uuid(),

  contactName: z.string().optional(),
  contactEmail: z.string().email().optional(),
  contactPhone: z.string().optional(),

  locationSize: z.string().optional(),
  locationFeatures: z.array(z.string()).optional(),
  locationTags: z.array(z.string()).optional(),
  accessibility: z.string().optional(),
  parkingInfo: z.string().optional(),
  hourlyRate: z.number().optional(),
  dailyRate: z.number().optional(),

  permitsRequired: z.boolean().default(false),
  restrictions: z.string().optional(),
  approvedBy: z.string().uuid().optional(),
  approvedAt: z.date().optional(),

  goldenMorningStart: z.string().optional(),
  goldenMorningEnd: z.string().optional(),
  goldenEveningStart: z.string().optional(),
  goldenEveningEnd: z.string().optional(),

  metadata: z.record(z.any()).optional(),
  isActive: z.boolean().default(true),
  createdBy: z.string().optional(),
  createdAt: z.date(),
  updatedAt: z.date(),
  deletedAt: z.date().nullable().optional(),
})

console.log("Updated location schema with optional projectId")
