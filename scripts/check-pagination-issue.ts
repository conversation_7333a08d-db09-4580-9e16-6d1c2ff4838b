import { db } from "@/lib/db"
import { locations } from "@/modules/location/schema"
import { eq, isNull, and, desc } from "drizzle-orm"
import { getLocations } from "@/modules/location/service"

async function main() {
  // Get a sample organization ID
  const sampleLocation = await db.query.locations.findFirst({
    where: isNull(locations.deletedAt),
    orderBy: [desc(locations.createdAt)],
  })
  
  if (!sampleLocation) {
    console.error("No locations found in database")
    process.exit(1)
  }
  
  const sampleOrgId = sampleLocation.organizationId
  
  // Get all locations for this organization directly from the database
  const allLocationsFromDb = await db.query.locations.findMany({
    where: and(
      eq(locations.organizationId, sampleOrgId),
      isNull(locations.deletedAt)
    ),
  })
  
  console.log(`Direct DB query found ${allLocationsFromDb.length} total locations`)
  
  // Count locations with null project IDs
  const nullProjectLocations = allLocationsFromDb.filter(loc => loc.projectId === null)
  console.log(`Of these, ${nullProjectLocations.length} have null project IDs`)
  
  // Test the getLocations function with pagination
  const pageSize = 12
  const page1Results = await getLocations(sampleOrgId, { page: 1, pageSize })
  console.log(`getLocations page 1 (pageSize=${pageSize}) returned ${page1Results.length} locations`)
  
  // Check if there should be more pages
  const totalPages = Math.ceil(allLocationsFromDb.length / pageSize)
  console.log(`Total pages should be: ${totalPages}`)
  
  // Check if the API would calculate pagination correctly
  console.log(`API would calculate total as: ${page1Results.length}`)
  console.log(`API would calculate totalPages as: ${Math.ceil(page1Results.length / pageSize)}`)
  
  // If there are more pages, check page 2
  if (totalPages > 1) {
    const page2Results = await getLocations(sampleOrgId, { page: 2, pageSize })
    console.log(`getLocations page 2 (pageSize=${pageSize}) returned ${page2Results.length} locations`)
    
    // Check if any locations with null projectId are on page 2
    const nullProjectOnPage2 = page2Results.filter(loc => !loc.projectId).length
    console.log(`Of these, ${nullProjectOnPage2} have null project IDs`)
  }
}

main()
