import { drizzle } from "drizzle-orm/postgres-js"
import postgres from "postgres"
import { sql } from "drizzle-orm"
import * as schema from "../lib/db/schema"

async function main() {
  // Use the connection URL directly
  const connectionString = "postgresql://sceneomatic_auth_owner:<EMAIL>/sceneomatic_auth?sslmode=require"
  
  try {
    // Create postgres client
    const queryClient = postgres(connectionString)
    const db = drizzle(queryClient, { schema })
    
    // Test the connection by querying the database version
    const result = await db.execute(sql`SELECT version();`)
    console.log("Database connection successful!")
    console.log("Database version:", result[0].version)

    // Count the number of organizations
    const orgCount = await db.execute(sql`SELECT COUNT(*) FROM organizations`)
    console.log(`Number of organizations: ${orgCount[0].count}`)

    // Count the number of users
    const userCount = await db.execute(sql`SELECT COUNT(*) FROM users`)
    console.log(`Number of users: ${userCount[0].count}`)

    console.log("Database test completed successfully!")
    
    // Close the connection
    await queryClient.end()
  } catch (error) {
    console.error("Database connection test failed:", error)
    throw error
  }
}

main()
  .then(() => process.exit(0))
  .catch((error) => {
    console.error(error)
    process.exit(1)
  })
