// Import required modules
import postgres from "postgres"
import * as dotenv from "dotenv"
import * as fs from "node:fs"
import * as path from "node:path"

// Load environment variables
dotenv.config()

// Get database connection string from environment variables
const connectionString = process.env.DATABASE_URL

if (!connectionString) {
  console.error("DATABASE_URL environment variable is not set")
  process.exit(1)
}

// Create a postgres client
const sql = postgres(connectionString, { max: 1 })

// Function to execute SQL from a file
async function executeSqlFile(filePath: string) {
  try {
    console.log(`Executing SQL from file: ${filePath}`)
    
    // Read the SQL file
    const sqlContent = fs.readFileSync(path.resolve(process.cwd(), filePath), 'utf8')
    
    // Execute the SQL
    await sql.unsafe(sqlContent)
    
    console.log(`Successfully executed SQL from file: ${filePath}`)
  } catch (error) {
    console.error(`Error executing SQL from file ${filePath}:`, error)
    throw error
  }
}

// Main function to execute all SQL files
async function main() {
  try {
    // Execute the SQL to create the scenes tables first
    console.log("Creating scenes tables...")
    await executeSqlFile('scripts/create-scenes-tables.sql')
    
    // Seed the scenes data
    console.log("Seeding scenes data...")
    await executeSqlFile('scripts/seed-scenes-data.sql')
    
    // Execute the SQL to create the favorites tables
    console.log("Creating favorites tables...")
    await executeSqlFile('scripts/create-favorites-tables.sql')
    
    // Execute the SQL to seed the favorites data
    console.log("Seeding favorites data...")
    await executeSqlFile('scripts/seed-favorites-data.sql')
    
    console.log("All SQL files executed successfully")
  } catch (error) {
    console.error("Error executing SQL files:", error)
    process.exit(1)
  } finally {
    // Close the database connection
    await sql.end()
  }
}

// Run the main function
main()
