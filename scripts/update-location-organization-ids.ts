import { db } from "@/lib/db"
import { locations } from "@/modules/location/schema"
import { eq } from "drizzle-orm"
import * as dotenv from "dotenv"

dotenv.config()

async function main() {
  console.log("Updating location organization IDs...")

  try {
    // The current organization ID in the database
    const currentOrgId = "af6520e4-b62b-4034-8e6c-5fb93ae41d93"
    
    // The new organization ID from Stytch
    const newOrgId = "3b75ccad-ef69-4314-9a10-9e1cf15c67d4"
    
    // Count locations with the current organization ID
    const locationsToUpdate = await db.query.locations.findMany({
      where: eq(locations.organizationId, currentOrgId)
    })
    
    console.log(`Found ${locationsToUpdate.length} locations with organization ID ${currentOrgId}`)
    
    if (locationsToUpdate.length === 0) {
      console.log("No locations to update.")
      return
    }
    
    // Confirm before proceeding
    console.log(`\nWARNING: This will update the organization ID for ${locationsToUpdate.length} locations.`)
    console.log(`From: ${currentOrgId}`)
    console.log(`To:   ${newOrgId}`)
    console.log("\nThis operation cannot be undone. Make sure you have a database backup.")
    
    // In a real script, we would prompt for confirmation here
    // For this example, we'll proceed automatically
    
    // Update the organization ID for all locations
    const result = await db
      .update(locations)
      .set({ organizationId: newOrgId })
      .where(eq(locations.organizationId, currentOrgId))
      .returning({ id: locations.id, name: locations.name })
    
    console.log(`\nSuccessfully updated ${result.length} locations:`)
    result.forEach((location, index) => {
      console.log(`${index + 1}. ${location.name} (${location.id})`)
    })
    
    console.log("\nLocation organization IDs updated successfully.")
  } catch (error) {
    console.error("Error updating location organization IDs:", error)
    throw error
  }
}

main()
  .then(() => process.exit(0))
  .catch((error) => {
    console.error(error)
    process.exit(1)
  })
