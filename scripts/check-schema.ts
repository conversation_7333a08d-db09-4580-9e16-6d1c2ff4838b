import postgres from "postgres"
import * as dotenv from "dotenv"

dotenv.config()

async function main() {
  // Get connection string from environment
  const connectionString = process.env.DATABASE_URL
  if (!connectionString) {
    console.error("DATABASE_URL is not defined")
    process.exit(1)
  }

  // Create postgres client
  const sql = postgres(connectionString)

  try {
    // Check organizations table schema
    console.log("Checking organizations table schema...")
    const orgSchema = await sql`
      SELECT column_name, data_type, character_maximum_length 
      FROM information_schema.columns 
      WHERE table_name = 'organizations'
      ORDER BY ordinal_position
    `
    console.log("Organizations table columns:", orgSchema)
    
    // Check if subscriptions table exists
    console.log("\nChecking if subscriptions table exists...")
    const subscriptionsTable = await sql`
      SELECT EXISTS (
        SELECT FROM information_schema.tables 
        WHERE table_name = 'subscriptions'
      )
    `
    console.log("Subscriptions table exists:", subscriptionsTable[0].exists)
    
    // Check if subscription_invoices table exists
    console.log("\nChecking if subscription_invoices table exists...")
    const invoicesTable = await sql`
      SELECT EXISTS (
        SELECT FROM information_schema.tables 
        WHERE table_name = 'subscription_invoices'
      )
    `
    console.log("Subscription invoices table exists:", invoicesTable[0].exists)
    
    // If subscriptions table exists, check its schema
    if (subscriptionsTable[0].exists) {
      console.log("\nChecking subscriptions table schema...")
      const subSchema = await sql`
        SELECT column_name, data_type, character_maximum_length 
        FROM information_schema.columns 
        WHERE table_name = 'subscriptions'
        ORDER BY ordinal_position
      `
      console.log("Subscriptions table columns:", subSchema)
    }

    // Close the connection
    await sql.end()
    
    process.exit(0)
  } catch (error) {
    console.error("Error checking schema:", error)
    process.exit(1)
  }
}

main()
