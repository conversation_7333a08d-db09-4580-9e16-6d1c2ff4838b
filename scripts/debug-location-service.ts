import { db } from "@/lib/db"
import { locations } from "@/modules/location/schema"
import { eq, isNull, and, desc } from "drizzle-orm"
import { getLocations } from "@/modules/location/service"

async function main() {
  console.log("Debugging location service...")
  
  try {
    // Get a sample organization ID
    const sampleLocation = await db.query.locations.findFirst({
      where: isNull(locations.deletedAt),
      orderBy: [desc(locations.createdAt)],
    })
    
    if (!sampleLocation) {
      console.error("No locations found in database")
      process.exit(1)
    }
    
    const sampleOrgId = sampleLocation.organizationId
    console.log(`Using organization ID: ${sampleOrgId}`)
    
    // Get all locations for this organization directly from the database
    const allLocationsFromDb = await db.query.locations.findMany({
      where: and(
        eq(locations.organizationId, sampleOrgId),
        isNull(locations.deletedAt)
      ),
    })
    
    console.log(`Direct DB query found ${allLocationsFromDb.length} locations`)
    
    // Count locations with null project IDs
    const nullProjectLocations = allLocationsFromDb.filter(loc => loc.projectId === null)
    console.log(`Of these, ${nullProjectLocations.length} have null project IDs`)
    
    // Print the null project locations
    if (nullProjectLocations.length > 0) {
      console.log("\nLocations with null project IDs:")
      for (const loc of nullProjectLocations) {
        console.log(`- ${loc.name} (ID: ${loc.id})`)
        // Print all fields to see if there's anything unusual
        console.log("  Fields:", JSON.stringify({
          isActive: loc.isActive,
          status: loc.status,
          type: loc.type,
          createdAt: loc.createdAt,
          updatedAt: loc.updatedAt,
          deletedAt: loc.deletedAt
        }, null, 2))
      }
    }
    
    // Now use the getLocations service function with debug logging
    console.log("\nTesting getLocations service function with debug logging:")
    
    // Monkey patch console.log to capture SQL queries
    const originalConsoleLog = console.log
    const capturedLogs: string[] = []
    console.log = function(...args) {
      capturedLogs.push(args.join(' '))
      originalConsoleLog.apply(console, args)
    }
    
    // Test with no filters
    const locationsNoFilter = await getLocations(sampleOrgId)
    console.log = originalConsoleLog // Restore console.log
    
    console.log(`getLocations with no filters returned ${locationsNoFilter.length} locations`)
    
    // Count locations with null project IDs in the result
    const nullProjectInResult = locationsNoFilter.filter(loc => !loc.projectId).length
    console.log(`Of these, ${nullProjectInResult} have null project IDs`)
    
    // Print captured logs that might contain SQL queries
    console.log("\nCaptured logs during getLocations call:")
    for (const log of capturedLogs) {
      if (log.includes("SQL") || log.includes("query") || log.includes("SELECT") || log.includes("WHERE")) {
        console.log(log)
      }
    }
    
    // Check if the locations returned by getLocations match the ones from direct DB query
    console.log("\nComparing results:")
    const directDbIds = new Set(allLocationsFromDb.map(loc => loc.id))
    const serviceIds = new Set(locationsNoFilter.map(loc => loc.id))
    
    // Find IDs that are in the direct DB query but not in the service result
    const missingIds = [...directDbIds].filter(id => !serviceIds.has(id))
    console.log(`IDs in direct DB query but missing from service result: ${missingIds.length}`)
    
    if (missingIds.length > 0) {
      console.log("Missing locations:")
      for (const id of missingIds) {
        const loc = allLocationsFromDb.find(l => l.id === id)
        if (loc) {
          console.log(`- ${loc.name} (ID: ${id})`)
          console.log("  Fields:", JSON.stringify({
            projectId: loc.projectId,
            isActive: loc.isActive,
            status: loc.status,
            type: loc.type,
            createdAt: loc.createdAt,
            updatedAt: loc.updatedAt,
            deletedAt: loc.deletedAt
          }, null, 2))
        }
      }
    }
    
  } catch (error) {
    console.error("Error debugging location service:", error)
    process.exit(1)
  }
  
  process.exit(0)
}

main()
