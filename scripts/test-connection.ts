import * as dotenv from "dotenv"
import { sql } from "drizzle-orm"
import { drizzle } from "drizzle-orm/postgres-js"
import postgres from "postgres"
import * as schema from "../lib/db/schema"

// Load environment variables from .env file
dotenv.config({ path: '../.env' })

// Check if DATABASE_URL is defined
const connectionString = process.env.DATABASE_URL
if (!connectionString) {
  throw new Error("DATABASE_URL is not defined")
}

// Create postgres client
const queryClient = postgres(connectionString)
const db = drizzle(queryClient, { schema })

async function main() {
  try {
    // Test the connection by querying the database version
    const result = await db.execute(sql`SELECT version();`)
    console.log("Database connection successful!")
    console.log("Database version:", result[0].version)

    // Count the number of organizations
    const orgCount = await db.execute(sql`SELECT COUNT(*) FROM organizations`)
    console.log(`Number of organizations: ${orgCount[0].count}`)

    // Count the number of users
    const userCount = await db.execute(sql`SELECT COUNT(*) FROM users`)
    console.log(`Number of users: ${userCount[0].count}`)

    console.log("Database test completed successfully!")
  } catch (error) {
    console.error("Database connection test failed:", error)
    throw error
  }
}

main()
  .then(() => process.exit(0))
  .catch((error) => {
    console.error(error)
    process.exit(1)
  })
