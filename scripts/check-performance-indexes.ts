import postgres from "postgres"
import * as dotenv from "dotenv"

dotenv.config()

async function checkIndexes() {
  // Get connection string from environment
  const connectionString = process.env.DATABASE_URL
  if (!connectionString) {
    console.error("DATABASE_URL is not defined")
    process.exit(1)
  }

  // Create postgres client
  const sql = postgres(connectionString)

  try {
    console.log("Checking performance indexes...")
    
    // Query to get all indexes in the database
    const result = await sql`
      SELECT 
        indexname, 
        tablename, 
        indexdef 
      FROM 
        pg_indexes 
      WHERE 
        schemaname = 'public' AND
        (
          indexname LIKE 'idx_map_views%' OR
          indexname LIKE 'idx_map_view_locations%' OR
          indexname LIKE 'idx_shared_map_links%' OR
          indexname LIKE 'idx_locations%' OR
          indexname LIKE 'idx_notifications%' OR
          indexname LIKE 'idx_notification_preferences%'
        )
      ORDER BY 
        tablename, 
        indexname;
    `
    
    // Display the results
    console.log("\nPerformance indexes found in the database:")
    console.log("==========================================")
    
    if (result.length === 0) {
      console.log("No performance indexes found. The migration may not have been run.")
      return
    }
    
    // Define a type for the index information
    type IndexInfo = {
      name: string;
      definition: string;
    };
    
    // Group indexes by table
    const indexesByTable: Record<string, IndexInfo[]> = {}
    
    for (const row of result) {
      const tableName = row.tablename
      if (!indexesByTable[tableName]) {
        indexesByTable[tableName] = []
      }
      indexesByTable[tableName].push({
        name: row.indexname,
        definition: row.indexdef
      })
    }
    
    // Print indexes by table
    for (const [tableName, indexes] of Object.entries(indexesByTable)) {
      console.log(`\nTable: ${tableName}`)
      console.log("-".repeat(tableName.length + 7))
      
      for (const index of indexes) {
        console.log(`  - ${index.name}`)
        console.log(`    ${index.definition}`)
      }
    }
    
    console.log("\nTotal indexes found:", result.length)
    console.log("\nPerformance indexes check completed!")
    
    // Close the connection
    await sql.end()
    
  } catch (error) {
    console.error("Error checking performance indexes:", error)
    process.exit(1)
  } finally {
    process.exit(0)
  }
}

checkIndexes()
