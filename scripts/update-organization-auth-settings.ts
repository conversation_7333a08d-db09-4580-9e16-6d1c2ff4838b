/**
 * This script updates the auth settings for all organizations in Stytch
 * to allow any email domain for JIT provisioning and invites.
 * 
 * Usage:
 * 1. Make sure you have the STYTCH_B2B_PROJECT_ID and STYTCH_B2B_SECRET environment variables set
 * 2. Run: npx tsx scripts/update-organization-auth-settings.ts
 */

import { B2BClient } from "stytch";
import dotenv from "dotenv";

// Define types for the Stytch client
interface StytchOrganizationsClient {
  search: (params: Record<string, unknown>) => Promise<{
    organizations: Array<{
      organization_id: string;
      organization_name: string;
      organization_slug: string;
      email_jit_provisioning?: string;
      email_invites?: string;
      [key: string]: unknown;
    }>;
  }>;
}

// Import axios for direct API calls
import axios from 'axios';

// Load environment variables
dotenv.config({ path: ".env.local" });

async function main() {
  // Create a new Stytch B2B client
  const projectId = process.env.STYTCH_B2B_PROJECT_ID;
  const secret = process.env.STYTCH_B2B_SECRET;
  
  if (!projectId || !secret) {
    console.error("Missing Stytch B2B credentials. Please set STYTCH_B2B_PROJECT_ID and STYTCH_B2B_SECRET environment variables.");
    process.exit(1);
  }
  
  const stytchClient = new B2BClient({
    project_id: projectId,
    secret,
  });
  
  try {
    // Get all organizations
    console.log("Fetching all organizations...");
    
    // Cast to our interface to bypass TypeScript error since the Stytch types might be outdated
    const organizationsClient = stytchClient.organizations as unknown as StytchOrganizationsClient;
    
    // Determine the correct endpoint based on the project ID
    // If it starts with "project-live-", use the production endpoint
    // Otherwise, use the test endpoint
    const isLiveProject = projectId.startsWith('project-live-');
    const baseURL = isLiveProject 
      ? 'https://api.stytch.com/v1/b2b'
      : 'https://test.stytch.com/v1/b2b';
    
    console.log(`Using ${isLiveProject ? 'production' : 'test'} endpoint: ${baseURL}`);
    
    // Create an axios instance for direct API calls
    const stytchAxios = axios.create({
      baseURL,
      auth: {
        username: projectId,
        password: secret
      },
      headers: {
        'Content-Type': 'application/json'
      }
    });
    const response = await organizationsClient.search({});
    
    const organizations = response.organizations;
    console.log(`Found ${organizations.length} organizations`);
    
    // Update each organization's auth settings
    for (const org of organizations) {
      console.log(`Updating auth settings for organization: ${org.organization_name} (${org.organization_id})`);
      
      try {
        // Use axios to call the update organization endpoint directly
        const updateResponse = await stytchAxios.put(
          `/organizations/${org.organization_id}`,
          {
            // Since we can't add common domains to allowed_domains, disable JIT provisioning
            email_jit_provisioning: "NOT_ALLOWED",
            // Allow invites for any email domain - this is the key setting to allow common domains
            email_invites: "ALL_ALLOWED",
          }
        );
        
        console.log(`✅ Successfully updated auth settings for ${org.organization_name}`);
        if (updateResponse?.data?.organization) {
          console.log(`   - email_jit_provisioning: ${updateResponse.data.organization.email_jit_provisioning}`);
          console.log(`   - email_invites: ${updateResponse.data.organization.email_invites}`);
        } else {
          console.log("   - Settings updated successfully, but response format is different than expected");
        }
      } catch (error) {
        console.error(`❌ Failed to update auth settings for ${org.organization_name}:`, error);
      }
    }
    
    console.log("Done!");
  } catch (error) {
    console.error("Error:", error);
    process.exit(1);
  }
}

main().catch(console.error);
