/**
 * <PERSON><PERSON><PERSON> to check location coordinates in the database
 * 
 * This script:
 * 1. Fetches all locations from the database
 * 2. Checks if each location has valid coordinates
 * 3. Logs information about the coordinates
 * 
 * Usage:
 * pnpm tsx scripts/check-location-coordinates.ts
 */

import { db } from "@/lib/db";
import { locations } from "@/lib/db/schema";
import { isNull } from "drizzle-orm";
import { getLatitude, getLongitude } from "@/modules/shared/postgis";
import dotenv from "dotenv";

// Load environment variables
dotenv.config();

// Define coordinates type
interface Coordinates {
  latitude: number;
  longitude: number;
}

// Check if coordinates are valid
function hasValidCoordinates(coordinates: unknown): boolean {
  if (!coordinates) return false;
  
  // Cast to Coordinates if it has the right shape
  const coords = coordinates as Partial<Coordinates>;
  
  // Check if coordinates are near 0,0 (default values)
  if (
    (typeof coords.latitude === 'number' && Math.abs(coords.latitude) < 0.001) &&
    (typeof coords.longitude === 'number' && Math.abs(coords.longitude) < 0.001)
  ) {
    return false;
  }
  
  return (
    typeof coords.latitude === 'number' &&
    typeof coords.longitude === 'number' &&
    !Number.isNaN(coords.latitude) &&
    !Number.isNaN(coords.longitude) &&
    coords.latitude >= -90 &&
    coords.latitude <= 90 &&
    coords.longitude >= -180 &&
    coords.longitude <= 180
  );
}

async function main() {
  try {
    console.log("Fetching locations from database...");
    
    // Get all active locations
    const allLocations = await db.query.locations.findMany({
      where: isNull(locations.deletedAt),
    });
    
    console.log(`Found ${allLocations.length} locations`);
    
    // Check each location's coordinates
    for (const location of allLocations) {
      console.log(`Location: ${location.name} (${location.id})`);
      
      // Check if coordinates field exists
      if (!location.coordinates) {
        console.log("  No coordinates field found");
        continue;
      }
      
      // Get latitude and longitude from coordinates object
      try {
        let latitude: number;
        let longitude: number;
        
        // Check if coordinates is a JSON object with latitude and longitude
        if (location.coordinates && typeof location.coordinates === 'object') {
          const coords = location.coordinates as Record<string, unknown>;
          latitude = typeof coords.latitude === 'number' ? coords.latitude : Number.NaN;
          longitude = typeof coords.longitude === 'number' ? coords.longitude : Number.NaN;
        } else {
          // Try PostGIS functions as fallback
          try {
            latitude = Number.parseFloat(getLatitude(location.coordinates).toString());
            longitude = Number.parseFloat(getLongitude(location.coordinates).toString());
          } catch {
            latitude = Number.NaN;
            longitude = Number.NaN;
          }
        }
        
        console.log(`  Coordinates: ${latitude}, ${longitude}`);
        
        // Check if coordinates are valid
        const coords = { latitude, longitude };
        const isValid = hasValidCoordinates(coords);
        
        console.log(`  Valid coordinates: ${isValid}`);
        
        // Check if coordinates are in the expected range for the US
        const isInUS = 
          !Number.isNaN(latitude) && !Number.isNaN(longitude) &&
          latitude >= 24 && latitude <= 50 && // Rough latitude range for continental US
          longitude >= -125 && longitude <= -66; // Rough longitude range for continental US
        
        console.log(`  In US range: ${isInUS}`);
        
        // Check if address exists
        if (location.address) {
          console.log(`  Address: ${JSON.stringify(location.address)}`);
        } else {
          console.log("  No address found");
        }
        
        // Check if metadata contains pricing information
        if (location.metadata && typeof location.metadata === 'object') {
          const metadata = location.metadata as Record<string, unknown>;
          
          if (metadata.hourlyRate) {
            console.log(`  Hourly rate (from metadata): $${metadata.hourlyRate}`);
          }
          
          if (metadata.dailyRate) {
            console.log(`  Daily rate (from metadata): $${metadata.dailyRate}`);
          }
          
          if (metadata.price) {
            console.log(`  Price (from metadata): ${metadata.price}`);
          }
        } else {
          console.log("  No pricing information found in metadata");
        }
      } catch (error) {
        console.error(`  Error parsing coordinates: ${error}`);
      }
      
      console.log(""); // Add empty line for readability
    }
    
    console.log("Finished checking location coordinates");
  } catch (error) {
    console.error("Error:", error);
    process.exit(1);
  }
}

main();
