import { db } from "@/lib/db"
import { sql } from "drizzle-orm"
import fs from "node:fs"
import path from "node:path"

async function main() {
  try {
    console.log("Running map views migration...")
    
    // Read the SQL file
    const sqlFilePath = path.join(process.cwd(), "drizzle/migrations/0006_map_views_tables.sql")
    const sqlContent = fs.readFileSync(sqlFilePath, "utf8")
    
    // Execute the SQL
    await db.execute(sql.raw(sqlContent))
    
    console.log("Map views migration completed successfully!")
  } catch (error) {
    console.error("Error running map views migration:", error)
    process.exit(1)
  } finally {
    process.exit(0)
  }
}

main()
