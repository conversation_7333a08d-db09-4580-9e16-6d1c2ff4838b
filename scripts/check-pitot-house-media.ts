import { db } from "@/lib/db";
import { locations, locationMedia } from "@/modules/location/schema";
import { eq, and, isNull, ilike } from "drizzle-orm";

/**
 * This script checks the database for the "Pitot House" location and its media entries.
 * It will help us understand if the images are actually stored in the database.
 */
async function checkPitotHouseMedia() {
  try {
    console.log("Checking for Pitot House location...");
    
    // Find the Pitot House location
    const pitotHouse = await db.query.locations.findFirst({
      where: ilike(locations.name, "%pitot house%"),
      with: {
        media: {
          where: isNull(locationMedia.deletedAt),
          with: {
            uploader: {
              columns: {
                id: true,
                name: true,
              }
            }
          }
        },
      },
    });

    if (!pitotHouse) {
      console.log("❌ Pitot House location not found in the database");
      
      // List all locations to help identify the correct name
      console.log("\nListing all locations:");
      const allLocations = await db.query.locations.findMany({
        columns: {
          id: true,
          name: true,
        },
        where: isNull(locations.deletedAt),
      });
      
      allLocations.forEach(loc => {
        console.log(`- ${loc.name} (ID: ${loc.id})`);
      });
      
      return;
    }

    console.log(`✅ Found Pitot House location: ${pitotHouse.name} (ID: ${pitotHouse.id})`);
    
    // Check if the location has media
    if (!pitotHouse.media || pitotHouse.media.length === 0) {
      console.log("❌ No media found for Pitot House location");
      
      // Check if there are any media entries in the database at all
      const totalMediaCount = await db.query.locationMedia.findMany({
        columns: {
          id: true,
        },
      });
      
      console.log(`Total media entries in the database: ${totalMediaCount.length}`);
      
      // Check if there are any entries in the location_media table for this location
      // This will check even soft-deleted entries
      const allMediaForLocation = await db.select().from(locationMedia).where(eq(locationMedia.locationId, pitotHouse.id));
      
      if (allMediaForLocation.length > 0) {
        console.log(`Found ${allMediaForLocation.length} media entries for this location (including soft-deleted):`);
        allMediaForLocation.forEach(media => {
          console.log(`- ID: ${media.id}`);
          console.log(`  URL: ${media.url}`);
          console.log(`  Type: ${media.type}`);
          console.log(`  Uploaded At: ${media.uploadedAt}`);
          console.log(`  Is Public: ${media.isPublic}`);
          console.log(`  Deleted At: ${media.deletedAt}`);
          console.log(`  Uploaded By: ${media.uploadedBy}`);
          console.log("---");
        });
      } else {
        console.log("No media entries found for this location at all (not even soft-deleted ones)");
      }
      
      return;
    }

    console.log(`✅ Found ${pitotHouse.media.length} media entries for Pitot House location:`);
    
    // Display media details
    pitotHouse.media.forEach((media, index) => {
      console.log(`\nMedia #${index + 1}:`);
      console.log(`- ID: ${media.id}`);
      console.log(`- URL: ${media.url}`);
      console.log(`- Type: ${media.type}`);
      console.log(`- Title: ${media.title || 'N/A'}`);
      console.log(`- Uploaded At: ${media.uploadedAt}`);
      console.log(`- Is Public: ${media.isPublic}`);
      
      // Check if uploader information is available
      if (media.uploader) {
        console.log(`- Uploader: ${media.uploader.name} (ID: ${media.uploader.id})`);
      } else if (media.uploadedBy) {
        console.log(`- Uploaded By: ${media.uploadedBy}`);
      } else {
        console.log(`- Uploader: Unknown`);
      }
    });

    // Check the API route that's used to fetch media
    console.log("\nChecking the API route that would be used to fetch this media...");
    
    // Simulate the API route query
    const apiMediaQuery = await db.query.locationMedia.findMany({
      where: (locationMedia, { eq, and, isNull }) => 
        and(
          eq(locationMedia.locationId, pitotHouse.id),
          isNull(locationMedia.deletedAt)
        ),
      orderBy: (locationMedia, { asc }) => [asc(locationMedia.ordering)],
      with: {
        uploader: {
          columns: {
            id: true,
            name: true,
          }
        }
      }
    });
    
    console.log(`API route would return ${apiMediaQuery.length} media items`);
    
    if (apiMediaQuery.length > 0) {
      console.log("First item from API route:", JSON.stringify(apiMediaQuery[0], null, 2));
    }
    
    // Check if the media is being properly transformed for the gallery
    console.log("\nChecking if the media would be properly transformed for the gallery...");
    
    function adaptMediaItemsForGallery(mediaItems: any[] = []) {
      return mediaItems.map(media => {
        // Determine the media type
        const mediaType = media.type.toLowerCase().includes('image') ? 'image' : 'video';
        
        // Handle uploader information safely
        let uploaderId = 'unknown';
        let uploaderName = 'Unknown User';
        
        // Check if media has an uploader object with id and name
        if (media.uploader && typeof media.uploader === 'object') {
          if ('id' in media.uploader) uploaderId = media.uploader.id;
          if ('name' in media.uploader) uploaderName = media.uploader.name;
        } 
        // Fall back to uploadedBy if available
        else if (media.uploadedBy) {
          uploaderId = media.uploadedBy;
          uploaderName = `User ${media.uploadedBy.substring(0, 8)}`;
        }
        
        // Create the adapted media item
        return {
          id: media.id,
          url: media.url,
          filename: media.title || 'Untitled',
          type: mediaType,
          thumbnailUrl: media.thumbnailUrl,
          uploadedBy: {
            id: uploaderId,
            name: uploaderName
          },
          uploadedAt: media.uploadedAt ? new Date(media.uploadedAt).toISOString() : new Date().toISOString()
        };
      });
    }
    
    const transformedMedia = adaptMediaItemsForGallery(apiMediaQuery);
    console.log(`Transformed ${transformedMedia.length} media items for gallery`);
    
    if (transformedMedia.length > 0) {
      console.log("First transformed item:", JSON.stringify(transformedMedia[0], null, 2));
    }
    
    console.log("\n✅ Database check completed");
  } catch (error) {
    console.error("Error checking Pitot House media:", error);
  }
}

// Run the check
checkPitotHouseMedia()
  .then(() => process.exit(0))
  .catch((error) => {
    console.error("Error:", error);
    process.exit(1);
  });
