import { db } from "@/lib/db"
import { sql } from "drizzle-orm"
import * as dotenv from "dotenv"

dotenv.config()

async function main() {
  try {
    console.log("Checking notification relations...")
    
    // Check if the location schema has a status column that conflicts with the notification status enum
    const result = await db.execute(sql`
      SELECT column_name, data_type, udt_name
      FROM information_schema.columns
      WHERE table_name = 'locations' AND column_name = 'status'
    `)
    
    console.log("Location status column:", result)
    
    // Check if the notification schema has the correct relations
    const notificationRelations = await db.execute(sql`
      SELECT
        tc.table_schema, 
        tc.constraint_name, 
        tc.table_name, 
        kcu.column_name, 
        ccu.table_schema AS foreign_table_schema,
        ccu.table_name AS foreign_table_name,
        ccu.column_name AS foreign_column_name 
      FROM 
        information_schema.table_constraints AS tc 
        JOIN information_schema.key_column_usage AS kcu
          ON tc.constraint_name = kcu.constraint_name
          AND tc.table_schema = kcu.table_schema
        JOIN information_schema.constraint_column_usage AS ccu
          ON ccu.constraint_name = tc.constraint_name
          AND ccu.table_schema = tc.table_schema
      WHERE tc.constraint_type = 'FOREIGN KEY' AND tc.table_name = 'notifications'
    `)
    
    console.log("Notification relations:", notificationRelations)
    
    // Check if the notification_type, notification_status, and notification_priority enums exist
    const enums = await db.execute(sql`
      SELECT n.nspname as enum_schema,
             t.typname as enum_name,
             e.enumlabel as enum_value
      FROM pg_type t
      JOIN pg_enum e ON t.oid = e.enumtypid
      JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace
      WHERE t.typname IN ('notification_type', 'notification_status', 'notification_priority')
      ORDER BY t.typname, e.enumsortorder
    `)
    
    console.log("Notification enums:", enums)
    
    // Check if the notifications table has the correct columns
    const notificationColumns = await db.execute(sql`
      SELECT column_name, data_type, udt_name
      FROM information_schema.columns
      WHERE table_name = 'notifications'
      ORDER BY ordinal_position
    `)
    
    console.log("Notification columns:", notificationColumns)
    
    console.log("Notification relations check complete.")
  } catch (error) {
    console.error("Error checking notification relations:", error)
  }
}

main()
  .then(() => process.exit(0))
  .catch((error) => {
    console.error(error)
    process.exit(1)
  })
