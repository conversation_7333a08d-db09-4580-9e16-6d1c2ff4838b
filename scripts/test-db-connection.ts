#!/usr/bin/env tsx

import { checkDatabaseConnection, db } from '../lib/db'
import { safeDbQuery } from '../lib/db/connection-utils'

async function testDatabaseConnection() {
  console.log('🔍 Testing database connection...')
  console.log('Database URL:', process.env.DATABASE_URL ? 'Set' : 'Not set')
  
  try {
    // Test 1: Basic connection check
    console.log('\n1. Testing basic connection...')
    const isHealthy = await checkDatabaseConnection()
    
    if (!isHealthy) {
      console.error('❌ Basic connection failed')
      return
    }
    
    console.log('✅ Basic connection successful')
    
    // Test 2: Simple query with retry logic
    console.log('\n2. Testing query with retry logic...')
    const result = await safeDbQuery(
      async () => {
        return await db.execute('SELECT NOW() as current_time, version() as db_version')
      },
      {
        timeout: 10000,
        maxRetries: 2,
        baseDelay: 1000
      }
    )
    
    console.log('✅ Query with retry successful:', {
      currentTime: result.rows[0]?.current_time,
      version: result.rows[0]?.db_version?.substring(0, 50) + '...'
    })
    
    // Test 3: Test organization query
    console.log('\n3. Testing organization table access...')
    const orgCount = await safeDbQuery(
      async () => {
        return await db.execute('SELECT COUNT(*) as count FROM organizations')
      },
      {
        timeout: 10000,
        maxRetries: 2
      }
    )
    
    console.log('✅ Organization table accessible. Count:', orgCount.rows[0]?.count)
    
    // Test 4: Test specific organization lookup
    console.log('\n4. Testing specific organization lookup...')
    const testOrgId = 'organization-live-3b75ccad-ef69-4314-9a10-9e1cf15c67d4'
    
    const orgResult = await safeDbQuery(
      async () => {
        return await db.execute(
          'SELECT id, name, slug, stytch_organization_id FROM organizations WHERE stytch_organization_id = $1',
          [testOrgId]
        )
      },
      {
        timeout: 10000,
        maxRetries: 2
      }
    )
    
    if (orgResult.rows.length > 0) {
      console.log('✅ Organization found:', {
        id: orgResult.rows[0]?.id,
        name: orgResult.rows[0]?.name,
        slug: orgResult.rows[0]?.slug,
        stytchOrgId: orgResult.rows[0]?.stytch_organization_id
      })
    } else {
      console.log('⚠️  Organization not found with Stytch ID:', testOrgId)
    }
    
    console.log('\n🎉 All database tests completed successfully!')
    
  } catch (error) {
    console.error('\n❌ Database test failed:', error)
    
    if (error instanceof Error) {
      console.error('Error details:', {
        message: error.message,
        code: (error as any).code,
        errno: (error as any).errno,
        address: (error as any).address,
        port: (error as any).port
      })
    }
  }
  
  process.exit(0)
}

// Run the test
testDatabaseConnection().catch(console.error)
