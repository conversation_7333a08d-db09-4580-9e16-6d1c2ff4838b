import { db } from "@/lib/db";
import { organizations } from "@/modules/organization/schema";
import { eq, and, isNull } from "drizzle-orm";

async function checkOrganization() {
  const slugToCheck = "wearefireflymedia-organization";
  console.log(`Checking for organization with slug: ${slugToCheck}`);

  try {
    // Query for active organization
    const activeOrg = await db.query.organizations.findFirst({
      where: and(eq(organizations.slug, slugToCheck), isNull(organizations.deletedAt)),
    });

    if (activeOrg) {
      console.log("Found active organization:");
      console.log(JSON.stringify(activeOrg, null, 2));
    } else {
      console.log(`No active organization found with slug: ${slugToCheck}`);

      // Check if it exists but is deleted
      const deletedOrg = await db.query.organizations.findFirst({
        where: eq(organizations.slug, slugToCheck),
      });

      if (deletedOrg) {
        console.log("Found a deleted organization with this slug:");
        console.log(JSON.stringify(deletedOrg, null, 2));
      } else {
        console.log(`No organization (active or deleted) found with slug: ${slugToCheck}`);
      }
    }
  } catch (error) {
    console.error("Error querying database:", error);
  } finally {
    // Ensure the script exits, especially if using a persistent DB connection pool
    // Depending on your db setup, you might need db.end() or similar
    console.log("Check complete.");
    process.exit(0); // Force exit if needed
  }
}

checkOrganization();
