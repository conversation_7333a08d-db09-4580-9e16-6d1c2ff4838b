import { db } from "@/lib/db"
import { sql } from "drizzle-orm"
// Schema imports are only for reference, not used in the script

async function main() {
  console.log("Starting shared location links migration...")

  try {
    // Check if the shared_location_links table already exists
    const tableExists = await checkIfTableExists("shared_location_links")
    
    if (tableExists) {
      console.log("shared_location_links table already exists, skipping migration")
      return
    }

    // Create the shared_location_links table
    await db.execute(sql`
      CREATE TABLE IF NOT EXISTS "shared_location_links" (
        "id" UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        "organization_id" UUID NOT NULL,
        "token" TEXT NOT NULL UNIQUE,
        "location_id" UUID NOT NULL REFERENCES "locations"("id"),
        "view_mode" TEXT NOT NULL DEFAULT 'client',
        "password_hash" TEXT,
        "expires_at" TIMESTAMP,
        "created_by" UUID NOT NULL REFERENCES "users"("id"),
        "created_at" TIMESTAMP NOT NULL DEFAULT now(),
        "updated_at" TIMESTAMP NOT NULL DEFAULT now()
      );
    `)
    
    console.log("Created shared_location_links table")

    // Create the shared_location_link_access_logs table
    await db.execute(sql`
      CREATE TABLE IF NOT EXISTS "shared_location_link_access_logs" (
        "id" UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        "shared_location_link_id" UUID NOT NULL REFERENCES "shared_location_links"("id") ON DELETE CASCADE,
        "ip_address" TEXT,
        "user_agent" TEXT,
        "accessed_at" TIMESTAMP NOT NULL DEFAULT now()
      );
    `)
    
    console.log("Created shared_location_link_access_logs table")

    // Create indexes for better performance
    await db.execute(sql`
      CREATE INDEX IF NOT EXISTS "shared_location_links_organization_id_idx" ON "shared_location_links" ("organization_id");
      CREATE INDEX IF NOT EXISTS "shared_location_links_location_id_idx" ON "shared_location_links" ("location_id");
      CREATE INDEX IF NOT EXISTS "shared_location_links_token_idx" ON "shared_location_links" ("token");
      CREATE INDEX IF NOT EXISTS "shared_location_link_access_logs_shared_location_link_id_idx" ON "shared_location_link_access_logs" ("shared_location_link_id");
    `)
    
    console.log("Created indexes for shared location links tables")

    console.log("Shared location links migration completed successfully")
  } catch (error) {
    console.error("Error during shared location links migration:", error)
    process.exit(1)
  }
}

async function checkIfTableExists(tableName: string): Promise<boolean> {
  const result = await db.execute(sql`
    SELECT EXISTS (
      SELECT FROM information_schema.tables 
      WHERE table_schema = 'public'
      AND table_name = ${tableName}
    );
  `)
  
  // Access the result based on the structure returned by db.execute
  const exists = result[0]?.exists
  return exists === true
}

main()
  .then(() => process.exit(0))
  .catch((error) => {
    console.error("Unhandled error during migration:", error)
    process.exit(1)
  })
