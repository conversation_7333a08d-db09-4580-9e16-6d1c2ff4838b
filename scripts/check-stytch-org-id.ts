import { extractUuidFromStytchId } from "@/lib/utils/stytchUtils"
import * as dotenv from "dotenv"

dotenv.config()

async function main() {
  console.log("Checking Stytch organization ID conversion...")

  try {
    // This is the Stytch organization ID from the browser logs
    const stytchOrgId = "organization-live-3b75ccad-ef69-4314-9a10-9e1cf15c67d4"
    
    // Extract UUID from Stytch organization ID
    const organizationUuid = extractUuidFromStytchId(stytchOrgId)
    
    console.log(`Stytch Organization ID: ${stytchOrgId}`)
    console.log(`Extracted UUID: ${organizationUuid}`)
    
    // This is the organization ID from the database (from check-map-data.ts)
    const databaseOrgId = "af6520e4-b62b-4034-8e6c-5fb93ae41d93"
    
    console.log(`Database Organization ID: ${databaseOrgId}`)
    console.log(`Do they match? ${organizationUuid === databaseOrgId ? 'Yes' : 'No'}`)
    
    if (organizationUuid !== databaseOrgId) {
      console.log("\nThe organization IDs don't match. This is likely the cause of the issue.")
      console.log("The locations are associated with a different organization ID than the one being used in the API request.")
      console.log("\nPossible solutions:")
      console.log("1. Update the locations in the database to use the correct organization ID")
      console.log("2. Update the API to use the organization ID from the database")
      console.log("3. Create a new organization with the correct ID")
    }
  } catch (error) {
    console.error("Error checking Stytch organization ID:", error)
    throw error
  }
}

main()
  .then(() => process.exit(0))
  .catch((error) => {
    console.error(error)
    process.exit(1)
  })
