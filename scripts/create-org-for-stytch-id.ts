import { db } from "@/lib/db"
import { organizations, organizationUserRoles } from "@/modules/organization/schema"
import { users } from "@/modules/user/schema"
import { eq } from "drizzle-orm"
import * as dotenv from "dotenv"
import { randomUUID } from "node:crypto"

dotenv.config()

async function main() {
  const stytchOrgId = "organization-live-3b75ccad-ef69-4314-9a10-9e1cf15c67d4"
  const stytchUserId = "member-live-43401ba0-bcf8-43e6-a726-ac203de9b44b"
  
  try {
    // Check if organization already exists
    const existingOrg = await db.query.organizations.findFirst({
      where: eq(organizations.stytchOrganizationId, stytchOrgId),
    })
    
    if (existingOrg) {
      console.log("Organization already exists:", existingOrg)
      return
    }
    
    // Create organization
    const [organization] = await db.insert(organizations).values({
      name: "Test Organization",
      slug: `test-org-${randomUUID().substring(0, 8)}`,
      stytchOrganizationId: stytchOrgId,
      isActive: true,
    }).returning()
    
    console.log("Organization created:", organization)
    
    // Find the user by Stytch ID
    const user = await db.query.users.findFirst({
      where: eq(users.stytchUserId, stytchUserId),
    })
    
    if (!user) {
      console.error("User not found with Stytch ID:", stytchUserId)
      return
    }
    
    // Add user to organization as owner
    const [membership] = await db.insert(organizationUserRoles).values({
      organizationId: organization.id,
      userId: user.id,
      role: "owner",
    }).returning()
    
    console.log("User added to organization:", membership)
    
    // Now let's check if we can find the organization by Stytch ID
    const createdOrg = await db.query.organizations.findFirst({
      where: eq(organizations.stytchOrganizationId, stytchOrgId),
      with: {
        members: {
          with: {
            user: true,
          },
        },
      },
    })
    
    console.log("Organization found by Stytch ID:", createdOrg)
  } catch (error) {
    console.error("Error creating organization:", error)
  }
}

main()
  .then(() => process.exit(0))
  .catch((error) => {
    console.error(error)
    process.exit(1)
  })
