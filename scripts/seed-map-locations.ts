import { db } from "../lib/db"
import { locations, projects } from "../lib/db/schema"
import * as dotenv from "dotenv"

dotenv.config()

// This script seeds additional locations for testing the map feature
// It requires that the seed-database.ts script has been run first to create the test organization and projects

async function main() {
  console.log("Seeding map locations...")

  try {
    // Get the test organization and projects
    const existingProjects = await db.select().from(projects).limit(2)
    
    if (existingProjects.length === 0) {
      console.error("No projects found. Please run seed-database.ts first.")
      process.exit(1)
    }

    const organizationId = existingProjects[0].organizationId
    const projectIds = existingProjects.map(p => p.id)

    console.log(`Using Organization ID: ${organizationId}`)
    console.log(`Using Project IDs: ${projectIds.join(", ")}`)

    // Create a variety of locations with different types and statuses
    const locationsData = [
      // Interior locations
      {
        name: "Downtown Loft",
        description: "Modern loft space with industrial features",
        address: {
          street: "123 Main St",
          city: "Los Angeles",
          state: "CA",
          postalCode: "90001",
          country: "USA",
          formatted: "123 Main St, Los Angeles, CA 90001, USA",
        },
        coordinates: {
          latitude: 34.0522,
          longitude: -118.2437,
        },
        type: "interior",
        status: "approved",
        projectId: projectIds[0],
        organizationId,
        isActive: true,
        permitsRequired: false,
        locationFeatures: ["high ceilings", "exposed brick", "large windows"],
        locationTags: ["urban", "modern", "industrial"],
      },
      {
        name: "Luxury Penthouse",
        description: "High-end penthouse with panoramic city views",
        address: {
          street: "456 Skyline Ave",
          city: "New York",
          state: "NY",
          postalCode: "10001",
          country: "USA",
          formatted: "456 Skyline Ave, New York, NY 10001, USA",
        },
        coordinates: {
          latitude: 40.7128,
          longitude: -74.0060,
        },
        type: "interior",
        status: "pending",
        projectId: projectIds[1],
        organizationId,
        isActive: true,
        permitsRequired: true,
        locationFeatures: ["panoramic views", "modern kitchen", "luxury finishes"],
        locationTags: ["luxury", "modern", "urban"],
      },
      {
        name: "Historic Mansion",
        description: "19th century mansion with period details",
        address: {
          street: "789 Heritage Ln",
          city: "Boston",
          state: "MA",
          postalCode: "02108",
          country: "USA",
          formatted: "789 Heritage Ln, Boston, MA 02108, USA",
        },
        coordinates: {
          latitude: 42.3601,
          longitude: -71.0589,
        },
        type: "interior",
        status: "secured",
        projectId: projectIds[0],
        organizationId,
        isActive: true,
        permitsRequired: true,
        locationFeatures: ["period details", "grand staircase", "ornate moldings"],
        locationTags: ["historic", "elegant", "classic"],
      },
      
      // Exterior locations
      {
        name: "Urban Alleyway",
        description: "Gritty urban alley with interesting textures",
        address: {
          street: "123 Back Alley",
          city: "Chicago",
          state: "IL",
          postalCode: "60601",
          country: "USA",
          formatted: "123 Back Alley, Chicago, IL 60601, USA",
        },
        coordinates: {
          latitude: 41.8781,
          longitude: -87.6298,
        },
        type: "exterior",
        status: "approved",
        projectId: projectIds[1],
        organizationId,
        isActive: true,
        permitsRequired: true,
        locationFeatures: ["graffiti", "cobblestone", "fire escapes"],
        locationTags: ["urban", "gritty", "industrial"],
      },
      {
        name: "Beachfront Property",
        description: "Private beach with stunning ocean views",
        address: {
          street: "456 Ocean Dr",
          city: "Miami",
          state: "FL",
          postalCode: "33139",
          country: "USA",
          formatted: "456 Ocean Dr, Miami, FL 33139, USA",
        },
        coordinates: {
          latitude: 25.7617,
          longitude: -80.1918,
        },
        type: "exterior",
        status: "unavailable",
        projectId: projectIds[0],
        organizationId,
        isActive: true,
        permitsRequired: true,
        locationFeatures: ["sandy beach", "ocean view", "palm trees"],
        locationTags: ["beach", "tropical", "scenic"],
      },
      {
        name: "Mountain Overlook",
        description: "Scenic mountain vista with panoramic views",
        address: {
          street: "789 Mountain Rd",
          city: "Denver",
          state: "CO",
          postalCode: "80202",
          country: "USA",
          formatted: "789 Mountain Rd, Denver, CO 80202, USA",
        },
        coordinates: {
          latitude: 39.7392,
          longitude: -104.9903,
        },
        type: "exterior",
        status: "approved",
        projectId: projectIds[1],
        organizationId,
        isActive: true,
        permitsRequired: false,
        locationFeatures: ["panoramic views", "hiking trails", "natural setting"],
        locationTags: ["nature", "scenic", "mountains"],
      },
      
      // Studio locations
      {
        name: "Soundstage A",
        description: "Large professional soundstage with green screen",
        address: {
          street: "123 Studio Way",
          city: "Burbank",
          state: "CA",
          postalCode: "91502",
          country: "USA",
          formatted: "123 Studio Way, Burbank, CA 91502, USA",
        },
        coordinates: {
          latitude: 34.1808,
          longitude: -118.3090,
        },
        type: "studio",
        status: "approved",
        projectId: projectIds[0],
        organizationId,
        isActive: true,
        permitsRequired: false,
        locationFeatures: ["green screen", "lighting grid", "sound insulation"],
        locationTags: ["professional", "controlled environment", "versatile"],
      },
      {
        name: "Indie Film Studio",
        description: "Small independent film studio with character",
        address: {
          street: "456 Indie Blvd",
          city: "Austin",
          state: "TX",
          postalCode: "78701",
          country: "USA",
          formatted: "456 Indie Blvd, Austin, TX 78701, USA",
        },
        coordinates: {
          latitude: 30.2672,
          longitude: -97.7431,
        },
        type: "studio",
        status: "pending",
        projectId: projectIds[1],
        organizationId,
        isActive: true,
        permitsRequired: false,
        locationFeatures: ["exposed brick", "natural light", "vintage equipment"],
        locationTags: ["indie", "character", "creative"],
      },
      
      // Other location types
      {
        name: "Abandoned Warehouse",
        description: "Spacious warehouse with industrial character",
        address: {
          street: "789 Industrial Pkwy",
          city: "Detroit",
          state: "MI",
          postalCode: "48201",
          country: "USA",
          formatted: "789 Industrial Pkwy, Detroit, MI 48201, USA",
        },
        coordinates: {
          latitude: 42.3314,
          longitude: -83.0458,
        },
        type: "warehouse",
        status: "approved",
        projectId: projectIds[0],
        organizationId,
        isActive: true,
        permitsRequired: true,
        locationFeatures: ["high ceilings", "industrial features", "open space"],
        locationTags: ["industrial", "gritty", "spacious"],
      },
      {
        name: "Cozy Cafe",
        description: "Charming cafe with vintage decor",
        address: {
          street: "123 Coffee St",
          city: "Portland",
          state: "OR",
          postalCode: "97201",
          country: "USA",
          formatted: "123 Coffee St, Portland, OR 97201, USA",
        },
        coordinates: {
          latitude: 45.5051,
          longitude: -122.6750,
        },
        type: "restaurant",
        status: "secured",
        projectId: projectIds[1],
        organizationId,
        isActive: true,
        permitsRequired: true,
        locationFeatures: ["vintage decor", "cozy atmosphere", "outdoor seating"],
        locationTags: ["cozy", "charming", "urban"],
      },
      {
        name: "Historic Theater",
        description: "Beautifully preserved art deco theater",
        address: {
          street: "456 Broadway",
          city: "San Francisco",
          state: "CA",
          postalCode: "94102",
          country: "USA",
          formatted: "456 Broadway, San Francisco, CA 94102, USA",
        },
        coordinates: {
          latitude: 37.7749,
          longitude: -122.4194,
        },
        type: "historic",
        status: "approved",
        projectId: projectIds[0],
        organizationId,
        isActive: true,
        permitsRequired: true,
        locationFeatures: ["art deco", "ornate details", "stage"],
        locationTags: ["historic", "elegant", "cultural"],
      },
      {
        name: "Modern Office Building",
        description: "Sleek glass and steel office building",
        address: {
          street: "789 Tech Blvd",
          city: "Seattle",
          state: "WA",
          postalCode: "98101",
          country: "USA",
          formatted: "789 Tech Blvd, Seattle, WA 98101, USA",
        },
        coordinates: {
          latitude: 47.6062,
          longitude: -122.3321,
        },
        type: "office",
        status: "rejected",
        projectId: projectIds[1],
        organizationId,
        isActive: true,
        permitsRequired: false,
        locationFeatures: ["glass facade", "open floor plan", "modern amenities"],
        locationTags: ["modern", "corporate", "sleek"],
      },
      {
        name: "Rustic Farmhouse",
        description: "Authentic farmhouse with rural charm",
        address: {
          street: "123 Country Rd",
          city: "Nashville",
          state: "TN",
          postalCode: "37203",
          country: "USA",
          formatted: "123 Country Rd, Nashville, TN 37203, USA",
        },
        coordinates: {
          latitude: 36.1627,
          longitude: -86.7816,
        },
        type: "house",
        status: "pending",
        projectId: projectIds[0],
        organizationId,
        isActive: true,
        permitsRequired: false,
        locationFeatures: ["rustic charm", "wooden beams", "large property"],
        locationTags: ["rural", "rustic", "authentic"],
      },
      {
        name: "Urban Park",
        description: "Vibrant city park with diverse landscapes",
        address: {
          street: "456 Park Ave",
          city: "Atlanta",
          state: "GA",
          postalCode: "30303",
          country: "USA",
          formatted: "456 Park Ave, Atlanta, GA 30303, USA",
        },
        coordinates: {
          latitude: 33.7490,
          longitude: -84.3880,
        },
        type: "park",
        status: "approved",
        projectId: projectIds[1],
        organizationId,
        isActive: true,
        permitsRequired: true,
        locationFeatures: ["open spaces", "water features", "walking paths"],
        locationTags: ["urban", "natural", "public"],
      },
    ]

    // Insert the locations
    const createdLocations = await Promise.all(
      locationsData.map(async (location) => {
        // Create a copy of the location data without creatorId, approvedBy, and approvedAt fields
        const { 
          name, description, address, coordinates, type, status, 
          projectId, organizationId, isActive, permitsRequired, 
          locationFeatures, locationTags 
        } = location;
        
        const locationData = {
          name, 
          description, 
          address, 
          coordinates, 
          type, 
          status, 
          projectId, 
          organizationId, 
          isActive, 
          permitsRequired, 
          locationFeatures, 
          locationTags
        };
        
        const [createdLocation] = await db.insert(locations).values(locationData).returning()
        return createdLocation
      }),
    )

    console.log(`Created ${createdLocations.length} map locations`)
    console.log("Map locations seeded successfully!")
  } catch (error) {
    console.error("Error seeding map locations:", error)
    throw error
  }
}

main()
  .then(() => process.exit(0))
  .catch((error) => {
    console.error(error)
    process.exit(1)
  })
