import { db } from "@/lib/db";
import { sql } from "drizzle-orm";
import { readFile } from "node:fs/promises";
import { join } from "node:path";

async function main() {
  try {
    console.log("Starting migration to add deleted_at column to location_media table...");
    
    // Read the SQL file
    const sqlPath = join(process.cwd(), "drizzle/migrations/0015_add_deleted_at_to_location_media.sql");
    const sqlContent = await readFile(sqlPath, "utf-8");
    
    // Execute the SQL
    await db.execute(sql.raw(sqlContent));
    
    console.log("Migration completed successfully!");
  } catch (error) {
    console.error("Error running migration:", error);
    process.exit(1);
  } finally {
    process.exit(0);
  }
}

main();
