import { db } from "@/lib/db"
import { locations } from "@/modules/location/schema"
import { eq, isNull, and, desc } from "drizzle-orm"
import { getLocations } from "@/modules/location/service"

async function main() {
  console.log("Verifying location query fix...")
  
  try {
    // Get a sample organization ID
    const sampleLocation = await db.query.locations.findFirst({
      where: isNull(locations.deletedAt),
      orderBy: [desc(locations.createdAt)],
    })
    
    if (!sampleLocation) {
      console.error("No locations found in database")
      process.exit(1)
    }
    
    const sampleOrgId = sampleLocation.organizationId
    console.log(`Using organization ID: ${sampleOrgId}`)
    
    // Get all locations for this organization directly from the database
    const allLocationsFromDb = await db.query.locations.findMany({
      where: and(
        eq(locations.organizationId, sampleOrgId),
        isNull(locations.deletedAt)
      ),
    })
    
    console.log(`Direct DB query found ${allLocationsFromDb.length} locations`)
    
    // Count locations with null project IDs
    const nullProjectLocations = allLocationsFromDb.filter(loc => loc.projectId === null)
    console.log(`Of these, ${nullProjectLocations.length} have null project IDs`)
    
    if (nullProjectLocations.length > 0) {
      console.log("\nLocations with null project IDs:")
      for (const loc of nullProjectLocations) {
        console.log(`- ${loc.name} (ID: ${loc.id})`)
      }
    }
    
    // Test the fixed getLocations function
    console.log("\nTesting fixed getLocations function:")
    
    // Test with no filters
    const locationsNoFilter = await getLocations(sampleOrgId)
    console.log(`getLocations with no filters returned ${locationsNoFilter.length} locations`)
    
    // Count locations with null project IDs in the result
    const nullProjectInResult = locationsNoFilter.filter(loc => !loc.projectId).length
    console.log(`Of these, ${nullProjectInResult} have null project IDs`)
    
    // Check if the locations returned by getLocations match the ones from direct DB query
    console.log("\nComparing results:")
    const directDbIds = new Set(allLocationsFromDb.map(loc => loc.id))
    const serviceIds = new Set(locationsNoFilter.map(loc => loc.id))
    
    // Find IDs that are in the direct DB query but not in the service result
    const missingIds = [...directDbIds].filter(id => !serviceIds.has(id))
    console.log(`IDs in direct DB query but missing from service result: ${missingIds.length}`)
    
    if (missingIds.length > 0) {
      console.log("Missing locations:")
      for (const id of missingIds) {
        const loc = allLocationsFromDb.find(l => l.id === id)
        if (loc) {
          console.log(`- ${loc.name} (ID: ${id})`)
        }
      }
      console.log("\nFIX FAILED: Some locations are still missing from the results")
      process.exit(1)
    } else {
      console.log("\nFIX SUCCESSFUL: All locations are now being returned correctly!")
      
      // Verify that null projectId locations are included
      if (nullProjectLocations.length > 0 && nullProjectInResult === nullProjectLocations.length) {
        console.log(`✅ All ${nullProjectLocations.length} locations with null projectId are now included in the results`)
      } else if (nullProjectLocations.length > 0) {
        console.log(`⚠️ Warning: Only ${nullProjectInResult} of ${nullProjectLocations.length} locations with null projectId are included`)
      } else {
        console.log("ℹ️ No locations with null projectId found to test with")
      }
    }
    
  } catch (error) {
    console.error("Error verifying location query fix:", error)
    process.exit(1)
  }
  
  process.exit(0)
}

main()
