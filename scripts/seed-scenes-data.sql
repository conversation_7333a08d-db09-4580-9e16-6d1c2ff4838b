-- Seed scenes data
DO $$
DECLARE
  project_id UUID;
  location_id UUID;
  user_id UUID;
  scene_id UUID;
  scene_location_id UUID;
BEGIN
  -- Get a project ID
  SELECT id INTO project_id FROM "projects" LIMIT 1;
  
  -- Get a location ID
  SELECT id INTO location_id FROM "locations" LIMIT 1;
  
  -- Get a user ID
  SELECT id INTO user_id FROM "users" LIMIT 1;
  
  -- Insert a scene
  INSERT INTO "scenes" (
    "name",
    "description",
    "scene_number",
    "time_of_day",
    "interior",
    "exterior",
    "status",
    "project_id",
    "final_location_id",
    "created_by_id"
  ) VALUES (
    'Opening Scene',
    'The main character is introduced in their everyday environment',
    'Scene 1',
    'Morning',
    true,
    false,
    'planning',
    project_id,
    location_id,
    user_id
  ) RETURNING id INTO scene_id;
  
  -- Insert a scene location
  INSERT INTO "scene_locations" (
    "scene_id",
    "location_id",
    "notes",
    "added_by_id",
    "is_selected"
  ) VALUES (
    scene_id,
    location_id,
    'This location has great natural lighting for morning scenes',
    user_id,
    true
  ) RETURNING id INTO scene_location_id;
  
  -- Insert a scene note
  INSERT INTO "scene_notes" (
    "scene_id",
    "content",
    "author_id"
  ) VALUES (
    scene_id,
    'We should consider using a wide-angle lens for the establishing shot',
    user_id
  );
  
  -- Insert a location vote
  INSERT INTO "location_votes" (
    "scene_location_id",
    "user_id",
    "vote"
  ) VALUES (
    scene_location_id,
    user_id,
    'up'
  );
  
  -- Insert another scene
  INSERT INTO "scenes" (
    "name",
    "description",
    "scene_number",
    "time_of_day",
    "interior",
    "exterior",
    "status",
    "project_id",
    "created_by_id"
  ) VALUES (
    'Climactic Confrontation',
    'The protagonist faces their antagonist in a final showdown',
    'Scene 42',
    'Night',
    false,
    true,
    'planning',
    project_id,
    user_id
  ) RETURNING id INTO scene_id;
  
  -- Insert scene locations for the second scene
  INSERT INTO "scene_locations" (
    "scene_id",
    "location_id",
    "notes",
    "added_by_id",
    "is_selected"
  ) VALUES (
    scene_id,
    location_id,
    'This location has dramatic lighting possibilities for night scenes',
    user_id,
    false
  );
  
  -- Get another location
  SELECT id INTO location_id FROM "locations" WHERE id != location_id LIMIT 1;
  
  -- Insert another scene location for the second scene
  INSERT INTO "scene_locations" (
    "scene_id",
    "location_id",
    "notes",
    "added_by_id",
    "is_selected"
  ) VALUES (
    scene_id,
    location_id,
    'Alternative location with more space for action sequences',
    user_id,
    false
  );
  
  -- Insert a scene note for the second scene
  INSERT INTO "scene_notes" (
    "scene_id",
    "content",
    "author_id"
  ) VALUES (
    scene_id,
    'Need to scout for a location with good acoustics for the dialogue',
    user_id
  );
END $$;
