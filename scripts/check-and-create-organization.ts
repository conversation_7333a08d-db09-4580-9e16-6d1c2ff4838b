import { db } from "@/lib/db"
import { organizations } from "@/modules/organization/schema"
import { eq, or } from "drizzle-orm"
import * as dotenv from "dotenv"

dotenv.config()

async function main() {
  console.log("Checking and updating organization...")

  try {
    // The Stytch organization ID
    const stytchOrgId = "organization-live-3b75ccad-ef69-4314-9a10-9e1cf15c67d4"
    
    // The UUID extracted from the Stytch organization ID
    const orgId = "3b75ccad-ef69-4314-9a10-9e1cf15c67d4"
    
    // The slug we want to use
    const slug = "wearefireflymedia-organization"
    
    // Check if the organization already exists by ID or slug
    const existingOrg = await db.query.organizations.findFirst({
      where: or(
        eq(organizations.id, orgId),
        eq(organizations.slug, slug)
      )
    })
    
    if (existingOrg) {
      console.log(`Organization already exists:`)
      console.log(existingOrg)
      
      // If the organization exists but has a different ID, update it
      if (existingOrg.id !== orgId) {
        console.log(`\nUpdating organization ID from ${existingOrg.id} to ${orgId}...`)
        
        // This would be the ideal approach, but it might not work due to foreign key constraints
        // Instead, we'll update the locations to use the existing organization ID
        console.log(`\nInstead of updating the organization ID, we'll update the locations to use the existing organization ID.`)
        console.log(`\nRun the following command to update the locations:`)
        console.log(`npx tsx scripts/update-location-organization-ids.ts --current-org-id af6520e4-b62b-4034-8e6c-5fb93ae41d93 --new-org-id ${existingOrg.id}`)
        
        return
      }
      
      // If the organization exists but doesn't have the Stytch organization ID, update it
      if (existingOrg.stytchOrganizationId !== stytchOrgId) {
        console.log(`\nUpdating Stytch organization ID from ${existingOrg.stytchOrganizationId} to ${stytchOrgId}...`)
        
        const updatedOrg = await db
          .update(organizations)
          .set({ stytchOrganizationId: stytchOrgId, updatedAt: new Date() })
          .where(eq(organizations.id, existingOrg.id))
          .returning()
        
        console.log("Organization updated successfully:")
        console.log(updatedOrg[0])
      }
      
      return
    }
    
    console.log(`Organization with ID ${orgId} and slug ${slug} does not exist. Creating it...`)
    
    try {
      // Create the organization
      const newOrg = await db.insert(organizations).values({
        id: orgId,
        name: "Firefly Media",
        slug: slug,
        stytchOrganizationId: stytchOrgId,
        createdAt: new Date(),
        updatedAt: new Date(),
        isActive: true,
        metadata: {
          theme: "dark",
          features: {
            mapEnabled: true,
            calendarEnabled: true,
            documentsEnabled: true
          }
        }
      }).returning()
      
      console.log("Organization created successfully:")
      console.log(newOrg[0])
    } catch (error) {
      console.error("Error creating organization:", error)
      console.log("\nFalling back to using the existing organization...")
      
      // Find the organization by slug
      const orgBySlug = await db.query.organizations.findFirst({
        where: eq(organizations.slug, slug)
      })
      
      if (orgBySlug) {
        console.log(`Found organization with slug ${slug}:`)
        console.log(orgBySlug)
        
        console.log(`\nRun the following command to update the locations:`)
        console.log(`npx tsx scripts/update-location-organization-ids.ts --current-org-id af6520e4-b62b-4034-8e6c-5fb93ae41d93 --new-org-id ${orgBySlug.id}`)
      } else {
        console.log("Could not find an organization with the specified slug.")
      }
    }
  } catch (error) {
    console.error("Error checking/creating organization:", error)
    throw error
  }
}

main()
  .then(() => process.exit(0))
  .catch((error) => {
    console.error(error)
    process.exit(1)
  })
