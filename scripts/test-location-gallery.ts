import { db } from "@/lib/db";
import { locationMedia } from "@/modules/location/schema";
import { eq, and, isNull } from "drizzle-orm";

/**
 * This script tests the location gallery functionality by:
 * 1. Fetching a location's media items
 * 2. Checking if the uploader relation is properly included
 * 3. Verifying that the media URLs are valid
 * 
 * Run with: npx tsx scripts/test-location-gallery.ts [locationId]
 */
async function main() {
  // Get location ID from command line args or use a default
  const locationId = process.argv[2] || "00000000-0000-0000-0000-000000000000";
  
  console.log(`Testing location gallery for location ID: ${locationId}`);
  
  try {
    // Fetch media items for the location with uploader relation
    const media = await db.query.locationMedia.findMany({
      where: and(
        eq(locationMedia.locationId, locationId),
        isNull(locationMedia.deletedAt)
      ),
      with: {
        uploader: {
          columns: {
            id: true,
            name: true,
          }
        }
      }
    });
    
    console.log(`Found ${media.length} media items for location`);
    
    // Check each media item
    media.forEach((item, index) => {
      console.log(`\nMedia item #${index + 1}:`);
      console.log(`ID: ${item.id}`);
      console.log(`Type: ${item.type}`);
      console.log(`URL: ${item.url}`);
      console.log(`Thumbnail URL: ${item.thumbnailUrl || 'None'}`);
      
      // Check if uploader relation is included
      if (item.uploader) {
        console.log(`Uploader: ${item.uploader.name} (${item.uploader.id})`);
      } else {
        console.log(`Uploader ID: ${item.uploadedBy || 'None'}`);
      }
      
      // Validate URL format
      if (!item.url) {
        console.error(`  ERROR: Missing URL for media item ${item.id}`);
      } else if (!item.url.startsWith('http')) {
        console.error(`  ERROR: Invalid URL format for media item ${item.id}: ${item.url}`);
      }
    });
    
    console.log("\nTest completed successfully");
  } catch (error) {
    console.error("Error testing location gallery:", error);
  } finally {
    process.exit(0);
  }
}

main();
