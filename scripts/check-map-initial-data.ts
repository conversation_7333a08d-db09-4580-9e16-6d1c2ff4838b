import { db } from "@/lib/db"
import { locations } from "@/modules/location/schema"
import { eq, isNull, desc, and } from "drizzle-orm"
import { getLocations } from "@/modules/location/service"

async function main() {
  console.log("Checking map initial data issue...")
  
  try {
    // Get the most recent 10 locations
    const recentLocations = await db.query.locations.findMany({
      where: isNull(locations.deletedAt),
      orderBy: [desc(locations.createdAt)],
      limit: 10,
    })
    
    console.log(`Found ${recentLocations.length} recent locations in database`)
    
    // Get a sample organization ID from the recent locations
    const sampleOrgId = recentLocations[0]?.organizationId
    
    if (sampleOrgId) {
      console.log(`Using organization ID: ${sampleOrgId}`)
      
      // Test with no filters (should include null project IDs)
      const allLocations = await getLocations(sampleOrgId)
      console.log(`getLocations with no filters returned ${allLocations.length} locations`)
      
      // Count locations with null project IDs in the result
      const nullProjectInResult = allLocations.filter(loc => !loc.projectId).length
      console.log(`Of these, ${nullProjectInResult} have null project IDs`)
      
      // Check if our two known null project ID locations are in the results
      const pitotHouse = allLocations.find(loc => loc.name === "Pitot House")
      const rosemaryBeach = allLocations.find(loc => loc.name === "Rosemary Beach")
      
      console.log("\nChecking for specific locations:")
      console.log(`Pitot House in results: ${pitotHouse ? 'YES' : 'NO'}`)
      console.log(`Rosemary Beach in results: ${rosemaryBeach ? 'YES' : 'NO'}`)
      
      // Now let's check what's happening in the database query
      console.log("\nChecking database query directly:")
      
      // Direct database query to check for null project IDs
      const nullProjectLocations = await db.query.locations.findMany({
        where: and(
          eq(locations.organizationId, sampleOrgId),
          isNull(locations.projectId),
          isNull(locations.deletedAt)
        ),
      })
      
      console.log(`Direct query found ${nullProjectLocations.length} locations with null project IDs`)
      
      if (nullProjectLocations.length > 0) {
        console.log("Locations with null project IDs:")
        for (const loc of nullProjectLocations) {
          console.log(`- ${loc.name} (ID: ${loc.id})`)
        }
      }
      
      // Check if the locations are active
      console.log("\nChecking if locations are active:")
      for (const loc of nullProjectLocations) {
        console.log(`- ${loc.name}: isActive = ${loc.isActive}`)
      }
      
      // Check if the locations have coordinates
      console.log("\nChecking if locations have valid coordinates:")
      for (const loc of nullProjectLocations) {
        const hasCoordinates = loc.coordinates && 
                              typeof loc.coordinates === 'object' && 
                              'latitude' in loc.coordinates && 
                              'longitude' in loc.coordinates
        console.log(`- ${loc.name}: hasCoordinates = ${hasCoordinates}`)
        if (hasCoordinates) {
          console.log(`  Coordinates: ${JSON.stringify(loc.coordinates)}`)
        }
      }
    }
    
  } catch (error) {
    console.error("Error checking map initial data:", error)
    process.exit(1)
  }
  
  process.exit(0)
}

main()
