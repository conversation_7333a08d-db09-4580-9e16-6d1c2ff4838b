#!/usr/bin/env ts-node

/**
 * <PERSON><PERSON><PERSON> to check the location gallery UI component
 * 
 * This script verifies that the location gallery component is correctly
 * displaying images and handling the media items properly.
 * 
 * Usage:
 * pnpm tsx scripts/check-location-gallery-ui.ts
 */

import { db } from "@/lib/db";
import { locationMedia, locations } from "@/lib/db/schema";
import { eq } from "drizzle-orm";
import { getLocation } from "@/modules/location/service";

async function main() {
  console.log("Checking location gallery UI...");

  // 1. Check if the Pitot House location exists
  const pitotHouse = await db.query.locations.findFirst({
    where: eq(locations.name, "Pitot House"),
  });

  if (!pitotHouse) {
    console.error("❌ Pitot House location not found");
    return;
  }

  console.log(`✅ Found Pitot House location with ID: ${pitotHouse.id}`);

  // 2. Check if the location has media items
  const mediaItems = await db.query.locationMedia.findMany({
    where: eq(locationMedia.locationId, pitotHouse.id),
  });

  if (mediaItems.length === 0) {
    console.error("❌ No media items found for Pitot House");
    return;
  }

  console.log(`✅ Found ${mediaItems.length} media items for Pitot House`);

  // 3. Check if we can fetch location with media relations
  const locationWithMedia = await getLocation(pitotHouse.id);

  if (!locationWithMedia || !locationWithMedia.media || locationWithMedia.media.length === 0) {
    console.error("❌ Failed to fetch location with media relations");
    return;
  }

  console.log(`✅ Successfully fetched location with ${locationWithMedia.media.length} media items`);

  // 4. Verify media item structure
  const firstItem = locationWithMedia.media[0];
  
  console.log("\nFirst media item details:");
  console.log(`- ID: ${firstItem.id}`);
  console.log(`- Title: ${firstItem.title || 'No title'}`);
  console.log(`- URL: ${firstItem.url}`);
  console.log(`- Type: ${firstItem.type}`);
  
  // 5. Check uploader relation
  if (firstItem.uploader) {
    console.log(`- Uploader: ${firstItem.uploader.name} (${firstItem.uploader.id})`);
    console.log("✅ Uploader relation is correctly included");
  } else {
    console.log("⚠️ Uploader relation is missing");
  }

  // 6. Verify URL is accessible
  try {
    const response = await fetch(firstItem.url, { method: 'HEAD' });
    if (response.ok) {
      console.log("✅ Media URL is accessible");
    } else {
      console.error(`❌ Media URL returned status: ${response.status}`);
    }
  } catch (error) {
    console.error("❌ Failed to check media URL:", error);
  }

  // 7. Provide instructions for manual verification
  console.log("\n=== Manual Verification Steps ===");
  console.log("1. Navigate to the Pitot House location details page");
  console.log("2. Verify that images are fully visible in the gallery (not cut off)");
  console.log("3. Click the fullscreen button on an image to test fullscreen mode");
  console.log("4. Test zoom in/out controls in fullscreen mode");
  console.log("5. Test navigation between images using the arrow buttons");
  console.log("6. Test thumbnail navigation in both normal and fullscreen modes");
  console.log("\nLocation URL to test:");
  console.log(`http://localhost:3000/organizations/your-org-slug/locations/${pitotHouse.id}`);
}

main()
  .then(() => process.exit(0))
  .catch((error) => {
    console.error("Error:", error);
    process.exit(1);
  });
