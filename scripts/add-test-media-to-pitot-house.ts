import { db } from "@/lib/db";
import { locationMedia } from "@/modules/location/schema";

// Pitot House location ID from the check-pitot-house-media.ts script
const PITOT_HOUSE_ID = "e7afb8f3-90ec-461e-98e4-b5e5f3aa8aad";

// Sample images for testing
const TEST_IMAGES = [
  {
    url: "https://images.unsplash.com/photo-1600585154340-be6161a56a0c?q=80&w=1470&auto=format&fit=crop",
    title: "Pitot House Exterior",
    type: "image",
  },
  {
    url: "https://images.unsplash.com/photo-1600607687920-4e2a09cf159d?q=80&w=1470&auto=format&fit=crop",
    title: "Pitot House Interior",
    type: "image",
  },
  {
    url: "https://images.unsplash.com/photo-1600566753086-00f18fb6b3ea?q=80&w=1470&auto=format&fit=crop",
    title: "Pitot House Garden",
    type: "image",
  }
];

async function addTestMediaToPitotHouse() {
  console.log("Adding test media to Pitot House location...");
  
  try {
    // First, check if the location exists
    const location = await db.query.locations.findFirst({
      where: (locations, { eq }) => eq(locations.id, PITOT_HOUSE_ID),
      columns: {
        id: true,
        name: true,
      },
    });
    
    if (!location) {
      console.error(`Location with ID ${PITOT_HOUSE_ID} not found`);
      return;
    }
    
    console.log(`Found location: ${location.name} (ID: ${location.id})`);
    
    // Check if there are already media items for this location
    const existingMedia = await db.query.locationMedia.findMany({
      where: (locationMedia, { eq }) => eq(locationMedia.locationId, PITOT_HOUSE_ID),
      columns: {
        id: true,
      },
    });
    
    if (existingMedia.length > 0) {
      console.log(`Location already has ${existingMedia.length} media items. Skipping...`);
      return;
    }
    
    // Add test media items
    const mediaItems = TEST_IMAGES.map((image, index) => ({
      locationId: PITOT_HOUSE_ID,
      url: image.url,
      thumbnailUrl: image.url,
      type: image.type,
      title: image.title,
      uploadedAt: new Date(),
      isPublic: true,
      ordering: index,
    }));
    
    const result = await db.insert(locationMedia).values(mediaItems).returning();
    
    console.log(`Successfully added ${result.length} media items to Pitot House location`);
    console.log("Media items:", result.map(item => ({ id: item.id, title: item.title })));
    
  } catch (error) {
    console.error("Error adding test media:", error);
  }
}

// Run the function
addTestMediaToPitotHouse().then(() => {
  console.log("Done");
  process.exit(0);
}).catch((error) => {
  console.error("Error:", error);
  process.exit(1);
});
