import { db } from "@/lib/db"
import { users } from "@/modules/user/schema"
import { organizations } from "@/modules/organization/schema"
import { NotificationService } from "@/modules/notification/service"
import { eq } from "drizzle-orm"
import * as dotenv from "dotenv"

dotenv.config()

async function main() {
  const stytchUserId = "member-live-43401ba0-bcf8-43e6-a726-ac203de9b44b"
  const stytchOrgId = "organization-live-3b75ccad-ef69-4314-9a10-9e1cf15c67d4"
  
  try {
    // Find user by Stytch ID
    const user = await db.query.users.findFirst({
      where: eq(users.stytchUserId, stytchUserId),
    })
    
    if (!user) {
      console.error("User not found with Stytch ID:", stytchUserId)
      return
    }
    
    console.log("User found:", user)
    
    // Find organization by Stytch ID
    const organization = await db.query.organizations.findFirst({
      where: eq(organizations.stytchOrganizationId, stytchOrgId),
    })
    
    if (!organization) {
      console.error("Organization not found with Stytch ID:", stytchOrgId)
      return
    }
    
    console.log("Organization found:", organization)
    
    // Create a test notification
    const notification = await NotificationService.createNotification({
      title: "Test Notification",
      message: "This is a test notification",
      type: "system_notification",
      priority: "medium",
      recipientId: user.id,
      organizationId: organization.id,
      metadata: {},
    })
    
    console.log("Notification created:", notification)
    
    // Get notification count
    const count = await NotificationService.getNotificationCount(user.id, organization.id)
    
    console.log("Notification count:", count)
    
    // Mark notification as read
    const updatedNotification = await NotificationService.markNotificationAsRead(notification.id)
    
    console.log("Notification marked as read:", updatedNotification)
    
    // Get updated notification count
    const updatedCount = await NotificationService.getNotificationCount(user.id, organization.id)
    
    console.log("Updated notification count:", updatedCount)
  } catch (error) {
    console.error("Error testing notification API:", error)
  }
}

main()
  .then(() => process.exit(0))
  .catch((error) => {
    console.error(error)
    process.exit(1)
  })
