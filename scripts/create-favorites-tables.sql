-- Create favorite_lists table
CREATE TABLE IF NOT EXISTS "favorite_lists" (
  "id" UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  "organization_id" UUID NOT NULL REFERENCES "organizations"("id"),
  "name" VARCHAR(255) NOT NULL,
  "type" VARCHAR(50) NOT NULL CHECK ("type" IN ('project', 'scene', 'custom')),
  "project_id" UUID REFERENCES "projects"("id"),
  "scene_id" UUID REFERENCES "scenes"("id"),
  "created_by" UUID NOT NULL REFERENCES "users"("id"),
  "created_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
  "updated_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
  "deleted_at" TIMESTAMP WITH TIME ZONE
);

-- Create favorite_list_locations junction table
CREATE TABLE IF NOT EXISTS "favorite_list_locations" (
  "id" UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  "favorite_list_id" UUID NOT NULL REFERENCES "favorite_lists"("id"),
  "location_id" UUID NOT NULL REFERENCES "locations"("id"),
  "added_by" UUID NOT NULL REFERENCES "users"("id"),
  "added_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW()
);

-- Create unique index to prevent duplicate entries
CREATE UNIQUE INDEX IF NOT EXISTS "favorite_list_location_idx" ON "favorite_list_locations" ("favorite_list_id", "location_id");

-- Add indexes for better query performance
CREATE INDEX IF NOT EXISTS "favorite_lists_organization_id_idx" ON "favorite_lists" ("organization_id");
CREATE INDEX IF NOT EXISTS "favorite_lists_project_id_idx" ON "favorite_lists" ("project_id") WHERE "project_id" IS NOT NULL;
CREATE INDEX IF NOT EXISTS "favorite_lists_scene_id_idx" ON "favorite_lists" ("scene_id") WHERE "scene_id" IS NOT NULL;
CREATE INDEX IF NOT EXISTS "favorite_lists_created_by_idx" ON "favorite_lists" ("created_by");
CREATE INDEX IF NOT EXISTS "favorite_list_locations_favorite_list_id_idx" ON "favorite_list_locations" ("favorite_list_id");
CREATE INDEX IF NOT EXISTS "favorite_list_locations_location_id_idx" ON "favorite_list_locations" ("location_id");
CREATE INDEX IF NOT EXISTS "favorite_list_locations_added_by_idx" ON "favorite_list_locations" ("added_by");
