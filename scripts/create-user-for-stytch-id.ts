import { db } from "@/lib/db"
import { users } from "@/modules/user/schema"
import { eq } from "drizzle-orm"
import * as dotenv from "dotenv"
import { randomUUID } from "node:crypto"

dotenv.config()

async function main() {
  const stytchUserId = "member-live-43401ba0-bcf8-43e6-a726-ac203de9b44b"
  const stytchOrgId = "organization-live-3b75ccad-ef69-4314-9a10-9e1cf15c67d4"
  
  try {
    // Check if user already exists
    const existingUser = await db.query.users.findFirst({
      where: eq(users.stytchUserId, stytchUserId),
    })
    
    if (existingUser) {
      console.log("User already exists:", existingUser)
      return
    }
    
    // Extract email from Stytch ID (for demo purposes)
    // In a real implementation, you would get this from Stytch API
    const email = `user-${randomUUID()}@example.com`
    
    // Create user
    const [user] = await db.insert(users).values({
      email,
      name: "Test User",
      stytchUserId,
      isActive: true,
    }).returning()
    
    console.log("User created:", user)
    
    // Now let's check if we can find the user by Stytch ID
    const createdUser = await db.query.users.findFirst({
      where: eq(users.stytchUserId, stytchUserId),
    })
    
    console.log("User found by Stytch ID:", createdUser)
  } catch (error) {
    console.error("Error creating user:", error)
  }
}

main()
  .then(() => process.exit(0))
  .catch((error) => {
    console.error(error)
    process.exit(1)
  })
