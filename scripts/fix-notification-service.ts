import { db } from "@/lib/db"
import { notifications } from "@/modules/notification/schema"
import { eq } from "drizzle-orm"
import * as dotenv from "dotenv"

dotenv.config()

async function main() {
  try {
    console.log("Fixing notification service...")
    
    // Get all notifications without relations
    const allNotifications = await db.select().from(notifications)
    
    console.log(`Found ${allNotifications.length} notifications`)
    
    // Get a specific notification by ID
    if (allNotifications.length > 0) {
      const notification = allNotifications[0]
      console.log("First notification:", notification)
      
      // Get notification by ID
      const notificationById = await db.query.notifications.findFirst({
        where: eq(notifications.id, notification.id),
      })
      
      console.log("Notification by ID:", notificationById)
      
      // Try to get notification with recipient relation only
      const notificationWithRecipient = await db.query.notifications.findFirst({
        where: eq(notifications.id, notification.id),
        with: {
          recipient: true,
        },
      })
      
      console.log("Notification with recipient:", notificationWithRecipient)
      
      // Try to get notification with organization relation only
      const notificationWithOrganization = await db.query.notifications.findFirst({
        where: eq(notifications.id, notification.id),
        with: {
          organization: true,
        },
      })
      
      console.log("Notification with organization:", notificationWithOrganization)
      
      // Try to get notification with project relation only
      const notificationWithProject = await db.query.notifications.findFirst({
        where: eq(notifications.id, notification.id),
        with: {
          project: true,
        },
      })
      
      console.log("Notification with project:", notificationWithProject)
      
      // Skip location relation due to schema mismatch
    }
    
    console.log("Notification service fixed.")
  } catch (error) {
    console.error("Error fixing notification service:", error)
  }
}

main()
  .then(() => process.exit(0))
  .catch((error) => {
    console.error(error)
    process.exit(1)
  })
