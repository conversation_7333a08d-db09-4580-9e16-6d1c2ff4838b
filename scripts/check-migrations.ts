import postgres from "postgres"

async function main() {
  // Use the connection URL directly
  const connectionString = "postgresql://sceneomatic_auth_owner:<EMAIL>/sceneomatic_auth?sslmode=require"
  
  // Create postgres client
  const sql = postgres(connectionString)

  try {
    console.log("Checking _drizzle_migrations table...")
    const migrations = await sql`SELECT * FROM _drizzle_migrations`
    console.log("Migrations:", migrations)
    
    // Close the connection
    await sql.end()
  } catch (error) {
    console.error("Error checking migrations:", error)
    process.exit(1)
  }
}

main()
  .then(() => process.exit(0))
  .catch((error) => {
    console.error(error)
    process.exit(1)
  })
