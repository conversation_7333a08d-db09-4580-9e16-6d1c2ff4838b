import { db } from "@/lib/db"
import { sql } from "drizzle-orm"
import fs from "node:fs"
import path from "node:path"

async function main() {
  try {
    console.log("Running shared map links migration...")
    
    // Read the SQL file
    const sqlFilePath = path.join(process.cwd(), "drizzle/migrations/0007_shared_map_links_tables.sql")
    const sqlContent = fs.readFileSync(sqlFilePath, "utf8")
    
    // Execute the SQL
    await db.execute(sql.raw(sqlContent))
    
    console.log("Shared map links migration completed successfully!")
  } catch (error) {
    console.error("Error running shared map links migration:", error)
    process.exit(1)
  } finally {
    process.exit(0)
  }
}

main()
