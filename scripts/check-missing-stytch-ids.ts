import { db } from "@/lib/db";
import { organizations } from "@/modules/organization/schema";
import { isNull } from "drizzle-orm";

/**
 * This script checks for organizations that are missing stytchOrganizationId
 * and provides options to fix them by creating corresponding Stytch organizations.
 */
async function main() {
  console.log("Checking for organizations missing Stytch IDs...");

  try {
    // Get all active organizations
    const allOrgs = await db.query.organizations.findMany({
      where: isNull(organizations.deletedAt),
    });

    console.log(`Found ${allOrgs.length} total active organizations.`);

    // Filter organizations missing Stytch IDs
    const orgsWithoutStytchId = allOrgs.filter(org => !org.stytchOrganizationId);

    if (orgsWithoutStytchId.length === 0) {
      console.log("✅ All organizations have Stytch IDs. No issues found.");
      return;
    }

    console.log(`⚠️ Found ${orgsWithoutStytchId.length} organizations missing Stytch IDs:`);
    
    // Display organizations missing Stytch IDs
    orgsWithoutStytchId.forEach((org, index) => {
      console.log(`${index + 1}. ID: ${org.id}, Name: ${org.name}, Slug: ${org.slug}`);
    });

    console.log("\nTo fix these organizations, you can run:");
    console.log("pnpm tsx scripts/create-stytch-orgs.ts");
    
    // Provide instructions for creating a fix script
    console.log("\nAlternatively, you can manually update each organization with a Stytch ID by:");
    console.log("1. Creating a Stytch organization for each missing one");
    console.log("2. Updating the database records with the corresponding Stytch organization IDs");
  } catch (error) {
    console.error("Error checking organizations:", error);
    process.exit(1);
  }
}

main()
  .then(() => process.exit(0))
  .catch((error) => {
    console.error(error);
    process.exit(1);
  });
