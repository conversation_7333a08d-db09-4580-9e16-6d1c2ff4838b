import { db } from "@/lib/db"
import * as dotenv from "dotenv"

dotenv.config()

async function main() {
  console.log("Checking map data...")

  try {
    // Check if there are any locations in the database
    const allLocations = await db.query.locations.findMany()
    console.log(`Total locations in database: ${allLocations.length}`)

    if (allLocations.length === 0) {
      console.log("No locations found in the database. You may need to run the seed-map-locations.ts script.")
      return
    }

    // Group locations by organization ID
    const locationsByOrg: Record<string, number> = {}
    allLocations.forEach(location => {
      const orgId = location.organizationId
      locationsByOrg[orgId] = (locationsByOrg[orgId] || 0) + 1
    })

    console.log("\nLocations by organization ID:")
    Object.entries(locationsByOrg).forEach(([orgId, count]) => {
      console.log(`- Organization ID: ${orgId}, Locations: ${count}`)
    })

    // Check if locations have status field
    const hasStatus = allLocations.every(location => location.status !== undefined)
    console.log(`\nAll locations have status field: ${hasStatus}`)
    
    if (!hasStatus) {
      console.log("Some locations are missing the status field. You may need to run the status migration.")
    }

    // Check location types
    const locationTypes = new Set<string>()
    allLocations.forEach(location => {
      if (location.type) {
        locationTypes.add(location.type)
      }
    })

    console.log("\nLocation types in database:")
    Array.from(locationTypes).forEach(type => {
      console.log(`- ${type}`)
    })

    // Check if locations have coordinates
    const hasCoordinates = allLocations.every(location => {
      if (!location.coordinates) return false;
      
      // Use type assertion to access coordinates properties
      const coords = location.coordinates as { latitude: number; longitude: number };
      return coords.latitude !== undefined && coords.longitude !== undefined;
    })
    console.log(`\nAll locations have coordinates: ${hasCoordinates}`)

    if (!hasCoordinates) {
      console.log("Some locations are missing coordinates. This will cause issues with the map display.")
    }

    // Check for deleted locations
    const deletedLocations = allLocations.filter(location => location.deletedAt !== null)
    console.log(`\nDeleted locations: ${deletedLocations.length}`)

    // Check for active locations
    const activeLocations = allLocations.filter(location => location.deletedAt === null)
    console.log(`Active locations: ${activeLocations.length}`)

    // Check for locations with projects
    const locationsWithProjects = allLocations.filter(location => location.projectId !== null)
    console.log(`\nLocations with projects: ${locationsWithProjects.length}`)

    // Check for locations without projects
    const locationsWithoutProjects = allLocations.filter(location => location.projectId === null)
    console.log(`Locations without projects: ${locationsWithoutProjects.length}`)

    console.log("\nMap data check completed.")
  } catch (error) {
    console.error("Error checking map data:", error)
    throw error
  }
}

main()
  .then(() => process.exit(0))
  .catch((error) => {
    console.error(error)
    process.exit(1)
  })
