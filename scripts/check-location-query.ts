import { db } from "@/lib/db"
import { locations } from "@/modules/location/schema"
import { eq, isNull, and, desc } from "drizzle-orm"
import { getLocations } from "@/modules/location/service"

async function main() {
  console.log("Checking location query issue...")
  
  try {
    // Get the most recent 10 locations
    const recentLocations = await db.query.locations.findMany({
      where: isNull(locations.deletedAt),
      orderBy: [desc(locations.createdAt)],
      limit: 10,
    })
    
    console.log(`Found ${recentLocations.length} recent locations in database`)
    
    // Get a sample organization ID from the recent locations
    const sampleOrgId = recentLocations[0]?.organizationId
    
    if (sampleOrgId) {
      console.log(`Using organization ID: ${sampleOrgId}`)
      
      // Get all locations for this organization directly from the database
      const allLocationsFromDb = await db.query.locations.findMany({
        where: and(
          eq(locations.organizationId, sampleOrgId),
          isNull(locations.deletedAt)
        ),
      })
      
      console.log(`Direct DB query found ${allLocationsFromDb.length} locations`)
      
      // Count locations with null project IDs
      const nullProjectLocations = allLocationsFromDb.filter(loc => loc.projectId === null)
      console.log(`Of these, ${nullProjectLocations.length} have null project IDs`)
      
      if (nullProjectLocations.length > 0) {
        console.log("\nLocations with null project IDs:")
        for (const loc of nullProjectLocations) {
          console.log(`- ${loc.name} (ID: ${loc.id})`)
        }
      }
      
      // Now use the getLocations service function
      console.log("\nTesting getLocations service function:")
      
      // Test with no filters
      const locationsNoFilter = await getLocations(sampleOrgId)
      console.log(`getLocations with no filters returned ${locationsNoFilter.length} locations`)
      
      // Count locations with null project IDs in the result
      const nullProjectInResult = locationsNoFilter.filter(loc => !loc.projectId).length
      console.log(`Of these, ${nullProjectInResult} have null project IDs`)
      
      // Test with explicit undefined projectId filter
      const locationsWithUndefinedFilter = await getLocations(sampleOrgId, { projectId: undefined })
      console.log(`getLocations with undefined projectId filter returned ${locationsWithUndefinedFilter.length} locations`)
      
      // Count locations with null project IDs in the result
      const nullProjectInUndefinedResult = locationsWithUndefinedFilter.filter(loc => !loc.projectId).length
      console.log(`Of these, ${nullProjectInUndefinedResult} have null project IDs`)
      
      // Test with empty string projectId filter
      const locationsWithEmptyFilter = await getLocations(sampleOrgId, { projectId: "" })
      console.log(`getLocations with empty string projectId filter returned ${locationsWithEmptyFilter.length} locations`)
      
      // Count locations with null project IDs in the result
      const nullProjectInEmptyResult = locationsWithEmptyFilter.filter(loc => !loc.projectId).length
      console.log(`Of these, ${nullProjectInEmptyResult} have null project IDs`)
      
      // Now let's try to debug the SQL query that's being generated
      console.log("\nDebugging SQL query:")
      
      // Log the query conditions
      console.log("Query conditions:", [
        `organizationId = ${sampleOrgId}`,
        `deletedAt IS NULL`
      ])
      
      // Check if the locations are active
      console.log("\nChecking if locations with null projectId are active:")
      for (const loc of nullProjectLocations) {
        console.log(`- ${loc.name}: isActive = ${loc.isActive}`)
      }
    }
    
  } catch (error) {
    console.error("Error checking location query:", error)
    process.exit(1)
  }
  
  process.exit(0)
}

main()
