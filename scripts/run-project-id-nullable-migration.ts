import { db } from "@/lib/db"
import { migrate } from "drizzle-orm/postgres-js/migrator"
import { join } from "node:path"

async function main() {
  console.log("Running migration to make project_id nullable in locations table...")
  
  try {
    // Run the migration
    await migrate(db, { migrationsFolder: join("drizzle", "migrations") })
    
    console.log("Migration completed successfully!")
  } catch (error) {
    console.error("Migration failed:", error)
    process.exit(1)
  }
  
  process.exit(0)
}

main()
