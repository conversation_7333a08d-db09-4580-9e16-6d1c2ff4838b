import { db } from "@/lib/db"
import { sql } from "drizzle-orm"
import { readFileSync } from "node:fs"
import { resolve } from "node:path"

async function runMigration() {
  try {
    console.log("Running performance indexes migration...")
    
    // Read the SQL file
    const sqlFilePath = resolve(process.cwd(), "drizzle/migrations/0008_performance_indexes.sql")
    const sqlContent = readFileSync(sqlFilePath, "utf8")
    
    // Execute the SQL
    await db.execute(sql.raw(sqlContent))
    
    console.log("Performance indexes migration completed successfully!")
  } catch (error) {
    console.error("Error running performance indexes migration:", error)
    process.exit(1)
  } finally {
    process.exit(0)
  }
}

runMigration()
