// Import required modules
import { drizzle } from "drizzle-orm/postgres-js"
import { migrate } from "drizzle-orm/postgres-js/migrator"
import postgres from "postgres"
import * as dotenv from "dotenv"

// Load environment variables
dotenv.config()

// Get database connection string from environment variables
const connectionString = process.env.DATABASE_URL

if (!connectionString) {
  console.error("DATABASE_URL environment variable is not set")
  process.exit(1)
}

// Create a postgres client
const sql = postgres(connectionString, { max: 1 })

// Create a drizzle instance
const drizzleDb = drizzle(sql)

// Run the migration
async function runMigration() {
  try {
    console.log("Running status column migration...")
    
    await migrate(drizzleDb, { migrationsFolder: "drizzle/migrations" })
    
    console.log("Status column migration completed successfully!")
  } catch (error) {
    console.error("Migration failed:", error)
    process.exit(1)
  } finally {
    await sql.end()
  }
}

runMigration()
