/**
 * <PERSON><PERSON><PERSON> to fix location coordinates in the database
 * 
 * This script:
 * 1. Fetches all locations from the database
 * 2. Checks if each location has valid coordinates
 * 3. If not, updates the coordinates using the address
 * 
 * Usage:
 * pnpm tsx scripts/fix-location-coordinates.ts
 */

import { db } from "@/lib/db";
import { locations } from "@/lib/db/schema";
import { eq, isNull } from "drizzle-orm";
import { geocodeAddressServer } from "@/lib/utils/geocoding";
import { createPoint } from "@/modules/shared/postgis";
import dotenv from "dotenv";

// Load environment variables
dotenv.config();

// Sample real addresses for different location types
const SAMPLE_ADDRESSES = [
  // Interior locations
  "123 Main St, New York, NY 10001",
  "456 Market St, San Francisco, CA 94105",
  "789 Michigan Ave, Chicago, IL 60611",
  
  // Exterior locations
  "1 Hollywood Blvd, Los Angeles, CA 90028",
  "200 Park Ave, New York, NY 10166",
  "301 Front St W, Toronto, ON M5V 2T6, Canada",
  
  // Studio locations
  "4000 Warner Blvd, Burbank, CA 91522",
  "100 Universal City Plaza, Universal City, CA 91608",
  "10201 W Pico Blvd, Los Angeles, CA 90064",
  
  // Residential locations
  "1600 Pennsylvania Avenue NW, Washington, DC 20500",
  "221B Baker St, London, UK",
  "350 Fifth Avenue, New York, NY 10118",
  
  // Warehouse locations
  "2000 Industrial Pkwy, Detroit, MI 48207",
  "3500 Deer Creek Rd, Palo Alto, CA 94304",
  "1 Infinite Loop, Cupertino, CA 95014",
  
  // Beach locations
  "Ocean Drive, Miami Beach, FL 33139",
  "1 Mandalay Beach Rd, Oxnard, CA 93035",
  "3500 Ocean Drive, Vero Beach, FL 32963",
];

// Get a random address from the sample list
function getRandomAddress(): string {
  return SAMPLE_ADDRESSES[Math.floor(Math.random() * SAMPLE_ADDRESSES.length)];
}

// Define address type
interface AddressObject {
  formatted?: string;
  street?: string;
  city?: string;
  state?: string;
  postalCode?: string;
  country?: string;
  [key: string]: string | undefined;
}

// Format an address object to a string
function formatAddress(address: AddressObject): string {
  if (address.formatted) return address.formatted;
  
  const parts = [];
  if (address.street) parts.push(address.street);
  if (address.city) parts.push(address.city);
  if (address.state) parts.push(address.state);
  if (address.postalCode) parts.push(address.postalCode);
  if (address.country) parts.push(address.country);
  
  return parts.join(", ");
}

async function main() {
  try {
    console.log("Fetching locations from database...");
    
    // Get all active locations
    const allLocations = await db.query.locations.findMany({
      where: isNull(locations.deletedAt),
    });
    
    console.log(`Found ${allLocations.length} locations`);
    
    // Update all locations with proper PostGIS coordinates
    for (const location of allLocations) {
      try {
        console.log(`Processing location: ${location.name} (${location.id})`);
        
        // Get address string
        let addressString;
        
        if (location.address && typeof location.address === 'object') {
          addressString = formatAddress(location.address as AddressObject);
        } else {
          // If no address, use a random sample address
          addressString = getRandomAddress();
          console.log("  No address found, using sample address:", addressString);
        }
        
        // Skip if address is empty
        if (!addressString) {
          console.log("  Skipping location with empty address");
          continue;
        }
        
        console.log("  Geocoding address:", addressString);
        
        // Geocode the address
        const { latitude, longitude, formattedAddress } = await geocodeAddressServer(
          addressString
        );
        
        console.log("  Geocoded to:", latitude, longitude);
        
        // Update the location with new coordinates
        const [updatedLocation] = await db
          .update(locations)
          .set({
            coordinates: createPoint(latitude, longitude),
            address: {
              street: location.address && typeof location.address === 'object' ? 
                (location.address as AddressObject).street || "" : "",
              city: location.address && typeof location.address === 'object' ? 
                (location.address as AddressObject).city || "" : "",
              state: location.address && typeof location.address === 'object' ? 
                (location.address as AddressObject).state || "" : "",
              postalCode: location.address && typeof location.address === 'object' ? 
                (location.address as AddressObject).postalCode || "" : "",
              country: location.address && typeof location.address === 'object' ? 
                (location.address as AddressObject).country || "" : "",
              formatted: formattedAddress,
            },
            updatedAt: new Date(),
          })
          .where(eq(locations.id, location.id))
          .returning();
        
        console.log("  Updated location:", updatedLocation.name);
      } catch (error: unknown) {
        console.error("  Error updating location", location.name, `(${location.id}):`, error);
      }
    }
    
    console.log("Finished updating location coordinates");
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    console.error("Error:", errorMessage);
    process.exit(1);
  }
}

main();
