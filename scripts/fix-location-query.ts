import { db } from "@/lib/db"
import { locations } from "@/modules/location/schema"
import { eq, isNull, and, or, desc } from "drizzle-orm"
import { getLocations } from "@/modules/location/service"

async function main() {
  console.log("Testing location query fix...")
  
  try {
    // Get a sample organization ID
    const sampleLocation = await db.query.locations.findFirst({
      where: isNull(locations.deletedAt),
      orderBy: [desc(locations.createdAt)],
    })
    
    if (!sampleLocation) {
      console.error("No locations found in database")
      process.exit(1)
    }
    
    const sampleOrgId = sampleLocation.organizationId
    console.log(`Using organization ID: ${sampleOrgId}`)
    
    // Get all locations for this organization directly from the database
    const allLocationsFromDb = await db.query.locations.findMany({
      where: and(
        eq(locations.organizationId, sampleOrgId),
        isNull(locations.deletedAt)
      ),
    })
    
    console.log(`Direct DB query found ${allLocationsFromDb.length} locations`)
    
    // Count locations with null project IDs
    const nullProjectLocations = allLocationsFromDb.filter(loc => loc.projectId === null)
    console.log(`Of these, ${nullProjectLocations.length} have null project IDs`)
    
    if (nullProjectLocations.length > 0) {
      console.log("\nLocations with null project IDs:")
      for (const loc of nullProjectLocations) {
        console.log(`- ${loc.name} (ID: ${loc.id})`)
      }
    }
    
    // Implement the fix by modifying the getLocations function
    // This is a temporary monkey patch for testing
    const originalGetLocations = require("@/modules/location/service").getLocations
    
    // Override the getLocations function with our fixed version
    require("@/modules/location/service").getLocations = async function(organizationId: string, filters: any = {}) {
      console.log("Using patched getLocations function with explicit NULL handling")
      
      // Extract pagination parameters
      const page = filters.page || 1
      const pageSize = filters.pageSize || 12
      const offset = (page - 1) * pageSize
      
      // Use the optimized index for organization_id + deleted_at
      const conditions = [eq(locations.organizationId, organizationId), isNull(locations.deletedAt)]
    
      // Apply status filter - uses the status index
      if (filters.status) {
        conditions.push(eq(locations.status, filters.status))
      }
    
      // Apply type filter - uses the type index
      if (filters.type) {
        conditions.push(eq(locations.type, filters.type))
      }
    
      // Apply project filter - uses the project_id index
      if (filters.projectId) {
        // If a specific project is requested, include only locations with that project ID
        conditions.push(eq(locations.projectId, filters.projectId))
      } else {
        // If no specific project is requested, explicitly include all locations
        // regardless of whether they have a project ID or not
        console.log("No projectId filter specified, explicitly including both null and non-null project IDs")
        
        // Explicitly include both null and non-null project IDs using OR condition
        // This ensures locations with null projectId are included
        conditions.push(or(isNull(locations.projectId), locations.projectId.isNotNull()))
      }
    
      // Apply search filter
      if (filters.search) {
        const searchTerm = `%${filters.search}%`
        conditions.push(or(like(locations.name, searchTerm), like(locations.description || "", searchTerm)))
      }
    
      // Apply tag filter - optimize the JSONB query
      if (filters.tags && filters.tags.length > 0) {
        // More efficient JSONB containment operator for array values
        // This uses the GIN index on the JSONB column if available
        const tagsArray = filters.tags
        
        // Use the @> operator for better performance with JSONB arrays
        // This checks if the metadata.tags JSONB array contains all the specified tags
        const jsonbCondition = sql`metadata->'tags' @> ${JSON.stringify(tagsArray)}::jsonb`
        if (jsonbCondition) {
          conditions.push(jsonbCondition as SQL<unknown>)
        }
      }
    
      // Only select the columns we need to reduce data transfer
      const result = await db.query.locations.findMany({
        where: and(...conditions),
        orderBy: locations.createdAt,
        // Apply pagination
        limit: pageSize,
        offset: offset,
        // Only select necessary columns
        columns: {
          id: true,
          name: true,
          description: true,
          address: true,
          coordinates: true,
          type: true,
          status: true,
          projectId: true,
          organizationId: true,
          metadata: true,
          isActive: true,
          createdAt: true,
          updatedAt: true,
          createdBy: true,
          approvedBy: true,
        },
        // Include only necessary relations with limited columns
        with: {
          creator: {
            columns: {
              id: true,
              name: true,
            }
          },
          approver: {
            columns: {
              id: true,
              name: true,
            }
          },
          project: {
            columns: {
              id: true,
              name: true,
            }
          },
        },
      })
    
      // Return the coordinates as they are (they're already in the correct format)
      return result.map((location) => ({
        ...location,
        coordinates: location.coordinates || { latitude: 0, longitude: 0 },
      }))
    }
    
    // Test the patched getLocations function
    console.log("\nTesting patched getLocations function:")
    
    // Test with no filters
    const locationsNoFilter = await getLocations(sampleOrgId)
    console.log(`Patched getLocations with no filters returned ${locationsNoFilter.length} locations`)
    
    // Count locations with null project IDs in the result
    const nullProjectInResult = locationsNoFilter.filter(loc => !loc.projectId).length
    console.log(`Of these, ${nullProjectInResult} have null project IDs`)
    
    // Check if the locations returned by getLocations match the ones from direct DB query
    console.log("\nComparing results:")
    const directDbIds = new Set(allLocationsFromDb.map(loc => loc.id))
    const serviceIds = new Set(locationsNoFilter.map(loc => loc.id))
    
    // Find IDs that are in the direct DB query but not in the service result
    const missingIds = [...directDbIds].filter(id => !serviceIds.has(id))
    console.log(`IDs in direct DB query but missing from service result: ${missingIds.length}`)
    
    if (missingIds.length > 0) {
      console.log("Missing locations:")
      for (const id of missingIds) {
        const loc = allLocationsFromDb.find(l => l.id === id)
        if (loc) {
          console.log(`- ${loc.name} (ID: ${id})`)
        }
      }
    } else {
      console.log("Success! All locations are now being returned correctly.")
    }
    
    // Restore the original function
    require("@/modules/location/service").getLocations = originalGetLocations
    
    console.log("\nRecommended fix:")
    console.log(`
In modules/location/service.ts, modify the getLocations function:

// Apply project filter - uses the project_id index
if (filters.projectId) {
  // If a specific project is requested, include only locations with that project ID
  conditions.push(eq(locations.projectId, filters.projectId))
} else {
  // If no specific project is requested, explicitly include all locations
  // regardless of whether they have a project ID or not
  console.log("No projectId filter specified, explicitly including both null and non-null project IDs")
  
  // Explicitly include both null and non-null project IDs using OR condition
  // This ensures locations with null projectId are included
  conditions.push(or(isNull(locations.projectId), locations.projectId.isNotNull()))
}
`)
    
  } catch (error) {
    console.error("Error testing location query fix:", error)
    process.exit(1)
  }
  
  process.exit(0)
}

main()
