import { db } from "@/lib/db";
import { projects, type PROJECT_STATUS } from "@/modules/project/schema";
// import { findOrganizationByStytchId } from "@/modules/organization/service"; // Removed Stytch lookup import
import dotenv from "dotenv";

dotenv.config();

// Use the provided internal database UUID directly
const ORGANIZATION_DB_UUID = "af6520e4-b62b-4034-8e6c-5fb93ae41d93";

// Define the type for seed data using the imported enum
type ProjectSeedData = {
  name: string;
  description: string;
  status: (typeof PROJECT_STATUS)[number]; // Use the enum type
  startDate: Date | null;
  endDate: Date | null;
};

const seedData: ProjectSeedData[] = [
  {
    name: "Downtown Revitalization",
    description: "Urban renewal project focusing on downtown area revitalization with multiple locations.",
    status: "active", // Matches enum
    startDate: new Date("2025-01-15"),
    endDate: new Date("2025-07-30"),
    // Add other relevant fields based on schema, e.g., clientName, budget if needed
  },
  {
    name: "Waterfront Development",
    description: "Mixed-use development along the waterfront with residential and commercial spaces.",
    status: "planning", // Matches enum
    startDate: new Date("2025-05-10"),
    endDate: new Date("2026-03-22"),
  },
  {
    name: "Historic District Preservation",
    description: "Preservation and restoration of buildings in the historic district.",
    status: "completed", // Matches enum
    startDate: new Date("2024-08-05"),
    endDate: new Date("2025-02-28"),
  },
  {
    name: "Community Center Expansion",
    description: "Expansion of the community center to include new facilities and services.",
    status: "active", // Matches enum
    startDate: new Date("2025-03-01"),
    endDate: new Date("2025-09-15"),
  },
  {
    name: "Park Renovation",
    description: "Comprehensive renovation of city parks including new amenities and landscaping.",
    status: "planning", // Matches enum
    startDate: new Date("2025-06-10"),
    endDate: null,
  },
  {
    name: "Transit Hub Construction",
    description: "Construction of a new transit hub connecting multiple transportation modes.",
    status: "archived", // Matches enum
    startDate: new Date("2024-11-01"),
    endDate: new Date("2025-08-30"),
  },
];

async function seedProjects() {
  console.log("Starting project seeding...");
  console.log(`Using Organization DB UUID: ${ORGANIZATION_DB_UUID}`);

  try {
    // 1. Prepare project data with the correct organizationId
    const projectsToInsert = seedData.map(project => ({
      ...project,
      organizationId: ORGANIZATION_DB_UUID, // Use the direct DB UUID
      // Ensure dates are handled correctly (null if not provided)
      startDate: project.startDate || null,
      endDate: project.endDate || null,
      // Add default values for other non-nullable fields if necessary
      // createdBy: null, // Example if needed, adjust based on schema constraints
      // budget: null, // Example
      // metadata: {}, // Example
    }));

    // 3. Insert projects
    console.log(`Inserting ${projectsToInsert.length} projects...`);
    await db.insert(projects).values(projectsToInsert).onConflictDoNothing(); // Avoid errors if script is run multiple times

    console.log("Project seeding completed successfully!");

  } catch (error) {
    console.error("Error during project seeding:", error);
    process.exit(1);
  } finally {
    // Removed db.end() call as it might not be supported/needed
    console.log("Seeding script finished.");
    process.exit(0);
  }
}

seedProjects();
