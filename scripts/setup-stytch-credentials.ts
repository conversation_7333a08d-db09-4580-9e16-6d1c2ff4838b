/**
 * This script helps set up the Stytch B2B credentials.
 * It checks if the credentials are set and provides instructions on how to set them.
 */

import * as fs from 'fs';
import * as path from 'path';
import { execSync } from 'child_process';

async function setupStytchCredentials() {
  console.log("=== Stytch B2B Credentials Setup Helper ===");
  
  // Check if the credentials are already set
  const projectId = process.env.STYTCH_B2B_PROJECT_ID;
  const secret = process.env.STYTCH_B2B_SECRET;
  
  console.log("\nChecking current environment variables:");
  console.log(`STYTCH_B2B_PROJECT_ID: ${projectId ? "Set" : "Not set"}`);
  console.log(`STYTCH_B2B_SECRET: ${secret ? "Set" : "Not set"}`);
  
  if (projectId && secret) {
    console.log("\n✅ Stytch B2B credentials are already set in the environment.");
    console.log("You can proceed with using the application.");
    return;
  }
  
  // Check if .env file exists
  const envPath = path.join(process.cwd(), '.env');
  const envLocalPath = path.join(process.cwd(), '.env.local');
  const envDevelopmentPath = path.join(process.cwd(), '.env.development');
  
  let envFile = '';
  let envFilePath = '';
  
  if (fs.existsSync(envLocalPath)) {
    envFile = fs.readFileSync(envLocalPath, 'utf8');
    envFilePath = envLocalPath;
    console.log("\nFound .env.local file.");
  } else if (fs.existsSync(envPath)) {
    envFile = fs.readFileSync(envPath, 'utf8');
    envFilePath = envPath;
    console.log("\nFound .env file.");
  } else if (fs.existsSync(envDevelopmentPath)) {
    envFile = fs.readFileSync(envDevelopmentPath, 'utf8');
    envFilePath = envDevelopmentPath;
    console.log("\nFound .env.development file.");
  } else {
    console.log("\nNo .env file found. Creating .env.local file.");
    envFilePath = envLocalPath;
    envFile = '';
  }
  
  // Check if the credentials are in the .env file
  const hasProjectId = envFile.includes('STYTCH_B2B_PROJECT_ID');
  const hasSecret = envFile.includes('STYTCH_B2B_SECRET');
  
  console.log(`\nChecking ${path.basename(envFilePath)} file:`);
  console.log(`STYTCH_B2B_PROJECT_ID: ${hasProjectId ? "Present" : "Not present"}`);
  console.log(`STYTCH_B2B_SECRET: ${hasSecret ? "Present" : "Not present"}`);
  
  // Prompt for credentials
  console.log("\n=== Instructions ===");
  console.log("To set up Stytch B2B credentials, you need to:");
  console.log("1. Log in to your Stytch dashboard at https://stytch.com/dashboard");
  console.log("2. Navigate to API Keys");
  console.log("3. Copy your Project ID and Secret");
  console.log("4. Add them to your environment variables or .env file");
  
  console.log("\n=== Example .env File ===");
  console.log("# Stytch B2B Credentials");
  console.log("STYTCH_B2B_PROJECT_ID=project-live-xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx");
  console.log("STYTCH_B2B_SECRET=secret-live-xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx");
  
  console.log("\n=== Next Steps ===");
  console.log(`1. Edit your ${path.basename(envFilePath)} file to add the missing credentials`);
  console.log("2. Restart your development server");
  console.log("3. Try uploading an image to the Pitot House location again");
  
  // Create or update .env file
  if (!fs.existsSync(envFilePath)) {
    const defaultEnv = `# Stytch B2B Credentials
STYTCH_B2B_PROJECT_ID=
STYTCH_B2B_SECRET=
`;
    fs.writeFileSync(envFilePath, defaultEnv);
    console.log(`\nCreated ${path.basename(envFilePath)} file with placeholders for Stytch B2B credentials.`);
  } else if (!hasProjectId || !hasSecret) {
    let updatedEnv = envFile;
    
    if (!updatedEnv.endsWith('\n')) {
      updatedEnv += '\n';
    }
    
    if (!hasProjectId) {
      updatedEnv += 'STYTCH_B2B_PROJECT_ID=\n';
    }
    
    if (!hasSecret) {
      updatedEnv += 'STYTCH_B2B_SECRET=\n';
    }
    
    fs.writeFileSync(envFilePath, updatedEnv);
    console.log(`\nUpdated ${path.basename(envFilePath)} file with placeholders for missing Stytch B2B credentials.`);
  }
  
  // Try to open the .env file in the default editor
  let opened = false;
  try {
    if (process.platform === 'win32') {
      execSync(`start ${envFilePath}`);
      opened = true;
    } else if (process.platform === 'darwin') {
      execSync(`open ${envFilePath}`);
      opened = true;
    } else {
      execSync(`xdg-open ${envFilePath}`);
      opened = true;
    }
  } catch {
    // Silently catch any errors
  }
  
  if (opened) {
    console.log(`\nOpened ${path.basename(envFilePath)} in your default editor.`);
  } else {
    console.log(`\nCould not open ${path.basename(envFilePath)} automatically. Please open it manually to add your credentials.`);
  }
}

// Run the setup function
setupStytchCredentials()
  .then(() => process.exit(0))
  .catch((error) => {
    console.error("Error:", error);
    process.exit(1);
  });
