import { db } from "@/lib/db";
import { sql } from "drizzle-orm";

async function main() {
  console.log("Starting location media migration...");

  try {
    // Create the location_media table
    await db.execute(sql`
      CREATE TABLE IF NOT EXISTS location_media (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        location_id UUID NOT NULL REFERENCES locations(id),
        url TEXT NOT NULL,
        thumbnail_url TEXT,
        type TEXT NOT NULL,
        title TEXT,
        description TEXT,
        uploaded_by UUID REFERENCES users(id),
        uploaded_at TIMESTAMP NOT NULL DEFAULT NOW(),
        metadata JSONB,
        file_size INTEGER,
        width INTEGER,
        height INTEGER,
        is_public BOOLEAN NOT NULL DEFAULT TRUE,
        ordering INTEGER NOT NULL DEFAULT 0
      );
    `);
    console.log("Created location_media table");

    // Create indexes for better performance
    await db.execute(sql`
      CREATE INDEX IF NOT EXISTS idx_location_media_location_id ON location_media(location_id);
    `);
    console.log("Created index on location_id");

    await db.execute(sql`
      CREATE INDEX IF NOT EXISTS idx_location_media_uploaded_by ON location_media(uploaded_by);
    `);
    console.log("Created index on uploaded_by");

    await db.execute(sql`
      CREATE INDEX IF NOT EXISTS idx_location_media_type ON location_media(type);
    `);
    console.log("Created index on type");

    console.log("Location media migration completed successfully");
  } catch (error) {
    console.error("Error during location media migration:", error);
    process.exit(1);
  }
}

main()
  .then(() => process.exit(0))
  .catch((error) => {
    console.error("Unhandled error:", error);
    process.exit(1);
  });
