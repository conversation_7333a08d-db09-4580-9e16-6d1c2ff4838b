import { db } from "@/lib/db"
import { users } from "@/modules/user/schema"
import { eq } from "drizzle-orm"
import * as dotenv from "dotenv"

dotenv.config()

async function main() {
  const stytchUserId = "member-live-43401ba0-bcf8-43e6-a726-ac203de9b44b"
  
  try {
    // Find user by Stytch ID
    const user = await db.query.users.findFirst({
      where: eq(users.stytchUserId, stytchUserId),
    })
    
    if (user) {
      console.log("User found:", user)
    } else {
      console.log("User not found with Stytch ID:", stytchUserId)
    }
  } catch (error) {
    console.error("Error finding user:", error)
  }
}

main()
  .then(() => process.exit(0))
  .catch((error) => {
    console.error(error)
    process.exit(1)
  })
