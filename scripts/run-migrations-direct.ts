import { drizzle } from "drizzle-orm/postgres-js"
import postgres from "postgres"
import { sql } from "drizzle-orm"
import * as fs from 'node:fs'
import * as path from 'node:path'

async function main() {
  // Use the connection URL directly
  const connectionString = "postgresql://sceneomatic_auth_owner:<EMAIL>/sceneomatic_auth?sslmode=require"
  
  // Create postgres client
  const queryClient = postgres(connectionString)
  const db = drizzle(queryClient)
  
  try {
    console.log("Running migrations manually...")
    
    // Read the SQL file content
    const migrationPath = path.join(process.cwd(), '..', 'drizzle', 'migrations', '0001_polar_integration.sql')
    const migrationSql = fs.readFileSync(migrationPath, 'utf8')
    
    // Create the _drizzle_migrations table if it doesn't exist
    await db.execute(sql`
      CREATE TABLE IF NOT EXISTS "_drizzle_migrations" (
        id VARCHAR(255) PRIMARY KEY,
        hash VARCHAR(255) NOT NULL,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL
      );
    `)
    
    // Execute the migration SQL directly, but skip the last part that tries to insert into _drizzle_migrations
    // since we'll handle that separately
    const migrationSqlWithoutInsert = migrationSql.split('-- Add metadata to track migration')[0]
    await db.execute(sql.raw(migrationSqlWithoutInsert))
    
    // Insert the migration record
    await db.execute(sql`
      INSERT INTO "_drizzle_migrations" (id, hash, created_at)
      VALUES ('0001_polar_integration', 'polar_integration_migration', NOW())
      ON CONFLICT DO NOTHING;
    `)
    
    console.log("Migration completed successfully")
    
    // Close the connection
    await queryClient.end()
  } catch (error) {
    console.error("Migration failed:", error)
    throw error
  }
}

main()
  .then(() => process.exit(0))
  .catch((error) => {
    console.error(error)
    process.exit(1)
  })
