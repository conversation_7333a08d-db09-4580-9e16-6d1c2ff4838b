// Using built-in fetch API

/**
 * This script tests the API endpoint that's used to save media to the database.
 * It simulates the request that would be made by the UploadThing onUploadComplete handler.
 */
async function testLocationMediaApi() {
  try {
    console.log("Testing location media API endpoint...");
    
    // Replace these values with actual values from your system
    const locationId = "e7afb8f3-90ec-461e-98e4-b5e5f3aa8aad"; // Pitot House ID from previous script
    const organizationId = "org-test-123"; // Replace with a valid organization ID
    const userId = "user-test-123"; // Replace with a valid user ID
    
    // You'll need valid authentication tokens
    // In a real scenario, these would come from a logged-in user's session
    const sessionToken = ""; // Replace with a valid session token if available
    const sessionJwt = ""; // Replace with a valid session JWT if available
    
    // If you don't have valid tokens, this test will likely fail with authentication errors
    // But it will still show us the request structure
    
    // Construct the URL for the API endpoint
    const apiUrl = "http://localhost:3000/api/locations/media";
    
    console.log(`Sending POST request to ${apiUrl}`);
    console.log("Request body:", {
      locationId,
      organizationId,
      url: "https://uploadthing.com/f/test-image.jpg",
      type: "image",
      uploadedBy: userId,
      title: "Test Image",
      fileSize: 1024 * 1024, // 1MB
      metadata: {
        key: "test-key",
        uploadedAt: new Date().toISOString(),
        stytchMemberId: userId
      }
    });
    
    // Prepare headers
    const headers: Record<string, string> = {
      "Content-Type": "application/json",
      "X-Stytch-Org-Id": organizationId,
      "X-Stytch-Member-Id": userId
    };
    
    // Add optional headers if available
    if (sessionToken) headers["X-Stytch-Session-Token"] = sessionToken;
    if (sessionJwt) headers["X-Stytch-Session-JWT"] = sessionJwt;
    
    // Send the request to the API endpoint
    const response = await fetch(apiUrl, {
      method: "POST",
      headers,
      body: JSON.stringify({
        locationId,
        organizationId,
        url: "https://uploadthing.com/f/test-image.jpg",
        type: "image",
        uploadedBy: userId,
        title: "Test Image",
        fileSize: 1024 * 1024, // 1MB
        metadata: {
          key: "test-key",
          uploadedAt: new Date().toISOString(),
          stytchMemberId: userId
        }
      })
    });
    
    console.log(`Response status: ${response.status}`);
    
    // Always get the response text first
    const responseText = await response.text();
    console.log("Response text:", responseText);
    
    if (response.ok) {
      try {
        const data = JSON.parse(responseText);
        console.log("Response data:", data);
        console.log("✅ API request successful");
      } catch {
        console.log("❌ Failed to parse JSON response");
      }
    } else {
      console.log("❌ API request failed with status:", response.status);
    }
    
    // Explain what might be happening
    console.log("\nPossible issues:");
    console.log("1. Authentication failure - The API requires valid Stytch authentication tokens");
    console.log("2. Missing or invalid organization ID");
    console.log("3. Missing or invalid location ID");
    console.log("4. Server-side error in the API endpoint");
    
    console.log("\nTo fix this issue:");
    console.log("1. Check the browser network tab when uploading an image to see the actual request");
    console.log("2. Verify that the authentication headers are being sent correctly");
    console.log("3. Check the server logs for any errors during the upload process");
    console.log("4. Ensure the location ID is valid and belongs to the organization");
    
  } catch (error) {
    console.error("Error testing location media API:", error);
  }
}

// Run the test
testLocationMediaApi()
  .then(() => process.exit(0))
  .catch((error) => {
    console.error("Error:", error);
    process.exit(1);
  });
