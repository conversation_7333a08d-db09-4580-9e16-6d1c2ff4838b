import { exec } from "child_process"
import * as dotenv from "dotenv"

dotenv.config()

if (!process.env.DATABASE_URL) {
  throw new Error("DATABASE_URL environment variable is required")
}

console.log("Generating migrations...")

// Run drizzle-kit generate
exec("npx drizzle-kit generate:pg", (error, stdout, stderr) => {
  if (error) {
    console.error(`Error generating migrations: ${error.message}`)
    return
  }

  if (stderr) {
    console.error(`Migration generation stderr: ${stderr}`)
    return
  }

  console.log(`Migration generation stdout: ${stdout}`)
  console.log("Migrations generated successfully!")
})
