import { db } from "@/lib/db"
import { locations } from "@/modules/location/schema"
import { eq, isNull, and, or, sql } from "drizzle-orm"
import { count } from "drizzle-orm"

async function main() {
  console.log("Creating a fix for the pagination calculation in the locations API...")
  
  console.log("\nThe issue:")
  console.log("1. The locations API route is incorrectly calculating the total count of locations")
  console.log("2. It's using the length of the paginated results (e.g., 12) instead of the total count from the database (e.g., 30)")
  console.log("3. This causes the pagination to show only one page, even when there are multiple pages of results")
  
  console.log("\nThe fix:")
  console.log("1. Modify the API route to get the total count from the database before applying pagination")
  console.log("2. Use this total count to calculate the correct number of pages")
  console.log("3. Return both the paginated results and the correct total count in the API response")
  
  console.log("\nHere's the code that needs to be changed in app/api/locations/route.ts:")
  console.log(`
  // Current code (incorrect):
  // Calculate pagination metadata
  const total = locations.length; // This should ideally come from the database
  const totalPages = Math.ceil(total / pageSize);

  // Return paginated response
  return NextResponse.json({
    locations,
    page,
    pageSize,
    total,
    totalPages
  });
  `)
  
  console.log(`
  // Fixed code:
  // Get the total count from the database
  const conditions = [
    eq(locations.organizationId, organization.id),
    isNull(locations.deletedAt)
  ];
  
  // Apply the same filters as in the main query
  if (filters.projectId) {
    conditions.push(eq(locations.projectId, filters.projectId));
  } else {
    // Include both null and non-null project IDs
    conditions.push(or(isNull(locations.projectId), sql\`\${locations.projectId} IS NOT NULL\`));
  }
  
  if (filters.status) conditions.push(eq(locations.status, filters.status));
  if (filters.type) conditions.push(eq(locations.type, filters.type));
  if (filters.search) {
    const searchTerm = \`%\${filters.search}%\`;
    conditions.push(or(like(locations.name, searchTerm), like(locations.description || "", searchTerm)));
  }
  
  // Get the total count
  const [{ value: total }] = await db
    .select({ value: count() })
    .from(locations)
    .where(and(...conditions));
  
  // Calculate pagination metadata
  const totalPages = Math.ceil(total / pageSize);

  // Return paginated response
  return NextResponse.json({
    locations,
    page,
    pageSize,
    total,
    totalPages
  });
  `)
  
  console.log("\nThis fix will ensure that the API returns the correct total count and number of pages,")
  console.log("which will make the pagination controls appear correctly on the locations page.")
}

main()
