import { db } from "@/lib/db"
import { locations } from "@/modules/location/schema"
import { eq, isNull, and, desc } from "drizzle-orm"
import { getLocations } from "@/modules/location/service"

async function main() {
  console.log("Verifying location query fix with pagination...")
  
  try {
    // Get a sample organization ID
    const sampleLocation = await db.query.locations.findFirst({
      where: isNull(locations.deletedAt),
      orderBy: [desc(locations.createdAt)],
    })
    
    if (!sampleLocation) {
      console.error("No locations found in database")
      process.exit(1)
    }
    
    const sampleOrgId = sampleLocation.organizationId
    console.log(`Using organization ID: ${sampleOrgId}`)
    
    // Get all locations for this organization directly from the database
    const allLocationsFromDb = await db.query.locations.findMany({
      where: and(
        eq(locations.organizationId, sampleOrgId),
        isNull(locations.deletedAt)
      ),
    })
    
    console.log(`Direct DB query found ${allLocationsFromDb.length} locations`)
    
    // Count locations with null project IDs
    const nullProjectLocations = allLocationsFromDb.filter(loc => loc.projectId === null)
    console.log(`Of these, ${nullProjectLocations.length} have null project IDs`)
    
    if (nullProjectLocations.length > 0) {
      console.log("\nLocations with null project IDs:")
      for (const loc of nullProjectLocations) {
        console.log(`- ${loc.name} (ID: ${loc.id})`)
      }
    }
    
    // Test the fixed getLocations function with a large pageSize to get all locations
    console.log("\nTesting fixed getLocations function with large pageSize:")
    
    // Test with no filters but a large pageSize to get all locations
    const locationsNoFilter = await getLocations(sampleOrgId, { pageSize: 100 })
    console.log(`getLocations with pageSize=100 returned ${locationsNoFilter.length} locations`)
    
    // Count locations with null project IDs in the result
    const nullProjectInResult = locationsNoFilter.filter(loc => !loc.projectId).length
    console.log(`Of these, ${nullProjectInResult} have null project IDs`)
    
    // Check if the locations returned by getLocations match the ones from direct DB query
    console.log("\nComparing results:")
    const directDbIds = new Set(allLocationsFromDb.map(loc => loc.id))
    const serviceIds = new Set(locationsNoFilter.map(loc => loc.id))
    
    // Find IDs that are in the direct DB query but not in the service result
    const missingIds = [...directDbIds].filter(id => !serviceIds.has(id))
    console.log(`IDs in direct DB query but missing from service result: ${missingIds.length}`)
    
    if (missingIds.length > 0) {
      console.log("Missing locations:")
      for (const id of missingIds) {
        const loc = allLocationsFromDb.find(l => l.id === id)
        if (loc) {
          console.log(`- ${loc.name} (ID: ${id})`)
        }
      }
      console.log("\nFIX FAILED: Some locations are still missing from the results")
      process.exit(1)
    } else {
      console.log("\nFIX SUCCESSFUL: All locations are now being returned correctly!")
      
      // Verify that null projectId locations are included
      if (nullProjectLocations.length > 0 && nullProjectInResult === nullProjectLocations.length) {
        console.log(`✅ All ${nullProjectLocations.length} locations with null projectId are now included in the results`)
      } else if (nullProjectLocations.length > 0) {
        console.log(`⚠️ Warning: Only ${nullProjectInResult} of ${nullProjectLocations.length} locations with null projectId are included`)
      } else {
        console.log("ℹ️ No locations with null projectId found to test with")
      }
    }
    
    // Now test pagination to make sure it works correctly
    console.log("\nTesting pagination:")
    
    // Get the first page with default pageSize (12)
    const page1 = await getLocations(sampleOrgId, { page: 1 })
    console.log(`Page 1 (default pageSize=12) returned ${page1.length} locations`)
    
    // Get the second page with default pageSize (12)
    const page2 = await getLocations(sampleOrgId, { page: 2 })
    console.log(`Page 2 (default pageSize=12) returned ${page2.length} locations`)
    
    // Get the third page with default pageSize (12)
    const page3 = await getLocations(sampleOrgId, { page: 3 })
    console.log(`Page 3 (default pageSize=12) returned ${page3.length} locations`)
    
    // Check if all locations are covered by pagination
    const allPaginatedIds = new Set([
      ...page1.map(loc => loc.id),
      ...page2.map(loc => loc.id),
      ...page3.map(loc => loc.id)
    ])
    
    const missingFromPagination = [...directDbIds].filter(id => !allPaginatedIds.has(id))
    console.log(`\nLocations missing from pagination: ${missingFromPagination.length}`)
    
    if (missingFromPagination.length > 0) {
      console.log("Missing locations from pagination:")
      for (const id of missingFromPagination) {
        const loc = allLocationsFromDb.find(l => l.id === id)
        if (loc) {
          console.log(`- ${loc.name} (ID: ${id})`)
        }
      }
    } else {
      console.log("✅ All locations are covered by pagination")
    }
    
    // Check specifically for null projectId locations in pagination
    const nullProjectIdsInPagination = nullProjectLocations
      .filter(loc => allPaginatedIds.has(loc.id))
      .map(loc => loc.id)
    
    console.log(`\nNull projectId locations in pagination: ${nullProjectIdsInPagination.length} of ${nullProjectLocations.length}`)
    
    if (nullProjectIdsInPagination.length === nullProjectLocations.length) {
      console.log("✅ All null projectId locations are included in pagination")
    } else {
      console.log("⚠️ Some null projectId locations are missing from pagination")
      
      // Find which page contains the null projectId locations
      for (const loc of nullProjectLocations) {
        const inPage1 = page1.some(l => l.id === loc.id)
        const inPage2 = page2.some(l => l.id === loc.id)
        const inPage3 = page3.some(l => l.id === loc.id)
        
        if (inPage1) {
          console.log(`- ${loc.name} (ID: ${loc.id}) is in Page 1`)
        } else if (inPage2) {
          console.log(`- ${loc.name} (ID: ${loc.id}) is in Page 2`)
        } else if (inPage3) {
          console.log(`- ${loc.name} (ID: ${loc.id}) is in Page 3`)
        } else {
          console.log(`- ${loc.name} (ID: ${loc.id}) is not in any page`)
        }
      }
    }
    
  } catch (error) {
    console.error("Error verifying location query fix:", error)
    process.exit(1)
  }
  
  process.exit(0)
}

main()
