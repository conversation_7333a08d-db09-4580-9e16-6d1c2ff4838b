import { readFileSync, writeFileSync } from 'fs'
import { execSync } from 'child_process'
import * as path from 'path'

async function main() {
  console.log("Creating PR for location query fix...")
  
  try {
    // Define the file path
    const servicePath = path.resolve(process.cwd(), 'modules/location/service.ts')
    
    // Read the current file content
    const currentContent = readFileSync(servicePath, 'utf8')
    
    // Define the search pattern - the code block we want to replace
    const searchPattern = `// Apply project filter - uses the project_id index
  if (filters.projectId) {
    // If a specific project is requested, include only locations with that project ID
    conditions.push(eq(locations.projectId, filters.projectId))
  } else {
    // If no specific project is requested, explicitly include all locations
    // regardless of whether they have a project ID or not
    // This is done by not adding any condition for projectId
    // The SQL will not filter on projectId, including both null and non-null values
    console.log("No projectId filter specified, including all locations regardless of project ID")
    
    // No additional condition needed here - by not adding a condition,
    // we're including both null and non-null project IDs
  }`
    
    // Define the replacement pattern - the fixed code block
    const replacementPattern = `// Apply project filter - uses the project_id index
  if (filters.projectId) {
    // If a specific project is requested, include only locations with that project ID
    conditions.push(eq(locations.projectId, filters.projectId))
  } else {
    // If no specific project is requested, explicitly include all locations
    // regardless of whether they have a project ID or not
    console.log("No projectId filter specified, explicitly including both null and non-null project IDs")
    
    // Explicitly include both null and non-null project IDs using OR condition
    // This ensures locations with null projectId are included
    conditions.push(or(isNull(locations.projectId), sql\`\${locations.projectId} IS NOT NULL\`))
  }`
    
    // Check if the search pattern exists in the current content
    if (!currentContent.includes(searchPattern)) {
      console.error("Could not find the code block to replace. The service.ts file may have been modified.")
      process.exit(1)
    }
    
    // Replace the search pattern with the replacement pattern
    const updatedContent = currentContent.replace(searchPattern, replacementPattern)
    
    // Write the updated content to a new file
    const newFilePath = path.resolve(process.cwd(), 'modules/location/service.fixed.ts')
    writeFileSync(newFilePath, updatedContent, 'utf8')
    
    console.log(`Fixed file created at: ${newFilePath}`)
    console.log("\nTo apply the fix:")
    console.log("1. Review the changes in the fixed file")
    console.log("2. Replace the original file with the fixed file:")
    console.log(`   cp ${newFilePath} ${servicePath}`)
    console.log("3. Test the fix by running:")
    console.log("   npx tsx scripts/check-location-query.ts")
    
    console.log("\nFix summary:")
    console.log("- Added explicit handling for null projectId values")
    console.log("- Used OR condition with isNull and IS NOT NULL to ensure all locations are included")
    console.log("- This will fix the issue where locations with null projectId are not appearing in the UI")
    
  } catch (error) {
    console.error("Error creating PR for location query fix:", error)
    process.exit(1)
  }
  
  process.exit(0)
}

main()
