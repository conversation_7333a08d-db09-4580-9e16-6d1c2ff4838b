import postgres from "postgres"
import * as dotenv from "dotenv"

dotenv.config()

async function main() {
  // Get connection string from environment
  const connectionString = process.env.DATABASE_URL
  if (!connectionString) {
    console.error("DATABASE_URL is not defined")
    process.exit(1)
  }

  // Create postgres client
  const sql = postgres(connectionString)

  try {
    // Check if notifications table exists
    console.log("\nChecking if notifications table exists...")
    const notificationsTable = await sql`
      SELECT EXISTS (
        SELECT FROM information_schema.tables 
        WHERE table_name = 'notifications'
      )
    `
    console.log("Notifications table exists:", notificationsTable[0].exists)
    
    // Check if notification_preferences table exists
    console.log("\nChecking if notification_preferences table exists...")
    const preferencesTable = await sql`
      SELECT EXISTS (
        SELECT FROM information_schema.tables 
        WHERE table_name = 'notification_preferences'
      )
    `
    console.log("Notification preferences table exists:", preferencesTable[0].exists)
    
    // If notifications table exists, check its schema
    if (notificationsTable[0].exists) {
      console.log("\nChecking notifications table schema...")
      const notificationSchema = await sql`
        SELECT column_name, data_type, character_maximum_length 
        FROM information_schema.columns 
        WHERE table_name = 'notifications'
        ORDER BY ordinal_position
      `
      console.log("Notifications table columns:", notificationSchema)
    }

    // If notification_preferences table exists, check its schema
    if (preferencesTable[0].exists) {
      console.log("\nChecking notification_preferences table schema...")
      const preferencesSchema = await sql`
        SELECT column_name, data_type, character_maximum_length 
        FROM information_schema.columns 
        WHERE table_name = 'notification_preferences'
        ORDER BY ordinal_position
      `
      console.log("Notification preferences table columns:", preferencesSchema)
    }

    // Close the connection
    await sql.end()
    
    process.exit(0)
  } catch (error) {
    console.error("Error checking schema:", error)
    process.exit(1)
  }
}

main()
