import { drizzle } from "drizzle-orm/postgres-js"
import postgres from "postgres"
import { sql } from "drizzle-orm"
import * as fs from 'node:fs'
import * as path from 'node:path'
import * as dotenv from "dotenv"

dotenv.config()

async function main() {
  // Get connection string from environment
  const connectionString = process.env.DATABASE_URL
  if (!connectionString) {
    console.error("DATABASE_URL is not defined")
    process.exit(1)
  }
  
  // Create postgres client
  const queryClient = postgres(connectionString)
  const db = drizzle(queryClient)
  
  try {
    console.log("Running notification tables migration manually...")
    
    // Read the SQL file content
    const migrationPath = path.join(process.cwd(), 'drizzle', 'migrations', '0002_notification_tables.sql')
    const migrationSql = fs.readFileSync(migrationPath, 'utf8')
    
    // Create the _drizzle_migrations table if it doesn't exist
    await db.execute(sql`
      CREATE TABLE IF NOT EXISTS "_drizzle_migrations" (
        id VARCHAR(255) PRIMARY KEY,
        hash VARCHAR(255) NOT NULL,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL
      );
    `)
    
    // Execute the migration SQL directly, but skip the last part that tries to insert into _drizzle_migrations
    // since we'll handle that separately
    const migrationSqlWithoutInsert = migrationSql.split('-- Add metadata to track migration')[0]
    await db.execute(sql.raw(migrationSqlWithoutInsert))
    
    // Insert the migration record
    await db.execute(sql`
      INSERT INTO "_drizzle_migrations" (id, hash, created_at)
      VALUES ('0002_notification_tables', 'notification_tables_migration', NOW())
      ON CONFLICT DO NOTHING;
    `)
    
    console.log("Migration completed successfully")
    
    // Close the connection
    await queryClient.end()
  } catch (error) {
    console.error("Migration failed:", error)
    throw error
  }
}

main()
  .then(() => process.exit(0))
  .catch((error) => {
    console.error(error)
    process.exit(1)
  })
