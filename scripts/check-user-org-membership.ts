import { db } from "@/lib/db"
import { users } from "@/modules/user/schema"
import { organizations, organizationUserRoles } from "@/modules/organization/schema"
import { eq, and } from "drizzle-orm"
import * as dotenv from "dotenv"

dotenv.config()

async function main() {
  const stytchUserId = "member-live-43401ba0-bcf8-43e6-a726-ac203de9b44b"
  const stytchOrgId = "organization-live-3b75ccad-ef69-4314-9a10-9e1cf15c67d4"
  
  try {
    // Find user by Stytch ID
    const user = await db.query.users.findFirst({
      where: eq(users.stytchUserId, stytchUserId),
    })
    
    if (!user) {
      console.error("User not found with Stytch ID:", stytchUserId)
      return
    }
    
    console.log("User found:", user)
    
    // Find organization by Stytch ID
    const organization = await db.query.organizations.findFirst({
      where: eq(organizations.stytchOrganizationId, stytchOrgId),
    })
    
    if (!organization) {
      console.error("Organization not found with Stytch ID:", stytchOrgId)
      return
    }
    
    console.log("Organization found:", organization)
    
    // Check if user is a member of the organization
    const membership = await db.query.organizationUserRoles.findFirst({
      where: and(
        eq(organizationUserRoles.userId, user.id),
        eq(organizationUserRoles.organizationId, organization.id)
      ),
    })
    
    if (membership) {
      console.log("User is a member of the organization:", membership)
    } else {
      console.log("User is NOT a member of the organization")
      
      // Add user to organization as owner
      const [newMembership] = await db.insert(organizationUserRoles).values({
        organizationId: organization.id,
        userId: user.id,
        role: "owner",
      }).returning()
      
      console.log("User added to organization:", newMembership)
    }
  } catch (error) {
    console.error("Error checking user organization membership:", error)
  }
}

main()
  .then(() => process.exit(0))
  .catch((error) => {
    console.error(error)
    process.exit(1)
  })
