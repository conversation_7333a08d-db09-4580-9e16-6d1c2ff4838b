/**
 * This script helps debug authentication issues with the location media API.
 * It tests different authentication methods and provides detailed logs.
 */

import { getStytchB2BClientAsync } from "@/lib/stytch-b2b";

async function debugLocationMediaAuth() {
  try {
    console.log("=== Location Media API Authentication Debugger ===");
    
    // 1. Test Stytch B2B client connection
    console.log("\n1. Testing Stytch B2B client connection...");
    try {
      await getStytchB2BClientAsync();
      console.log("✅ Successfully initialized Stytch B2B client");
      
      // Get environment info
      const projectID = process.env.STYTCH_PROJECT_ID || "unknown";
      console.log(`Project ID: ${projectID.substring(0, 8)}...`);
      
      const env = process.env.STYTCH_ENV || "unknown";
      console.log(`Environment: ${env}`);
    } catch (error) {
      console.error("❌ Failed to initialize Stytch B2B client:", error);
    }
    
    // 2. Test direct API access with different auth methods
    console.log("\n2. Testing direct API access with different auth methods...");
    
    // Define the Pitot House ID for reference
    // This ID is used in the testApiWithHeaders function
    const pitotHouseId = "e7afb8f3-90ec-461e-98e4-b5e5f3aa8aad"; // Pitot House ID
    console.log(`Using Pitot House ID: ${pitotHouseId}`);
    const organizationId = "org-test-123"; // Replace with a valid organization ID
    const userId = "user-test-123"; // Replace with a valid user ID
    
    // Base URL for the API
    const apiUrl = "http://localhost:3000/api/locations/media";
    
    // Test with no auth headers
    console.log("\nTest 2.1: No auth headers");
    await testApiWithHeaders(apiUrl, {
      'Content-Type': 'application/json'
    });
    
    // Test with only organization ID
    console.log("\nTest 2.2: Only organization ID");
    await testApiWithHeaders(apiUrl, {
      'Content-Type': 'application/json',
      'X-Stytch-Org-Id': organizationId
    });
    
    // Test with organization ID and member ID
    console.log("\nTest 2.3: Organization ID and member ID");
    await testApiWithHeaders(apiUrl, {
      'Content-Type': 'application/json',
      'X-Stytch-Org-Id': organizationId,
      'X-Stytch-Member-Id': userId
    });
    
    // Test with dummy session token
    console.log("\nTest 2.4: With dummy session token");
    await testApiWithHeaders(apiUrl, {
      'Content-Type': 'application/json',
      'X-Stytch-Org-Id': organizationId,
      'X-Stytch-Member-Id': userId,
      'X-Stytch-Session-Token': 'dummy-token'
    });
    
    // Test with dummy session JWT
    console.log("\nTest 2.5: With dummy session JWT");
    await testApiWithHeaders(apiUrl, {
      'Content-Type': 'application/json',
      'X-Stytch-Org-Id': organizationId,
      'X-Stytch-Member-Id': userId,
      'X-Stytch-Session-JWT': 'dummy-jwt'
    });
    
    console.log("\n=== Debug Complete ===");
    console.log("Check the server logs for more detailed information about authentication failures.");
    console.log("The API endpoint is returning HTML login pages instead of JSON responses, which indicates authentication failures.");
    console.log("\nRecommendations:");
    console.log("1. Check that the Stytch environment variables are correctly set");
    console.log("2. Verify that the middleware is correctly extracting authentication tokens from cookies");
    console.log("3. Ensure that the RBAC permissions are correctly configured");
    console.log("4. Consider implementing a more robust error handling mechanism in the API endpoint");
    
  } catch (error) {
    console.error("Error during debugging:", error);
  }
}

async function testApiWithHeaders(url: string, headers: Record<string, string>) {
  try {
    // Make a simple GET request to test authentication
    const response = await fetch(`${url}?locationId=e7afb8f3-90ec-461e-98e4-b5e5f3aa8aad`, {
      method: 'GET',
      headers
    });
    
    console.log(`Status: ${response.status}`);
    
    // Check if the response is HTML (login page) or JSON
    const contentType = response.headers.get('content-type') || '';
    console.log(`Content-Type: ${contentType}`);
    
    if (contentType.includes('text/html')) {
      console.log("❌ Received HTML response (likely a login page)");
    } else if (contentType.includes('application/json')) {
      console.log("✅ Received JSON response");
      try {
        const data = await response.json();
        console.log("Response data:", data);
      } catch {
        console.log("Failed to parse JSON response");
      }
    } else {
      console.log("⚠️ Unexpected content type");
    }
  } catch (error) {
    console.error("Request failed:", error);
  }
}

// Run the debug function
debugLocationMediaAuth()
  .then(() => process.exit(0))
  .catch((error) => {
    console.error("Error:", error);
    process.exit(1);
  });
