-- Create scenes table
CREATE TABLE IF NOT EXISTS "scenes" (
  "id" UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  "name" VARCHAR(255) NOT NULL,
  "description" TEXT,
  "scene_number" VARCHAR(50),
  "time_of_day" VARCHAR(50),
  "interior" BOOLEAN DEFAULT false,
  "exterior" BOOLEAN DEFAULT false,
  "status" VARCHAR(50) NOT NULL DEFAULT 'planning',
  "project_id" UUID NOT NULL REFERENCES "projects"("id") ON DELETE CASCADE,
  "final_location_id" UUID REFERENCES "locations"("id"),
  "created_by_id" UUID REFERENCES "users"("id"),
  "metadata" JSONB,
  "created_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
  "updated_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
  "deleted_at" TIMESTAMP WITH TIME ZONE
);

-- Create scene_locations table
CREATE TABLE IF NOT EXISTS "scene_locations" (
  "id" UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  "scene_id" UUID NOT NULL REFERENCES "scenes"("id") ON DELETE CASCADE,
  "location_id" UUID NOT NULL REFERENCES "locations"("id") ON DELETE CASCADE,
  "notes" TEXT,
  "added_by_id" UUID REFERENCES "users"("id"),
  "is_selected" BOOLEAN DEFAULT false,
  "created_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
  "updated_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW()
);

-- Create scene_notes table
CREATE TABLE IF NOT EXISTS "scene_notes" (
  "id" UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  "scene_id" UUID NOT NULL REFERENCES "scenes"("id") ON DELETE CASCADE,
  "content" TEXT NOT NULL,
  "author_id" UUID REFERENCES "users"("id"),
  "created_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
  "updated_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW()
);

-- Create location_votes table
CREATE TABLE IF NOT EXISTS "location_votes" (
  "id" UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  "scene_location_id" UUID NOT NULL REFERENCES "scene_locations"("id") ON DELETE CASCADE,
  "user_id" UUID NOT NULL REFERENCES "users"("id") ON DELETE CASCADE,
  "vote" VARCHAR(10) NOT NULL,
  "created_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
  "updated_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW()
);

-- Add indexes for better query performance
CREATE INDEX IF NOT EXISTS "scenes_project_id_idx" ON "scenes" ("project_id");
CREATE INDEX IF NOT EXISTS "scenes_final_location_id_idx" ON "scenes" ("final_location_id");
CREATE INDEX IF NOT EXISTS "scenes_created_by_id_idx" ON "scenes" ("created_by_id");
CREATE INDEX IF NOT EXISTS "scene_locations_scene_id_idx" ON "scene_locations" ("scene_id");
CREATE INDEX IF NOT EXISTS "scene_locations_location_id_idx" ON "scene_locations" ("location_id");
CREATE INDEX IF NOT EXISTS "scene_locations_added_by_id_idx" ON "scene_locations" ("added_by_id");
CREATE INDEX IF NOT EXISTS "scene_notes_scene_id_idx" ON "scene_notes" ("scene_id");
CREATE INDEX IF NOT EXISTS "scene_notes_author_id_idx" ON "scene_notes" ("author_id");
CREATE INDEX IF NOT EXISTS "location_votes_scene_location_id_idx" ON "location_votes" ("scene_location_id");
CREATE INDEX IF NOT EXISTS "location_votes_user_id_idx" ON "location_votes" ("user_id");
