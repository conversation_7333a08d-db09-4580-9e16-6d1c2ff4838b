import { db } from "@/lib/db"
import { locations } from "@/modules/location/schema"
import { organizations } from "@/modules/organization/schema"
import { eq, isNull, and } from "drizzle-orm"
// We'll use the built-in fetch API instead of node-fetch
// @ts-expect-error - Ignore the node-fetch import error
import fetch from "node-fetch"

async function main() {
  console.log("Verifying map initial data API fix...")
  
  try {
    // Get a sample organization
    const sampleOrg = await db.query.organizations.findFirst({
      where: isNull(organizations.deletedAt),
    })
    
    if (!sampleOrg) {
      console.error("No organizations found in database")
      process.exit(1)
    }
    
    console.log(`Using organization: ${sampleOrg.name} (${sampleOrg.id})`)
    
    // Get all locations for this organization directly from the database
    const allLocationsFromDb = await db.query.locations.findMany({
      where: and(
        eq(locations.organizationId, sampleOrg.id),
        isNull(locations.deletedAt)
      ),
    })
    
    console.log(`Direct DB query found ${allLocationsFromDb.length} locations`)
    
    // Count locations with null project IDs
    const nullProjectLocations = allLocationsFromDb.filter(loc => loc.projectId === null)
    console.log(`Of these, ${nullProjectLocations.length} have null project IDs`)
    
    if (nullProjectLocations.length > 0) {
      console.log("\nLocations with null project IDs:")
      for (const loc of nullProjectLocations) {
        console.log(`- ${loc.name} (ID: ${loc.id})`)
      }
    }
    
    // Call the map initial data API
    console.log("\nCalling map initial data API...")
    
    // Get the organization slug
    const orgSlug = sampleOrg.slug || sampleOrg.name.toLowerCase().replace(/\s+/g, '-')
    
    // Mock headers that would normally be set by middleware
    const headers = {
      'X-Stytch-Org-Id': sampleOrg.stytchOrganizationId || 'mock-stytch-id',
      'X-Stytch-Session-Token': 'mock-session-token',
      'X-Stytch-Session-JWT': 'mock-session-jwt'
    }
    
    // Call the API
    // @ts-expect-error - Ignore type errors for the fetch call
    const response = await fetch(`http://localhost:3000/api/map/initial-data?organizationSlug=${orgSlug}`, {
      headers
    })
    
    if (!response.ok) {
      console.error(`API call failed with status ${response.status}`)
      const errorText = await response.text()
      console.error(`Error: ${errorText}`)
      process.exit(1)
    }
    
    // @ts-expect-error - Ignore type errors for the response.json() call
    const data = await response.json()
    
    // Check if the API returned the expected data
    if (!data.locations) {
      console.error("API response does not include locations")
      console.error("Response:", JSON.stringify(data, null, 2))
      process.exit(1)
    }
    
    console.log(`API returned ${data.locations.length} locations`)
    
    // Count locations with null project IDs in the API response
    // @ts-expect-error - Ignore type errors for the data.locations array
    const nullProjectInApi = data.locations.filter(loc => !loc.projectId).length
    console.log(`Of these, ${nullProjectInApi} have null project IDs`)
    
    // Check if all locations with null projectId are included in the API response
    // @ts-expect-error - Ignore type errors for the data.locations array
    const apiLocationIds = new Set(data.locations.map(loc => loc.id))
    const missingNullProjectLocations = nullProjectLocations.filter(loc => !apiLocationIds.has(loc.id))
    
    if (missingNullProjectLocations.length > 0) {
      console.log("\nLocations with null projectId missing from API response:")
      for (const loc of missingNullProjectLocations) {
        console.log(`- ${loc.name} (ID: ${loc.id})`)
      }
      console.log("\nFIX FAILED: Some locations with null projectId are missing from the API response")
    } else if (nullProjectLocations.length > 0) {
      console.log("\nFIX SUCCESSFUL: All locations with null projectId are included in the API response")
    } else {
      console.log("\nNo locations with null projectId found to test with")
    }
    
  } catch (error) {
    console.error("Error verifying map initial data API fix:", error)
    process.exit(1)
  }
  
  process.exit(0)
}

main()
