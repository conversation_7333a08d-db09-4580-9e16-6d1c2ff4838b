// Import required modules
import { db } from "@/lib/db"
import { favoriteLists, favoriteListLocations } from "@/modules/favorites/schema"
import { locations } from "@/modules/location/schema"
import { projects } from "@/modules/project/schema"
import { eq, and, isNull } from "drizzle-orm"
import { extractUuidFromStytchId } from "@/lib/utils/stytchUtils"
import * as dotenv from "dotenv"

// Load environment variables
dotenv.config()

// Sample data - Using the organization ID from the seed-map-locations.ts script
const ORGANIZATION_ID = "af6520e4-b62b-4034-8e6c-5fb93ae41d93"
const STYTCH_MEMBER_ID = "member-live-43401ba0-bcf8-43e6-a726-ac203de9b44b"

async function seedFavorites() {
  try {
    console.log("Starting to seed favorites data...")
    
    // Use the organization ID directly and extract UUID from Stytch member ID
    const organizationId = ORGANIZATION_ID
    const userId = extractUuidFromStytchId(STYTCH_MEMBER_ID)
    
    if (!userId) {
      console.error("Failed to extract UUID from Stytch member ID")
      process.exit(1)
    }
    
    console.log(`Using organization ID: ${organizationId}`)
    console.log(`Using user ID: ${userId}`)
    
    // Get some locations to add to favorites
    const locationsList = await db.query.locations.findMany({
      where: and(eq(locations.organizationId, organizationId), isNull(locations.deletedAt)),
      limit: 5
    })
    
    if (locationsList.length === 0) {
      console.error("No locations found for the organization")
      process.exit(1)
    }
    
    console.log(`Found ${locationsList.length} locations`)
    
    // Create a custom favorite list
    const [customList] = await db.insert(favoriteLists)
      .values({
        organizationId,
        name: "My Favorite Locations",
        type: "custom",
        createdById: userId,
      })
      .returning()
    
    console.log(`Created custom favorite list: ${customList.name} (${customList.id})`)
    
    // Add locations to the custom list
    for (const location of locationsList) {
      await db.insert(favoriteListLocations)
        .values({
          favoriteListId: customList.id,
          locationId: location.id,
          addedById: userId,
        })
      
      console.log(`Added location ${location.name} (${location.id}) to the custom list`)
    }
    
    // Create a project-based favorite list
    // First, get a project
    const projectsList = await db.query.projects.findMany({
      where: and(eq(projects.organizationId, organizationId), isNull(projects.deletedAt)),
      limit: 1
    })
    
    if (projectsList.length > 0) {
      const project = projectsList[0]
      
      const [projectList] = await db.insert(favoriteLists)
        .values({
          organizationId,
          name: `${project.name} Locations`,
          type: "project",
          projectId: project.id,
          createdById: userId,
        })
        .returning()
      
      console.log(`Created project favorite list: ${projectList.name} (${projectList.id})`)
      
      // Add a couple of locations to the project list
      for (const location of locationsList.slice(0, 2)) {
        await db.insert(favoriteListLocations)
          .values({
            favoriteListId: projectList.id,
            locationId: location.id,
            addedById: userId,
          })
        
        console.log(`Added location ${location.name} (${location.id}) to the project list`)
      }
    }
    
    console.log("Favorites seeding completed successfully")
  } catch (error) {
    console.error("Error seeding favorites:", error)
    process.exit(1)
  }
}

// Run the seeding function
seedFavorites()
