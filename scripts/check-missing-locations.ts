import { db } from "@/lib/db"
import { locations } from "@/modules/location/schema"
import { eq, isNull, desc } from "drizzle-orm"

async function main() {
  console.log("Checking for recently added locations...")
  
  try {
    // Get the most recent 10 locations
    const recentLocations = await db.query.locations.findMany({
      where: isNull(locations.deletedAt),
      orderBy: [desc(locations.createdAt)],
      limit: 10,
    })
    
    console.log(`Found ${recentLocations.length} recent locations:`)
    
    // Display detailed information about each location
    recentLocations.forEach((location, index) => {
      console.log(`\n--- Location ${index + 1} ---`)
      console.log(`ID: ${location.id}`)
      console.log(`Name: ${location.name}`)
      console.log(`Organization ID: ${location.organizationId}`)
      console.log(`Project ID: ${location.projectId || 'NULL'}`)
      console.log(`Type: ${location.type}`)
      console.log(`Status: ${location.status}`)
      console.log(`Is Active: ${location.isActive}`)
      console.log(`Created At: ${location.createdAt}`)
      console.log(`Updated At: ${location.updatedAt}`)
      console.log(`Deleted At: ${location.deletedAt}`)
      
      // Check for any potential issues
      const issues = []
      
      if (!location.isActive) {
        issues.push("Location is not active")
      }
      
      if (location.deletedAt) {
        issues.push("Location is marked as deleted")
      }
      
      if (issues.length > 0) {
        console.log("Potential issues:")
        issues.forEach(issue => console.log(`- ${issue}`))
      } else {
        console.log("No obvious issues found with this location")
      }
    })
    
    // Check for locations with null project IDs
    const nullProjectLocations = await db.query.locations.findMany({
      where: isNull(locations.projectId),
    })
    
    console.log(`\n\nFound ${nullProjectLocations.length} locations with NULL project_id`)
    
  // Check if the getLocations function works correctly with null project IDs
  console.log("\nTesting getLocations function with null project IDs...")
  
  // Import the getLocations function
  const { getLocations } = await import("@/modules/location/service")
  
  // Get a sample organization ID from the recent locations
  const sampleOrgId = recentLocations[0]?.organizationId
  
  if (sampleOrgId) {
    // Test with no filters (should include null project IDs)
    const allLocations = await getLocations(sampleOrgId)
    console.log(`getLocations with no filters returned ${allLocations.length} locations`)
    
    // Count locations with null project IDs in the result
    const nullProjectInResult = allLocations.filter(loc => !loc.projectId).length
    console.log(`Of these, ${nullProjectInResult} have null project IDs`)
    
    // Log all location IDs and their project IDs to debug
    console.log("\nAll location IDs and their project IDs:")
    allLocations.forEach(loc => {
      console.log(`Location ID: ${loc.id}, Name: ${loc.name}, Project ID: ${loc.projectId || 'NULL'}`)
    })
    
    // Directly check if our two known null project ID locations are in the results
    const pitotHouse = allLocations.find(loc => loc.name === "Pitot House")
    const rosemaryBeach = allLocations.find(loc => loc.name === "Rosemary Beach")
    
    console.log("\nChecking for specific locations:")
    console.log(`Pitot House in results: ${pitotHouse ? 'YES' : 'NO'}`)
    console.log(`Rosemary Beach in results: ${rosemaryBeach ? 'YES' : 'NO'}`)
    
    // Test with a specific project ID filter
    if (recentLocations[0]?.projectId) {
      const projectLocations = await getLocations(sampleOrgId, { projectId: recentLocations[0].projectId })
      console.log(`getLocations with projectId filter returned ${projectLocations.length} locations`)
    }
  }
    
  } catch (error) {
    console.error("Error checking locations:", error)
    process.exit(1)
  }
  
  process.exit(0)
}

main()
