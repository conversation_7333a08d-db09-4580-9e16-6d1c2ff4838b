import { db } from "../lib/db"
import { organizations, users, organizationUserRoles, projects, locations } from "../lib/db/schema"
import { PROJECT_STATUS } from "../modules/project/schema"
import * as dotenv from "dotenv"
import { generateSlug } from "../lib/utils/string"

dotenv.config()

async function main() {
  console.log("Seeding database...")

  try {
    // Create a test user
    const [testUser] = await db
      .insert(users)
      .values({
        email: "<EMAIL>",
        name: "Firefly Media",
        isActive: true,
      })
      .returning()

    console.log("Created test user:", testUser.id)

    // Create a test organization
    const [testOrg] = await db
      .insert(organizations)
      .values({
        name: "Firefly Media Organization",
        slug: generateSlug("Firefly Media Organization"),
        description: "A media organization for development",
        isActive: true,
      })
      .returning()

    console.log("Created test organization:", testOrg.id)

    // Add user to organization as owner
    await db.insert(organizationUserRoles).values({
      organizationId: testOrg.id,
      userId: testUser.id,
      role: "owner",
    })

    // Create test projects
    const projectsData = [
      {
        name: "Downtown Renovation",
        description: "Renovation of downtown office buildings",
        status: PROJECT_STATUS[0], // "active"
        organizationId: testOrg.id,
      },
      {
        name: "Warehouse Expansion",
        description: "Expansion of warehouse facilities",
        status: PROJECT_STATUS[2], // "planning"
        organizationId: testOrg.id,
      },
    ]

    const createdProjects = await Promise.all(
      projectsData.map(async (project) => {
        const [createdProject] = await db.insert(projects).values(project).returning()
        return createdProject
      }),
    )

    console.log(`Created ${createdProjects.length} test projects`)

    // Create test locations
    const locationsData = [
      {
        name: "Main Office",
        description: "Company headquarters",
        address: {
          street: "123 Business Ave",
          city: "New York",
          state: "NY",
          postalCode: "10001",
          country: "USA",
          formatted: "123 Business Ave, New York, NY 10001, USA",
        },
        coordinates: {
          latitude: 40.7128,
          longitude: -74.006,
        },
        type: "office",
        projectId: createdProjects[0].id,
        organizationId: testOrg.id,
        isActive: true,
      },
      {
        name: "North Warehouse",
        description: "Main storage facility",
        address: {
          street: "456 Industrial Pkwy",
          city: "Chicago",
          state: "IL",
          postalCode: "60007",
          country: "USA",
          formatted: "456 Industrial Pkwy, Chicago, IL 60007, USA",
        },
        coordinates: {
          latitude: 41.8781,
          longitude: -87.6298,
        },
        type: "warehouse",
        projectId: createdProjects[1].id,
        organizationId: testOrg.id,
        isActive: true,
      },
    ]

    const createdLocations = await Promise.all(
      locationsData.map(async (location) => {
        const [createdLocation] = await db.insert(locations).values(location).returning()
        return createdLocation
      }),
    )

    console.log(`Created ${createdLocations.length} test locations`)

    console.log("Database seeded successfully!")
  } catch (error) {
    console.error("Error seeding database:", error)
    throw error
  }
}

main()
  .then(() => process.exit(0))
  .catch((error) => {
    console.error(error)
    process.exit(1)
  })
