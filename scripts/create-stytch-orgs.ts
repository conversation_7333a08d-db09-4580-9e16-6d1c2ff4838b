import { db } from "@/lib/db";
import { organizations } from "@/modules/organization/schema";
import { isNull, eq } from "drizzle-orm";
import { createStytchOrganization } from "@/modules/organization/stytch";
import * as readline from "readline";

/**
 * This script creates Stytch organizations for any organizations in the database
 * that are missing a stytchOrganizationId.
 */
async function main() {
  console.log("Checking for organizations missing Stytch IDs...");

  try {
    // Get all active organizations
    const allOrgs = await db.query.organizations.findMany({
      where: isNull(organizations.deletedAt),
    });

    console.log(`Found ${allOrgs.length} total active organizations.`);

    // Filter organizations missing Stytch IDs
    const orgsWithoutStytchId = allOrgs.filter(org => !org.stytchOrganizationId);

    if (orgsWithoutStytchId.length === 0) {
      console.log("✅ All organizations have Stytch IDs. No action needed.");
      return;
    }

    console.log(`⚠️ Found ${orgsWithoutStytchId.length} organizations missing Stytch IDs:`);
    
    // Display organizations missing Stytch IDs
    orgsWithoutStytchId.forEach((org, index) => {
      console.log(`${index + 1}. ID: ${org.id}, Name: ${org.name}, Slug: ${org.slug}`);
    });

    // Ask for confirmation before proceeding
    const confirm = await new Promise<boolean>((resolve) => {
      const rl = readline.createInterface({
        input: process.stdin,
        output: process.stdout
      });
      
      rl.question('Do you want to create Stytch organizations for these organizations? (y/N) ', (answer) => {
        rl.close();
        resolve(answer.toLowerCase() === 'y');
      });
    });

    if (!confirm) {
      console.log("Operation cancelled.");
      return;
    }

    console.log("\nCreating Stytch organizations...");
    
    // Process each organization
    for (const org of orgsWithoutStytchId) {
      try {
        console.log(`Creating Stytch organization for: ${org.name} (${org.slug})...`);
        
        // Create organization in Stytch
        const stytchOrg = await createStytchOrganization(org.name, org.slug);
        
        // Update the organization in the database with the new Stytch ID
        await db.update(organizations)
          .set({ 
            stytchOrganizationId: stytchOrg.organization_id,
            updatedAt: new Date()
          })
          .where(eq(organizations.id, org.id));
        
        console.log(`✅ Successfully created Stytch organization and updated database record for ${org.name}`);
        console.log(`   Stytch Organization ID: ${stytchOrg.organization_id}`);
      } catch (error) {
        console.error(`❌ Failed to create Stytch organization for ${org.name}:`, error);
        // Continue with the next organization
      }
    }

    console.log("\nOperation completed.");
  } catch (error) {
    console.error("Error processing organizations:", error);
    process.exit(1);
  }
}

main()
  .then(() => process.exit(0))
  .catch((error) => {
    console.error(error);
    process.exit(1);
  });
