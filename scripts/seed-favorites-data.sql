-- Insert a custom favorite list
INSERT INTO "favorite_lists" (
  "organization_id", 
  "name", 
  "type", 
  "created_by"
) VALUES (
  'af6520e4-b62b-4034-8e6c-5fb93ae41d93', -- Organization ID from seed-map-locations.ts
  'My Favorite Locations', 
  'custom',
  (SELECT id FROM "users" LIMIT 1) -- Get the first user ID
);

-- Get the ID of the favorite list we just created
DO $$
DECLARE
  favorite_list_id UUID;
  user_id UUID;
BEGIN
  -- Get the favorite list ID
  SELECT id INTO favorite_list_id FROM "favorite_lists" WHERE "name" = 'My Favorite Locations' LIMIT 1;
  
  -- Get a user ID
  SELECT id INTO user_id FROM "users" LIMIT 1;
  
  -- Add some locations to the favorite list
  INSERT INTO "favorite_list_locations" (
    "favorite_list_id",
    "location_id",
    "added_by"
  )
  SELECT 
    favorite_list_id,
    id,
    user_id
  FROM "locations"
  LIMIT 5;
  
  -- Insert a project-based favorite list
  INSERT INTO "favorite_lists" (
    "organization_id", 
    "name", 
    "type", 
    "project_id",
    "created_by"
  ) 
  SELECT
    'af6520e4-b62b-4034-8e6c-5fb93ae41d93', -- Organization ID
    name || ' Locations', -- Project name + " Locations"
    'project',
    id,
    user_id
  FROM "projects"
  WHERE "organization_id" = 'af6520e4-b62b-4034-8e6c-5fb93ae41d93'
  LIMIT 1;
  
  -- Get the ID of the project favorite list we just created
  SELECT id INTO favorite_list_id FROM "favorite_lists" WHERE "type" = 'project' LIMIT 1;
  
  -- Add some locations to the project favorite list
  INSERT INTO "favorite_list_locations" (
    "favorite_list_id",
    "location_id",
    "added_by"
  )
  SELECT 
    favorite_list_id,
    id,
    user_id
  FROM "locations"
  LIMIT 3;
END $$;
