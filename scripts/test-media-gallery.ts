import { db } from "@/lib/db";
import { locations, locationMedia } from "@/modules/location/schema";
import { eq, and, isNull, asc } from "drizzle-orm";

/**
 * This script tests the media gallery functionality by:
 * 1. Fetching a location with its media
 * 2. Logging the media data structure to verify it includes the uploader relation
 * 3. Checking if the media data is properly formatted for the gallery
 */
async function testMediaGallery() {
  try {
    // Get the first location with media
    const locationResults = await db.query.locations.findMany({
      where: isNull(locations.deletedAt),
      with: {
        media: {
          where: (locationMedia, { eq, and, isNull }) => 
            and(
              eq(locationMedia.isPublic, true),
              isNull(locationMedia.deletedAt)
            ),
          orderBy: [asc(locationMedia.ordering)],
          with: {
            uploader: {
              columns: {
                id: true,
                name: true,
              }
            }
          }
        },
      },
      limit: 5,
    });

    console.log(`Found ${locationResults.length} locations`);

    // Find a location with media
    const locationWithMedia = locationResults.find((location) => location.media && location.media.length > 0);

    if (!locationWithMedia) {
      console.log("No locations with media found");
      return;
    }

    console.log(`Location "${locationWithMedia.name}" has ${locationWithMedia.media.length} media items`);
    
    // Log the first media item to see its structure
    if (locationWithMedia.media.length > 0) {
      console.log("First media item structure:", JSON.stringify(locationWithMedia.media[0], null, 2));
      
      // Check if the uploader relation is included
      const firstMedia = locationWithMedia.media[0];
      if ('uploader' in firstMedia) {
        console.log("✅ Media includes uploader relation:", firstMedia.uploader);
      } else {
        console.log("❌ Media does not include uploader relation");
      }
    }

    // Test the media transformation function
    function adaptMediaItemsForGallery(mediaItems: any[] = []) {
      console.log('Adapting media items for gallery, count:', mediaItems.length);
      
      return mediaItems.map(media => {
        // Determine the media type
        const mediaType = media.type.toLowerCase().includes('image') ? 'image' : 'video';
        
        // Handle uploader information safely
        let uploaderId = 'unknown';
        let uploaderName = 'Unknown User';
        
        // Check if media has an uploader object with id and name
        if (media.uploader && typeof media.uploader === 'object') {
          if ('id' in media.uploader) uploaderId = media.uploader.id;
          if ('name' in media.uploader) uploaderName = media.uploader.name;
        } 
        // Fall back to uploadedBy if available
        else if (media.uploadedBy) {
          uploaderId = media.uploadedBy;
          uploaderName = `User ${media.uploadedBy.substring(0, 8)}`;
        }
        
        // Create the adapted media item
        return {
          id: media.id,
          url: media.url,
          filename: media.title || 'Untitled',
          type: mediaType,
          thumbnailUrl: media.thumbnailUrl,
          uploadedBy: {
            id: uploaderId,
            name: uploaderName
          },
          uploadedAt: media.uploadedAt ? new Date(media.uploadedAt).toISOString() : new Date().toISOString()
        };
      });
    }

    // Transform the media items
    const transformedMedia = adaptMediaItemsForGallery(locationWithMedia.media);
    console.log("Transformed media items:", JSON.stringify(transformedMedia, null, 2));
    
    console.log("✅ Test completed successfully");
  } catch (error) {
    console.error("Error testing media gallery:", error);
  }
}

// Run the test
testMediaGallery()
  .then(() => process.exit(0))
  .catch((error) => {
    console.error("Error:", error);
    process.exit(1);
  });
