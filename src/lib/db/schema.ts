import { pgTable, serial, text, timestamp, boolean, integer, uuid, pgEnum, json, unique, real, varchar, date, foreignKey, primaryKey } from "drizzle-orm/pg-core";
import { relations } from "drizzle-orm";
import { sql } from "drizzle-orm";

// Custom PostGIS types
export const point = (columnName: string) => sql`GEOGRAPHY(POINT, 4326)`.as(columnName);
export const polygon = (columnName: string) => sql`GEOGRAPHY(POLYGON, 4326)`.as(columnName);

// Enumerations
export const roleEnum = pgEnum("role", ["admin", "manager", "scout", "viewer"]);
export const projectStatusEnum = pgEnum("project_status", ["draft", "active", "completed", "archived"]);
export const locationStatusEnum = pgEnum("location_status", ["pending", "approved", "rejected", "secured", "unavailable"]);
export const documentTypeEnum = pgEnum("document_type", ["permit", "contract", "release", "insurance", "misc"]);
export const documentStatusEnum = pgEnum("document_status", ["draft", "pending_signature", "signed", "expired", "revoked"]);
export const taskStatusEnum = pgEnum("task_status", ["pending", "in_progress", "completed", "cancelled"]);
export const taskPriorityEnum = pgEnum("task_priority", ["low", "medium", "high", "urgent"]);
export const paymentStatusEnum = pgEnum("payment_status", ["pending", "paid", "failed", "refunded"]);
export const subscriptionTierEnum = pgEnum("subscription_tier", ["basic", "professional", "enterprise"]);

// Organizations Table
export const organizations = pgTable("organizations", {
  id: uuid("id").defaultRandom().primaryKey(),
  name: varchar("name", { length: 255 }).notNull(),
  slug: varchar("slug", { length: 255 }).notNull().unique(),
  logo: text("logo"),
  createdAt: timestamp("created_at").defaultNow().notNull(),
  updatedAt: timestamp("updated_at").defaultNow().notNull(),
  polarCustomerId: varchar("polar_customer_id", { length: 255 }).unique(),
  stytchOrganizationId: varchar("stytch_organization_id", { length: 255 }).unique(),
  subscriptionTier: subscriptionTierEnum("subscription_tier").default("basic"),
  subscriptionStatus: varchar("subscription_status", { length: 50 }),
  subscriptionValidUntil: timestamp("subscription_valid_until"),
  maxProjects: integer("max_projects").default(5),
  maxLocationsPerProject: integer("max_locations_per_project").default(50),
  maxTeamMembers: integer("max_team_members").default(5),
  maxStorage: integer("max_storage").default(10), // in GB
  settings: json("settings")
});

// Users Table (extends Stytch users)
export const users = pgTable("users", {
  id: uuid("id").primaryKey(), // Match with database ID
  email: varchar("email", { length: 255 }).notNull().unique(),
  firstName: varchar("first_name", { length: 100 }),
  lastName: varchar("last_name", { length: 100 }),
  avatarUrl: text("avatar_url"),
  role: roleEnum("role").default("viewer"),
  createdAt: timestamp("created_at").defaultNow().notNull(),
  updatedAt: timestamp("updated_at").defaultNow().notNull(),
  lastLogin: timestamp("last_login"),
  preferences: json("preferences"),
  isActive: boolean("is_active").default(true),
  organizationId: uuid("organization_id").references(() => organizations.id, { onDelete: "set null" }),
  stytchUserId: varchar("stytch_user_id", { length: 255 }).unique()
});

// Organization to User relationships table
export const organizationMembers = pgTable("organization_members", {
  id: uuid("id").defaultRandom().primaryKey(),
  organizationId: uuid("organization_id").notNull().references(() => organizations.id, { onDelete: "cascade" }),
  userId: uuid("user_id").notNull().references(() => users.id, { onDelete: "cascade" }),
  role: roleEnum("role").default("viewer").notNull(),
  createdAt: timestamp("created_at").defaultNow().notNull(),
  invitedBy: uuid("invited_by").references(() => users.id),
  inviteAccepted: boolean("invite_accepted").default(false),
  lastAccessed: timestamp("last_accessed"),
  stytchMemberId: varchar("stytch_member_id", { length: 255 }).unique()
}, (table) => {
  return {
    uniqueMember: unique().on(table.organizationId, table.userId)
  };
});

// Projects Table
export const projects = pgTable("projects", {
  id: uuid("id").defaultRandom().primaryKey(),
  name: varchar("name", { length: 255 }).notNull(),
  description: text("description"),
  organizationId: uuid("organization_id").notNull().references(() => organizations.id, { onDelete: "cascade" }),
  createdBy: uuid("created_by").references(() => users.id),
  status: projectStatusEnum("status").default("draft"),
  startDate: date("start_date"),
  endDate: date("end_date"),
  budget: real("budget"),
  createdAt: timestamp("created_at").defaultNow().notNull(),
  updatedAt: timestamp("updated_at").defaultNow().notNull(),
  thumbnailUrl: text("thumbnail_url"),
  clientName: varchar("client_name", { length: 255 }),
  clientContact: varchar("client_contact", { length: 255 }),
  productionType: varchar("production_type", { length: 100 }),
  settings: json("settings"),
  metadata: json("metadata"),
  isArchived: boolean("is_archived").default(false)
});

// Project Members Table
export const projectMembers = pgTable("project_members", {
  id: uuid("id").defaultRandom().primaryKey(),
  projectId: uuid("project_id").notNull().references(() => projects.id, { onDelete: "cascade" }),
  userId: uuid("user_id").notNull().references(() => users.id, { onDelete: "cascade" }),
  role: roleEnum("role").default("viewer").notNull(),
  createdAt: timestamp("created_at").defaultNow().notNull(),
  addedBy: uuid("added_by").references(() => users.id),
  lastAccessed: timestamp("last_accessed")
}, (table) => {
  return {
    uniqueMember: unique().on(table.projectId, table.userId)
  };
});

// Locations Table
export const locations = pgTable("locations", {
  id: uuid("id").defaultRandom().primaryKey(),
  name: varchar("name", { length: 255 }).notNull(),
  description: text("description"),
  organizationId: uuid("organization_id").notNull().references(() => organizations.id, { onDelete: "cascade" }),
  createdBy: uuid("created_by").references(() => users.id),
  status: locationStatusEnum("status").default("pending"),
  createdAt: timestamp("created_at").defaultNow().notNull(),
  updatedAt: timestamp("updated_at").defaultNow().notNull(),
  
  // Location details
  address: text("address"),
  city: varchar("city", { length: 100 }),
  state: varchar("state", { length: 100 }),
  zipCode: varchar("zip_code", { length: 20 }),
  country: varchar("country", { length: 100 }),
  coordinates: point("coordinates"), // PostGIS point type for lat/lng
  boundary: polygon("boundary"), // Optional polygon boundary for the location
  
  // Contact information
  contactName: varchar("contact_name", { length: 255 }),
  contactEmail: varchar("contact_email", { length: 255 }),
  contactPhone: varchar("contact_phone", { length: 100 }),
  
  // Location specifics
  locationSize: varchar("location_size", { length: 100 }),
  locationFeatures: json("location_features"), // Array of features/amenities
  locationTags: json("location_tags"), // Array of tags for categorization
  accessibility: text("accessibility"),
  parkingInfo: text("parking_info"),
  hourlyRate: real("hourly_rate"),
  dailyRate: real("daily_rate"),
  
  // Approval and restrictions
  permitsRequired: boolean("permits_required").default(false),
  restrictions: text("restrictions"),
  approvedBy: uuid("approved_by").references(() => users.id),
  approvedAt: timestamp("approved_at"),
  
  // Metadata and settings
  metadata: json("metadata"),
  isArchived: boolean("is_archived").default(false)
});

// Project to Location junction table
export const projectLocations = pgTable("project_locations", {
  id: uuid("id").defaultRandom().primaryKey(),
  projectId: uuid("project_id").notNull().references(() => projects.id, { onDelete: "cascade" }),
  locationId: uuid("location_id").notNull().references(() => locations.id, { onDelete: "cascade" }),
  addedBy: uuid("added_by").references(() => users.id),
  addedAt: timestamp("added_at").defaultNow().notNull(),
  notes: text("notes"),
  status: locationStatusEnum("status").default("pending"),
  isPrimary: boolean("is_primary").default(false),
  shootDate: date("shoot_date"),
  shootStartTime: varchar("shoot_start_time", { length: 50 }),
  shootEndTime: varchar("shoot_end_time", { length: 50 })
}, (table) => {
  return {
    uniqueLocation: unique().on(table.projectId, table.locationId)
  };
});

// Location Media Table
export const locationMedia = pgTable("location_media", {
  id: uuid("id").defaultRandom().primaryKey(),
  locationId: uuid("location_id").notNull().references(() => locations.id, { onDelete: "cascade" }),
  url: text("url").notNull(),
  thumbnailUrl: text("thumbnail_url"),
  type: varchar("type", { length: 50 }).notNull(), // image, video, etc.
  title: varchar("title", { length: 255 }),
  description: text("description"),
  uploadedBy: uuid("uploaded_by").references(() => users.id),
  uploadedAt: timestamp("uploaded_at").defaultNow().notNull(),
  metadata: json("metadata"),
  fileSize: integer("file_size"), // In bytes
  width: integer("width"),
  height: integer("height"),
  isPublic: boolean("is_public").default(true),
  ordering: integer("ordering").default(0)
});

// Custom Maps Table
export const maps = pgTable("maps", {
  id: uuid("id").defaultRandom().primaryKey(),
  name: varchar("name", { length: 255 }).notNull(),
  description: text("description"),
  organizationId: uuid("organization_id").notNull().references(() => organizations.id, { onDelete: "cascade" }),
  projectId: uuid("project_id").references(() => projects.id, { onDelete: "cascade" }),
  createdBy: uuid("created_by").references(() => users.id),
  createdAt: timestamp("created_at").defaultNow().notNull(),
  updatedAt: timestamp("updated_at").defaultNow().notNull(),
  center: point("center"), // Center point of the map
  zoom: real("zoom").default(10),
  style: varchar("style", { length: 100 }).default("streets"),
  isPublic: boolean("is_public").default(false),
  settings: json("settings"),
  mapLayers: json("map_layers") // Configuration for different layers
});

// Map Markers Table
export const mapMarkers = pgTable("map_markers", {
  id: uuid("id").defaultRandom().primaryKey(),
  mapId: uuid("map_id").notNull().references(() => maps.id, { onDelete: "cascade" }),
  locationId: uuid("location_id").references(() => locations.id, { onDelete: "set null" }),
  coordinates: point("coordinates").notNull(),
  title: varchar("title", { length: 255 }),
  description: text("description"),
  color: varchar("color", { length: 50 }).default("#FF0000"),
  icon: varchar("icon", { length: 100 }).default("pin"),
  createdBy: uuid("created_by").references(() => users.id),
  createdAt: timestamp("created_at").defaultNow(),
  isVisible: boolean("is_visible").default(true),
  customData: json("custom_data")
});

// Documents Table
export const documents = pgTable("documents", {
  id: uuid("id").defaultRandom().primaryKey(),
  organizationId: uuid("organization_id").notNull().references(() => organizations.id, { onDelete: "cascade" }),
  projectId: uuid("project_id").references(() => projects.id, { onDelete: "cascade" }),
  locationId: uuid("location_id").references(() => locations.id, { onDelete: "cascade" }),
  name: varchar("name", { length: 255 }).notNull(),
  description: text("description"),
  documentType: documentTypeEnum("document_type").notNull(),
  url: text("url").notNull(),
  status: documentStatusEnum("status").default("draft"),
  version: integer("version").default(1),
  createdBy: uuid("created_by").references(() => users.id),
  createdAt: timestamp("created_at").defaultNow().notNull(),
  updatedAt: timestamp("updated_at").defaultNow().notNull(),
  expiresAt: timestamp("expires_at"),
  signingUrl: text("signing_url"),
  signedBy: uuid("signed_by").references(() => users.id),
  signedAt: timestamp("signed_at"),
  metadata: json("metadata"),
  fileSize: integer("file_size"), // In bytes
  isPublic: boolean("is_public").default(false),
  isArchived: boolean("is_archived").default(false)
});

// Tasks Table
export const tasks = pgTable("tasks", {
  id: uuid("id").defaultRandom().primaryKey(),
  title: varchar("title", { length: 255 }).notNull(),
  description: text("description"),
  organizationId: uuid("organization_id").notNull().references(() => organizations.id, { onDelete: "cascade" }),
  projectId: uuid("project_id").references(() => projects.id, { onDelete: "cascade" }),
  locationId: uuid("location_id").references(() => locations.id, { onDelete: "set null" }),
  assignedTo: uuid("assigned_to").references(() => users.id),
  createdBy: uuid("created_by").references(() => users.id),
  status: taskStatusEnum("status").default("pending"),
  priority: taskPriorityEnum("priority").default("medium"),
  dueDate: timestamp("due_date"),
  createdAt: timestamp("created_at").defaultNow().notNull(),
  updatedAt: timestamp("updated_at").defaultNow().notNull(),
  completedAt: timestamp("completed_at"),
  completedBy: uuid("completed_by").references(() => users.id),
  taskType: varchar("task_type", { length: 100 }), // scouting, permit application, etc.
  metadata: json("metadata"),
  isArchived: boolean("is_archived").default(false)
});

// Comments Table (for locations, documents, tasks, etc.)
export const comments = pgTable("comments", {
  id: uuid("id").defaultRandom().primaryKey(),
  content: text("content").notNull(),
  organizationId: uuid("organization_id").notNull().references(() => organizations.id, { onDelete: "cascade" }),
  projectId: uuid("project_id").references(() => projects.id, { onDelete: "cascade" }),
  locationId: uuid("location_id").references(() => locations.id, { onDelete: "cascade" }),
  documentId: uuid("document_id").references(() => documents.id, { onDelete: "cascade" }),
  taskId: uuid("task_id").references(() => tasks.id, { onDelete: "cascade" }),
  createdBy: uuid("created_by").references(() => users.id),
  createdAt: timestamp("created_at").defaultNow().notNull(),
  updatedAt: timestamp("updated_at").defaultNow().notNull(),
  parentId: uuid("parent_id").references(() => comments.id, { onDelete: "set null" }),
  isEdited: boolean("is_edited").default(false)
});

// Subscriptions Table (for Polar integration)
export const subscriptions = pgTable("subscriptions", {
  id: uuid("id").defaultRandom().primaryKey(),
  organizationId: uuid("organization_id").notNull().references(() => organizations.id, { onDelete: "cascade" }),
  polarSubscriptionId: varchar("polar_subscription_id", { length: 255 }).unique().notNull(),
  tier: subscriptionTierEnum("tier").notNull(),
  status: varchar("status", { length: 50 }).notNull(),
  currentPeriodStart: timestamp("current_period_start").notNull(),
  currentPeriodEnd: timestamp("current_period_end").notNull(),
  cancelAtPeriodEnd: boolean("cancel_at_period_end").default(false),
  createdAt: timestamp("created_at").defaultNow().notNull(),
  updatedAt: timestamp("updated_at").defaultNow().notNull(),
  metadata: json("metadata"),
  quantity: integer("quantity").default(1)
});

// Subscription Invoices Table
export const subscriptionInvoices = pgTable("subscription_invoices", {
  id: uuid("id").defaultRandom().primaryKey(),
  organizationId: uuid("organization_id").notNull().references(() => organizations.id, { onDelete: "cascade" }),
  subscriptionId: uuid("subscription_id").references(() => subscriptions.id, { onDelete: "set null" }),
  polarInvoiceId: varchar("polar_invoice_id", { length: 255 }).unique().notNull(),
  amount: integer("amount").notNull(), // in cents
  currency: varchar("currency", { length: 10 }).default("usd"),
  status: paymentStatusEnum("status").notNull(),
  invoiceUrl: text("invoice_url"),
  invoiceNumber: varchar("invoice_number", { length: 100 }),
  invoiceDate: timestamp("invoice_date").notNull(),
  paidAt: timestamp("paid_at")
});

// Audit Logs Table
export const auditLogs = pgTable("audit_logs", {
  id: uuid("id").defaultRandom().primaryKey(),
  organizationId: uuid("organization_id").notNull().references(() => organizations.id, { onDelete: "cascade" }),
  userId: uuid("user_id").references(() => users.id),
  action: varchar("action", { length: 100 }).notNull(),
  resourceType: varchar("resource_type", { length: 100 }).notNull(), // project, location, document, etc.
  resourceId: uuid("resource_id"),
  details: json("details"),
  ipAddress: varchar("ip_address", { length: 50 }),
  userAgent: text("user_agent"),
  createdAt: timestamp("created_at").defaultNow().notNull()
});

// AI Tag Suggestions Table
export const aiTagSuggestions = pgTable("ai_tag_suggestions", {
  id: uuid("id").defaultRandom().primaryKey(),
  organizationId: uuid("organization_id").notNull().references(() => organizations.id, { onDelete: "cascade" }),
  locationId: uuid("location_id").references(() => locations.id, { onDelete: "cascade" }),
  mediaId: uuid("media_id").references(() => locationMedia.id, { onDelete: "cascade" }),
  tags: json("tags").notNull(), // Array of suggested tags
  confidence: real("confidence").notNull(),
  createdAt: timestamp("created_at").defaultNow().notNull(),
  isApplied: boolean("is_applied").default(false),
  appliedBy: uuid("applied_by").references(() => users.id),
  appliedAt: timestamp("applied_at")
});

// Weather Data for Locations
export const locationWeather = pgTable("location_weather", {
  id: uuid("id").defaultRandom().primaryKey(),
  locationId: uuid("location_id").notNull().references(() => locations.id, { onDelete: "cascade" }),
  date: date("date").notNull(),
  weatherData: json("weather_data").notNull(),
  sunrise: varchar("sunrise", { length: 20 }),
  sunset: varchar("sunset", { length: 20 }),
  goldenMorningStart: varchar("golden_morning_start", { length: 20 }),
  goldenMorningEnd: varchar("golden_morning_end", { length: 20 }),
  goldenEveningStart: varchar("golden_evening_start", { length: 20 }),
  goldenEveningEnd: varchar("golden_evening_end", { length: 20 }),
  fetchedAt: timestamp("fetched_at").defaultNow().notNull()
}, (table) => {
  return {
    uniqueWeatherDate: unique().on(table.locationId, table.date)
  };
});

// Relations definitions
export const organizationsRelations = relations(organizations, ({ many }) => ({
  users: many(users),
  members: many(organizationMembers),
  projects: many(projects),
  locations: many(locations),
  documents: many(documents),
  tasks: many(tasks),
  comments: many(comments),
  subscriptions: many(subscriptions),
  auditLogs: many(auditLogs)
}));

export const usersRelations = relations(users, ({ one, many }) => ({
  organization: one(organizations, {
    fields: [users.organizationId],
    references: [organizations.id]
  }),
  organizationMembers: many(organizationMembers),
  projectMembers: many(projectMembers),
  createdProjects: many(projects, { relationName: "createdProjects" }),
  createdLocations: many(locations, { relationName: "createdLocations" }),
  createdTasks: many(tasks, { relationName: "createdTasks" }),
  assignedTasks: many(tasks, { relationName: "assignedTasks" }),
  comments: many(comments)
}));

export const projectsRelations = relations(projects, ({ one, many }) => ({
  organization: one(organizations, {
    fields: [projects.organizationId],
    references: [organizations.id]
  }),
  creator: one(users, {
    fields: [projects.createdBy],
    references: [users.id],
    relationName: "createdProjects"
  }),
  members: many(projectMembers),
  locations: many(projectLocations),
  documents: many(documents),
  tasks: many(tasks),
  maps: many(maps)
}));

export const locationsRelations = relations(locations, ({ one, many }) => ({
  organization: one(organizations, {
    fields: [locations.organizationId],
    references: [organizations.id]
  }),
  creator: one(users, {
    fields: [locations.createdBy],
    references: [users.id],
    relationName: "createdLocations"
  }),
  projects: many(projectLocations),
  media: many(locationMedia),
  documents: many(documents),
  tasks: many(tasks),
  weather: many(locationWeather),
  aiSuggestions: many(aiTagSuggestions)
}));

export const documentsRelations = relations(documents, ({ one, many }) => ({
  organization: one(organizations, {
    fields: [documents.organizationId],
    references: [organizations.id]
  }),
  project: one(projects, {
    fields: [documents.projectId],
    references: [projects.id]
  }),
  location: one(locations, {
    fields: [documents.locationId],
    references: [locations.id]
  }),
  creator: one(users, {
    fields: [documents.createdBy],
    references: [users.id]
  }),
  comments: many(comments)
}));

export const tasksRelations = relations(tasks, ({ one, many }) => ({
  organization: one(organizations, {
    fields: [tasks.organizationId],
    references: [organizations.id]
  }),
  project: one(projects, {
    fields: [tasks.projectId],
    references: [projects.id]
  }),
  location: one(locations, {
    fields: [tasks.locationId],
    references: [locations.id]
  }),
  creator: one(users, {
    fields: [tasks.createdBy],
    references: [users.id],
    relationName: "createdTasks"
  }),
  assignee: one(users, {
    fields: [tasks.assignedTo],
    references: [users.id],
    relationName: "assignedTasks"
  }),
  comments: many(comments)
}));

// Export all tables for use in the application
export {
  organizations,
  users,
  organizationMembers,
  projects,
  projectMembers,
  locations,
  projectLocations,
  locationMedia,
  maps,
  mapMarkers,
  documents,
  tasks,
  comments,
  subscriptions,
  subscriptionInvoices,
  auditLogs,
  aiTagSuggestions,
  locationWeather
};
