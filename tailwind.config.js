/** @type {import('tailwindcss').Config} */
import animate from "tailwindcss-animate";
import { withUt } from "uploadthing/tw";

const config = {
  darkMode: ['class'],
  content: [
    './app/**/*.{js,ts,jsx,tsx,mdx}',
    './pages/**/*.{js,ts,jsx,tsx,mdx}',
    './components/**/*.{js,ts,jsx,tsx,mdx}',
  ],
  theme: {
    extend: {
      utilities: {
        '.border-border': {
          borderColor: 'hsl(var(--border))'
        }
      },
      borderRadius: {
        lg: 'var(--radius)',
        md: 'calc(var(--radius) - 2px)',
        sm: 'calc(var(--radius) - 4px)'
      },
      colors: {
        background: 'hsl(var(--background))',
        foreground: 'hsl(var(--foreground))',
        card: {
          DEFAULT: 'hsl(var(--card))',
          foreground: 'hsl(var(--card-foreground))'
        },
        popover: {
          DEFAULT: 'hsl(var(--popover))',
          foreground: 'hsl(var(--popover-foreground))'
        },
        primary: {
          DEFAULT: 'hsl(var(--primary))',
          foreground: 'hsl(var(--primary-foreground))',
          dark: 'hsl(var(--primary-dark))'
        },
        secondary: {
          DEFAULT: 'hsl(var(--secondary))',
          foreground: 'hsl(var(--secondary-foreground))'
        },
        muted: {
          DEFAULT: 'hsl(var(--muted))',
          foreground: 'hsl(var(--muted-foreground))'
        },
        accent: {
          DEFAULT: 'hsl(var(--accent))',
          foreground: 'hsl(var(--accent-foreground))'
        },
        destructive: {
          DEFAULT: 'hsl(var(--destructive))',
          foreground: 'hsl(var(--destructive-foreground))'
        },
        border: 'hsl(var(--border))',
        'border-medium': 'hsl(var(--border-medium))',
        input: 'hsl(var(--input))',
        ring: 'hsl(var(--ring))',
        sidebar: {
          bg: 'hsl(var(--sidebar-bg))',
          text: 'hsl(var(--sidebar-text))'
        },
        status: {
          green: {
            bg: 'hsl(var(--status-green-bg))',
            text: 'hsl(var(--status-green-text))'
          },
          yellow: {
            bg: 'hsl(var(--status-yellow-bg))',
            text: 'hsl(var(--status-yellow-text))'
          },
          red: {
            bg: 'hsl(var(--status-red-bg))',
            text: 'hsl(var(--status-red-text))'
          },
          blue: {
            bg: 'hsl(var(--status-blue-bg))',
            text: 'hsl(var(--status-blue-text))'
          }
        },
        chart: {
          '1': 'hsl(var(--chart-1))',
          '2': 'hsl(var(--chart-2))',
          '3': 'hsl(var(--chart-3))',
          '4': 'hsl(var(--chart-4))',
          '5': 'hsl(var(--chart-5))'
        },
        // Futuristic colors
        neon: {
          blue: 'hsl(var(--neon-blue))',
          green: 'hsl(var(--neon-green))',
          pink: 'hsl(var(--neon-pink))',
          yellow: 'hsl(var(--neon-yellow))'
        },
        'deep-black': 'hsl(var(--deep-black))'
      }
    }
  },
  plugins: [animate],
};

export default withUt(config);
