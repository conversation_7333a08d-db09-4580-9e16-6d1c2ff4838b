import { NextResponse } from "next/server";
import { getLocations } from "@/modules/location/service";
import { findOrganizationByStytchId } from "@/modules/organization/service";
import { checkPermission } from "@/lib/rbac/checkPermission";
import { db } from "@/lib/db";
import { eq, and, isNull, sql } from "drizzle-orm";
import { locations } from "@/modules/location/schema";

// Define common filter combinations
interface FilterParams {
  status?: string;
  type?: string;
  projectId?: string;
}

const filterMap: Record<string, FilterParams> = {
  "all": {},
  "pending": { status: "pending" },
  "approved": { status: "approved" },
  "rejected": { status: "rejected" },
  "secured": { status: "secured" },
  "studio": { type: "studio" },
  "outdoor": { type: "outdoor" },
  "residential": { type: "residential" },
  "commercial": { type: "commercial" },
};

export async function GET(
  request: Request,
  { params }: { params: { filter: string } }
) {
  try {
    // Parse the filter from the URL
    // Ensure params is properly awaited
    const filter = await params.filter;
    
    // Check if the filter is valid
    if (!filterMap[filter]) {
      return NextResponse.json({ error: "Invalid filter" }, { status: 400 });
    }
    
    // Get the filter parameters
    const filterParams = filterMap[filter];
    
    // Get the organization ID from the request headers
    const stytchOrgId = request.headers.get('X-Stytch-Org-Id');
    const sessionToken = request.headers.get('X-Stytch-Session-Token');
    const sessionJwt = request.headers.get('X-Stytch-Session-JWT');
    
    if (!stytchOrgId) {
      return NextResponse.json({ error: "Organization ID is required" }, { status: 400 });
    }
    
    // Create the auth params object for checkPermission
    const authParams = {
      session_token: sessionToken || null,
      session_jwt: sessionJwt || null
    };
    
    // Check permission for viewing locations
    const hasPermission = await checkPermission(authParams, stytchOrgId, 'view', 'location');
    if (!hasPermission) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }
    
    // Get the organization
    const organization = await findOrganizationByStytchId(stytchOrgId);
    
    if (!organization) {
      return NextResponse.json({ error: "Organization not found" }, { status: 404 });
    }
    
    // Get locations with the filter
    const locationResults = await getLocations(organization.id, {
      ...filterParams,
      page: 1,
      pageSize: 12,
    });
    
    // Get the total count of locations with this filter
    // We need to query the database directly to get the total count
    // because getLocations only returns the paginated results
    const countConditions = [
      eq(locations.organizationId, organization.id),
      isNull(locations.deletedAt)
    ];
    
    // Apply the same filters as in the main query
    if (filterParams.status) {
      countConditions.push(eq(locations.status, filterParams.status));
    }
    
    if (filterParams.type) {
      countConditions.push(eq(locations.type, filterParams.type));
    }
    
    // Get the total count
    const [{ value: total }] = await db
      .select({ value: sql<number>`count(*)` })
      .from(locations)
      .where(and(...countConditions));
    
    // Calculate total pages
    const totalPages = Math.ceil(total / 12);
    
    // Return the locations with caching headers
    return NextResponse.json(
      {
        locations: locationResults,
        page: 1,
        pageSize: 12,
        total,
        totalPages,
        filter
      },
      {
        headers: {
          'Cache-Control': 'public, s-maxage=60, stale-while-revalidate=600'
        }
      }
    );
  } catch (error) {
    console.error("Error fetching common locations:", error);
    return NextResponse.json({ error: "Failed to fetch locations" }, { status: 500 });
  }
}

// Enable Incremental Static Regeneration
export const revalidate = 60; // Revalidate every 60 seconds
