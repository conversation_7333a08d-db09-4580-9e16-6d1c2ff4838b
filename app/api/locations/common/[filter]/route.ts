import { NextResponse } from "next/server";
import { getLocations } from "@/modules/location/service";
import { findOrganizationByStytchId } from "@/modules/organization/service";
import { checkPermission } from "@/lib/rbac/checkPermission";

// Define common filter combinations
interface FilterParams {
  status?: string;
  type?: string;
  projectId?: string;
}

const filterMap: Record<string, FilterParams> = {
  "all": {},
  "pending": { status: "pending" },
  "approved": { status: "approved" },
  "rejected": { status: "rejected" },
  "secured": { status: "secured" },
  "studio": { type: "studio" },
  "outdoor": { type: "outdoor" },
  "residential": { type: "residential" },
  "commercial": { type: "commercial" },
};

export async function GET(
  request: Request,
  { params }: { params: { filter: string } }
) {
  try {
    // Parse the filter from the URL
    // Ensure params is properly awaited
    const filter = await params.filter;
    
    // Check if the filter is valid
    if (!filterMap[filter]) {
      return NextResponse.json({ error: "Invalid filter" }, { status: 400 });
    }
    
    // Get the filter parameters
    const filterParams = filterMap[filter];
    
    // Get the organization ID from the request headers
    const stytchOrgId = request.headers.get('X-Stytch-Org-Id');
    const sessionToken = request.headers.get('X-Stytch-Session-Token');
    const sessionJwt = request.headers.get('X-Stytch-Session-JWT');
    
    if (!stytchOrgId) {
      return NextResponse.json({ error: "Organization ID is required" }, { status: 400 });
    }
    
    // Create the auth params object for checkPermission
    const authParams = {
      session_token: sessionToken || null,
      session_jwt: sessionJwt || null
    };
    
    // Check permission for viewing locations
    const hasPermission = await checkPermission(authParams, stytchOrgId, 'view', 'location');
    if (!hasPermission) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }
    
    // Get the organization
    const organization = await findOrganizationByStytchId(stytchOrgId);
    
    if (!organization) {
      return NextResponse.json({ error: "Organization not found" }, { status: 404 });
    }
    
    // Get locations with the filter
    const locationResults = await getLocations(organization.id, {
      ...filterParams,
      page: 1,
      pageSize: 12,
    });

    // Return the locations with caching headers
    return NextResponse.json(
      {
        ...locationResults,
        filter
      },
      {
        headers: {
          'Cache-Control': 'public, s-maxage=60, stale-while-revalidate=600'
        }
      }
    );
  } catch (error) {
    console.error("Error fetching common locations:", error);
    return NextResponse.json({ error: "Failed to fetch locations" }, { status: 500 });
  }
}

// Enable Incremental Static Regeneration
export const revalidate = 60; // Revalidate every 60 seconds
