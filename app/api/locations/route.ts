import { NextResponse } from "next/server";
import type { NextRequest } from "next/server"; // Import NextRequest
import { getLocations, createLocation } from "@/modules/location/service";
import { findOrganizationByStytchId } from "@/modules/organization/service";
// createLocationSchema is used internally by createLocation service, no need to import here
// validate is redundant as service handles validation
import { z } from "zod"; // Keep Zod for error instance check
import { checkPermission } from "@/lib/rbac/checkPermission"; // Import RBAC check function

export async function GET(request: NextRequest) {
  try {
    console.log("GET /api/locations - Starting request");
    const stytchOrgId = request.headers.get('X-Stytch-Org-Id');
    const sessionToken = request.headers.get('X-Stytch-Session-Token');
    const sessionJwt = request.headers.get('X-Stytch-Session-JWT');

    console.log(`Headers: Org ID: ${stytchOrgId ? "Present" : "Missing"}, Session Token: ${sessionToken ? "Present" : "Missing"}, Session JWT: ${sessionJwt ? "Present" : "Missing"}`);

    // Authorization Check: RBAC check for reading locations
     // Ensure stytchOrgId exists before checking permission
     if (!stytchOrgId) {
        console.warn('RBAC check failed: Missing Stytch Org ID header'); // Use simple string
        return NextResponse.json({ error: 'Forbidden - Missing Organization Context' }, { status: 403 });
     }
     
     // Create the auth params object for checkPermission
     const authParams = {
       session_token: sessionToken || null,
       session_jwt: sessionJwt || null
     };
     
     // Use 'view' instead of 'read' to match the Stytch RBAC policy
     console.log("Checking RBAC permission for viewing locations");
     const hasPermission = await checkPermission(authParams, stytchOrgId, 'view', 'location');
     if (!hasPermission) {
       console.warn(`RBAC check failed: User in org ${stytchOrgId} does not have 'view' permission on 'location'`); // Keep template literal here as it uses interpolation
       return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
     }
     console.log("RBAC permission check passed");

    const { searchParams } = new URL(request.url);
    
    // Extract all query parameters
    const projectId = searchParams.get("projectId");
    const status = searchParams.get("status");
    const type = searchParams.get("type");
    const search = searchParams.get("search");
    const page = Number.parseInt(searchParams.get("page") || "1", 10);
    const pageSize = Number.parseInt(searchParams.get("pageSize") || "12", 10);
    
    console.log(`Query params: projectId=${projectId}, status=${status}, type=${type}, search=${search}, page=${page}, pageSize=${pageSize}`);

    // Prepare filters for the service function
    const filters: { 
      projectId?: string;
      status?: string;
      type?: string;
      search?: string;
      page?: number;
      pageSize?: number;
    } = {
      page,
      pageSize
    };
    
    // If projectId is provided, filter by it
    // If not provided, the getLocations function will include all locations
    // regardless of whether they have a project ID or not
    if (projectId) filters.projectId = projectId;
    if (status) filters.status = status;
    if (type) filters.type = type;
    if (search) filters.search = search;

    // Get organization by Stytch organization ID
    console.log(`Finding organization by Stytch ID: ${stytchOrgId}`);
    const organization = await findOrganizationByStytchId(stytchOrgId);
    
    if (!organization) {
      console.warn(`Organization not found for Stytch organization ID: ${stytchOrgId}`);
      return NextResponse.json({ error: "Organization not found" }, { status: 404 });
    }
    console.log(`Found organization: ${organization.name} (${organization.id})`);

    // Always fetch locations for the authenticated organization, optionally filtered by projectId
    console.log(`Fetching locations for organization ID: ${organization.id} with filters`);
    const locationsResult = await getLocations(organization.id, filters);
    console.log(`Found ${locationsResult.locations.length} locations`);

    // Return paginated response with caching headers
    return NextResponse.json(
      locationsResult,
      {
        status: 200,
        headers: {
          'Cache-Control': 'public, s-maxage=10, stale-while-revalidate=59'
        }
      }
    );
  } catch (error) {
    console.error("Error fetching locations:", error)
    return NextResponse.json({ error: "Failed to fetch locations" }, { status: 500 })
  }
}

export async function POST(request: NextRequest) {
  try {
    const stytchOrgId = request.headers.get('X-Stytch-Org-Id');
    const memberId = request.headers.get('X-Stytch-Member-Id'); // Get memberId for createdBy
    const sessionToken = request.headers.get('X-Stytch-Session-Token');
    const sessionJwt = request.headers.get('X-Stytch-Session-JWT');

    // Authorization Check: RBAC check for creating locations
     // Ensure stytchOrgId and memberId exist
      if (!stytchOrgId || !memberId) {
        console.warn('RBAC check failed: Missing Stytch Org ID or Member ID header'); // Use simple string
        return NextResponse.json({ error: 'Forbidden - Missing Organization/Member Context' }, { status: 403 });
     }
     
     // Create the auth params object for checkPermission
     const authParams = {
       session_token: sessionToken || null,
       session_jwt: sessionJwt || null
     };
     
     const hasPermission = await checkPermission(authParams, stytchOrgId, 'create', 'location');
     if (!hasPermission) {
       console.warn(`RBAC check failed: User ${memberId} in org ${stytchOrgId} does not have 'create' permission on 'location'`); // Keep template literal here as it uses interpolation
       return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
     }

    const body = await request.json();

    // Get organization by Stytch organization ID
    const organization = await findOrganizationByStytchId(stytchOrgId);
    
    if (!organization) {
      console.warn(`Organization not found for Stytch organization ID: ${stytchOrgId}`);
      return NextResponse.json({ error: "Organization not found" }, { status: 404 });
    }

    // Add required fields from authenticated context
    const locationData = {
      ...body,
      organizationId: organization.id, // Set organizationId from the database
      createdBy: memberId, // Set createdBy from authenticated session
      // Ensure status defaults correctly if not provided, service likely handles this via schema default
    };

    // Create the location - Service handles validation internally
    const location = await createLocation(locationData);

    return NextResponse.json(location, { status: 201 });
  } catch (error) {
    console.error("Error creating location:", error)

    if (error instanceof z.ZodError) {
      return NextResponse.json({ error: "Validation error", details: error.errors }, { status: 400 })
    }

    return NextResponse.json({ error: "Failed to create location" }, { status: 500 })
  }
}
