import type { NextRequest } from "next/server"
import { NextResponse } from "next/server"
import { getSharedLocationLinkByToken, logSharedLocationLinkAccess } from "@/modules/location/service"

// GET /api/locations/shared/[token] - Get a shared location by token
export async function GET(
  req: NextRequest,
  { params }: { params: { token: string } }
) {
  try {
    const token = params.token
    
    if (!token) {
      return NextResponse.json({ error: "Token is required" }, { status: 400 })
    }
    
    // Get password from query parameters if provided
    const { searchParams } = new URL(req.url)
    const password = searchParams.get("password") || undefined
    
    // Get the shared location link
    const sharedLink = await getSharedLocationLinkByToken(token, password)
    
    if (!sharedLink) {
      return NextResponse.json({ error: "Shared location link not found or expired" }, { status: 404 })
    }
    
    // If the link is password protected and no password was provided, return 401
    if (sharedLink.isPasswordProtected && !password) {
      return NextResponse.json({ 
        error: "Password required", 
        isPasswordProtected: true 
      }, { status: 401 })
    }
    
    // Log access to the shared location link
    await logSharedLocationLinkAccess(
      sharedLink.id,
      req.headers.get("x-forwarded-for") || req.ip,
      req.headers.get("user-agent") || undefined
    )
    
    return NextResponse.json(sharedLink)
  } catch (error) {
    console.error("Error getting shared location:", error)
    
    // If the error is about password, return 401
    if (error instanceof Error && error.message === "Password required") {
      return NextResponse.json({ 
        error: "Password required", 
        isPasswordProtected: true 
      }, { status: 401 })
    }
    
    if (error instanceof Error && error.message === "Invalid password") {
      return NextResponse.json({ error: "Invalid password" }, { status: 401 })
    }
    
    return NextResponse.json(
      { error: "Failed to get shared location" },
      { status: 500 }
    )
  }
}
