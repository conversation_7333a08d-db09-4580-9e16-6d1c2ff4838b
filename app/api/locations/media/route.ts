import { NextResponse } from "next/server";
import type { NextRequest } from "next/server";
import { db } from "@/lib/db";
import { locationMedia } from "@/modules/location/schema";
import { findOrganizationByStytchId } from "@/modules/organization/service";
import { checkPermission } from "@/lib/rbac/checkPermission";
import { z } from "zod";

// Schema for validating the request body
const createMediaSchema = z.object({
  locationId: z.string().uuid(),
  organizationId: z.string(),
  url: z.string().url(),
  type: z.string(),
  uploadedBy: z.string(),
  title: z.string().optional(),
  description: z.string().optional(),
  fileSize: z.number().optional(),
  metadata: z.record(z.any()).optional(),
});

export async function POST(request: NextRequest) {
  try {
    // Get authentication headers
    const stytchOrgId = request.headers.get('X-Stytch-Org-Id');
    const memberId = request.headers.get('X-Stytch-Member-Id');
    const sessionToken = request.headers.get('X-Stytch-Session-Token');
    const sessionJwt = request.headers.get('X-Stytch-Session-JWT');

    // Authorization Check: RBAC check for creating location media
    if (!stytchOrgId || !memberId) {
      console.warn('RBAC check failed: Missing Stytch Org ID or Member ID header');
      return NextResponse.json({ error: 'Forbidden - Missing Organization/Member Context' }, { status: 403 });
    }
    
    // Create the auth params object for checkPermission
    const authParams = {
      session_token: sessionToken || null,
      session_jwt: sessionJwt || null
    };
    
    const hasPermission = await checkPermission(authParams, stytchOrgId, 'create', 'location');
    if (!hasPermission) {
      console.warn(`RBAC check failed: User ${memberId} in org ${stytchOrgId} does not have 'create' permission on 'location'`);
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    // Parse and validate the request body
    const body = await request.json();
    const validatedData = createMediaSchema.parse(body);

    // Get organization by Stytch organization ID
    const organization = await findOrganizationByStytchId(stytchOrgId);
    
    if (!organization) {
      console.warn(`Organization not found for Stytch organization ID: ${stytchOrgId}`);
      return NextResponse.json({ error: "Organization not found" }, { status: 404 });
    }

    // Verify that the location belongs to the organization
    // This is a security check to prevent cross-tenant access
    const locationOrganizationId = validatedData.organizationId;
    if (organization.id !== locationOrganizationId) {
      console.warn(`Organization mismatch: ${organization.id} vs ${locationOrganizationId}`);
      return NextResponse.json({ error: "Organization mismatch" }, { status: 403 });
    }

    // Insert the media record into the database
    const [media] = await db
      .insert(locationMedia)
      .values({
        locationId: validatedData.locationId,
        url: validatedData.url,
        type: validatedData.type,
        title: validatedData.title || validatedData.url.split('/').pop() || 'Untitled',
        description: validatedData.description,
        uploadedBy: validatedData.uploadedBy,
        uploadedAt: new Date(),
        fileSize: validatedData.fileSize,
        metadata: validatedData.metadata || {},
        isPublic: true,
        ordering: 0, // Default ordering
      })
      .returning();

    return NextResponse.json(media, { status: 201 });
  } catch (error) {
    console.error("Error creating location media:", error);

    if (error instanceof z.ZodError) {
      return NextResponse.json({ error: "Validation error", details: error.errors }, { status: 400 });
    }

    return NextResponse.json({ error: "Failed to create location media" }, { status: 500 });
  }
}

export async function GET(request: NextRequest) {
  try {
    // Get authentication headers
    const stytchOrgId = request.headers.get('X-Stytch-Org-Id');
    const sessionToken = request.headers.get('X-Stytch-Session-Token');
    const sessionJwt = request.headers.get('X-Stytch-Session-JWT');

    // Authorization Check: RBAC check for viewing location media
    if (!stytchOrgId) {
      console.warn('RBAC check failed: Missing Stytch Org ID header');
      return NextResponse.json({ error: 'Forbidden - Missing Organization Context' }, { status: 403 });
    }
    
    // Create the auth params object for checkPermission
    const authParams = {
      session_token: sessionToken || null,
      session_jwt: sessionJwt || null
    };
    
    const hasPermission = await checkPermission(authParams, stytchOrgId, 'view', 'location');
    if (!hasPermission) {
      console.warn(`RBAC check failed: User in org ${stytchOrgId} does not have 'view' permission on 'location'`);
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    // Get organization by Stytch organization ID
    const organization = await findOrganizationByStytchId(stytchOrgId);
    
    if (!organization) {
      console.warn(`Organization not found for Stytch organization ID: ${stytchOrgId}`);
      return NextResponse.json({ error: "Organization not found" }, { status: 404 });
    }

    // Get the locationId from the query parameters
    const { searchParams } = new URL(request.url);
    const locationId = searchParams.get('locationId');

    if (!locationId) {
      return NextResponse.json({ error: "Location ID is required" }, { status: 400 });
    }

    // Query the database for media associated with the location
    const media = await db.query.locationMedia.findMany({
      where: (locationMedia, { eq, and }) => 
        and(
          eq(locationMedia.locationId, locationId),
          eq(locationMedia.isPublic, true)
        ),
      orderBy: (locationMedia, { asc }) => [asc(locationMedia.ordering)],
    });

    return NextResponse.json({ media });
  } catch (error) {
    console.error("Error fetching location media:", error);
    return NextResponse.json({ error: "Failed to fetch location media" }, { status: 500 });
  }
}
