import { NextResponse } from "next/server";
import type { NextRequest } from "next/server";
import { db } from "@/lib/db";
import { locations } from "@/modules/location/schema";
import { sql } from "drizzle-orm";
import { findOrganizationByStytchId } from "@/modules/organization/service";
import { checkPermission } from "@/lib/rbac/checkPermission";
import { cache } from "@/lib/cache";
import { safeDbQuery } from "@/lib/db/connection-utils";

export async function GET(request: NextRequest) {
  try {
    console.log("GET /api/locations/metadata - Starting request");
    
    const stytchOrgId = request.headers.get('X-Stytch-Org-Id');
    const sessionToken = request.headers.get('X-Stytch-Session-Token');
    const sessionJwt = request.headers.get('X-Stytch-Session-JWT');

    // Authorization Check
    if (!stytchOrgId) {
      console.warn('RBAC check failed: Missing Stytch Org ID header');
      return NextResponse.json({ error: 'Forbidden - Missing Organization Context' }, { status: 403 });
    }
    
    // Create the auth params object for checkPermission
    const authParams = {
      session_token: sessionToken || null,
      session_jwt: sessionJwt || null
    };
    
    // Check permission for viewing locations
    console.log("Checking RBAC permission for viewing locations metadata");
    const hasPermission = await checkPermission(authParams, stytchOrgId, 'view', 'location');
    if (!hasPermission) {
      console.warn(`RBAC check failed: User in org ${stytchOrgId} does not have 'view' permission on 'location'`);
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }
    
    // Get organization by Stytch organization ID
    console.log(`Finding organization by Stytch ID: ${stytchOrgId}`);
    const organization = await findOrganizationByStytchId(stytchOrgId);
    
    if (!organization) {
      console.warn(`Organization not found for Stytch organization ID: ${stytchOrgId}`);
      return NextResponse.json({ error: "Organization not found" }, { status: 404 });
    }
    
    // Use cache to avoid redundant database queries
    const cacheKey = `locations-metadata:${organization.id}`;
    
    const metadata = await cache.getOrSet(
      cacheKey,
      async () => {
        console.log(`Fetching location metadata for organization ID: ${organization.id}`);
        
        // Get distinct location types with retry logic
        const typesResult = await safeDbQuery(
          () => db
            .select({ type: locations.type })
            .from(locations)
            .where(sql`${locations.organizationId} = ${organization.id} AND ${locations.deletedAt} IS NULL`)
            .groupBy(locations.type),
          { timeout: 10000, maxRetries: 2 }
        );

        const types = typesResult.map(row => row.type);

        // Get distinct location statuses with retry logic
        const statusesResult = await safeDbQuery(
          () => db
            .select({ status: locations.status })
            .from(locations)
            .where(sql`${locations.organizationId} = ${organization.id} AND ${locations.deletedAt} IS NULL`)
            .groupBy(locations.status),
          { timeout: 10000, maxRetries: 2 }
        );

        const statuses = statusesResult.map(row => row.status);

        // Get total count of locations with retry logic
        const totalCountResult = await safeDbQuery(
          () => db
            .select({ value: sql<number>`count(*)` })
            .from(locations)
            .where(sql`${locations.organizationId} = ${organization.id} AND ${locations.deletedAt} IS NULL`),
          { timeout: 10000, maxRetries: 2 }
        );

        const totalCount = totalCountResult[0]?.value || 0;

        // Get count of locations by status with retry logic
        const countByStatusResult = await safeDbQuery(
          () => db
            .select({
              status: locations.status,
              count: sql<number>`count(*)`
            })
            .from(locations)
            .where(sql`${locations.organizationId} = ${organization.id} AND ${locations.deletedAt} IS NULL`)
            .groupBy(locations.status),
          { timeout: 10000, maxRetries: 2 }
        );

        const countByStatus = Object.fromEntries(
          countByStatusResult.map(row => [row.status, row.count])
        );

        // Get count of locations by type with retry logic
        const countByTypeResult = await safeDbQuery(
          () => db
            .select({
              type: locations.type,
              count: sql<number>`count(*)`
            })
            .from(locations)
            .where(sql`${locations.organizationId} = ${organization.id} AND ${locations.deletedAt} IS NULL`)
            .groupBy(locations.type),
          { timeout: 10000, maxRetries: 2 }
        );

        const countByType = Object.fromEntries(
          countByTypeResult.map(row => [row.type, row.count])
        );
        
        return {
          types,
          statuses,
          totalCount,
          countByStatus,
          countByType
        };
      },
      { ttl: 5 * 60 * 1000 } // Cache for 5 minutes
    );
    
    console.log(`Successfully fetched location metadata for organization ID: ${organization.id}`);
    
    // Return the metadata with caching headers
    return NextResponse.json(metadata, {
      headers: {
        'Cache-Control': 'public, s-maxage=300, stale-while-revalidate=600'
      }
    });
  } catch (error) {
    console.error("Error fetching location metadata:", error);
    return NextResponse.json({ error: "Failed to fetch location metadata" }, { status: 500 });
  }
}
