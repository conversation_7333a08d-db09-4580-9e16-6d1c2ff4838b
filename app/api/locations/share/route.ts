import type { NextRequest } from "next/server"
import { NextResponse } from "next/server"
import { checkPermission } from "@/lib/rbac/checkPermission"
import { getAuthParamsFromRequest } from "@/lib/utils/authUtils"
import { extractUuidFromStytchId } from "@/lib/utils/stytchUtils"
import { z } from "zod"
import { createSharedLocationLink, getSharedLocationLinksForOrganization, deleteSharedLocationLink } from "@/modules/location/service"

// Define schema for creating shared location links
const createSharedLocationLinkSchema = z.object({
  organizationId: z.string(),
  locationId: z.string().uuid(),
  viewMode: z.enum(["client", "admin"]).default("client"),
  expiresIn: z.number().optional(),
  password: z.string().optional(),
})

// POST /api/locations/share - Create a new shared location link
export async function POST(req: NextRequest) {
  try {
    // Authenticate the request
    const authParams = getAuthParamsFromRequest(req)
    const stytchOrgId = req.headers.get('X-Stytch-Org-Id')
    const memberId = req.headers.get('X-Stytch-Member-Id')

    if (!authParams || !stytchOrgId || !memberId) {
      console.warn('RBAC check failed: Missing Auth Params, Org ID, or Member ID header')
      return NextResponse.json({ error: 'Forbidden - Missing Authentication, Organization, or Member Context' }, { status: 403 })
    }

    // Check if the user has permission to share locations
    const hasPermission = await checkPermission(
      authParams,
      stytchOrgId,
      'share',
      'location'
    )

    if (!hasPermission) {
      return NextResponse.json({ error: "Forbidden" }, { status: 403 })
    }

    // Parse and validate request body
    const body = await req.json()
    const result = createSharedLocationLinkSchema.safeParse(body)
    
    if (!result.success) {
      return NextResponse.json(
        { error: "Invalid request body", details: result.error.format() },
        { status: 400 }
      )
    }
    
    // Extract UUID from Stytch organization ID
    const organizationId = extractUuidFromStytchId(result.data.organizationId)
    
    if (!organizationId) {
      return NextResponse.json({ error: "Invalid organization ID" }, { status: 400 })
    }
    
    // Extract UUID from Stytch member ID
    const userId = extractUuidFromStytchId(memberId)
    
    if (!userId) {
      return NextResponse.json({ error: "Invalid member ID" }, { status: 400 })
    }
    
    // Create shared location link
    const sharedLink = await createSharedLocationLink(
      organizationId,
      userId,
      {
        locationId: result.data.locationId,
        viewMode: result.data.viewMode,
        password: result.data.password,
        expiresIn: result.data.expiresIn,
      }
    )
    
    return NextResponse.json(sharedLink)
  } catch (error) {
    console.error("Error creating shared location link:", error)
    return NextResponse.json(
      { error: "Failed to create shared location link" },
      { status: 500 }
    )
  }
}

// GET /api/locations/share - Get all shared location links for an organization
export async function GET(req: NextRequest) {
  try {
    // Authenticate the request
    const authParams = getAuthParamsFromRequest(req)
    const stytchOrgId = req.headers.get('X-Stytch-Org-Id')

    if (!authParams || !stytchOrgId) {
      console.warn('RBAC check failed: Missing Auth Params or Stytch Org ID header')
      return NextResponse.json({ error: 'Forbidden - Missing Authentication or Organization Context' }, { status: 403 })
    }

    // Get the organization ID from the query parameters
    const { searchParams } = new URL(req.url)
    const organizationIdParam = searchParams.get("organizationId")

    if (!organizationIdParam) {
      return NextResponse.json({ error: "Organization ID is required" }, { status: 400 })
    }
    
    // Extract UUID from Stytch organization ID
    const organizationId = extractUuidFromStytchId(organizationIdParam)
    
    if (!organizationId) {
      return NextResponse.json({ error: "Invalid organization ID" }, { status: 400 })
    }

    // Check if the user has permission to view shared location links
    const hasPermission = await checkPermission(
      authParams,
      stytchOrgId,
      'view',
      'location'
    )

    if (!hasPermission) {
      return NextResponse.json({ error: "Forbidden" }, { status: 403 })
    }

    // Get shared location links
    const links = await getSharedLocationLinksForOrganization(organizationId)

    return NextResponse.json(links)
  } catch (error) {
    console.error("Error getting shared location links:", error)
    return NextResponse.json(
      { error: "Failed to get shared location links" },
      { status: 500 }
    )
  }
}

// DELETE /api/locations/share - Delete a shared location link
export async function DELETE(req: NextRequest) {
  try {
    // Authenticate the request
    const authParams = getAuthParamsFromRequest(req)
    const stytchOrgId = req.headers.get('X-Stytch-Org-Id')

    if (!authParams || !stytchOrgId) {
      console.warn('RBAC check failed: Missing Auth Params or Stytch Org ID header')
      return NextResponse.json({ error: 'Forbidden - Missing Authentication or Organization Context' }, { status: 403 })
    }

    // Check if the user has permission to delete shared location links
    const hasPermission = await checkPermission(
      authParams,
      stytchOrgId,
      'delete',
      'location'
    )

    if (!hasPermission) {
      return NextResponse.json({ error: "Forbidden" }, { status: 403 })
    }

    // Get link ID from URL
    const url = new URL(req.url)
    const id = url.searchParams.get("id")
    const organizationIdParam = url.searchParams.get("organizationId")
    
    if (!id) {
      return NextResponse.json(
        { error: "Missing link ID" },
        { status: 400 }
      )
    }
    
    if (!organizationIdParam) {
      return NextResponse.json({ error: "Organization ID is required" }, { status: 400 })
    }
    
    // Extract UUID from Stytch organization ID
    const organizationId = extractUuidFromStytchId(organizationIdParam)
    
    if (!organizationId) {
      return NextResponse.json({ error: "Invalid organization ID" }, { status: 400 })
    }
    
    // Delete shared location link
    const success = await deleteSharedLocationLink(id, organizationId)
    
    if (!success) {
      return NextResponse.json(
        { error: "Shared location link not found" },
        { status: 404 }
      )
    }
    
    return NextResponse.json({ success: true })
  } catch (error) {
    console.error("Error deleting shared location link:", error)
    return NextResponse.json(
      { error: "Failed to delete shared location link" },
      { status: 500 }
    )
  }
}
