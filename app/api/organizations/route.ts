import { NextResponse } from "next/server"
import type { NextRequest } from "next/server"
import { createStytchOrganization, getStytchOrganizations } from "@/modules/organization/stytch"

// GET /api/organizations - List organizations
export async function GET() {
  try {
    // Get organizations from Stytch
    let organizations: Array<{
      organization_id: string;
      organization_name: string;
      organization_slug: string;
    }> = []
    
    try {
      organizations = await getStytchOrganizations()
    } catch (error) {
      console.error("Error fetching organizations from Stytch:", error)
      // Fallback to mock data if Stytch API fails
      if (process.env.NODE_ENV === "development") {
        organizations = [
          { organization_id: "org-1", organization_name: "Acme Inc", organization_slug: "acme-inc" },
          { organization_id: "org-2", organization_name: "Globex Corp", organization_slug: "globex-corp" },
        ]
      }
    }
    
    // Transform the data to match our API format
    const data = organizations.map(org => ({
      id: org.organization_id,
      name: org.organization_name,
      slug: org.organization_slug,
    }))
    
    return NextResponse.json({
      success: true,
      data,
    })
  } catch (error) {
    console.error("Error listing organizations:", error)
    return NextResponse.json(
      { success: false, error: "An unexpected error occurred" },
      { status: 500 }
    )
  }
}

// POST /api/organizations - Create a new organization
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { name, slug } = body
    
    if (!name || !slug) {
      return NextResponse.json(
        { success: false, error: "Name and slug are required" },
        { status: 400 }
      )
    }
    
    // Create the organization in Stytch
    let organization: {
      organization_id: string;
      organization_name: string;
      organization_slug: string;
    };
    
    try {
      // Extract allowed domains from the request if provided
      const allowedDomains = body.allowedDomains as string[] | undefined;
      
      try {
        // Create the organization in Stytch
        organization = await createStytchOrganization(name, slug, allowedDomains);
      } catch (stytchError) {
        // Check if the error is because the slug is already used
        const errorObj = stytchError instanceof Error ? 
          JSON.parse(stytchError.message.includes('{') ? 
            stytchError.message.substring(stytchError.message.indexOf('{')) : 
            '{}') : {};
        
        if (errorObj.error_type === "organization_slug_already_used") {
          // Generate a unique slug by adding a random suffix
          const uniqueSlug = `${slug}-${Math.floor(Math.random() * 10000)}`;
          console.log(`Slug "${slug}" already exists, trying with "${uniqueSlug}" instead`);
          
          // Try again with the new slug
          organization = await createStytchOrganization(name, uniqueSlug, allowedDomains);
        } else {
          // Re-throw other errors
          throw stytchError;
        }
      }
    } catch (error) {
      console.error("Error creating organization in Stytch:", error);
      
      // Fallback to mock data in development
      if (process.env.NODE_ENV === "development") {
        organization = {
          organization_id: `org-${Math.floor(Math.random() * 1000)}`,
          organization_name: name,
          organization_slug: slug,
        };
      } else {
        throw error; // Re-throw in production
      }
    }
    
    // Transform the data to match our API format
    const data = {
      id: organization.organization_id,
      name: organization.organization_name,
      slug: organization.organization_slug,
    };
    
    return NextResponse.json({
      success: true,
      data,
    })
  } catch (error) {
    console.error("Error creating organization:", error)
    return NextResponse.json(
      { success: false, error: "An unexpected error occurred" },
      { status: 500 }
    )
  }
}
