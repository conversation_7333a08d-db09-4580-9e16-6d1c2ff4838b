import { NextResponse } from "next/server";
import type { NextRequest } from "next/server";
import { checkPermission } from "@/lib/rbac/checkPermission"; // Import RBAC check function
// These imports are commented out for now since we're using mock data
// import {
//   getStytchOrganizationMembers,
//   inviteUserToOrganization,
//   removeUserFromOrganization
// } from "@/modules/organization/stytch"

// GET /api/organizations/[organizationId]/members - Get organization members
export async function GET(
  request: NextRequest,
  { params }: { params: { organizationId: string } } // Changed id to organizationId
) {
  try {
    const { organizationId } = params;
    const stytchOrgId = request.headers.get('X-Stytch-Org-Id');
    const sessionToken = request.headers.get('X-Stytch-Session-Token');
    const sessionJwt = request.headers.get('X-Stytch-Session-JWT');
    const tokenForCheck = sessionToken || sessionJwt;

    // Authorization Check: RBAC check for reading members
    const hasPermission = await checkPermission(tokenForCheck, stytchOrgId, 'read', 'member');
    if (!hasPermission) {
      console.warn(`RBAC check failed: User in org ${stytchOrgId} does not have 'read' permission on 'member'`);
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    // TODO: Verify user should see members of the target organizationId if different from stytchOrgId

    // For now, we'll return mock data for development
    // Use the organizationId to determine which organization's members to return
    console.log(`Getting members for organization ${organizationId}`) // Changed id to organizationId

    if (process.env.NODE_ENV === "development") {
      return NextResponse.json({
        success: true,
        data: [
          { id: "user-1", email: "<EMAIL>", name: "User One", role: "admin" },
          { id: "user-2", email: "<EMAIL>", name: "User Two", role: "member" },
        ],
      })
    }

    // In production, you would call getStytchOrganizationMembers()
    // const members = await getStytchOrganizationMembers(organizationId) // Changed id to organizationId

    return NextResponse.json({
      success: true,
      data: [],
    })
  } catch (error) {
    console.error("Error getting organization members:", error)
    return NextResponse.json(
      { success: false, error: "An unexpected error occurred" },
      { status: 500 }
    )
  }
}

// POST /api/organizations/[organizationId]/members - Invite a user to the organization
export async function POST(
  request: NextRequest,
  { params }: { params: { organizationId: string } } // Changed id to organizationId
) {
  try {
    const { organizationId } = params;
    const stytchOrgId = request.headers.get('X-Stytch-Org-Id');
    const sessionToken = request.headers.get('X-Stytch-Session-Token');
    const sessionJwt = request.headers.get('X-Stytch-Session-JWT');
    const tokenForCheck = sessionToken || sessionJwt;
    const body = await request.json();
    const { email, role } = body;

    // Authorization Check: RBAC check for inviting (creating) members
    const hasPermission = await checkPermission(tokenForCheck, stytchOrgId, 'create', 'member');
     if (!hasPermission) {
      console.warn(`RBAC check failed: User in org ${stytchOrgId} does not have 'create' permission on 'member'`);
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    // TODO: Verify user should be inviting to the target organizationId if different from stytchOrgId

    // Log the organization ID for debugging
    console.log(`Inviting user to organization ${organizationId}`);

    if (!email) {
      return NextResponse.json(
        { success: false, error: "Email is required" },
        { status: 400 }
      );
    }

    // For now, we'll return mock data for development
    if (process.env.NODE_ENV === "development") {
      return NextResponse.json({
        success: true,
        data: {
          email,
          role: role || "member",
          status: "invited",
        },
      })
    }

    // In production, you would call inviteUserToOrganization()
    // const result = await inviteUserToOrganization(organizationId, email, role || "member") // Changed id to organizationId

    return NextResponse.json({
      success: true,
      data: {
        email,
        role: role || "member",
        status: "invited",
      },
    })
  } catch (error) {
    console.error("Error inviting user to organization:", error)
    return NextResponse.json(
      { success: false, error: "An unexpected error occurred" },
      { status: 500 }
    )
  }
}

// DELETE /api/organizations/[organizationId]/members/[userId] - Remove a user from the organization
export async function DELETE(
  request: NextRequest,
  { params }: { params: { organizationId: string; userId: string } } // Changed id to organizationId
) {
  try {
    const { organizationId, userId } = params;
    const stytchOrgId = request.headers.get('X-Stytch-Org-Id');
    const sessionToken = request.headers.get('X-Stytch-Session-Token');
    const sessionJwt = request.headers.get('X-Stytch-Session-JWT');
    const tokenForCheck = sessionToken || sessionJwt;

     // Authorization Check: RBAC check for removing (deleting) members
    const hasPermission = await checkPermission(tokenForCheck, stytchOrgId, 'delete', 'member');
     if (!hasPermission) {
      console.warn(`RBAC check failed: User in org ${stytchOrgId} does not have 'delete' permission on 'member'`);
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    // TODO: Verify user should be deleting from the target organizationId if different from stytchOrgId

    if (!userId) {
      return NextResponse.json(
        { success: false, error: "User ID is required" },
        { status: 400 }
      );
    }

    // For now, we'll return mock data for development
    if (process.env.NODE_ENV === "development") {
      return NextResponse.json({
        success: true,
        message: `User ${userId} removed from organization ${organizationId}`, // Changed id to organizationId
      })
    }

    // In production, you would call removeUserFromOrganization()
    // await removeUserFromOrganization(organizationId, userId) // Changed id to organizationId

    return NextResponse.json({
      success: true,
      message: `User ${userId} removed from organization ${organizationId}`, // Changed id to organizationId
    })
  } catch (error) {
    console.error("Error removing user from organization:", error)
    return NextResponse.json(
      { success: false, error: "An unexpected error occurred" },
      { status: 500 }
    )
  }
}
