import type { NextRequest } from 'next/server'; // Use type import
import { NextResponse } from 'next/server';
import { getStytchB2BClient } from '@/lib/stytch-b2b'; // Correct import
import { checkPermission } from '@/lib/rbac/checkPermission';
import { getAuthParamsFromRequest } from '@/lib/utils/authUtils';

// Define the expected request body structure using Zod or an interface
import { z } from 'zod';

const updateMemberSchema = z.object({
  roles: z.array(z.string()).min(1, "At least one role must be provided"),
});

export async function PATCH(
  request: NextRequest,
  { params }: { params: { organizationId: string; memberId: string } }
): Promise<NextResponse> { // Add return type annotation
  const { organizationId, memberId } = params;

  // 1. Extract authentication parameters
  const authParams = getAuthParamsFromRequest(request);
  if (!authParams) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }

  // 2. Check permission
  try {
    // Verify the correct action/resource based on your Stytch policy
    const canUpdateRoles = await checkPermission(
      authParams,
      organizationId,
      'update', // Or custom action like 'manage_members'
      `stytch.member.${memberId}` // Or `organization.${organizationId}` if using custom action
    );

    if (!canUpdateRoles) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }
  } catch (error) {
    console.error("Error during permission check:", error);
    return NextResponse.json({ error: 'Internal Server Error during permission check' }, { status: 500 });
  }


  // 3. Parse and validate request body
  let validatedData: z.infer<typeof updateMemberSchema>;
  try {
    const body = await request.json();
    validatedData = updateMemberSchema.parse(body);
  } catch (error: unknown) {
    if (error instanceof z.ZodError) {
      return NextResponse.json({ error: 'Invalid request body', details: error.errors }, { status: 400 });
    }
    console.error('Error parsing request body:', error);
    return NextResponse.json({ error: 'Bad Request' }, { status: 400 });
  }

  // 4. Call Stytch API
  try {
    const stytchClient = getStytchB2BClient();
    const response = await stytchClient.organizations.members.update({
      organization_id: organizationId,
      member_id: memberId,
      roles: validatedData.roles,
    });

    // Stytch SDK throws an error for non-2xx responses, so checking status_code might be redundant
    // if (response.status_code !== 200) { ... }

    // 5. Return success response
    return NextResponse.json(response.member, { status: 200 });

  } catch (error: unknown) {
    console.error('Error updating Stytch member:', error);
    // Consider more specific error handling based on StytchError if needed
    return NextResponse.json({ error: 'Internal Server Error updating member role' }, { status: 500 });
  }
} // End of PATCH function
