import { NextResponse } from "next/server"
import type { NextRequest } from "next/server"
import loadStytch from "@/lib/loadStytch"

// POST /api/organizations/[organizationId]/switch - Switch to a different organization
export async function POST(
  request: NextRequest,
  context: { params: Promise<{ organizationId: string }> }
) {
  try {
    const stytchClient = loadStytch()
    // Await the params promise and then destructure
    const { organizationId } = await context.params;

    // Log the organization ID for debugging
    console.log(`Switching to organization ${organizationId}`) // Changed id to organizationId

    // Get the session token from cookies
    const sessionToken = request.cookies.get("stytch_session")?.value ||
                         request.cookies.get("stytch_session_jwt")?.value

    if (!sessionToken) {
      return NextResponse.json(
        { success: false, error: "Not authenticated" },
        { status: 401 }
      )
    }

    try {
      // Verify the user's session and get member details
      const authResponse = await stytchClient.sessions.authenticate({
        session_token: sessionToken
      });
      const authenticatedMemberId = authResponse.member_session.member_id;

      console.log(`Session authenticated successfully for member ${authenticatedMemberId}`);

      // Explicitly check if the authenticated member belongs to the target organization
      try {
        await stytchClient.organizations.members.get({
          organization_id: organizationId, // Target organization ID from params
          member_id: authenticatedMemberId, // Authenticated member ID from session
        });
        console.log(`Membership verified: Member ${authenticatedMemberId} belongs to Org ${organizationId}`);
      } catch (membershipError: unknown) { // Use unknown instead of any
        // Check if the error is an object and has the expected properties before accessing them
        let isMembershipNotFoundError = false;
        if (typeof membershipError === 'object' && membershipError !== null) {
          const errorObj = membershipError as { status_code?: number; error_type?: string }; // Type assertion
          if (errorObj.status_code === 404 || errorObj.error_type === 'member_not_found') {
            isMembershipNotFoundError = true;
          }
        }

        if (isMembershipNotFoundError) {
           console.warn(`Membership check failed: Member ${authenticatedMemberId} not found in Org ${organizationId}`);
           return NextResponse.json({ success: false, error: "Forbidden - Not a member of target organization" }, { status: 403 });
        }
        // Re-throw other unexpected errors during membership check
        console.error("Unexpected error during membership check:", membershipError);
        throw membershipError;
      }

      // Fetch organization details (optional, could rely on membership check only)
      // If needed for response data:
      const orgResult = await stytchClient.organizations.get({
        organization_id: organizationId
      });


      // Create a response with the organization ID
      const response = NextResponse.json({
        success: true,
        message: `Switched to organization ${organizationId}`, // Changed id to organizationId
        organization: {
          id: orgResult.organization.organization_id,
          name: orgResult.organization.organization_name,
          slug: orgResult.organization.organization_slug,
        }
      })

      // Set the organization ID cookie
      response.cookies.set("current_organization", organizationId, { // Changed id to organizationId
        httpOnly: true,
        secure: process.env.NODE_ENV === "production",
        maxAge: 60 * 60 * 24 * 7, // 1 week
        path: "/",
      })

      return response
    } catch (verificationError) {
      console.error("Error verifying session or organization:", verificationError)
      return NextResponse.json(
        { success: false, error: "Failed to switch organization" },
        { status: 403 }
      )
    }
  } catch (error) {
    console.error("Error switching organization:", error)
    return NextResponse.json(
      { success: false, error: "An unexpected error occurred" },
      { status: 500 }
    )
  }
}
