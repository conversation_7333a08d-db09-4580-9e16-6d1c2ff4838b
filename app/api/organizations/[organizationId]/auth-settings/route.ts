import type { NextRequest } from "next/server";
import { NextResponse } from "next/server";
import { getStytchB2BClient } from "@/lib/stytch-b2b";
import { getOrganization } from "@/modules/organization/service";
import axios from "axios";
import { checkPermission } from "@/lib/rbac/checkPermission"; // Import RBAC check function


interface StytchOrganizationsClient {
  get: (params: {
    organization_id: string;
  }) => Promise<{
    organization: Record<string, unknown>;
  }>;
}

export async function PUT(
  request: NextRequest,
  { params }: { params: { organizationId: string } } // Changed id to organizationId
) {
  try {
    const organizationId = await params.organizationId; // DB Org ID
    const stytchOrgId = request.headers.get('X-Stytch-Org-Id'); // Authenticated Stytch Org ID
    const sessionToken = request.headers.get('X-Stytch-Session-Token');
    const sessionJwt = request.headers.get('X-Stytch-Session-JWT');
    const body = await request.json();
    const { email_jit_provisioning, email_invites } = body;

    // Authorization Check: RBAC check for updating auth settings
    const hasPermission = await checkPermission(
      { session_token: sessionToken, session_jwt: sessionJwt },
      stytchOrgId,
      'update',
      'organization.settings.auth'
    );
    if (!hasPermission) {
      console.warn(`RBAC check failed: User in org ${stytchOrgId} does not have 'update' permission on 'organization.settings.auth'`);
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    // TODO: Verify user should be updating settings for the target organizationId if different from stytchOrgId

    // Get the organization to verify it exists
    const organization = await getOrganization(organizationId);

    if (!organization.stytchOrganizationId) {
      return NextResponse.json(
        { success: false, error: "Organization does not have a Stytch ID" },
        { status: 400 }
      );
    }

    // We don't need the Stytch client for the PUT endpoint since we're using axios directly

    // Prepare the update parameters
    const updateParams: {
      organization_id: string;
      email_jit_provisioning?: string;
      email_invites?: string;
    } = {
      organization_id: organization.stytchOrganizationId
    };

    // Add email_jit_provisioning if provided
    if (email_jit_provisioning) {
      updateParams.email_jit_provisioning = email_jit_provisioning;
    }

    // Add email_invites if provided
    if (email_invites) {
      updateParams.email_invites = email_invites;
    }

    // Get the Stytch credentials
    const projectId = process.env.STYTCH_B2B_PROJECT_ID;
    const secret = process.env.STYTCH_B2B_SECRET;

    if (!projectId || !secret) {
      return NextResponse.json(
        { success: false, error: "Missing Stytch B2B credentials" },
        { status: 500 }
      );
    }

    // Determine the correct endpoint based on the project ID
    // If it starts with "project-live-", use the production endpoint
    // Otherwise, use the test endpoint
    const isLiveProject = projectId.startsWith('project-live-');
    const baseURL = isLiveProject
      ? 'https://api.stytch.com/v1/b2b'
      : 'https://test.stytch.com/v1/b2b';

    console.log(`Using ${isLiveProject ? 'production' : 'test'} endpoint: ${baseURL}`);

    // Create an axios instance for direct API calls
    const stytchAxios = axios.create({
      baseURL,
      auth: {
        username: projectId,
        password: secret
      },
      headers: {
        'Content-Type': 'application/json'
      }
    });

    // Update the organization settings in Stytch using axios
    const response = await stytchAxios.put(
      `/organizations/${organization.stytchOrganizationId}`,
      {
        // Since we can't add common domains to allowed_domains, disable JIT provisioning
        // If user tries to enable JIT provisioning, we'll set it to NOT_ALLOWED
        email_jit_provisioning: "NOT_ALLOWED",
        // email_invites can be ALL_ALLOWED directly - this is the key setting to allow common domains
        email_invites: updateParams.email_invites || "ALL_ALLOWED"
      }
    );

    return NextResponse.json({
      success: true,
      message: "Organization auth settings updated successfully",
      organization: response.data.organization,
    });
  } catch (error) {
    console.error("Error updating organization auth settings:", error);
    return NextResponse.json(
      {
        success: false,
        error: error instanceof Error ? error.message : String(error),
      },
      { status: 500 }
    );
  }
}

export async function GET(
  _request: NextRequest,
  { params }: { params: { organizationId: string } } // Changed id to organizationId
) {
  try {
    const organizationId = await params.organizationId; // DB Org ID
    const stytchOrgId = _request.headers.get('X-Stytch-Org-Id'); // Authenticated Stytch Org ID - Use _request
    const sessionToken = _request.headers.get('X-Stytch-Session-Token'); // Use _request
    const sessionJwt = _request.headers.get('X-Stytch-Session-JWT'); // Use _request

    // Authorization Check: RBAC check for reading auth settings
    const hasPermission = await checkPermission(
      { session_token: sessionToken, session_jwt: sessionJwt },
      stytchOrgId,
      'read',
      'organization.settings.auth'
    );
    if (!hasPermission) {
      console.warn(`RBAC check failed: User in org ${stytchOrgId} does not have 'read' permission on 'organization.settings.auth'`);
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

     // TODO: Verify user should be reading settings for the target organizationId if different from stytchOrgId

    // Get the organization to verify it exists
    const organization = await getOrganization(organizationId);

    if (!organization.stytchOrganizationId) {
      return NextResponse.json(
        { success: false, error: "Organization does not have a Stytch ID" },
        { status: 400 }
      );
    }

    // Get the Stytch B2B client
    const stytchClient = getStytchB2BClient();

    // Get the organization from Stytch
    // Double cast to bypass TypeScript error since the Stytch types might be outdated
    const organizationsClient = stytchClient.organizations as unknown as StytchOrganizationsClient;
    const response = await organizationsClient.get({
      organization_id: organization.stytchOrganizationId
    });

    return NextResponse.json({
      success: true,
      organization: response.organization,
    });
  } catch (error) {
    console.error("Error getting organization auth settings:", error);
    return NextResponse.json(
      {
        success: false,
        error: error instanceof Error ? error.message : String(error),
      },
      { status: 500 }
    );
  }
}
