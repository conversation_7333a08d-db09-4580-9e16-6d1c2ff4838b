import { NextResponse } from "next/server";
import type { NextRequest } from "next/server";
import { checkPermission } from "@/lib/rbac/checkPermission"; // Import RBAC check function
// We're dynamically importing organization service functions as needed

// GET /api/organizations/[organizationId] - Get a specific organization
export async function GET(
  request: NextRequest,
  { params }: { params: { organizationId: string } } // Changed id to organizationId
) {
  try {
    // Await params to ensure it's fully resolved
    const resolvedParams = await Promise.resolve(params);
    const { organizationId: targetOrganizationDbId } = resolvedParams; // Use a distinct name
    const stytchOrgId = request.headers.get('X-Stytch-Org-Id');
    const sessionToken = request.headers.get('X-Stytch-Session-Token');
    const sessionJwt = request.headers.get('X-Stytch-Session-JWT');
    // Create AuthParams object for checkPermission
    const authParams = {
      session_token: sessionToken,
      session_jwt: sessionJwt
    };

    // Authorization Check: RBAC check for reading organization details
    // Note: Stytch RBAC typically checks against the *authenticated* org (stytchOrgId),
    // not necessarily the target org in the URL unless the policy is structured that way.
    // Assuming the check is whether the user can read *any* org details within their current session context.
    // If the check needs to be more specific (e.g., can user in org A read details of org B?),
    // the policy or check logic might need adjustment.
    const hasPermission = await checkPermission(authParams, stytchOrgId, 'read', 'organization');
    if (!hasPermission) {
      console.warn(`RBAC check failed: User in org ${stytchOrgId} does not have 'read' permission on 'organization'`);
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    // TODO: Add logic to verify if the authenticated user (stytchOrgId) should have access
    // to the specific organization requested in the URL (targetOrganizationDbId).
    // This might involve checking membership or relationships if applicable.

    // Try to get the organization from the database
    try {
      // Import the necessary functions
      const { getOrganization, getOrganizationBySlug, findOrganizationByStytchId } = await import("@/modules/organization/service");
      const { getOrganizationByStytchId } = await import("@/modules/organization/stytch");
      
      // Define organization type to avoid implicit any
      // Using Record<string, unknown> as a safer alternative to 'any'
      let organization: Record<string, unknown> | null = null;
      
      // First try to get the organization by ID (if it's a UUID)
      const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i;
      if (uuidRegex.test(targetOrganizationDbId)) {
        try {
          organization = await getOrganization(targetOrganizationDbId);
        } catch (error) {
          // Suppress error and continue to next method
          console.log(`Organization not found by ID: ${error instanceof Error ? error.message : String(error)}`);
        }
      }
      
      // If not found by ID, try by slug
      if (!organization) {
        try {
          organization = await getOrganizationBySlug(targetOrganizationDbId);
        } catch (error) {
          // Suppress error and continue to next method
          console.log(`Organization not found by slug: ${error instanceof Error ? error.message : String(error)}`);
        }
      }
      
      // If not found by slug, try by Stytch organization ID
      if (!organization) {
        try {
          organization = await findOrganizationByStytchId(targetOrganizationDbId);
        } catch (error) {
          // Suppress error and continue to next method
          console.log(`Organization not found by Stytch ID: ${error instanceof Error ? error.message : String(error)}`);
        }
      }
      
      // If not found by any of the above, try to get the organization from Stytch directly
      if (!organization && targetOrganizationDbId.startsWith("org-")) {
        try {
          const stytchOrg = await getOrganizationByStytchId(targetOrganizationDbId);
          if (stytchOrg) {
            organization = stytchOrg;
          }
        } catch (error) {
          // Suppress error and continue
          console.log(`Organization not found in Stytch: ${error instanceof Error ? error.message : String(error)}`);
        }
      }
      
      // If organization is found, return it
      if (organization) {
        return NextResponse.json({
          success: true,
          data: organization,
        });
      }
      
      /* 
      // Mock data section commented out for production readiness
      // For development mode, provide mock data for specific IDs
      if (process.env.NODE_ENV === "development") {
        if (targetOrganizationDbId === "org-1") {
          return NextResponse.json({
            success: true,
            data: { id: "org-1", name: "Acme Inc", slug: "acme-inc" },
          });
        }
        
        if (targetOrganizationDbId === "org-2") {
          return NextResponse.json({
            success: true,
            data: { id: "org-2", name: "Globex Corp", slug: "globex-corp" },
          });
        }
        
        // Add mock data for wearefireflymedia-organization
        if (targetOrganizationDbId === "wearefireflymedia-organization") {
          return NextResponse.json({
            success: true,
            data: { 
              id: "wearefireflymedia-organization", 
              name: "WeAre FireflyMedia", 
              slug: "wearefireflymedia",
              stytchOrganizationId: "wearefireflymedia-organization"
            },
          });
        }
      }
      */
      
      // If organization not found
      return NextResponse.json(
        { success: false, error: "Organization not found" },
        { status: 404 }
      );
    } catch (error) {
      console.error("Error retrieving organization:", error);
      return NextResponse.json(
        { success: false, error: "An error occurred while retrieving the organization" },
        { status: 500 }
      );
    }
  } catch (error) {
    console.error("Error getting organization:", error)
    return NextResponse.json(
      { success: false, error: "An unexpected error occurred" },
      { status: 500 }
    )
  }
}

// PATCH /api/organizations/[organizationId] - Update a specific organization
export async function PATCH(
  request: NextRequest,
  { params }: { params: { organizationId: string } } // Changed id to organizationId
) {
  try {
    // Await params to ensure it's fully resolved
    const resolvedParams = await Promise.resolve(params);
    const { organizationId: targetOrganizationDbId } = resolvedParams; // Use a distinct name
    const stytchOrgId = request.headers.get('X-Stytch-Org-Id');
    const sessionToken = request.headers.get('X-Stytch-Session-Token');
    const sessionJwt = request.headers.get('X-Stytch-Session-JWT');
    // Create AuthParams object for checkPermission
    const authParams = {
      session_token: sessionToken,
      session_jwt: sessionJwt
    };
    const body = await request.json();
    const { name, slug } = body;

    // Authorization Check: RBAC check for updating organization details
    const hasPermission = await checkPermission(authParams, stytchOrgId, 'update', 'organization');
     if (!hasPermission) {
      console.warn(`RBAC check failed: User in org ${stytchOrgId} does not have 'update' permission on 'organization'`);
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    // TODO: Add logic to verify if the authenticated user (stytchOrgId) should have access
    // to update the specific organization requested in the URL (targetOrganizationDbId).

    /* 
    // Mock data section commented out for production readiness
    if (process.env.NODE_ENV === "development") {
      return NextResponse.json({
        success: true,
        data: { id: targetOrganizationDbId, name: name || "Updated Org", slug: slug || "updated-org" },
      })
    }
    */

    // Call updateOrganization for production use
    try {
      const { updateOrganization } = await import("@/modules/organization/service");
      const organization = await updateOrganization(targetOrganizationDbId, { name, slug });
      
      return NextResponse.json({
        success: true,
        data: organization,
      });
    } catch (error) {
      console.error("Error updating organization in database:", error);
      return NextResponse.json(
        { success: false, error: "Failed to update organization" },
        { status: 500 }
      );
    }
  } catch (error) {
    console.error("Error updating organization:", error)
    return NextResponse.json(
      { success: false, error: "An unexpected error occurred" },
      { status: 500 }
    )
  }
}

// DELETE /api/organizations/[organizationId] - Delete a specific organization
export async function DELETE(
  request: NextRequest,
  { params }: { params: { organizationId: string } } // Changed id to organizationId
) {
  try {
    // Await params to ensure it's fully resolved
    const resolvedParams = await Promise.resolve(params);
    const { organizationId: targetOrganizationDbId } = resolvedParams; // Use a distinct name
    const stytchOrgId = request.headers.get('X-Stytch-Org-Id');
    const sessionToken = request.headers.get('X-Stytch-Session-Token');
    const sessionJwt = request.headers.get('X-Stytch-Session-JWT');
    // Create AuthParams object for checkPermission
    const authParams = {
      session_token: sessionToken,
      session_jwt: sessionJwt
    };

    // Authorization Check: RBAC check for deleting organization
    const hasPermission = await checkPermission(authParams, stytchOrgId, 'delete', 'organization');
     if (!hasPermission) {
      console.warn(`RBAC check failed: User in org ${stytchOrgId} does not have 'delete' permission on 'organization'`);
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

     // TODO: Add logic to verify if the authenticated user (stytchOrgId) should have access
    // to delete the specific organization requested in the URL (targetOrganizationDbId).

    /* 
    // Mock data section commented out for production readiness
    if (process.env.NODE_ENV === "development") {
      return NextResponse.json({
        success: true,
        message: `Organization ${targetOrganizationDbId} deleted successfully`,
      })
    }
    */

    // Call deleteOrganization for production use
    try {
      const { deleteOrganization } = await import("@/modules/organization/service");
      await deleteOrganization(targetOrganizationDbId);
      
      return NextResponse.json({
        success: true,
        message: `Organization ${targetOrganizationDbId} deleted successfully`,
      });
    } catch (error) {
      console.error("Error deleting organization from database:", error);
      return NextResponse.json(
        { success: false, error: "Failed to delete organization" },
        { status: 500 }
      );
    }
  } catch (error) {
    console.error("Error deleting organization:", error)
    return NextResponse.json(
      { success: false, error: "An unexpected error occurred" },
      { status: 500 }
    )
  }
}
