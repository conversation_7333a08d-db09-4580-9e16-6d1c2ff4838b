import type { NextRequest } from "next/server";
import { NextResponse } from "next/server";
import { z } from "zod"; // Import Zod for validation
import { getProjects, createProject } from "@/modules/project/service"; // Import service functions
import { createProjectSchema } from "@/modules/project/model"; // Import Zod schema for validation
import { checkPermission } from "@/lib/rbac/checkPermission"; // Import RBAC check function

export async function GET(
  request: NextRequest,
  context: { params: Promise<{ organizationId: string }> }
) {
  // Await the params promise and then destructure
  const { organizationId } = await context.params;
  const memberId = request.headers.get('X-Stytch-Member-Id'); // Keep for logging if needed
  const stytchOrgId = request.headers.get('X-Stytch-Org-Id'); // Stytch Org ID from authenticated session
  const sessionToken = request.headers.get('X-Stytch-Session-Token');
  const sessionJwt = request.headers.get('X-Stytch-Session-JWT');
  // No longer need tokenForCheck, pass object directly

  console.log(`GET /api/organizations/${organizationId}/projects (DB ID)`);
  console.log('Member ID from header:', memberId);
  console.log('Stytch Org ID from header:', stytchOrgId);

  // Get organization details to check if it has a stytchOrganizationId
  try {
    // Import the getOrganization function at the top of the file
    const { getOrganization } = await import("@/modules/organization/service");
    const organization = await getOrganization(organizationId);
    
    // If the organization has a stytchOrganizationId, perform RBAC check
    if (organization.stytchOrganizationId) {
      // Authorization Check: RBAC check for viewing projects (using 'view' action)
      const hasPermission = await checkPermission(
        { session_token: sessionToken, session_jwt: sessionJwt }, // Pass AuthParams object
        stytchOrgId, 
        'view', 
        'project'
      );
      if (!hasPermission) {
        console.warn(`RBAC check failed: User ${memberId} does not have 'view' permission on 'project' in org ${stytchOrgId}`); // Updated log message
        return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
      }
    } else {
      // If the organization doesn't have a stytchOrganizationId, bypass RBAC check
      console.log(`Organization ${organizationId} doesn't have a stytchOrganizationId, bypassing RBAC check`);
    }
  } catch (error) {
    console.error(`Error getting organization ${organizationId}:`, error);
    return NextResponse.json({ error: 'Failed to verify organization' }, { status: 500 });
  }

  // Check if organizationId from URL is valid (basic check)
  if (!organizationId) {
      console.error("Organization ID missing from URL parameters.");
      return NextResponse.json({ error: 'Organization ID is required in URL' }, { status: 400 });
  }


  try {
    // Get search query from URL parameters
    const url = new URL(request.url);
    const searchQuery = url.searchParams.get("search") || undefined;

    // Fetch projects using the service function, passing the search query
    const projects = await getProjects(organizationId, searchQuery);
    return NextResponse.json(projects);
  } catch (error) {
    console.error("Error fetching projects:", error);
    return NextResponse.json({ error: 'Failed to fetch projects' }, { status: 500 });
  }
}

export async function POST(
  request: NextRequest,
  context: { params: Promise<{ organizationId: string }> }
) {
  // Await the params promise and then destructure
  const { organizationId } = await context.params;
  const memberId = request.headers.get('X-Stytch-Member-Id'); // Keep for logging if needed
  const stytchOrgId = request.headers.get('X-Stytch-Org-Id'); // Stytch Org ID from authenticated session
  const sessionToken = request.headers.get('X-Stytch-Session-Token');
  const sessionJwt = request.headers.get('X-Stytch-Session-JWT');
  // No longer need tokenForCheck, pass object directly

  console.log(`POST /api/organizations/${organizationId}/projects (DB ID)`);
  console.log('Member ID from header:', memberId);
  console.log('Stytch Org ID from header:', stytchOrgId);

  // Get organization details to check if it has a stytchOrganizationId
  try {
    // Import the getOrganization function at the top of the file
    const { getOrganization } = await import("@/modules/organization/service");
    const organization = await getOrganization(organizationId);
    
    // If the organization has a stytchOrganizationId, perform RBAC check
    if (organization.stytchOrganizationId) {
      // Authorization Check: RBAC check for creating projects
      const hasPermission = await checkPermission(
        { session_token: sessionToken, session_jwt: sessionJwt }, // Pass AuthParams object
        stytchOrgId, 
        'create', 
        'project'
      );
      if (!hasPermission) {
        console.warn(`RBAC check failed: User ${memberId} does not have 'create' permission on 'project' in org ${stytchOrgId}`);
        return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
      }
    } else {
      // If the organization doesn't have a stytchOrganizationId, bypass RBAC check
      console.log(`Organization ${organizationId} doesn't have a stytchOrganizationId, bypassing RBAC check`);
    }
  } catch (error) {
    console.error(`Error getting organization ${organizationId}:`, error);
    return NextResponse.json({ error: 'Failed to verify organization' }, { status: 500 });
  }

  // Check if organizationId from URL is valid (basic check)
   if (!organizationId) {
      console.error("Organization ID missing from URL parameters.");
      return NextResponse.json({ error: 'Organization ID is required in URL' }, { status: 400 });
  }

  try {
    const body = await request.json();

    // Validate body using the imported Zod schema
    const validationResult = createProjectSchema.safeParse(body);
    if (!validationResult.success) {
      return NextResponse.json(
        { error: "Invalid input", details: validationResult.error.errors },
        { status: 400 }
      );
    }
    const validatedData = validationResult.data;

    console.log("Validated Request Body:", validatedData);

    // Create project using the service function, including organizationId
    const newProject = await createProject({
      ...validatedData,
      organizationId: organizationId, // Add organizationId to the data object
    });

    return NextResponse.json(newProject, { status: 201 });
  } catch (error) {
    console.error("Error creating project:", error);
    if (error instanceof z.ZodError) {
      // Should be caught by safeParse, but good practice
      return NextResponse.json(
        { error: "Invalid input", details: error.errors },
        { status: 400 }
      );
    }
    // Handle other potential errors (e.g., database errors from the service)
    return NextResponse.json({ error: "Failed to create project" }, { status: 500 });
  }
}
