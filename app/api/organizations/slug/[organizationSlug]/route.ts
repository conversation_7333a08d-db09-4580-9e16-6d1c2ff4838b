import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';
import { getOrganizationBySlug } from '@/modules/organization/service';
import { NotFoundError } from '@/modules/shared/errors';
import loadStytch from '@/lib/loadStytch'; // Import Stytch client loader

// ... other imports (removed duplicate NextRequest import)

export async function GET(
  request: NextRequest,
  context: { params: Promise<{ organizationSlug: string }> }
) {
  // Await the params promise and then destructure
  const { organizationSlug } = await context.params;
  const authenticatedMemberId = request.headers.get('X-Stytch-Member-Id'); // Get authenticated member ID

  // Ensure member ID exists (basic auth check from middleware)
  if (!authenticatedMemberId) {
    console.warn("[API /org/slug] Unauthorized - Missing Member ID header.");
    return NextResponse.json({ error: 'Unauthorized - Missing Member ID' }, { status: 401 });
  }

  // Ensure slug exists
  if (!organizationSlug) {
    console.error("[API /org/slug] Organization slug is missing in params.");
    return NextResponse.json({ error: 'Organization slug is required' }, { status: 400 });
  }

  console.log(`[API /org/slug] Received request for slug: '${organizationSlug}' by member: '${authenticatedMemberId}'`);

  try {
    // 1. Fetch organization by slug
    console.log(`[API /org/slug] Attempting to call getOrganizationBySlug with slug: '${organizationSlug}'`);
    const organization = await getOrganizationBySlug(organizationSlug);
    console.log(`[API /org/slug] Found organization DB ID: ${organization.id}, Stytch ID: ${organization.stytchOrganizationId} for slug: ${organizationSlug}`);

    if (!organization.stytchOrganizationId) {
        console.error(`[API /org/slug] Organization ${organization.id} found by slug, but missing stytchOrganizationId.`);
        // Instead of returning a 500 error, return the organization without Stytch verification
        // This allows the application to continue working with organizations that don't have Stytch integration
        console.log(`[API /org/slug] Returning organization without Stytch verification`);
        
        // Return relevant organization details without requiring Stytch verification
        const responseData = {
            id: organization.id,
            name: organization.name,
            slug: organization.slug,
            stytchOrganizationId: null // Explicitly set to null
        };
        
        return NextResponse.json(responseData);
    }

    // 2. Verify authenticated user is a member of this organization
    const stytchClient = loadStytch();
    try {
        await stytchClient.organizations.members.get({
          organization_id: organization.stytchOrganizationId, // Target organization's Stytch ID
          member_id: authenticatedMemberId, // Authenticated member ID from header
        });
        console.log(`[API /org/slug] Membership verified: Member ${authenticatedMemberId} belongs to Org ${organization.stytchOrganizationId}`);
    } catch (membershipError: unknown) {
        // Check if the error is an object and has the expected properties before accessing them
        let isMembershipNotFoundError = false;
        if (typeof membershipError === 'object' && membershipError !== null) {
          const errorObj = membershipError as { status_code?: number; error_type?: string }; // Type assertion
          if (errorObj.status_code === 404 || errorObj.error_type === 'member_not_found') {
            isMembershipNotFoundError = true;
          }
        }
        if (isMembershipNotFoundError) {
           console.warn(`[API /org/slug] Membership check failed: Member ${authenticatedMemberId} not found in Org ${organization.stytchOrganizationId}`);
           // Return 404 for the slug itself, as the user shouldn't know it exists if they aren't a member
           return NextResponse.json({ error: `Organization with slug '${organizationSlug}' not found or access denied.` }, { status: 404 });
        }
        // Re-throw other unexpected errors during membership check
        console.error("[API /org/slug] Unexpected error during Stytch membership check:", membershipError);
        throw membershipError; // Let the outer catch handle it
    }

    // 3. Return relevant organization details if membership is verified
    const responseData = {
        id: organization.id, // This is the internal DB UUID
        name: organization.name,
        slug: organization.slug,
        stytchOrganizationId: organization.stytchOrganizationId // Include Stytch ID if needed elsewhere
    };

    return NextResponse.json(responseData);

  } catch (error) {
    console.error(`[API /org/slug] Error fetching organization by slug '${organizationSlug}':`, error);
    if (error instanceof NotFoundError) {
      console.warn(`[API /org/slug] getOrganizationBySlug threw NotFoundError for slug: '${organizationSlug}'. Returning 404.`);
      return NextResponse.json({ error: error.message }, { status: 404 });
    }
    // Default case for other errors within the catch block
    console.error(`[API /org/slug] Unexpected error for slug '${organizationSlug}'. Returning 500.`);
    return NextResponse.json({ error: 'Failed to fetch organization' }, { status: 500 });
  }
  // No code should be here, the function should have returned within try or catch
} // This is line 60 (approx)
