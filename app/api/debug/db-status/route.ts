import { NextResponse } from "next/server";
import type { NextRequest } from "next/server"; // Import NextRequest
import { db } from "@/lib/db";
import { sql, count } from "drizzle-orm"; // Import count
import { checkPermission } from "@/lib/rbac/checkPermission"; // Import RBAC check function
import { organizations, users, projects, locations } from "@/lib/db/schema"; // Import table schemas

export async function GET(request: NextRequest) {
  try {
    const stytchOrgId = request.headers.get('X-Stytch-Org-Id');
    const sessionToken = request.headers.get('X-Stytch-Session-Token');
    const sessionJwt = request.headers.get('X-Stytch-Session-JWT');
    const tokenForCheck = sessionToken || sessionJwt;

    // Authorization Check: RBAC check for accessing debug info (e.g., requires 'admin' role)
    if (!stytchOrgId) {
       console.warn('RBAC check failed: Missing Stytch Org ID header for debug endpoint');
       return NextResponse.json({ error: 'Forbidden - Missing Organization Context' }, { status: 403 });
    }
    // Assuming a resource like 'debug.db_status' and action 'read' is defined in Stytch policy for admins
    const hasPermission = await checkPermission(tokenForCheck, stytchOrgId, 'read', 'debug.db_status');
    if (!hasPermission) {
      console.warn(`RBAC check failed: User in org ${stytchOrgId} does not have 'read' permission on 'debug.db_status'`);
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    // Test the connection by querying the database version
    const result = await db.execute(sql`SELECT version();`);

    // Count the number of records in each table correctly
    const orgCountResult = await db.select({ count: count() }).from(organizations);
    const userCountResult = await db.select({ count: count() }).from(users);
    const projectCountResult = await db.select({ count: count() }).from(projects);
    const locationCountResult = await db.select({ count: count() }).from(locations);

    // Extract the count value (result is an array with one object)
    const orgCount = orgCountResult[0]?.count ?? 0;
    const userCount = userCountResult[0]?.count ?? 0;
    const projectCount = projectCountResult[0]?.count ?? 0;
    const locationCount = locationCountResult[0]?.count ?? 0;


    return NextResponse.json({
      success: true,
      version: result[0]?.version ?? 'N/A', // Safely access version
      counts: {
        organizations: orgCount,
        users: userCount,
        projects: projectCount,
        locations: locationCount,
      },
      timestamp: new Date().toISOString(),
    })
  } catch (error) {
    console.error("Database connection test failed:", error)

    return NextResponse.json(
      {
        success: false,
        error: error instanceof Error ? error.message : "Unknown error",
        timestamp: new Date().toISOString(),
      },
      { status: 500 },
    )
  }
}
