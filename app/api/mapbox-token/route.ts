import { NextResponse } from "next/server";
import type { NextRequest } from "next/server"; // Import NextRequest

export async function GET(request: NextRequest) { // Change type to NextRequest
  // Basic Authentication Check: Ensure a valid session exists (indicated by middleware header)
  const memberId = request.headers.get('X-Stytch-Member-Id');
  if (!memberId) {
    console.warn('Mapbox token request denied: Missing Member ID header (unauthenticated)');
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }

  // Only return the token if it's properly prefixed for client usage
  const token = process.env.NEXT_PUBLIC_MAPBOX_ACCESS_TOKEN || "";

  if (!token) {
     console.error('Mapbox token is missing from environment variables (NEXT_PUBLIC_MAPBOX_ACCESS_TOKEN)');
     // Avoid returning an empty token, return an error instead
      return NextResponse.json({ error: 'Mapbox token configuration error' }, { status: 500 });
   }

   // Add Cache-Control header to the response
   return NextResponse.json({ token }, {
     headers: {
       'Cache-Control': 'public, max-age=86400, s-maxage=86400', // Cache for 24 hours
     },
   });
}
