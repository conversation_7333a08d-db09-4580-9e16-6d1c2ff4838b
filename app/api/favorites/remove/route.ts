import type { NextRequest } from "next/server"
import { NextResponse } from "next/server"
import { removeFavoriteLocationSchema } from "@/modules/favorites/schema"
import { removeLocationFromFavoriteList } from "@/modules/favorites/service"
import { checkPermission } from "@/lib/rbac/checkPermission"
import { getAuthParamsFromRequest } from "@/lib/utils/authUtils"
import { extractUuidFromStytchId } from "@/lib/utils/stytchUtils"

// POST /api/favorites/remove - Remove a location from a favorite list
export async function POST(req: NextRequest) {
  try {
    // Authenticate the request
    const authParams = getAuthParamsFromRequest(req)
    const stytchOrgId = req.headers.get('X-Stytch-Org-Id')
    const memberId = req.headers.get('X-Stytch-Member-Id')

    if (!authParams || !stytchOrgId || !memberId) {
      console.warn('RBAC check failed: Missing Auth Params, Org ID, or Member ID header')
      return NextResponse.json({ error: 'Forbidden - Missing Authentication, Organization, or Member Context' }, { status: 403 })
    }

    // Parse and validate the request body
    const body = await req.json()
    const validatedData = removeFavoriteLocationSchema.safeParse(body)

    if (!validatedData.success) {
      return NextResponse.json(
        { error: "Invalid request data", details: validatedData.error.format() },
        { status: 400 }
      )
    }

    // Extract data from validated input
    const { listId } = validatedData.data
    
    // Extract UUID from Stytch organization ID
    const organizationId = extractUuidFromStytchId(validatedData.data.organizationId)
    
    if (!organizationId) {
      return NextResponse.json({ error: "Invalid organization ID" }, { status: 400 })
    }
    
    // Extract UUID from Stytch location ID if it's in Stytch format
    const locationId = extractUuidFromStytchId(validatedData.data.locationId) || validatedData.data.locationId

    // Check if the user has permission to edit favorites
    const hasPermission = await checkPermission(
      authParams,
      stytchOrgId,
      'edit',
      'map'  // Use 'map' resource ID instead of 'map.favorites'
    )

    if (!hasPermission) {
      return NextResponse.json({ error: "Forbidden" }, { status: 403 })
    }

    // Remove the location from the favorite list
    const success = await removeLocationFromFavoriteList({
      favoriteListId: listId,
      locationId,
      organizationId,
    })

    return NextResponse.json({ success })
  } catch (error) {
    console.error("Error removing location from favorite list:", error)
    return NextResponse.json(
      { error: error instanceof Error ? error.message : "Failed to remove location from favorite list" },
      { status: 500 }
    )
  }
}
