import type { NextRequest } from "next/server"
import { NextResponse } from "next/server"
// Remove unused zod import
import { 
  createFavoriteListSchema, 
  updateFavoriteListSchema, 
  deleteFavoriteListSchema 
} from "@/modules/favorites/schema"
import { 
  getFavoriteLists, 
  createFavoriteList, 
  updateFavoriteList, 
  deleteFavoriteList 
} from "@/modules/favorites/service"
import { checkPermission } from "@/lib/rbac/checkPermission"
import { getAuthParamsFromRequest } from "@/lib/utils/authUtils"
import { extractUuidFromStytchId } from "@/lib/utils/stytchUtils"

// GET /api/favorites - Get all favorites for an organization
export async function GET(req: NextRequest) {
  try {
    // Authenticate the request
    const authParams = getAuthParamsFromRequest(req)
    const stytchOrgId = req.headers.get('X-Stytch-Org-Id')

    if (!authParams || !stytchOrgId) {
      console.warn('RBAC check failed: Missing Auth Params or Stytch Org ID header')
      return NextResponse.json({ error: 'Forbidden - Missing Authentication or Organization Context' }, { status: 403 })
    }

    // Get the organization ID from the query parameters
    const { searchParams } = new URL(req.url)
    const organizationIdParam = searchParams.get("organizationId")

    if (!organizationIdParam) {
      return NextResponse.json({ error: "Organization ID is required" }, { status: 400 })
    }
    
    // Extract UUID from Stytch organization ID
    const organizationId = extractUuidFromStytchId(organizationIdParam)
    
    if (!organizationId) {
      return NextResponse.json({ error: "Invalid organization ID" }, { status: 400 })
    }

    // Check if the user has permission to view favorites
    const hasPermission = await checkPermission(
      authParams,
      stytchOrgId,
      'view', // Use 'view' action for the 'map' resource
      'map'   // Use 'map' resource ID
    )

    if (!hasPermission) {
      return NextResponse.json({ error: "Forbidden" }, { status: 403 })
    }

    // Get all favorites for the organization
    const favorites = await getFavoriteLists(organizationId)

    return NextResponse.json(favorites)
  } catch (error) {
    console.error("Error getting favorites:", error)
    return NextResponse.json(
      { error: error instanceof Error ? error.message : "Failed to get favorites" },
      { status: 500 }
    )
  }
}

// POST /api/favorites - Create a new favorite list
export async function POST(req: NextRequest) {
  try {
    // Authenticate the request
    const authParams = getAuthParamsFromRequest(req)
    const stytchOrgId = req.headers.get('X-Stytch-Org-Id')
    const memberId = req.headers.get('X-Stytch-Member-Id')

    if (!authParams || !stytchOrgId || !memberId) {
      console.warn('RBAC check failed: Missing Auth Params, Org ID, or Member ID header')
      return NextResponse.json({ error: 'Forbidden - Missing Authentication, Organization, or Member Context' }, { status: 403 })
    }

    // Parse and validate the request body
    const body = await req.json()
    const validatedData = createFavoriteListSchema.safeParse(body)

    if (!validatedData.success) {
      return NextResponse.json(
        { error: "Invalid request data", details: validatedData.error.format() },
        { status: 400 }
      )
    }

    // Extract data from validated input
    const { name, type, projectId, sceneId } = validatedData.data
    
    // Extract UUID from Stytch organization ID
    const organizationId = extractUuidFromStytchId(validatedData.data.organizationId)
    
    if (!organizationId) {
      return NextResponse.json({ error: "Invalid organization ID" }, { status: 400 })
    }
    
    // Extract UUID from Stytch member ID
    const createdById = extractUuidFromStytchId(memberId)
    
    if (!createdById) {
      return NextResponse.json({ error: "Invalid member ID" }, { status: 400 })
    }

    // Check if the user has permission to create favorites
    const hasPermission = await checkPermission(
      authParams,
      stytchOrgId,
      'edit', // Use 'edit' action for the 'map' resource
      'map'   // Use 'map' resource ID
    )

    if (!hasPermission) {
      return NextResponse.json({ error: "Forbidden" }, { status: 403 })
    }

    // Create the favorite list
    const favoriteList = await createFavoriteList({
      organizationId,
      name,
      type,
      projectId,
      sceneId,
      createdById,
    })

    return NextResponse.json(favoriteList)
  } catch (error) {
    console.error("Error creating favorite list:", error)
    return NextResponse.json(
      { error: error instanceof Error ? error.message : "Failed to create favorite list" },
      { status: 500 }
    )
  }
}

// PUT /api/favorites - Update a favorite list
export async function PUT(req: NextRequest) {
  try {
    // Authenticate the request
    const authParams = getAuthParamsFromRequest(req)
    const stytchOrgId = req.headers.get('X-Stytch-Org-Id')
    const memberId = req.headers.get('X-Stytch-Member-Id')

    if (!authParams || !stytchOrgId || !memberId) {
      console.warn('RBAC check failed: Missing Auth Params, Org ID, or Member ID header')
      return NextResponse.json({ error: 'Forbidden - Missing Authentication, Organization, or Member Context' }, { status: 403 })
    }

    // Parse and validate the request body
    const body = await req.json()
    const validatedData = updateFavoriteListSchema.safeParse(body)

    if (!validatedData.success) {
      return NextResponse.json(
        { error: "Invalid request data", details: validatedData.error.format() },
        { status: 400 }
      )
    }

    // Extract data from validated input
    const { listId, name } = validatedData.data
    
    // Extract UUID from Stytch organization ID
    const organizationId = extractUuidFromStytchId(validatedData.data.organizationId)
    
    if (!organizationId) {
      return NextResponse.json({ error: "Invalid organization ID" }, { status: 400 })
    }

    // Check if the user has permission to update favorites
    const hasPermission = await checkPermission(
      authParams,
      stytchOrgId,
      'edit', // Use 'edit' action for the 'map' resource
      'map'   // Use 'map' resource ID
    )

    if (!hasPermission) {
      return NextResponse.json({ error: "Forbidden" }, { status: 403 })
    }

    // Update the favorite list
    const updatedList = await updateFavoriteList({
      listId,
      name,
      organizationId,
    })

    if (!updatedList) {
      return NextResponse.json({ error: "Favorite list not found" }, { status: 404 })
    }

    return NextResponse.json(updatedList)
  } catch (error) {
    console.error("Error updating favorite list:", error)
    return NextResponse.json(
      { error: error instanceof Error ? error.message : "Failed to update favorite list" },
      { status: 500 }
    )
  }
}

// DELETE /api/favorites - Delete a favorite list
export async function DELETE(req: NextRequest) {
  try {
    // Authenticate the request
    const authParams = getAuthParamsFromRequest(req)
    const stytchOrgId = req.headers.get('X-Stytch-Org-Id')
    const memberId = req.headers.get('X-Stytch-Member-Id')

    if (!authParams || !stytchOrgId || !memberId) {
      console.warn('RBAC check failed: Missing Auth Params, Org ID, or Member ID header')
      return NextResponse.json({ error: 'Forbidden - Missing Authentication, Organization, or Member Context' }, { status: 403 })
    }

    // Parse and validate the request body
    const body = await req.json()
    const validatedData = deleteFavoriteListSchema.safeParse(body)

    if (!validatedData.success) {
      return NextResponse.json(
        { error: "Invalid request data", details: validatedData.error.format() },
        { status: 400 }
      )
    }

    // Extract data from validated input
    const { listId } = validatedData.data
    
    // Extract UUID from Stytch organization ID
    const organizationId = extractUuidFromStytchId(validatedData.data.organizationId)
    
    if (!organizationId) {
      return NextResponse.json({ error: "Invalid organization ID" }, { status: 400 })
    }

    // Check if the user has permission to delete favorites
    const hasPermission = await checkPermission(
      authParams,
      stytchOrgId,
      'delete', // Use 'delete' action for the 'map' resource
      'map'   // Use 'map' resource ID
    )

    if (!hasPermission) {
      return NextResponse.json({ error: "Forbidden" }, { status: 403 })
    }

    // Delete the favorite list
    await deleteFavoriteList({
      listId,
      organizationId,
    })

    return NextResponse.json({ success: true })
  } catch (error) {
    console.error("Error deleting favorite list:", error)
    return NextResponse.json(
      { error: error instanceof Error ? error.message : "Failed to delete favorite list" },
      { status: 500 }
    )
  }
}
