import type { NextRequest } from "next/server"
import { NextResponse } from "next/server"
import { addLocationToFavoriteListSchema } from "@/modules/favorites/schema"
import { addLocationToFavoriteList } from "@/modules/favorites/service"
import { checkPermission } from "@/lib/rbac/checkPermission"
import { getAuthParamsFromRequest } from "@/lib/utils/authUtils"
import { extractUuidFromStytchId } from "@/lib/utils/stytchUtils"

// POST /api/favorites/add - Add a location to a favorite list
export async function POST(req: NextRequest) {
  try {
    // Authenticate the request
    const authParams = getAuthParamsFromRequest(req)
    const stytchOrgId = req.headers.get('X-Stytch-Org-Id')
    const memberId = req.headers.get('X-Stytch-Member-Id')

    if (!authParams || !stytchOrgId || !memberId) {
      console.warn('RBAC check failed: Missing Auth Params, Org ID, or Member ID header')
      return NextResponse.json({ error: 'Forbidden - Missing Authentication, Organization, or Member Context' }, { status: 403 })
    }

    // Parse and validate the request body
    const body = await req.json()
    const validatedData = addLocationToFavoriteListSchema.safeParse(body)

    if (!validatedData.success) {
      return NextResponse.json(
        { error: "Invalid request data", details: validatedData.error.format() },
        { status: 400 }
      )
    }

    // Extract data from validated input
    const { listId } = validatedData.data
    
    // Extract UUID from Stytch organization ID
    const organizationId = extractUuidFromStytchId(validatedData.data.organizationId)
    
    if (!organizationId) {
      return NextResponse.json({ error: "Invalid organization ID" }, { status: 400 })
    }
    
    // Extract UUID from Stytch location ID if it's in Stytch format
    const locationId = extractUuidFromStytchId(validatedData.data.locationId) || validatedData.data.locationId
    
    // Extract UUID from Stytch member ID
    const addedById = extractUuidFromStytchId(memberId)
    
    if (!addedById) {
      return NextResponse.json({ error: "Invalid member ID" }, { status: 400 })
    }

    // Check if the user has permission to edit favorites
    const hasPermission = await checkPermission(
      authParams,
      stytchOrgId,
      'edit',
      'map'  // Use 'map' resource ID instead of 'map.favorites'
    )

    if (!hasPermission) {
      return NextResponse.json({ error: "Forbidden" }, { status: 403 })
    }

    // Add the location to the favorite list
    const success = await addLocationToFavoriteList({
      favoriteListId: listId,
      locationId,
      addedById,
      organizationId,
    })

    return NextResponse.json({ success })
  } catch (error) {
    console.error("Error adding location to favorite list:", error)
    return NextResponse.json(
      { error: error instanceof Error ? error.message : "Failed to add location to favorite list" },
      { status: 500 }
    )
  }
}
