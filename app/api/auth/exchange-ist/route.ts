import type { NextRequest } from "next/server"
import { NextResponse } from "next/server"
import loadStytch from "@/lib/loadStytch"

export async function POST(request: NextRequest) {
  try {
    const stytchClient = loadStytch()
    const body = await request.json()
    const { intermediate_session_token, organization_id } = body

    if (!intermediate_session_token) {
      return NextResponse.json(
        { success: false, error: "Intermediate session token is required" },
        { status: 400 }
      )
    }

    if (!organization_id) {
      return NextResponse.json(
        { success: false, error: "Organization ID is required" },
        { status: 400 }
      )
    }

    console.log(`Exchanging intermediate session token for organization: ${organization_id}`)

    try {
      // Exchange the intermediate session token for a session
      const exchangeResp = await stytchClient.discovery.intermediateSessions.exchange({
        intermediate_session_token,
        organization_id
      })
      
      console.log("Successfully exchanged intermediate session token")
      
      // Create response with session cookies
      const response = NextResponse.json({
        success: true,
        message: "Authentication successful",
        user: exchangeResp.member,
        organizationId: exchangeResp.organization?.organization_id,
      })
      
      // Set session token cookie
      if (exchangeResp.session_token) {
        response.cookies.set({
          name: "stytch_session",
          value: exchangeResp.session_token,
          path: "/",
          httpOnly: true,
          secure: process.env.NODE_ENV === "production",
          sameSite: "lax",
          maxAge: 60 * 60 * 24 * 7, // 1 week
        })
      }
      
      // Set session JWT cookie
      if (exchangeResp.session_jwt) {
        response.cookies.set({
          name: "stytch_session_jwt",
          value: exchangeResp.session_jwt,
          path: "/",
          httpOnly: true,
          secure: process.env.NODE_ENV === "production",
          sameSite: "lax",
          maxAge: 60 * 60 * 24 * 7, // 1 week
        })
      }
      
      // Set organization ID cookie
      response.cookies.set({
        name: "current_organization",
        value: organization_id,
        path: "/",
        httpOnly: true,
        secure: process.env.NODE_ENV === "production",
        sameSite: "lax",
        maxAge: 60 * 60 * 24 * 7, // 1 week
      })
      
      return response
    } catch (error) {
      console.error("Error exchanging intermediate session token:", error)
      return NextResponse.json(
        { success: false, error: "Failed to exchange intermediate session token" },
        { status: 400 }
      )
    }
  } catch (error) {
    console.error("Error in exchange-ist API:", error)
    return NextResponse.json(
      { success: false, error: "An unexpected error occurred" },
      { status: 500 }
    )
  }
}
