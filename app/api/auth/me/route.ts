import { NextResponse } from "next/server";
import type { NextRequest } from "next/server";
// No need to load Stytch client here if relying on middleware headers

export async function GET(request: NextRequest) {
  try {
    // Get user details from headers set by middleware
    const memberId = request.headers.get('X-Stytch-Member-Id');
    const organizationId = request.headers.get('X-Stytch-Org-Id');
    const rolesString = request.headers.get('X-Stytch-Roles');

    // Middleware should ensure these headers exist for authenticated routes
    if (!memberId || !organizationId) {
      console.error("Auth 'me' endpoint reached without required headers (memberId or organizationId). This indicates a middleware issue.");
      return NextResponse.json(
        { success: false, error: "Authentication context missing" },
        { status: 500 } // Internal server error because middleware should prevent this
      );
    }

    // Parse roles (optional, but potentially useful for client)
    let roles: string[] = [];
    if (rolesString) {
      try {
        roles = JSON.parse(rolesString);
      } catch (parseError) {
        console.error("Failed to parse roles header:", parseError);
        // Proceed without roles if parsing fails, but log it
      }
    }

    // TODO: Fetch additional user details (like name, email) if needed.
    // This might require a database lookup using memberId or a Stytch API call
    // if the headers don't contain everything. For now, return what we have.

    // Return the user information from headers
    return NextResponse.json({
      id: memberId,
      // email: ???, // Need to fetch if required
      // name: ???, // Need to fetch if required
      organizationId: organizationId,
      roles: roles, // Return parsed roles
    });

  } catch (error) {
    console.error("Auth error:", error)
    return NextResponse.json(
      { success: false, error: "An unexpected error occurred" },
      { status: 500 }
    )
  }
}
