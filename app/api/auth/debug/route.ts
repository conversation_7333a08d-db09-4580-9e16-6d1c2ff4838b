import type { NextRequest } from "next/server";
import { NextResponse } from "next/server";
import { B2BClient } from "stytch";

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { email } = body;

    if (!email) {
      return NextResponse.json(
        { success: false, error: "Email is required" },
        { status: 400 }
      );
    }

    console.log("Debug API route: Creating Stytch B2B client directly");
    
    // Create a new Stytch B2B client directly
    const projectId = process.env.STYTCH_B2B_PROJECT_ID;
    const secret = process.env.STYTCH_B2B_SECRET;
    
    console.log(`Project ID: ${projectId ? "Set" : "Not set"}`);
    console.log(`Secret: ${secret ? "Set" : "Not set"}`);
    
    if (!projectId || !secret) {
      console.error("Missing Stytch B2B credentials");
      return NextResponse.json(
        { success: false, error: "Missing Stytch B2B credentials" },
        { status: 500 }
      );
    }
    
    const stytchClient = new B2BClient({
      project_id: projectId,
      secret,
    });
    
    console.log("Debug API route: Stytch B2B client created successfully");
    
    // Get the redirect URL
    const redirectUrl = `${process.env.NEXT_PUBLIC_APP_URL}/authenticate`;
    console.log(`Debug API route: Redirect URL: ${redirectUrl}`);
    
    // Send a magic link using the discovery flow
    console.log(`Debug API route: Sending magic link to ${email}`);
    const response = await stytchClient.magicLinks.email.discovery.send({
      email_address: email,
      discovery_redirect_url: redirectUrl,
    });
    
    console.log("Debug API route: Magic link sent successfully");
    console.log("Debug API route: Response:", response);
    
    return NextResponse.json({
      success: true,
      message: "Magic link sent successfully",
      response,
    });
  } catch (error) {
    console.error("Debug API route error:", error);
    return NextResponse.json(
      { 
        success: false, 
        error: error instanceof Error ? error.message : String(error),
        stack: error instanceof Error ? error.stack : undefined,
      },
      { status: 500 }
    );
  }
}
