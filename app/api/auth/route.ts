import type { NextRequest } from "next/server"
import { NextResponse } from "next/server"
import { login } from "@/modules/auth/actions"

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { email, organizationId } = body

    if (!email) {
      return NextResponse.json(
        { success: false, error: "Email is required" },
        { status: 400 }
      )
    }

    // Create a FormData object to pass to the login action
    const formData = new FormData()
    formData.append("email", email)
    if (organizationId) {
      formData.append("organizationId", organizationId)
    }

    // Call the login action
    const result = await login(formData)

    if (!result.success) {
      return NextResponse.json(
        { success: false, error: result.error },
        { status: 400 }
      )
    }

    return NextResponse.json({ success: true, message: result.message })
  } catch (error) {
    console.error("Login error:", error)
    return NextResponse.json(
      { success: false, error: "An unexpected error occurred" },
      { status: 500 }
    )
  }
}
