import { type NextRequest, NextResponse } from "next/server"
import { authenticateMagicLink } from "@/modules/auth/stytch-b2b"
import { createUser, getUserByStytchId } from "@/modules/user/service"

export async function GET(request: NextRequest) {
  try {
    const searchParams = request.nextUrl.searchParams
    const token = searchParams.get("token")
    const tokenType = searchParams.get("stytch_token_type")

    if (!token) {
      return NextResponse.redirect(new URL("/auth/login?error=missing_token", request.url))
    }

    // Check if this is a discovery token
    const isDiscoveryToken = tokenType === "discovery"
    
    // Authenticate the token
    const result = await authenticateMagicLink(token, isDiscoveryToken ? "discovery" : undefined)

    if (!result.success) {
      return NextResponse.redirect(new URL("/auth/login?error=invalid_token", request.url))
    }

    // Handle discovery flow
    if (isDiscoveryToken && result.intermediateSessionToken) {
      // Create response with redirect
      const response = NextResponse.redirect(
        new URL(
          result.discoveredOrganizations && result.discoveredOrganizations.length > 0
            ? "/auth/organization/select"
            : "/auth/organization/create",
          request.url
        )
      )
      
      // Set cookie on the response
      response.cookies.set("intermediate_session_token", result.intermediateSessionToken, {
        httpOnly: true,
        secure: process.env.NODE_ENV === "production",
        maxAge: 60 * 10, // 10 minutes
        path: "/",
      })
      
      return response
    }

    // Handle regular authentication flow
    if (result.sessionToken && result.user) {
      // Create response with redirect to the organization selection page
      const response = NextResponse.redirect(new URL("/auth/organization/select", request.url))
      
      // Set cookie on the response
      response.cookies.set("session_token", result.sessionToken, {
        httpOnly: true,
        secure: process.env.NODE_ENV === "production",
        maxAge: 60 * 60 * 24 * 7, // 1 week
        path: "/",
      })

      // Check if user exists in our database
      const userId = typeof result.user === 'object' && 'id' in result.user 
        ? result.user.id as string
        : undefined

      if (userId) {
        let user = await getUserByStytchId(userId)

        if (!user) {
          // Create new user in our database
          user = await createUser({
            email: typeof result.user === 'object' && 'email' in result.user 
              ? result.user.email as string 
              : "<EMAIL>",
            name: "New User",
            stytchUserId: userId,
          })
        }
      }

      return response
    }

    // If we get here, something went wrong
    return NextResponse.redirect(new URL("/auth/login?error=authentication_failed", request.url))
  } catch (error) {
    console.error("Auth callback error:", error)
    return NextResponse.redirect(new URL("/auth/login?error=server_error", request.url))
  }
}
