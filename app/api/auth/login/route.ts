import type { NextRequest } from "next/server"
import { NextResponse } from "next/server"
import loadStytch from "@/lib/loadStytch"
import { authConfig } from "@/lib/config/auth"
import { z } from "zod"

const loginSchema = z.object({
  email: z.string().email("Please enter a valid email address"),
  organizationId: z.string().nullable().optional(),
  redirectUrl: z.string().optional(),
})

export async function POST(request: NextRequest) {
  try {
    const stytchClient = loadStytch()
    const body = await request.json()
    const { email, organizationId, redirectUrl } = body

    if (!email) {
      return NextResponse.json(
        { success: false, error: "Email is required" },
        { status: 400 }
      )
    }

    try {
      // Validate the input
      const validatedData = loginSchema.parse({ 
        email, 
        organizationId,
        redirectUrl
      })

      // Use custom redirectUrl if provided, otherwise use default
      const finalRedirectUrl = validatedData.redirectUrl || 
        `${process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000'}${authConfig.callbackUrl}`
      
      console.log(`Sending magic link to ${email} with redirect URL: ${finalRedirectUrl}`)
      
      if (organizationId) {
        // Organization-specific login
        await stytchClient.magicLinks.email.loginOrSignup({
          email_address: email,
          login_redirect_url: finalRedirectUrl,
          organization_id: organizationId,
        })
      } else {
        // Discovery flow
        await stytchClient.magicLinks.email.discovery.send({
          email_address: email,
          discovery_redirect_url: finalRedirectUrl,
        })
      }

      return NextResponse.json({ 
        success: true, 
        message: "Check your email for a login link" 
      })
    } catch (validationError) {
      console.error("Validation error:", validationError)
      
      if (validationError instanceof z.ZodError) {
        return NextResponse.json(
          { success: false, error: validationError.errors[0]?.message || "Invalid input" },
          { status: 400 }
        )
      }
      
      throw validationError // Re-throw for the outer catch block
    }
  } catch (error) {
    console.error("Login error:", error)
    return NextResponse.json(
      { success: false, error: "An unexpected error occurred" },
      { status: 500 }
    )
  }
}
