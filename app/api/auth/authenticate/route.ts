import type { NextRequest } from "next/server"
import { NextResponse } from "next/server"
import loadStytch from "@/lib/loadStytch"

export async function POST(request: NextRequest) {
  try {
    const stytchClient = loadStytch()
    const body = await request.json()
    const { token, token_type } = body

    if (!token) {
      return NextResponse.json(
        { success: false, error: "Token is required" },
        { status: 400 }
      )
    }

    console.log(`Authenticating token: ${token.substring(0, 10)}..., type: ${token_type || 'unknown'}`)

    // Handle discovery flow
    if (token_type === "discovery") {
      try {
        // Authenticate the discovery token
        const authResp = await stytchClient.magicLinks.discovery.authenticate({
          discovery_magic_links_token: token,
        })

        console.log("Discovery authentication successful")
        
        // Get the intermediate session token
        const ist = authResp.intermediate_session_token
        
        // Get the email from the authentication response
        const email = authResp.email_address
        console.log(`User email: ${email}`)
        
        // Check if the user has any discovered organizations
        if (authResp.discovered_organizations && authResp.discovered_organizations.length > 0) {
          console.log(`User has ${authResp.discovered_organizations.length} discovered organizations`)
          
          // Return the discovered organizations so the frontend can handle organization selection
          return NextResponse.json({
            success: true,
            message: "Discovery authentication successful",
            intermediateSessionToken: ist,
            discoveredOrganizations: authResp.discovered_organizations,
          })
        }
        
        // If no discovered organizations, search for organizations by email domain
        console.log("No discovered organizations, searching for organizations by email domain")
        
        try {
          // Search for organizations that the user might be eligible to join
          // Note: The search API doesn't have a direct way to search by email,
          // so we'll just get all organizations and filter them later
          const searchResp = await stytchClient.organizations.search({})
          
          if (searchResp.organizations && searchResp.organizations.length > 0) {
            console.log(`Found ${searchResp.organizations.length} organizations by email domain`)
            
            // Return the found organizations so the frontend can handle organization selection
            return NextResponse.json({
              success: true,
              message: "Organizations found by email domain",
              intermediateSessionToken: ist,
              discoveredOrganizations: searchResp.organizations,
            })
          }
        } catch (searchError) {
          console.error("Error searching for organizations:", searchError)
          // Continue with organization creation if search fails
        }
        
        console.log("No organizations found, user needs to create one")
        
        // Return the intermediate session token so the frontend can handle organization creation
        return NextResponse.json({
          success: true,
          message: "Discovery authentication successful, no organizations found",
          intermediateSessionToken: ist,
          discoveredOrganizations: [],
        })
      } catch (error) {
        console.error("Error authenticating discovery token:", error)
        return NextResponse.json(
          { success: false, error: "Failed to authenticate discovery token" },
          { status: 400 }
        )
      }
    } else if (token_type === "magic_links") {
      // Handle regular magic link authentication
      try {
        const authResp = await stytchClient.magicLinks.authenticate({
          magic_links_token: token,
          session_duration_minutes: 60 * 24 * 7 // 1 week
        })
        
        console.log("Magic link authentication successful")
        
        // Create response with session cookies
        const response = NextResponse.json({
          success: true,
          message: "Authentication successful",
          user: authResp.member,
          organizationId: authResp.organization?.organization_id,
        })
        
        // Set session token cookie
        if (authResp.session_token) {
          response.cookies.set({
            name: "stytch_session",
            value: authResp.session_token,
            path: "/",
            httpOnly: true,
            secure: process.env.NODE_ENV === "production",
            sameSite: "lax",
            maxAge: 60 * 60 * 24 * 7, // 1 week
          })
        }
        
        // Set session JWT cookie
        if (authResp.session_jwt) {
          response.cookies.set({
            name: "stytch_session_jwt",
            value: authResp.session_jwt,
            path: "/",
            httpOnly: true,
            secure: process.env.NODE_ENV === "production",
            sameSite: "lax",
            maxAge: 60 * 60 * 24 * 7, // 1 week
          })
        }
        
        // Set organization ID cookie if available
        if (authResp.organization?.organization_id) {
          response.cookies.set({
            name: "current_organization",
            value: authResp.organization.organization_id,
            path: "/",
            httpOnly: true,
            secure: process.env.NODE_ENV === "production",
            sameSite: "lax",
            maxAge: 60 * 60 * 24 * 7, // 1 week
          })
        }
        
        return response
      } catch (error) {
        console.error("Error authenticating magic link token:", error)
        return NextResponse.json(
          { success: false, error: "Failed to authenticate magic link token" },
          { status: 400 }
        )
      }
    } else {
      // Handle unknown token type
      return NextResponse.json(
        { success: false, error: `Unrecognized token type: '${token_type}'` },
        { status: 400 }
      )
    }
  } catch (error) {
    console.error("Authentication error:", error)
    return NextResponse.json(
      { success: false, error: "An unexpected error occurred" },
      { status: 500 }
    )
  }
}
