import { NextResponse } from "next/server"
import type { NextRequest } from "next/server"
import loadStytch from "@/lib/loadStytch"

export async function POST(request: NextRequest) {
  try {
    const stytchClient = loadStytch()
    
    // Get the session token from cookies - check both cookie names that <PERSON><PERSON><PERSON> might use
    const sessionToken = request.cookies.get("stytch_session")?.value || 
                         request.cookies.get("stytch_session_jwt")?.value
    
    // If there's a session token, revoke it with <PERSON><PERSON><PERSON>
    if (sessionToken) {
      try {
        await stytchClient.sessions.revoke({
          session_token: sessionToken
        })
        console.log("Session revoked successfully")
      } catch (revokeError) {
        console.error("Error revoking session:", revokeError)
        // Continue with logout even if session revocation fails
      }
    }
    
    // Create a response
    const response = NextResponse.json({
      success: true,
      message: "Logged out successfully",
    })
    
    // Clear all auth cookies
    response.cookies.set({
      name: "stytch_session",
      value: "",
      path: "/",
      maxAge: 0,
    })
    
    response.cookies.set({
      name: "stytch_session_jwt",
      value: "",
      path: "/",
      maxAge: 0,
    })
    
    response.cookies.set({
      name: "current_organization",
      value: "",
      path: "/",
      maxAge: 0,
    })
    
    return response
  } catch (error) {
    console.error("Logout error:", error)
    return NextResponse.json(
      { success: false, error: "An unexpected error occurred" },
      { status: 500 }
    )
  }
}
