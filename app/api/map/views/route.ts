import type { NextRequest } from "next/server"
import { NextResponse } from "next/server"
import { checkPermission } from "@/lib/rbac/checkPermission"
import { getAuthParamsFromRequest } from "@/lib/utils/authUtils"
import { extractUuidFromStytchId } from "@/lib/utils/stytchUtils"
import { createSavedViewSchema } from "@/modules/map/model"
import {
  getSavedViewsForOrganization,
  createSavedView,
  updateSavedView,
  deleteSavedView,
} from "@/modules/map/service"

// GET /api/map/views - Get all saved views for an organization
export async function GET(req: NextRequest) {
  try {
    // Authenticate the request
    const authParams = getAuthParamsFromRequest(req)
    const stytchOrgId = req.headers.get('X-Stytch-Org-Id')

    if (!authParams || !stytchOrgId) {
      console.warn('RBAC check failed: Missing Auth Params or Stytch Org ID header')
      return NextResponse.json({ error: 'Forbidden - Missing Authentication or Organization Context' }, { status: 403 })
    }

    // Get the organization ID from the query parameters
    const { searchParams } = new URL(req.url)
    const organizationIdParam = searchParams.get("organizationId")

    if (!organizationIdParam) {
      return NextResponse.json({ error: "Organization ID is required" }, { status: 400 })
    }
    
    // Extract UUID from Stytch organization ID
    const organizationId = extractUuidFromStytchId(organizationIdParam)
    
    if (!organizationId) {
      return NextResponse.json({ error: "Invalid organization ID" }, { status: 400 })
    }

    // Check if the user has permission to view map views
    const hasPermission = await checkPermission(
      authParams,
      stytchOrgId,
      'view',
      'map'
    )

    if (!hasPermission) {
      return NextResponse.json({ error: "Forbidden" }, { status: 403 })
    }

    // Get saved views
    const views = await getSavedViewsForOrganization(organizationId)

    // Add Cache-Control header
    return NextResponse.json(views, {
      headers: {
        'Cache-Control': 'private, max-age=300', // Cache for 5 minutes
      },
    });
  } catch (error) {
    console.error("Error getting saved views:", error)
    return NextResponse.json(
      { error: "Failed to get saved views" },
      { status: 500 }
    )
  }
}

// POST /api/map/views - Create a new saved view
export async function POST(req: NextRequest) {
  try {
    // Authenticate the request
    const authParams = getAuthParamsFromRequest(req)
    const stytchOrgId = req.headers.get('X-Stytch-Org-Id')
    const memberId = req.headers.get('X-Stytch-Member-Id')

    if (!authParams || !stytchOrgId || !memberId) {
      console.warn('RBAC check failed: Missing Auth Params, Org ID, or Member ID header')
      return NextResponse.json({ error: 'Forbidden - Missing Authentication, Organization, or Member Context' }, { status: 403 })
    }

    // Check if the user has permission to create map views
    const hasPermission = await checkPermission(
      authParams,
      stytchOrgId,
      'edit',
      'map'
    )

    if (!hasPermission) {
      return NextResponse.json({ error: "Forbidden" }, { status: 403 })
    }

    // Parse and validate request body
    const body = await req.json()
    const result = createSavedViewSchema.safeParse(body)
    
    if (!result.success) {
      return NextResponse.json(
        { error: "Invalid request body", details: result.error.format() },
        { status: 400 }
      )
    }
    
    // Extract UUID from Stytch organization ID
    const organizationId = extractUuidFromStytchId(result.data.organizationId)
    
    if (!organizationId) {
      return NextResponse.json({ error: "Invalid organization ID" }, { status: 400 })
    }
    
    // Extract UUID from Stytch member ID
    const userId = extractUuidFromStytchId(memberId)
    
    if (!userId) {
      return NextResponse.json({ error: "Invalid member ID" }, { status: 400 })
    }
    
    const { name, description, filters, viewSettings, selectedLocationIds } = result.data
    
    // Create saved view
    const view = await createSavedView(
      organizationId,
      name,
      userId,
      viewSettings,
      filters,
      description,
      selectedLocationIds
    )
    
    return NextResponse.json(view)
  } catch (error) {
    console.error("Error creating saved view:", error)
    return NextResponse.json(
      { error: "Failed to create saved view" },
      { status: 500 }
    )
  }
}

// PUT /api/map/views - Update a saved view
export async function PUT(req: NextRequest) {
  try {
    // Authenticate the request
    const authParams = getAuthParamsFromRequest(req)
    const stytchOrgId = req.headers.get('X-Stytch-Org-Id')
    const memberId = req.headers.get('X-Stytch-Member-Id')

    if (!authParams || !stytchOrgId || !memberId) {
      console.warn('RBAC check failed: Missing Auth Params, Org ID, or Member ID header')
      return NextResponse.json({ error: 'Forbidden - Missing Authentication, Organization, or Member Context' }, { status: 403 })
    }

    // Check if the user has permission to update map views
    const hasPermission = await checkPermission(
      authParams,
      stytchOrgId,
      'edit',
      'map'
    )

    if (!hasPermission) {
      return NextResponse.json({ error: "Forbidden" }, { status: 403 })
    }

    // Parse request body
    const body = await req.json()
    
    if (!body.id) {
      return NextResponse.json(
        { error: "Missing view ID" },
        { status: 400 }
      )
    }
    
    // Extract UUID from Stytch organization ID
    const organizationId = extractUuidFromStytchId(body.organizationId)
    
    if (!organizationId) {
      return NextResponse.json({ error: "Invalid organization ID" }, { status: 400 })
    }
    
    // Extract UUID from Stytch member ID
    const userId = extractUuidFromStytchId(memberId)
    
    if (!userId) {
      return NextResponse.json({ error: "Invalid member ID" }, { status: 400 })
    }
    
    // Update saved view
    const view = await updateSavedView(
      body.id,
      organizationId,
      userId,
      {
        name: body.name,
        description: body.description,
        viewSettings: body.viewSettings,
        filters: body.filters,
        selectedLocationIds: body.selectedLocationIds,
      }
    )
    
    if (!view) {
      return NextResponse.json(
        { error: "View not found" },
        { status: 404 }
      )
    }
    
    return NextResponse.json(view)
  } catch (error) {
    console.error("Error updating saved view:", error)
    return NextResponse.json(
      { error: "Failed to update saved view" },
      { status: 500 }
    )
  }
}

// DELETE /api/map/views - Delete a saved view
export async function DELETE(req: NextRequest) {
  try {
    // Authenticate the request
    const authParams = getAuthParamsFromRequest(req)
    const stytchOrgId = req.headers.get('X-Stytch-Org-Id')

    if (!authParams || !stytchOrgId) {
      console.warn('RBAC check failed: Missing Auth Params or Stytch Org ID header')
      return NextResponse.json({ error: 'Forbidden - Missing Authentication or Organization Context' }, { status: 403 })
    }

    // Check if the user has permission to delete map views
    const hasPermission = await checkPermission(
      authParams,
      stytchOrgId,
      'delete',
      'map'
    )

    if (!hasPermission) {
      return NextResponse.json({ error: "Forbidden" }, { status: 403 })
    }

    // Get view ID from URL
    const url = new URL(req.url)
    const id = url.searchParams.get("id")
    const organizationIdParam = url.searchParams.get("organizationId")
    
    if (!id) {
      return NextResponse.json(
        { error: "Missing view ID" },
        { status: 400 }
      )
    }
    
    if (!organizationIdParam) {
      return NextResponse.json({ error: "Organization ID is required" }, { status: 400 })
    }
    
    // Extract UUID from Stytch organization ID
    const organizationId = extractUuidFromStytchId(organizationIdParam)
    
    if (!organizationId) {
      return NextResponse.json({ error: "Invalid organization ID" }, { status: 400 })
    }
    
    // Delete saved view
    const success = await deleteSavedView(id, organizationId)
    
    if (!success) {
      return NextResponse.json(
        { error: "View not found" },
        { status: 404 }
      )
    }
    
    return NextResponse.json({ success: true })
  } catch (error) {
    console.error("Error deleting saved view:", error)
    return NextResponse.json(
      { error: "Failed to delete saved view" },
      { status: 500 }
    )
  }
}
