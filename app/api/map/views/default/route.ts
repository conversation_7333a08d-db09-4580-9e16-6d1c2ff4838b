import { NextResponse } from "next/server";
import type { NextRequest } from "next/server";
import { checkPermission } from "@/lib/rbac/checkPermission";

export async function POST(request: NextRequest) {
  try {
    const stytchOrgId = request.headers.get('X-Stytch-Org-Id');
    const sessionToken = request.headers.get('X-Stytch-Session-Token');
    const sessionJwt = request.headers.get('X-Stytch-Session-JWT');

    // Authorization Check
    if (!stytchOrgId) {
      console.warn('RBAC check failed: Missing Stytch Org ID header');
      return NextResponse.json({ error: 'Forbidden - Missing Organization Context' }, { status: 403 });
    }

    // Check permission for updating map views
    const canUpdateMapViews = await checkPermission(
      { session_token: sessionToken, session_jwt: sessionJwt },
      stytchOrgId,
      'update',
      'map'
    );
    
    if (!canUpdateMapViews) {
      console.warn(`RBAC check failed: User in org ${stytchOrgId} does not have 'update' permission on 'map'`);
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    const { searchParams } = new URL(request.url);
    const id = searchParams.get("id");
    
    if (!id) {
      return NextResponse.json({ error: "View ID is required" }, { status: 400 });
    }

    // This is a placeholder - we would normally update the default view in the database
    return NextResponse.json({
      id,
      isDefault: true,
      updatedAt: new Date().toISOString(),
    });
  } catch (error) {
    console.error("Error setting default view:", error);
    return NextResponse.json({ error: "Failed to set default view" }, { status: 500 });
  }
}
