import type { NextRequest } from "next/server";
import { NextResponse } from "next/server";
import { getAuthParamsFromRequest } from "@/lib/utils/authUtils";
import { checkPermission } from "@/lib/rbac/checkPermission";
import { MapExportService } from "@/modules/map/export-service";
import * as locationService from "@/modules/location/service";
import type { ExportOptions } from "@/components/maps/export-map-modal";

/**
 * POST /api/map/export
 * 
 * Export map data to PDF or CSV
 */
export async function POST(req: NextRequest) {
  try {
    // Authenticate the request
    const authParams = getAuthParamsFromRequest(req);
    const stytchOrgId = req.headers.get('X-Stytch-Org-Id');

    if (!authParams || !stytchOrgId) {
      console.warn('RBAC check failed: Missing Auth Params or Stytch Org ID header');
      return NextResponse.json(
        { error: 'Forbidden - Missing Authentication or Organization Context' }, 
        { status: 403 }
      );
    }

    // Check if the user has permission to export map data
    const hasPermission = await checkPermission(
      authParams,
      stytchOrgId,
      'view',
      'map'
    );

    if (!hasPermission) {
      return NextResponse.json({ error: "Forbidden" }, { status: 403 });
    }

    // Parse request body
    const body = await req.json();
    const { organizationId, locationIds, options } = body as {
      organizationId: string;
      locationIds: string[];
      options: ExportOptions;
    };

    // Validate request
    if (!organizationId) {
      return NextResponse.json(
        { error: "Organization ID is required" },
        { status: 400 }
      );
    }

    if (!options) {
      return NextResponse.json(
        { error: "Export options are required" },
        { status: 400 }
      );
    }

    // Fetch locations
    let locations = [];
    
    if (options.includeAllLocations) {
      // Fetch all locations for the organization
      const fetchedLocations = await locationService.getLocations(organizationId, {
        page: 1,
        pageSize: 1000, // Set a reasonable limit
      });
      
      // Ensure all locations are valid
      locations = fetchedLocations.filter(loc => loc !== null);
    } else if (locationIds && locationIds.length > 0) {
      // Fetch only the selected locations
      const locationPromises = locationIds.map(id => {
        try {
          return locationService.getLocation(id);
        } catch (error) {
          console.error(`Failed to fetch location ${id}:`, error);
          return null;
        }
      });
      
      const fetchedLocations = await Promise.all(locationPromises);
      
      // Filter out any null results (locations that weren't found)
      locations = fetchedLocations.filter(loc => loc !== null);
    } else {
      return NextResponse.json(
        { error: "No locations selected for export" },
        { status: 400 }
      );
    }

    // Generate export data
    const exportData = {
      url: await MapExportService.exportMap(locations, options),
      format: options.format,
      count: locations.length,
    };

    return NextResponse.json(exportData);
  } catch (error) {
    console.error("Error exporting map:", error);
    return NextResponse.json(
      { error: "Failed to export map" },
      { status: 500 }
    );
  }
}
