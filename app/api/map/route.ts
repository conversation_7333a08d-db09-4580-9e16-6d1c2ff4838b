import { NextResponse } from "next/server";
import type { NextRequest } from "next/server"; // Import NextRequest
import { getLocations } from "@/modules/location/service";
import { getProjects } from "@/modules/project/service";
import { findOrganizationByStytchId } from "@/modules/organization/service";
import { mapFilterSchema } from "@/modules/map/model";
import { validate } from "@/modules/shared/validation";
import { z } from "zod";
import { checkPermission } from "@/lib/rbac/checkPermission"; // Import RBAC check function
// Remove unused imports
// import { extractUuidFromStytchId } from "@/lib/utils/stytchUtils";
// import { getCurrentUser } from "@/modules/auth/service"

export async function GET(request: NextRequest) { // Change type to NextRequest
  try {
    const stytchOrgId = request.headers.get('X-Stytch-Org-Id');
    const sessionToken = request.headers.get('X-Stytch-Session-Token');
    const sessionJwt = request.headers.get('X-Stytch-Session-JWT');

    // Authorization Check: RBAC check for reading map data (requires reading locations and projects)
    if (!stytchOrgId) {
       console.warn('RBAC check failed: Missing Stytch Org ID header');
       return NextResponse.json({ error: 'Forbidden - Missing Organization Context' }, { status: 403 });
    }

    // Check permission for locations
    const canReadLocations = await checkPermission(
      { session_token: sessionToken, session_jwt: sessionJwt },
      stytchOrgId,
      'view', // Use 'view' action for 'location' resource
      'location'
    );
    if (!canReadLocations) {
      console.warn(`RBAC check failed: User in org ${stytchOrgId} does not have 'read' permission on 'location'`);
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    // Check permission for projects
    const canReadProjects = await checkPermission(
      { session_token: sessionToken, session_jwt: sessionJwt },
      stytchOrgId,
      'view', // Use 'view' action for 'project' resource
      'project'
    );
    if (!canReadProjects) {
      console.warn(`RBAC check failed: User in org ${stytchOrgId} does not have 'read' permission on 'project'`);
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    const { searchParams } = new URL(request.url);
    // Ignore organizationId from query params, use authenticated stytchOrgId

    // Parse filter parameters
    const locationTypes = searchParams.get("locationTypes")?.split(",") || []
    const projectIds = searchParams.get("projectIds")?.split(",") || []
    const searchQuery = searchParams.get("searchQuery") || undefined
    const status = searchParams.get("status")?.split(",") || []
    const dateFrom = searchParams.get("dateFrom") || undefined
    const dateTo = searchParams.get("dateTo") || undefined
    const favoriteListId = searchParams.get("favoriteListId") || undefined
    
    // Parse numeric filters
    const hourlyRateMin = searchParams.get("hourlyRateMin") ? Number.parseFloat(searchParams.get("hourlyRateMin") as string) : undefined
    const hourlyRateMax = searchParams.get("hourlyRateMax") ? Number.parseFloat(searchParams.get("hourlyRateMax") as string) : undefined
    const dailyRateMin = searchParams.get("dailyRateMin") ? Number.parseFloat(searchParams.get("dailyRateMin") as string) : undefined
    const dailyRateMax = searchParams.get("dailyRateMax") ? Number.parseFloat(searchParams.get("dailyRateMax") as string) : undefined
    
    // Parse boolean filters
    const permitsRequired = searchParams.get("permitsRequired") ? searchParams.get("permitsRequired") === "true" : undefined
    
    // Parse array filters
    const locationTags = searchParams.get("locationTags")?.split(",") || []
    const locationFeatures = searchParams.get("locationFeatures")?.split(",") || []

    // Validate filters
    const filters = validate(mapFilterSchema, {
      locationTypes: locationTypes.length > 0 ? locationTypes : undefined,
      projectIds: projectIds.length > 0 ? projectIds : undefined,
      searchQuery,
      status: status.length > 0 ? status : undefined,
      dateFrom,
      dateTo,
      favoriteListId,
      hourlyRateMin,
      hourlyRateMax,
      dailyRateMin,
      dailyRateMax,
      permitsRequired,
      locationTags: locationTags.length > 0 ? locationTags : undefined,
      locationFeatures: locationFeatures.length > 0 ? locationFeatures : undefined,
    });

    // Get organization by Stytch organization ID
    const organization = await findOrganizationByStytchId(stytchOrgId);
    
    if (!organization) {
      console.warn(`Organization not found for Stytch organization ID: ${stytchOrgId}`);
      return NextResponse.json({ error: "Organization not found" }, { status: 404 });
    }

    // Get locations based on filters using the organization ID from the database
    const locations = await getLocations(organization.id, {
      type: filters.locationTypes?.length ? filters.locationTypes[0] : undefined,
      status: filters.status?.length ? filters.status[0] : undefined,
      projectId: filters.projectIds?.length ? filters.projectIds[0] : undefined,
      search: filters.searchQuery,
      tags: filters.locationTags,
    });

    // Get projects for the organization using the organization ID from the database
    const projects = await getProjects(organization.id);

    return NextResponse.json({
      locations,
      projects,
    })
  } catch (error) {
    console.error("Error fetching map data:", error)

    if (error instanceof z.ZodError) {
      return NextResponse.json({ error: "Invalid filter parameters", details: error.errors }, { status: 400 })
    }

    return NextResponse.json({ error: "Failed to fetch map data" }, { status: 500 })
  }
}
