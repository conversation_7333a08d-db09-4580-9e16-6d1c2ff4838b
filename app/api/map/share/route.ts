import type { NextRequest } from "next/server"
import { NextResponse } from "next/server"
import { checkPermission } from "@/lib/rbac/checkPermission"
import { getAuthParamsFromRequest } from "@/lib/utils/authUtils"
import { extractUuidFromStytchId } from "@/lib/utils/stytchUtils"
import { createSharedMapLinkSchema } from "@/modules/map/model"
import {
  createSharedMapLink,
  getSharedMapLinksForOrganization,
  deleteSharedMapLink,
} from "@/modules/map/service"

// POST /api/map/share - Create a new shared map link
export async function POST(req: NextRequest) {
  try {
    // Authenticate the request
    const authParams = getAuthParamsFromRequest(req)
    const stytchOrgId = req.headers.get('X-Stytch-Org-Id')
    const memberId = req.headers.get('X-Stytch-Member-Id')

    if (!authParams || !stytchOrgId || !memberId) {
      console.warn('RBAC check failed: Missing Auth Params, Org ID, or Member ID header')
      return NextResponse.json({ error: 'Forbidden - Missing Authentication, Organization, or Member Context' }, { status: 403 })
    }

    // Check if the user has permission to share map views
    const hasPermission = await checkPermission(
      authParams,
      stytchOrgId,
      'share',
      'map'
    )

    if (!hasPermission) {
      return NextResponse.json({ error: "Forbidden" }, { status: 403 })
    }

    // Parse and validate request body
    const body = await req.json()
    const result = createSharedMapLinkSchema.safeParse(body)
    
    if (!result.success) {
      return NextResponse.json(
        { error: "Invalid request body", details: result.error.format() },
        { status: 400 }
      )
    }
    
    // Extract UUID from Stytch organization ID
    const organizationId = extractUuidFromStytchId(result.data.organizationId)
    
    if (!organizationId) {
      return NextResponse.json({ error: "Invalid organization ID" }, { status: 400 })
    }
    
    // Extract UUID from Stytch member ID
    const userId = extractUuidFromStytchId(memberId)
    
    if (!userId) {
      return NextResponse.json({ error: "Invalid member ID" }, { status: 400 })
    }
    
    // Create shared map link
    const sharedLink = await createSharedMapLink(
      organizationId,
      userId,
      {
        mapViewId: result.data.mapViewId,
        filters: result.data.filters,
        viewSettings: result.data.viewSettings,
        password: result.data.password,
        expiresIn: result.data.expiresIn,
        locationIds: result.data.locationIds,
      }
    )
    
    return NextResponse.json(sharedLink)
  } catch (error) {
    console.error("Error creating shared map link:", error)
    return NextResponse.json(
      { error: "Failed to create shared map link" },
      { status: 500 }
    )
  }
}

// GET /api/map/share - Get all shared map links for an organization
export async function GET(req: NextRequest) {
  try {
    // Authenticate the request
    const authParams = getAuthParamsFromRequest(req)
    const stytchOrgId = req.headers.get('X-Stytch-Org-Id')

    if (!authParams || !stytchOrgId) {
      console.warn('RBAC check failed: Missing Auth Params or Stytch Org ID header')
      return NextResponse.json({ error: 'Forbidden - Missing Authentication or Organization Context' }, { status: 403 })
    }

    // Get the organization ID from the query parameters
    const { searchParams } = new URL(req.url)
    const organizationIdParam = searchParams.get("organizationId")

    if (!organizationIdParam) {
      return NextResponse.json({ error: "Organization ID is required" }, { status: 400 })
    }
    
    // Extract UUID from Stytch organization ID
    const organizationId = extractUuidFromStytchId(organizationIdParam)
    
    if (!organizationId) {
      return NextResponse.json({ error: "Invalid organization ID" }, { status: 400 })
    }

    // Check if the user has permission to view shared map links
    const hasPermission = await checkPermission(
      authParams,
      stytchOrgId,
      'view',
      'map'
    )

    if (!hasPermission) {
      return NextResponse.json({ error: "Forbidden" }, { status: 403 })
    }

    // Get shared map links
    const links = await getSharedMapLinksForOrganization(organizationId)

    return NextResponse.json(links)
  } catch (error) {
    console.error("Error getting shared map links:", error)
    return NextResponse.json(
      { error: "Failed to get shared map links" },
      { status: 500 }
    )
  }
}

// DELETE /api/map/share - Delete a shared map link
export async function DELETE(req: NextRequest) {
  try {
    // Authenticate the request
    const authParams = getAuthParamsFromRequest(req)
    const stytchOrgId = req.headers.get('X-Stytch-Org-Id')

    if (!authParams || !stytchOrgId) {
      console.warn('RBAC check failed: Missing Auth Params or Stytch Org ID header')
      return NextResponse.json({ error: 'Forbidden - Missing Authentication or Organization Context' }, { status: 403 })
    }

    // Check if the user has permission to delete shared map links
    const hasPermission = await checkPermission(
      authParams,
      stytchOrgId,
      'delete',
      'map'
    )

    if (!hasPermission) {
      return NextResponse.json({ error: "Forbidden" }, { status: 403 })
    }

    // Get link ID from URL
    const url = new URL(req.url)
    const id = url.searchParams.get("id")
    const organizationIdParam = url.searchParams.get("organizationId")
    
    if (!id) {
      return NextResponse.json(
        { error: "Missing link ID" },
        { status: 400 }
      )
    }
    
    if (!organizationIdParam) {
      return NextResponse.json({ error: "Organization ID is required" }, { status: 400 })
    }
    
    // Extract UUID from Stytch organization ID
    const organizationId = extractUuidFromStytchId(organizationIdParam)
    
    if (!organizationId) {
      return NextResponse.json({ error: "Invalid organization ID" }, { status: 400 })
    }
    
    // Delete shared map link
    const success = await deleteSharedMapLink(id, organizationId)
    
    if (!success) {
      return NextResponse.json(
        { error: "Shared map link not found" },
        { status: 404 }
      )
    }
    
    return NextResponse.json({ success: true })
  } catch (error) {
    console.error("Error deleting shared map link:", error)
    return NextResponse.json(
      { error: "Failed to delete shared map link" },
      { status: 500 }
    )
  }
}
