import type { NextRequest } from "next/server"
import { NextResponse } from "next/server"
import { accessSharedMapLinkSchema } from "@/modules/map/model"
import { getSharedMapLinkByToken, logSharedMapLinkAccess } from "@/modules/map/service"

// GET /api/map/shared/[token] - Access a shared map link
export async function GET(
  req: NextRequest,
  { params }: { params: { token: string } }
) {
  try {
    const token = params.token
    
    if (!token) {
      return NextResponse.json({ error: "Token is required" }, { status: 400 })
    }
    
    // Get password from query parameters if provided
    const { searchParams } = new URL(req.url)
    const password = searchParams.get("password") || undefined
    
    // Validate parameters
    const result = accessSharedMapLinkSchema.safeParse({ token, password })
    
    if (!result.success) {
      return NextResponse.json(
        { error: "Invalid parameters", details: result.error.format() },
        { status: 400 }
      )
    }
    
    try {
      // Get the shared link
      const sharedLink = await getSharedMapLinkByToken(token, password)
      
      if (!sharedLink) {
        return NextResponse.json({ error: "Shared link not found or expired" }, { status: 404 })
      }
      
      // Log access
      const ipAddress = req.headers.get("x-forwarded-for")
      const userAgent = req.headers.get("user-agent")
      
      await logSharedMapLinkAccess(sharedLink.id, ipAddress || undefined, userAgent || undefined)
      
      // Return a simplified version of the shared link for public consumption
      return NextResponse.json({
        id: sharedLink.id,
        organizationId: sharedLink.id.split('-')[0], // Just use the first part of the UUID as a reference
        viewSettings: sharedLink.viewSettings,
        locations: sharedLink.locations?.map(location => ({
          id: location.id,
          name: location.name,
          description: location.description,
          address: {
            formatted: location.address?.formatted || "",
          },
          coordinates: location.coordinates,
          type: location.type,
          status: location.status,
        })),
        isPasswordProtected: !!sharedLink.passwordHash,
      })
    } catch (error) {
      if (error instanceof Error && error.message === "Password required") {
        return NextResponse.json(
          { error: "Password required", isPasswordProtected: true },
          { status: 401 }
        )
      }
      
      if (error instanceof Error && error.message === "Invalid password") {
        return NextResponse.json(
          { error: "Invalid password", isPasswordProtected: true },
          { status: 401 }
        )
      }
      
      throw error
    }
  } catch (error) {
    console.error("Error accessing shared map link:", error)
    return NextResponse.json(
      { error: "Failed to access shared map link" },
      { status: 500 }
    )
  }
}
