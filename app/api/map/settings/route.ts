import { NextResponse } from "next/server"
import { mapViewSettingsSchema } from "@/modules/map/model"
import { saveUserMapSettings } from "@/modules/map/service"
import { validate } from "@/modules/shared/validation"
import { z } from "zod"
import { getCurrentUser } from "@/modules/auth/service"

export async function POST(request: Request) {
  try {
    // Get the current user to ensure they're authenticated
    const user = await getCurrentUser()

    const body = await request.json()

    // Validate the settings
    const validatedSettings = validate(mapViewSettingsSchema, body)

    // Save the settings
    await saveUserMapSettings(user.id, validatedSettings)

    return NextResponse.json({ success: true })
  } catch (error) {
    console.error("Error saving map settings:", error)

    if (error instanceof z.ZodError) {
      return NextResponse.json({ error: "Invalid settings", details: error.errors }, { status: 400 })
    }

    return NextResponse.json({ error: "Failed to save map settings" }, { status: 500 })
  }
}
