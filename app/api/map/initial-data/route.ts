import { NextResponse } from "next/server";
import type { NextRequest } from "next/server";
import { getOrganizationBySlug } from "@/modules/organization/service";
import { getLocations } from "@/modules/location/service";
import { getSavedViewsForOrganization } from "@/modules/map/service";
import { getFavoriteLists } from "@/modules/favorites/service";
import { getCurrentUserId } from "@/lib/utils/auth-utils";
import { getAuthParamsFromRequest } from "@/lib/utils/authUtils";
import { checkPermission } from "@/lib/rbac/checkPermission";
import { extractUuidFromStytchId } from "@/lib/utils/stytchUtils";

/**
 * Combined endpoint for initial map data
 * This reduces multiple API calls by fetching all required data in parallel
 */
export async function GET(req: NextRequest) {
  // Get authentication parameters
  const authParams = getAuthParamsFromRequest(req);
  const stytchOrgId = req.headers.get('X-Stytch-Org-Id');
  
  if (!authParams || !stytchOrgId) {
    console.warn('RBAC check failed: Missing Auth Params or Stytch Org ID header');
    return NextResponse.json({ error: 'Forbidden - Missing Authentication or Organization Context' }, { status: 403 });
  }
  
  // Check permissions for map, locations, and favorites
  const [canViewMap, canViewLocations] = await Promise.all([
    checkPermission(authParams, stytchOrgId, 'view', 'map'),
    checkPermission(authParams, stytchOrgId, 'view', 'location')
  ]);
  
  if (!canViewMap || !canViewLocations) {
    console.warn(`RBAC check failed: User does not have required permissions`);
    return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
  }
  
  // Get user ID
  const userId = await getCurrentUserId();
  if (!userId) {
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
  }
  
  // Parse query parameters
  const url = new URL(req.url);
  const organizationSlug = url.searchParams.get("organizationSlug");
  
  if (!organizationSlug) {
    return NextResponse.json(
      { error: "Organization slug is required" }, 
      { status: 400 }
    );
  }
  
  try {
    // Get organization by slug
    const organization = await getOrganizationBySlug(organizationSlug);
    if (!organization) {
      return NextResponse.json({ error: "Organization not found" }, { status: 404 });
    }
    
    // Get organization ID from Stytch ID
    const organizationId = extractUuidFromStytchId(stytchOrgId);
    if (!organizationId) {
      return NextResponse.json({ error: "Invalid organization ID" }, { status: 400 });
    }
    
    // Fetch all required data in parallel for better performance
    const [locationsData, views, favorites] = await Promise.all([
      // Get locations with filters - explicitly set projectId to undefined to include all locations
      getLocations(organization.id, {
        // Default filters
        type: undefined,
        status: undefined,
        projectId: undefined, // Explicitly undefined to include locations with null project_id
        search: undefined,
        tags: undefined
      }),
      // Get saved map views
      getSavedViewsForOrganization(organization.id),
      // Get favorite lists
      getFavoriteLists(organization.id)
    ]);
    
    // Return combined data with appropriate cache headers
    return NextResponse.json(
      {
        organization,
        locations: locationsData,
        views,
        favorites,
      },
      {
        headers: {
          // Private cache for 1 minute
          "Cache-Control": "private, max-age=60",
        },
      }
    );
  } catch (error) {
    console.error("Error fetching initial map data:", error);
    return NextResponse.json(
      { error: "Failed to fetch initial map data" },
      { status: 500 }
    );
  }
}
