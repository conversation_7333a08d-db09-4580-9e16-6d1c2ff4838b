import type { NextRequest } from "next/server" // Use import type
import { NextResponse } from "next/server"
import { NotificationService } from "@/modules/notification/service"
import { checkPermission } from "@/lib/rbac/checkPermission"
import { getUserIdFromStytchId, getOrgIdFromStytchId } from "@/lib/utils/userUtils"

/**
 * GET /api/notifications/count
 * 
 * Get notification count for the current user
 */
export async function GET(req: NextRequest) {
  try {
    const memberId = req.headers.get('X-Stytch-Member-Id')
    const stytchOrgId = req.headers.get('X-Stytch-Org-Id')
    const sessionToken = req.headers.get('X-Stytch-Session-Token')
    const sessionJwt = req.headers.get('X-Stytch-Session-JWT')

    console.log('GET /api/notifications/count')
    console.log('Member ID from header:', memberId)
    console.log('Stytch Org ID from header:', stytchOrgId)

    // Check permission
    const hasPermission = await checkPermission(
      { session_token: sessionToken, session_jwt: sessionJwt },
      stytchOrgId,
      'read',
      'notification'
    )

    if (!hasPermission) {
      console.warn(`RBAC check failed: User ${memberId} does not have 'read' permission on 'notification' in org ${stytchOrgId}`)
      return NextResponse.json({ error: "Forbidden" }, { status: 403 })
    }

    // Check if memberId and stytchOrgId are available
    if (!memberId || !stytchOrgId) {
      console.error("Missing required headers: memberId or stytchOrgId")
      return NextResponse.json({ error: "Missing required headers" }, { status: 400 })
    }

    // Get user and organization IDs from Stytch IDs
    const userId = await getUserIdFromStytchId(memberId)
    const orgId = await getOrgIdFromStytchId(stytchOrgId)
    
    if (!userId || !orgId) {
      console.error("Failed to find user or organization with the provided Stytch IDs")
      return NextResponse.json({ error: "Invalid Stytch IDs" }, { status: 400 })
    }
    
    // Get notification count
    const count = await NotificationService.getNotificationCount(userId, orgId)
    
    // Add Cache-Control header
    return NextResponse.json(count, {
      headers: {
        'Cache-Control': 'private, max-age=30', // Cache for 30 seconds
      },
    });
  } catch (error) {
    console.error("Error fetching notification count:", error)
    return NextResponse.json(
      { error: "Failed to fetch notification count" },
      { status: 500 }
    )
  }
}
