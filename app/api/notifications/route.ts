import { NextRequest, NextResponse } from "next/server"
import { z } from "zod"
import { NotificationService } from "@/modules/notification/service"
import { getNotificationsQuerySchema } from "@/modules/notification/model"
import { checkPermission } from "@/lib/rbac/checkPermission"
import { getUserIdFromStytchId, getOrgIdFromStytchId } from "@/lib/utils/userUtils"

/**
 * GET /api/notifications
 * 
 * Get notifications for the current user with pagination and filtering
 */
export async function GET(req: NextRequest) {
  try {
    const memberId = req.headers.get('X-Stytch-Member-Id')
    const stytchOrgId = req.headers.get('X-Stytch-Org-Id')
    const sessionToken = req.headers.get('X-Stytch-Session-Token')
    const sessionJwt = req.headers.get('X-Stytch-Session-JWT')

    console.log('GET /api/notifications')
    console.log('Member ID from header:', memberId)
    console.log('Stytch Org ID from header:', stytchOrgId)

    // Check permission
    const hasPermission = await checkPermission(
      { session_token: sessionToken, session_jwt: sessionJwt },
      stytchOrgId,
      'read',
      'notification'
    )

    if (!hasPermission) {
      console.warn(`RBAC check failed: User ${memberId} does not have 'read' permission on 'notification' in org ${stytchOrgId}`)
      return NextResponse.json({ error: "Forbidden" }, { status: 403 })
    }

    // Get query parameters
    const url = new URL(req.url)
    const status = url.searchParams.get("status") || undefined
    const type = url.searchParams.get("type") || undefined
    const priority = url.searchParams.get("priority") || undefined
    const limit = Number.parseInt(url.searchParams.get("limit") || "20")
    const offset = Number.parseInt(url.searchParams.get("offset") || "0")

    // Get user and organization IDs from Stytch IDs
    const userId = await getUserIdFromStytchId(memberId)
    const orgId = await getOrgIdFromStytchId(stytchOrgId)
    
    if (!userId || !orgId) {
      console.error("Failed to find user or organization with the provided Stytch IDs")
      return NextResponse.json({ error: "Invalid Stytch IDs" }, { status: 400 })
    }
    
    // Validate query parameters
    const queryParams = {
      recipientId: userId,
      organizationId: orgId,
      status,
      type,
      priority,
      limit,
      offset,
    }

    try {
      const validatedParams = getNotificationsQuerySchema.parse(queryParams)
      
      // Get notifications
      const notifications = await NotificationService.getNotifications(validatedParams)
      
      return NextResponse.json(notifications)
    } catch (validationError) {
      if (validationError instanceof z.ZodError) {
        return NextResponse.json(
          { error: "Invalid query parameters", details: validationError.format() },
          { status: 400 }
        )
      }
      throw validationError
    }
  } catch (error) {
    console.error("Error fetching notifications:", error)
    return NextResponse.json(
      { error: "Failed to fetch notifications" },
      { status: 500 }
    )
  }
}
