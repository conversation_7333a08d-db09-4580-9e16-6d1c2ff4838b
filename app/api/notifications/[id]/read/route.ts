import { NextRequest, NextResponse } from "next/server"
import { NotificationService } from "@/modules/notification/service"
import { checkPermission } from "@/lib/rbac/checkPermission"
import { getUserIdFromStytchId } from "@/lib/utils/userUtils"

/**
 * PUT /api/notifications/[id]/read
 * 
 * Mark a notification as read
 */
export async function PUT(
  req: NextRequest,
  context: { params: { id: string } }
) {
  try {
    const { id } = context.params
    const memberId = req.headers.get('X-Stytch-Member-Id')
    const stytchOrgId = req.headers.get('X-Stytch-Org-Id')
    const sessionToken = req.headers.get('X-Stytch-Session-Token')
    const sessionJwt = req.headers.get('X-Stytch-Session-JWT')

    console.log(`PUT /api/notifications/${id}/read`)
    console.log('Member ID from header:', memberId)
    console.log('Stytch Org ID from header:', stytchOrgId)

    // Check permission
    const hasPermission = await checkPermission(
      { session_token: sessionToken, session_jwt: sessionJwt },
      stytchOrgId,
      'mark_as_read',
      'notification'
    )

    if (!hasPermission) {
      console.warn(`RBAC check failed: User ${memberId} does not have 'mark_as_read' permission on 'notification' in org ${stytchOrgId}`)
      return NextResponse.json({ error: "Forbidden" }, { status: 403 })
    }

    // Check if memberId and stytchOrgId are available
    if (!memberId || !stytchOrgId) {
      console.error("Missing required headers: memberId or stytchOrgId")
      return NextResponse.json({ error: "Missing required headers" }, { status: 400 })
    }

    // Get the notification to verify ownership
    const notification = await NotificationService.getNotificationById(id)
    
    if (!notification) {
      return NextResponse.json({ error: "Notification not found" }, { status: 404 })
    }

    // Get user ID from Stytch ID
    const userId = await getUserIdFromStytchId(memberId)
    
    if (!userId) {
      console.error("Failed to find user with the provided Stytch ID")
      return NextResponse.json({ error: "Invalid Stytch ID" }, { status: 400 })
    }
    
    // Verify that the notification belongs to the user
    if (notification.recipientId !== userId) {
      console.warn(`User ${memberId} (UUID: ${userId}) attempted to mark notification ${id} as read, but it belongs to user ${notification.recipientId}`)
      return NextResponse.json({ error: "Forbidden" }, { status: 403 })
    }

    // Mark notification as read
    const updatedNotification = await NotificationService.markNotificationAsRead(id)
    
    return NextResponse.json(updatedNotification)
  } catch (error) {
    console.error("Error marking notification as read:", error)
    return NextResponse.json(
      { error: "Failed to mark notification as read" },
      { status: 500 }
    )
  }
}
