import { NextRequest, NextResponse } from "next/server"
import { NotificationService } from "@/modules/notification/service"
import { checkPermission } from "@/lib/rbac/checkPermission"
import { getUserIdFromStytchId, getOrgIdFromStytchId } from "@/lib/utils/userUtils"

/**
 * PUT /api/notifications/read-all
 * 
 * Mark all notifications as read for the current user
 */
export async function PUT(req: NextRequest) {
  try {
    const memberId = req.headers.get('X-Stytch-Member-Id')
    const stytchOrgId = req.headers.get('X-Stytch-Org-Id')
    const sessionToken = req.headers.get('X-Stytch-Session-Token')
    const sessionJwt = req.headers.get('X-Stytch-Session-JWT')

    console.log('PUT /api/notifications/read-all')
    console.log('Member ID from header:', memberId)
    console.log('Stytch Org ID from header:', stytchOrgId)

    // Check permission
    const hasPermission = await checkPermission(
      { session_token: sessionToken, session_jwt: sessionJwt },
      stytchOrgId,
      'mark_as_read',
      'notification'
    )

    if (!hasPermission) {
      console.warn(`RBAC check failed: User ${memberId} does not have 'mark_as_read' permission on 'notification' in org ${stytchOrgId}`)
      return NextResponse.json({ error: "Forbidden" }, { status: 403 })
    }

    // Check if memberId and stytchOrgId are available
    if (!memberId || !stytchOrgId) {
      console.error("Missing required headers: memberId or stytchOrgId")
      return NextResponse.json({ error: "Missing required headers" }, { status: 400 })
    }

    // Get user and organization IDs from Stytch IDs
    const userId = await getUserIdFromStytchId(memberId)
    const orgId = await getOrgIdFromStytchId(stytchOrgId)
    
    if (!userId || !orgId) {
      console.error("Failed to find user or organization with the provided Stytch IDs")
      return NextResponse.json({ error: "Invalid Stytch IDs" }, { status: 400 })
    }
    
    // Mark all notifications as read
    const updatedCount = await NotificationService.markAllNotificationsAsRead(userId, orgId)
    
    return NextResponse.json({ count: updatedCount })
  } catch (error) {
    console.error("Error marking all notifications as read:", error)
    return NextResponse.json(
      { error: "Failed to mark all notifications as read" },
      { status: 500 }
    )
  }
}
