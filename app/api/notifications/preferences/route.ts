import type { NextRequest } from "next/server" // Use import type
import { NextResponse } from "next/server"
import { NotificationService } from "@/modules/notification/service"
import { checkPermission } from "@/lib/rbac/checkPermission"
import { z } from "zod"
import { bulkUpdateNotificationPreferencesSchema } from "@/modules/notification/model"
import { getUserIdFromStytchId, getOrgIdFromStytchId } from "@/lib/utils/userUtils"

/**
 * GET /api/notifications/preferences
 * 
 * Get notification preferences for the current user
 */
export async function GET(req: NextRequest) {
  try {
    const memberId = req.headers.get('X-Stytch-Member-Id')
    const stytchOrgId = req.headers.get('X-Stytch-Org-Id')
    const sessionToken = req.headers.get('X-Stytch-Session-Token')
    const sessionJwt = req.headers.get('X-Stytch-Session-JWT')

    console.log('GET /api/notifications/preferences')
    console.log('Member ID from header:', memberId)
    console.log('Stytch Org ID from header:', stytchOrgId)

    // Check permission
    const hasPermission = await checkPermission(
      { session_token: sessionToken, session_jwt: sessionJwt },
      stytchOrgId,
      'read',
      'notification'
    )

    if (!hasPermission) {
      console.warn(`RBAC check failed: User ${memberId} does not have 'read' permission on 'notification' in org ${stytchOrgId}`)
      return NextResponse.json({ error: "Forbidden" }, { status: 403 })
    }

    // Check if memberId and stytchOrgId are available
    if (!memberId || !stytchOrgId) {
      console.error("Missing required headers: memberId or stytchOrgId")
      return NextResponse.json({ error: "Missing required headers" }, { status: 400 })
    }

    // Get user and organization IDs from Stytch IDs
    const userId = await getUserIdFromStytchId(memberId)
    const orgId = await getOrgIdFromStytchId(stytchOrgId)
    
    if (!userId || !orgId) {
      console.error("Failed to find user or organization with the provided Stytch IDs")
      return NextResponse.json({ error: "Invalid Stytch IDs" }, { status: 400 })
    }
    
    // Get notification preferences
    const preferences = await NotificationService.getNotificationPreferences(userId, orgId)
    
    // Add cache headers
    return NextResponse.json({ preferences }, { // Ensure response matches expected structure if needed
      status: 200,
      headers: {
        'Content-Type': 'application/json',
        'Cache-Control': 'private, max-age=300', // Cache for 5 minutes
      },
    });
  } catch (error) {
    console.error("Error fetching notification preferences:", error)
    return NextResponse.json(
      { error: "Failed to fetch notification preferences" },
      { status: 500 }
    )
  }
}

/**
 * PUT /api/notifications/preferences
 * 
 * Update notification preferences for the current user
 */
export async function PUT(req: NextRequest) {
  try {
    const memberId = req.headers.get('X-Stytch-Member-Id')
    const stytchOrgId = req.headers.get('X-Stytch-Org-Id')
    const sessionToken = req.headers.get('X-Stytch-Session-Token')
    const sessionJwt = req.headers.get('X-Stytch-Session-JWT')

    console.log('PUT /api/notifications/preferences')
    console.log('Member ID from header:', memberId)
    console.log('Stytch Org ID from header:', stytchOrgId)

    // Check permission
    const hasPermission = await checkPermission(
      { session_token: sessionToken, session_jwt: sessionJwt },
      stytchOrgId,
      'manage_preferences',
      'notification'
    )

    if (!hasPermission) {
      console.warn(`RBAC check failed: User ${memberId} does not have 'manage_preferences' permission on 'notification' in org ${stytchOrgId}`)
      return NextResponse.json({ error: "Forbidden" }, { status: 403 })
    }

    // Check if memberId and stytchOrgId are available
    if (!memberId || !stytchOrgId) {
      console.error("Missing required headers: memberId or stytchOrgId")
      return NextResponse.json({ error: "Missing required headers" }, { status: 400 })
    }

    // Parse and validate request body
    try {
      const body = await req.json()
      
      // Get user and organization IDs from Stytch IDs
      const userId = await getUserIdFromStytchId(memberId)
      const orgId = await getOrgIdFromStytchId(stytchOrgId)
      
      if (!userId || !orgId) {
        console.error("Failed to find user or organization with the provided Stytch IDs")
        return NextResponse.json({ error: "Invalid Stytch IDs" }, { status: 400 })
      }
      
      // Add memberId and organizationId to the body
      const data = {
        ...body,
        userId,
        organizationId: orgId,
      }
      
      const validatedData = bulkUpdateNotificationPreferencesSchema.parse(data)
      
      // Update notification preferences
      const updatedPreferences = await NotificationService.bulkUpdateNotificationPreferences(validatedData)
      
      return NextResponse.json(updatedPreferences)
    } catch (validationError) {
      if (validationError instanceof z.ZodError) {
        return NextResponse.json(
          { error: "Invalid request body", details: validationError.format() },
          { status: 400 }
        )
      }
      throw validationError
    }
  } catch (error) {
    console.error("Error updating notification preferences:", error)
    return NextResponse.json(
      { error: "Failed to update notification preferences" },
      { status: 500 }
    )
  }
}

/**
 * POST /api/notifications/preferences/initialize
 * 
 * Initialize default notification preferences for the current user
 */
export async function POST(req: NextRequest) {
  try {
    const memberId = req.headers.get('X-Stytch-Member-Id')
    const stytchOrgId = req.headers.get('X-Stytch-Org-Id')
    const sessionToken = req.headers.get('X-Stytch-Session-Token')
    const sessionJwt = req.headers.get('X-Stytch-Session-JWT')

    console.log('POST /api/notifications/preferences/initialize')
    console.log('Member ID from header:', memberId)
    console.log('Stytch Org ID from header:', stytchOrgId)

    // Check permission
    const hasPermission = await checkPermission(
      { session_token: sessionToken, session_jwt: sessionJwt },
      stytchOrgId,
      'manage_preferences',
      'notification'
    )

    if (!hasPermission) {
      console.warn(`RBAC check failed: User ${memberId} does not have 'manage_preferences' permission on 'notification' in org ${stytchOrgId}`)
      return NextResponse.json({ error: "Forbidden" }, { status: 403 })
    }

    // Check if memberId and stytchOrgId are available
    if (!memberId || !stytchOrgId) {
      console.error("Missing required headers: memberId or stytchOrgId")
      return NextResponse.json({ error: "Missing required headers" }, { status: 400 })
    }

    // Get user and organization IDs from Stytch IDs
    const userId = await getUserIdFromStytchId(memberId)
    const orgId = await getOrgIdFromStytchId(stytchOrgId)
    
    if (!userId || !orgId) {
      console.error("Failed to find user or organization with the provided Stytch IDs")
      return NextResponse.json({ error: "Invalid Stytch IDs" }, { status: 400 })
    }

    // --- Optimization: Check if preferences already exist ---
    console.log(`Checking existing preferences for userId: ${userId}, orgId: ${orgId}`);
    const existingPreferences = await NotificationService.getNotificationPreferences(userId, orgId);
    
    if (existingPreferences && existingPreferences.length > 0) {
      console.log("Preferences already initialized, returning existing.");
      // Return existing preferences or a simple message
      return NextResponse.json(existingPreferences); 
      // Or: return NextResponse.json({ message: 'Preferences already initialized' }, { status: 200 });
    }
    // --- End Optimization ---
    
    // Initialize default notification preferences only if they don't exist
    console.log("No existing preferences found, initializing defaults...");
    const preferences = await NotificationService.initializeDefaultPreferences(userId, orgId)
    
    return NextResponse.json(preferences)
  } catch (error) {
    console.error("Error initializing notification preferences:", error)
    return NextResponse.json(
      { error: "Failed to initialize notification preferences" },
      { status: 500 }
    )
  }
}
