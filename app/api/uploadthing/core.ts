import { createUploadthing, type FileRouter } from "uploadthing/next";
import { UploadThingError } from "uploadthing/server";
import { getStytchB2BClientAsync } from "@/lib/stytch-b2b";

const f = createUploadthing();

// Auth function to verify user is logged in
const auth = async (req: Request) => {
  try {
    // Get the session from the request headers
    const sessionJwt = req.headers.get("cookie")?.match(/stytch_session_jwt=([^;]+)/)?.[1];
    const sessionToken = req.headers.get("cookie")?.match(/stytch_session=([^;]+)/)?.[1];
    
    if (!sessionJwt && !sessionToken) {
      throw new UploadThingError("No session token found");
    }
    
    // Use the Stytch client to authenticate the session
    const stytchClient = await getStytchB2BClientAsync();
    let memberId: string;
    let organizationId: string;
    
    if (sessionJwt) {
      const response = await stytchClient.sessions.authenticateJwt({ session_jwt: sessionJwt });
      memberId = response.member_session.member_id;
      organizationId = response.member_session.organization_id;
    } else if (sessionToken) {
      const response = await stytchClient.sessions.authenticate({ session_token: sessionToken });
      memberId = response.member_session.member_id;
      organizationId = response.member_session.organization_id;
    } else {
      throw new UploadThingError("Authentication failed");
    }
    
    return { 
      id: memberId,
      organizationId: organizationId
    };
  } catch (error) {
    console.error("Auth error:", error);
    throw new UploadThingError("Authentication failed");
  }
};

// FileRouter for your app, can contain multiple FileRoutes
export const ourFileRouter = {
  // Location photos uploader
  locationPhotos: f({ 
    image: {
      maxFileSize: "8MB",
      maxFileCount: 20,
    }
  })
    .middleware(async ({ req }) => {
      try {
        // This code runs on your server before upload
        const user = await auth(req);
        
        // Extract locationId from the request headers
        const locationId = req.headers.get("x-location-id");
        
        if (!locationId) {
          throw new UploadThingError("Location ID is required");
        }
        
        // Whatever is returned here is accessible in onUploadComplete as `metadata`
        return { 
          userId: user.id,
          organizationId: user.organizationId,
          locationId: locationId,
          uploadedAt: new Date().toISOString()
        };
      } catch (error) {
        console.error("Middleware error:", error);
        throw new UploadThingError("Failed to authenticate upload request");
      }
    })
    .onUploadComplete(async ({ metadata, file }) => {
      // This code RUNS ON YOUR SERVER after upload
      console.log("Upload complete for userId:", metadata.userId);
      console.log("Organization ID:", metadata.organizationId);
      console.log("Location ID:", metadata.locationId);
      console.log("File URL:", file.ufsUrl);
      
      try {
        // Save the file information to the database
        // This would typically be done through an API endpoint
        await fetch('/api/locations/media', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            locationId: metadata.locationId,
            organizationId: metadata.organizationId,
            url: file.ufsUrl,
            type: 'image',
            uploadedBy: metadata.userId,
            title: file.name,
            fileSize: file.size,
            metadata: {
              key: file.key,
              uploadedAt: metadata.uploadedAt
            }
          })
        });
      } catch (error) {
        console.error("Error saving media to database:", error);
        // Continue even if database save fails, to not block the client
      }
      
      // Return data that will be accessible on the client
      return { 
        uploadedBy: metadata.userId,
        organizationId: metadata.organizationId,
        locationId: metadata.locationId,
        url: file.ufsUrl,
        name: file.name,
        size: file.size,
        key: file.key,
        type: "image",
        uploadedAt: metadata.uploadedAt
      };
    }),

  // Floor plans and documents uploader
  locationDocuments: f({
    image: {
      maxFileSize: "8MB",
      maxFileCount: 5,
    },
    pdf: {
      maxFileSize: "16MB",
      maxFileCount: 10,
    },
    "application/vnd.openxmlformats-officedocument.wordprocessingml.document": {
      maxFileSize: "16MB",
      maxFileCount: 10,
    },
    "application/msword": {
      maxFileSize: "16MB",
      maxFileCount: 10,
    },
  })
    .middleware(async ({ req }) => {
      try {
        // This code runs on your server before upload
        const user = await auth(req);
        
        // Extract locationId from the request URL
        const url = new URL(req.url);
        const locationId = url.searchParams.get("locationId");
        
        if (!locationId) {
          throw new UploadThingError("Location ID is required");
        }
        
        // Whatever is returned here is accessible in onUploadComplete as `metadata`
        return { 
          userId: user.id,
          organizationId: user.organizationId,
          locationId: locationId,
          uploadedAt: new Date().toISOString()
        };
      } catch (error) {
        console.error("Middleware error:", error);
        throw new UploadThingError("Failed to authenticate upload request");
      }
    })
    .onUploadComplete(async ({ metadata, file }) => {
      // This code RUNS ON YOUR SERVER after upload
      console.log("Upload complete for userId:", metadata.userId);
      console.log("Organization ID:", metadata.organizationId);
      console.log("Location ID:", metadata.locationId);
      console.log("File URL:", file.ufsUrl);
      
      // Determine file type based on file type
      let fileType = "document";
      if (file.type.includes("image")) {
        fileType = "image";
      } else if (file.type.includes("pdf")) {
        fileType = "pdf";
      } else if (
        file.type.includes("document") ||
        file.type.includes("msword")
      ) {
        fileType = "doc";
      }
      
      try {
        // Save the file information to the database
        // This would typically be done through an API endpoint
        await fetch('/api/locations/media', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            locationId: metadata.locationId,
            organizationId: metadata.organizationId,
            url: file.ufsUrl,
            type: fileType,
            uploadedBy: metadata.userId,
            title: file.name,
            fileSize: file.size,
            metadata: {
              key: file.key,
              uploadedAt: metadata.uploadedAt
            }
          })
        });
      } catch (error) {
        console.error("Error saving media to database:", error);
        // Continue even if database save fails, to not block the client
      }
      
      // Return data that will be accessible on the client
      return { 
        uploadedBy: metadata.userId,
        organizationId: metadata.organizationId,
        locationId: metadata.locationId,
        url: file.ufsUrl,
        name: file.name,
        size: file.size,
        key: file.key,
        type: fileType,
        uploadedAt: metadata.uploadedAt
      };
    }),
} satisfies FileRouter;

export type OurFileRouter = typeof ourFileRouter;
