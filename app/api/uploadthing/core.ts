import { createUploadthing, type FileRouter } from "uploadthing/next";
import { UploadThingError } from "uploadthing/server";
import { getStytchB2BClientAsync } from "@/lib/stytch-b2b";
import { getUserIdFromStytchMemberId } from "@/lib/utils/userUtils";

const f = createUploadthing();

// Auth function to verify user is logged in
const auth = async (req: Request) => {
  try {
    // Get the session from the request headers
    const sessionJwt = req.headers.get("cookie")?.match(/stytch_session_jwt=([^;]+)/)?.[1];
    const sessionToken = req.headers.get("cookie")?.match(/stytch_session=([^;]+)/)?.[1];
    
    if (!sessionJwt && !sessionToken) {
      throw new UploadThingError("No session token found");
    }
    
    // Use the Stytch client to authenticate the session
    const stytchClient = await getStytchB2BClientAsync();
    let memberId: string;
    let organizationId: string;
    
    if (sessionJwt) {
      const response = await stytchClient.sessions.authenticateJwt({ session_jwt: sessionJwt });
      memberId = response.member_session.member_id;
      organizationId = response.member_session.organization_id;
    } else if (sessionToken) {
      const response = await stytchClient.sessions.authenticate({ session_token: sessionToken });
      memberId = response.member_session.member_id;
      organizationId = response.member_session.organization_id;
    } else {
      throw new UploadThingError("Authentication failed");
    }
    
    return { 
      id: memberId,
      organizationId: organizationId
    };
  } catch (error) {
    console.error("Auth error:", error);
    throw new UploadThingError("Authentication failed");
  }
};

// FileRouter for your app, can contain multiple FileRoutes
export const ourFileRouter = {
  // Location photos uploader
  locationPhotos: f({ 
    image: {
      maxFileSize: "8MB",
      maxFileCount: 20,
    }
  })
    .middleware(async ({ req }) => {
      try {
        // This code runs on your server before upload
        const user = await auth(req);
        
        // Extract locationId from the request headers
        const locationId = req.headers.get("x-location-id");
        
        if (!locationId) {
          throw new UploadThingError("Location ID is required");
        }
        
        // Whatever is returned here is accessible in onUploadComplete as `metadata`
        return { 
          userId: user.id,
          organizationId: user.organizationId,
          locationId: locationId,
          uploadedAt: new Date().toISOString()
        };
      } catch (error) {
        console.error("Middleware error:", error);
        throw new UploadThingError("Failed to authenticate upload request");
      }
    })
    .onUploadComplete(async ({ metadata, file, req }) => {
      // This code RUNS ON YOUR SERVER after upload
      console.log("========== UPLOAD COMPLETE HANDLER STARTED ==========");
      console.log("Upload complete for userId:", metadata.userId);
      console.log("Organization ID:", metadata.organizationId);
      console.log("Location ID:", metadata.locationId);
      console.log("File URL:", file.ufsUrl);
      console.log("File type:", file.type);
      console.log("File name:", file.name);
      console.log("File size:", file.size);
      
      try {
        // Convert Stytch member ID to database user ID
        const databaseUserId = await getUserIdFromStytchMemberId(metadata.userId);
        
        if (!databaseUserId) {
          console.error(`Could not find database user ID for Stytch member ID: ${metadata.userId}`);
          // Continue with the upload even if we can't find the user ID
          // This ensures the upload doesn't fail for the user
        }
        
        // Get the session information from the request cookies
        const cookies = req.headers.get("cookie") || "";
        const sessionJwt = cookies.match(/stytch_session_jwt=([^;]+)/)?.[1];
        const sessionToken = cookies.match(/stytch_session=([^;]+)/)?.[1];
        
        // Check if we have valid authentication tokens
        if (!sessionJwt && !sessionToken) {
          console.error("No authentication tokens found in cookies");
          throw new Error("Authentication failed - No session tokens found");
        }
        
        // Construct the absolute URL for the API endpoint
        // In Next.js server components/API routes, we need to use absolute URLs
        const protocol = process.env.NODE_ENV === 'production' ? 'https' : 'http';
        const host = req.headers.get('host') || 'localhost:3000';
        const apiUrl = `${protocol}://${host}/api/locations/media`;
        
        // Prepare headers for the API request
        const headers = { 
          'Content-Type': 'application/json',
          // Include the required authentication headers
          'X-Stytch-Org-Id': metadata.organizationId,
          'X-Stytch-Member-Id': metadata.userId,
          ...(sessionToken ? { 'X-Stytch-Session-Token': sessionToken } : {}),
          ...(sessionJwt ? { 'X-Stytch-Session-JWT': sessionJwt } : {})
        };
        
        console.log("Authentication headers:", {
          'X-Stytch-Org-Id': metadata.organizationId,
          'X-Stytch-Member-Id': metadata.userId,
          'Has Session Token': !!sessionToken,
          'Has Session JWT': !!sessionJwt
        });
        
        // Prepare the request body
        const requestBody = {
          locationId: metadata.locationId,
          organizationId: metadata.organizationId,
          url: file.ufsUrl,
          type: 'image',
          uploadedBy: databaseUserId || metadata.userId, // Use database ID if available, fall back to Stytch ID
          title: file.name,
          fileSize: file.size,
          metadata: {
            key: file.key,
            uploadedAt: metadata.uploadedAt,
            stytchMemberId: metadata.userId // Store the original Stytch ID for reference
          }
        };
        
        console.log(`Saving media to database for location ${metadata.locationId}...`);
        console.log(`API URL: ${apiUrl}`);
        console.log(`Headers: ${JSON.stringify(headers, null, 2)}`);
        console.log(`Request body: ${JSON.stringify(requestBody, null, 2)}`);
        
        // Save the file information to the database through the API endpoint
        const response = await fetch(apiUrl, {
          method: 'POST',
          headers,
          body: JSON.stringify(requestBody)
        });
        
        // Check if the request was successful
        if (!response.ok) {
          const errorText = await response.text();
          console.error(`Failed to save media to database. Status: ${response.status}, Error: ${errorText}`);
        } else {
          const responseData = await response.json();
          console.log(`Media saved successfully to database. ID: ${responseData.id}`);
        }
      } catch (error) {
        console.error("Error saving media to database:", error);
        // Continue even if database save fails, to not block the client
      }
      
      // Return data that will be accessible on the client
      return { 
        uploadedBy: metadata.userId,
        organizationId: metadata.organizationId,
        locationId: metadata.locationId,
        url: file.ufsUrl,
        name: file.name,
        size: file.size,
        key: file.key,
        type: "image",
        uploadedAt: metadata.uploadedAt
      };
    }),

  // Floor plans and documents uploader
  locationDocuments: f({
    image: {
      maxFileSize: "8MB",
      maxFileCount: 5,
    },
    pdf: {
      maxFileSize: "16MB",
      maxFileCount: 10,
    },
    "application/vnd.openxmlformats-officedocument.wordprocessingml.document": {
      maxFileSize: "16MB",
      maxFileCount: 10,
    },
    "application/msword": {
      maxFileSize: "16MB",
      maxFileCount: 10,
    },
  })
    .middleware(async ({ req }) => {
      try {
        // This code runs on your server before upload
        const user = await auth(req);
        
        // Extract locationId from the request headers or URL
        const locationIdFromHeader = req.headers.get("x-location-id");
        const url = new URL(req.url);
        const locationIdFromQuery = url.searchParams.get("locationId");
        const locationId = locationIdFromHeader || locationIdFromQuery;
        
        if (!locationId) {
          throw new UploadThingError("Location ID is required");
        }
        
        // Whatever is returned here is accessible in onUploadComplete as `metadata`
        return { 
          userId: user.id,
          organizationId: user.organizationId,
          locationId: locationId,
          uploadedAt: new Date().toISOString()
        };
      } catch (error) {
        console.error("Middleware error:", error);
        throw new UploadThingError("Failed to authenticate upload request");
      }
    })
    .onUploadComplete(async ({ metadata, file, req }) => {
      // This code RUNS ON YOUR SERVER after upload
      console.log("========== DOCUMENT UPLOAD COMPLETE HANDLER STARTED ==========");
      console.log("Upload complete for userId:", metadata.userId);
      console.log("Organization ID:", metadata.organizationId);
      console.log("Location ID:", metadata.locationId);
      console.log("File URL:", file.ufsUrl);
      console.log("File type:", file.type);
      console.log("File name:", file.name);
      console.log("File size:", file.size);
      
      // Determine file type based on file type
      let fileType = "document";
      if (file.type.includes("image")) {
        fileType = "image";
      } else if (file.type.includes("pdf")) {
        fileType = "pdf";
      } else if (
        file.type.includes("document") ||
        file.type.includes("msword")
      ) {
        fileType = "doc";
      }
      
      try {
        // Convert Stytch member ID to database user ID
        const databaseUserId = await getUserIdFromStytchMemberId(metadata.userId);
        
        if (!databaseUserId) {
          console.error(`Could not find database user ID for Stytch member ID: ${metadata.userId}`);
          // Continue with the upload even if we can't find the user ID
          // This ensures the upload doesn't fail for the user
        }
        
        // Get the session information from the request cookies
        const cookies = req.headers.get("cookie") || "";
        const sessionJwt = cookies.match(/stytch_session_jwt=([^;]+)/)?.[1];
        const sessionToken = cookies.match(/stytch_session=([^;]+)/)?.[1];
        
        // Check if we have valid authentication tokens
        if (!sessionJwt && !sessionToken) {
          console.error("No authentication tokens found in cookies");
          throw new Error("Authentication failed - No session tokens found");
        }
        
        // Construct the absolute URL for the API endpoint
        // In Next.js server components/API routes, we need to use absolute URLs
        const protocol = process.env.NODE_ENV === 'production' ? 'https' : 'http';
        const host = req.headers.get('host') || 'localhost:3000';
        const apiUrl = `${protocol}://${host}/api/locations/media`;
        
        // Prepare headers for the API request
        const headers = { 
          'Content-Type': 'application/json',
          // Include the required authentication headers
          'X-Stytch-Org-Id': metadata.organizationId,
          'X-Stytch-Member-Id': metadata.userId,
          ...(sessionToken ? { 'X-Stytch-Session-Token': sessionToken } : {}),
          ...(sessionJwt ? { 'X-Stytch-Session-JWT': sessionJwt } : {})
        };
        
        console.log("Authentication headers for document upload:", {
          'X-Stytch-Org-Id': metadata.organizationId,
          'X-Stytch-Member-Id': metadata.userId,
          'Has Session Token': !!sessionToken,
          'Has Session JWT': !!sessionJwt
        });
        
        // Prepare the request body
        const requestBody = {
          locationId: metadata.locationId,
          organizationId: metadata.organizationId,
          url: file.ufsUrl,
          type: fileType,
          uploadedBy: databaseUserId || metadata.userId, // Use database ID if available, fall back to Stytch ID
          title: file.name,
          fileSize: file.size,
          metadata: {
            key: file.key,
            uploadedAt: metadata.uploadedAt,
            stytchMemberId: metadata.userId // Store the original Stytch ID for reference
          }
        };
        
        console.log(`Saving document to database for location ${metadata.locationId}...`);
        console.log(`API URL: ${apiUrl}`);
        console.log(`Headers: ${JSON.stringify(headers, null, 2)}`);
        console.log(`Request body: ${JSON.stringify(requestBody, null, 2)}`);
        
        // Save the file information to the database through the API endpoint
        const response = await fetch(apiUrl, {
          method: 'POST',
          headers,
          body: JSON.stringify(requestBody)
        });
        
        // Check if the request was successful
        if (!response.ok) {
          const errorText = await response.text();
          console.error(`Failed to save document to database. Status: ${response.status}, Error: ${errorText}`);
        } else {
          const responseData = await response.json();
          console.log(`Document saved successfully to database. ID: ${responseData.id}`);
        }
      } catch (error) {
        console.error("Error saving media to database:", error);
        // Continue even if database save fails, to not block the client
      }
      
      // Return data that will be accessible on the client
      return { 
        uploadedBy: metadata.userId,
        organizationId: metadata.organizationId,
        locationId: metadata.locationId,
        url: file.ufsUrl,
        name: file.name,
        size: file.size,
        key: file.key,
        type: fileType,
        uploadedAt: metadata.uploadedAt
      };
    }),
} satisfies FileRouter;

export type OurFileRouter = typeof ourFileRouter;
