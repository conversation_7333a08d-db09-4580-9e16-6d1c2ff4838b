import { type NextRequest, NextResponse } from "next/server"
import crypto from "node:crypto"
import { 
  getSubscriptionByPolarId, 
  createSubscription, 
  updateSubscription 
} from "@/modules/subscription/service"
import { 
  getInvoiceByPolarId, 
  createInvoice 
} from "@/modules/subscription/service"
import { getOrganizationByPolarCustomerId } from "@/modules/organization/service"

export async function POST(request: NextRequest) {
  try {
    const body = await request.text()
    const signature = request.headers.get("polar-signature")

    if (!signature) {
      return NextResponse.json({ error: "Missing Polar signature" }, { status: 401 })
    }

    // Verify webhook signature
    const secret = process.env.POLAR_WEBHOOK_SECRET

    if (!secret) {
      console.error("Missing POLAR_WEBHOOK_SECRET environment variable")
      return NextResponse.json({ error: "Server configuration error" }, { status: 500 })
    }

    const hmac = crypto.createHmac("sha256", secret)
    hmac.update(body)
    const expectedSignature = hmac.digest("hex")

    if (signature !== expectedSignature) {
      return NextResponse.json({ error: "Invalid signature" }, { status: 401 })
    }

    // Process webhook event
    const event = JSON.parse(body)
    const eventType = event.type

    console.log(`Processing Polar webhook event: ${eventType}`)

    switch (eventType) {
      case "subscription.created":
      case "subscription.updated": {
        const subscription = event.data.subscription
        const organization = await getOrganizationByPolarCustomerId(subscription.customer_id)

        if (!organization) {
          console.error(`Organization not found for Polar customer ID: ${subscription.customer_id}`)
          return NextResponse.json({ error: "Organization not found" }, { status: 404 })
        }

        const existingSubscription = await getSubscriptionByPolarId(subscription.id)

        if (existingSubscription) {
          // Update existing subscription
          await updateSubscription(existingSubscription.id, {
            tier: subscription.plan.name.toLowerCase(),
            status: subscription.status,
            currentPeriodStart: new Date(subscription.current_period_start),
            currentPeriodEnd: new Date(subscription.current_period_end),
            cancelAtPeriodEnd: subscription.cancel_at_period_end,
            metadata: subscription.metadata || {}
          })
        } else {
          // Create new subscription
          await createSubscription({
            organizationId: organization.id,
            polarSubscriptionId: subscription.id,
            tier: subscription.plan.name.toLowerCase(),
            status: subscription.status,
            currentPeriodStart: new Date(subscription.current_period_start),
            currentPeriodEnd: new Date(subscription.current_period_end),
            cancelAtPeriodEnd: subscription.cancel_at_period_end,
            metadata: subscription.metadata || {},
            quantity: subscription.quantity || 1
          })
        }

        // Update organization subscription details
        await updateOrganizationSubscription(organization.id, subscription)
        break
      }

      case "invoice.created":
      case "invoice.paid": {
        const invoice = event.data.invoice
        const organization = await getOrganizationByPolarCustomerId(invoice.customer_id)

        if (!organization) {
          console.error(`Organization not found for Polar customer ID: ${invoice.customer_id}`)
          return NextResponse.json({ error: "Organization not found" }, { status: 404 })
        }

        const existingInvoice = await getInvoiceByPolarId(invoice.id)

        if (!existingInvoice) {
          // Get subscription if available
          let subscriptionId: string | null = null; // Initialize as null instead of undefined
          if (invoice.subscription_id) {
            const subscription = await getSubscriptionByPolarId(invoice.subscription_id);
            if (subscription) {
              subscriptionId = subscription.id
            }
          }

          // Create new invoice
          await createInvoice({
            organizationId: organization.id,
            subscriptionId,
            polarInvoiceId: invoice.id,
            amount: invoice.amount_due,
            currency: invoice.currency,
            status: invoice.status,
            invoiceUrl: invoice.hosted_invoice_url,
            invoiceNumber: invoice.number,
            invoiceDate: new Date(invoice.created),
            paidAt: invoice.status === "paid" ? new Date() : null // Changed undefined to null
          });
        }
        break
      }

      default:
        console.log(`Unhandled Polar webhook event type: ${eventType}`)
    }

    return NextResponse.json({ success: true })
  } catch (error) {
    console.error("Polar webhook error:", error)
    return NextResponse.json({ error: "Internal server error" }, { status: 500 })
  }
}

/**
 * Update organization subscription details
 * @param organizationId Organization ID
 * @param subscription Polar subscription data
 */
async function updateOrganizationSubscription(
  organizationId: string, 
  subscription: {
    plan: { name: string };
    status: string;
    current_period_end: string | number;
  }
) {
  try {
    const { updateOrganization } = await import("@/modules/organization/service")
    
    // Map subscription tier to organization limits
    const tierLimits = getTierLimits(subscription.plan.name.toLowerCase())
    
    await updateOrganization(organizationId, {
      subscriptionTier: subscription.plan.name.toLowerCase(),
      subscriptionStatus: subscription.status,
      subscriptionValidUntil: new Date(subscription.current_period_end),
      ...tierLimits
    })
  } catch (error) {
    console.error("Error updating organization subscription:", error)
  }
}

/**
 * Get organization limits based on subscription tier
 * @param tier Subscription tier
 * @returns Organization limits
 */
function getTierLimits(tier: string) {
  switch (tier) {
    case "basic":
      return {
        maxProjects: 5,
        maxLocationsPerProject: 50,
        maxTeamMembers: 5,
        maxStorage: 10 // GB
      }
    case "professional":
      return {
        maxProjects: 20,
        maxLocationsPerProject: 200,
        maxTeamMembers: 15,
        maxStorage: 50 // GB
      }
    case "enterprise":
      return {
        maxProjects: 100,
        maxLocationsPerProject: 1000,
        maxTeamMembers: 50,
        maxStorage: 250 // GB
      }
    default:
      return {
        maxProjects: 5,
        maxLocationsPerProject: 50,
        maxTeamMembers: 5,
        maxStorage: 10 // GB
      }
  }
}
