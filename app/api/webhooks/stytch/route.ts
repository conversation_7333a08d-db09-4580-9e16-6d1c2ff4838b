import { type NextRequest, NextResponse } from "next/server"
import crypto from "crypto"

export async function POST(request: NextRequest) {
  try {
    const body = await request.text()
    const signature = request.headers.get("stytch-signature")

    if (!signature) {
      return NextResponse.json({ error: "Missing Stytch signature" }, { status: 401 })
    }

    // Verify webhook signature
    const secret = process.env.STYTCH_WEBHOOK_SECRET

    if (!secret) {
      console.error("Missing STYTCH_WEBHOOK_SECRET environment variable")
      return NextResponse.json({ error: "Server configuration error" }, { status: 500 })
    }

    const hmac = crypto.createHmac("sha256", secret)
    hmac.update(body)
    const expectedSignature = hmac.digest("hex")

    if (signature !== expectedSignature) {
      return NextResponse.json({ error: "Invalid signature" }, { status: 401 })
    }

    // Process webhook event
    const event = JSON.parse(body)

    switch (event.event_type) {
      case "user.created":
        // Handle user creation
        console.log("User created:", event.user.user_id)
        break
      case "user.deleted":
        // Handle user deletion
        console.log("User deleted:", event.user.user_id)
        break
      // Add more event handlers as needed
    }

    return NextResponse.json({ success: true })
  } catch (error) {
    console.error("Stytch webhook error:", error)
    return NextResponse.json({ error: "Internal server error" }, { status: 500 })
  }
}
