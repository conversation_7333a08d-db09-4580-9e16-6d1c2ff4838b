import { MapPin } from "lucide-react"
import { LoginOrSignupDiscoveryForm } from "@/components/auth/login-or-signup-discovery-form"

export default function LoginPage() {
  return (
    <div className="flex min-h-screen items-center justify-center bg-muted/40 p-4">
      <div className="w-full max-w-md space-y-6">
        <div className="flex flex-col items-center space-y-2 text-center">
          <div className="flex items-center gap-2">
            <MapPin className="h-6 w-6 text-primary" />
            <span className="text-xl font-bold">Scene-o-matic</span>
          </div>
          <h1 className="text-2xl font-bold">Welcome to Scene-o-matic</h1>
          <p className="text-muted-foreground">Sign in to your account to continue</p>
        </div>
        
        <div className="bg-white p-6 rounded-lg shadow-sm">
          <LoginOrSignupDiscoveryForm />
        </div>
        
        <div className="text-center text-sm">
          By signing in, you agree to our terms of service and privacy policy.
        </div>
      </div>
    </div>
  )
}
