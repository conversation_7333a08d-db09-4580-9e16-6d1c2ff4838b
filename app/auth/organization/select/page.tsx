"use client"

import { useState, useEffect } from "react"
import { use<PERSON><PERSON><PERSON> } from "next/navigation"
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>ader2 } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, CardContent, CardDescription, Card<PERSON>ooter, CardHeader, CardTitle } from "@/components/ui/card"
import Link from "next/link"
import { useAuth } from "@/hooks/use-auth"

type Organization = {
  id: string
  name: string
  slug: string
}

export default function SelectOrganizationPage() {
  const router = useRouter()
  // const { user } = useAuth() // Uncomment if user data is needed
  useAuth() // Just to ensure auth context is available
  const [organizations, setOrganizations] = useState<Organization[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [selectedOrgId, setSelectedOrgId] = useState<string | null>(null)
  const [isSubmitting, setIsSubmitting] = useState(false)

  // Load discovered organizations from localStorage
  useEffect(() => {
    try {
      setIsLoading(true)
      setError(null)

      // Check if we have discovered organizations in localStorage
      const discoveredOrgsJson = localStorage.getItem("stytch_discovered_organizations")
      
      if (discoveredOrgsJson) {
        const discoveredOrgs = JSON.parse(discoveredOrgsJson)
        
        // Transform the discovered organizations to match our expected format
        const formattedOrgs = discoveredOrgs.map((org: { 
          organization_id: string; 
          organization_name: string; 
          organization_slug: string;
        }) => ({
          id: org.organization_id,
          name: org.organization_name,
          slug: org.organization_slug,
        }))
        
        setOrganizations(formattedOrgs)
        console.log("Loaded discovered organizations from localStorage:", formattedOrgs)
      } else {
        // If no discovered organizations in localStorage, fetch from API
        fetchOrganizationsFromAPI()
      }
    } catch (err) {
      console.error("Error loading discovered organizations:", err)
      // Fallback to API if localStorage parsing fails
      fetchOrganizationsFromAPI()
    } finally {
      setIsLoading(false)
    }
  }, [])
  
  // Fetch organizations from API as fallback
  const fetchOrganizationsFromAPI = async () => {
    try {
      setIsLoading(true)
      setError(null)

      const response = await fetch("/api/organizations")
      const data = await response.json()

      if (!response.ok) {
        throw new Error(data.error || "Failed to fetch organizations")
      }

      setOrganizations(data.data || [])
    } catch (err) {
      console.error("Error fetching organizations:", err)
      setError("Failed to load organizations")
    } finally {
      setIsLoading(false)
    }
  }

  const handleContinue = async () => {
    if (!selectedOrgId) return

    try {
      setIsSubmitting(true)
      setError(null)

      // Get the intermediate session token from localStorage
      const ist = localStorage.getItem("stytch_intermediate_session_token")
      
      if (ist) {
        // If we have an intermediate session token, exchange it for a session
        try {
          const exchangeResponse = await fetch("/api/auth/exchange-ist", {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
            },
            body: JSON.stringify({
              intermediate_session_token: ist,
              organization_id: selectedOrgId,
            }),
          })
          
          if (!exchangeResponse.ok) {
            const data = await exchangeResponse.json()
            throw new Error(data.error || "Failed to exchange intermediate session token")
          }
          
          // Clear localStorage items
          localStorage.removeItem("stytch_intermediate_session_token")
          localStorage.removeItem("stytch_discovered_organizations")

          // Find the slug for the selected organization
          const selectedOrg = organizations.find(org => org.id === selectedOrgId);
          if (selectedOrg?.slug) {
            // Redirect to the dynamic organization dashboard
            router.push(`/organizations/${selectedOrg.slug}`)
          } else {
            // Fallback or error handling if slug not found (shouldn't happen ideally)
            console.error("Selected organization slug not found after IST exchange.");
            setError("Could not find organization details to redirect.");
          }
          return
        } catch (exchangeError) {
          console.error("Error exchanging intermediate session token:", exchangeError)
          // Fall back to regular organization switch if exchange fails
        }
      }
      
      // Regular organization switch (for existing sessions)
      const response = await fetch(`/api/organizations/${selectedOrgId}/switch`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
      })

      if (!response.ok) {
        const data = await response.json()
        throw new Error(data.error || "Failed to select organization")
      }
      
      const result = await response.json();

      // Use the slug from the API response or find it in state
      const slug = result.organization?.slug || organizations.find(org => org.id === selectedOrgId)?.slug;

      if (slug) {
         // Redirect to the dynamic organization dashboard
        router.push(`/organizations/${slug}`)
      } else {
         // Fallback or error handling if slug not found
        console.error("Selected organization slug not found after switch API call.");
        setError("Could not find organization details to redirect.");
      }
    } catch (err) {
      console.error("Error selecting organization:", err)
      setError(err instanceof Error ? err.message : "An unexpected error occurred")
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <div className="flex min-h-screen items-center justify-center bg-muted/40 p-4">
      <div className="w-full max-w-md space-y-6">
        <div className="flex flex-col items-center space-y-2 text-center">
          <div className="flex items-center gap-2">
            <MapPin className="h-6 w-6 text-primary" />
            <span className="text-xl font-bold">Scene-o-matic</span>
          </div>
          <h1 className="text-2xl font-bold">Select an Organization</h1>
          <p className="text-muted-foreground">Choose an organization to continue</p>
        </div>
        
        <Card>
          <CardHeader>
            <CardTitle>Your Organizations</CardTitle>
            <CardDescription>Select an organization to access its projects</CardDescription>
          </CardHeader>
          <CardContent>
            {isLoading ? (
              <div className="flex justify-center py-8">
                <Loader2 className="h-8 w-8 animate-spin text-primary" />
              </div>
            ) : error ? (
              <div className="text-center py-8">
                <p className="text-red-500">{error}</p>
                <Button
                  variant="outline"
                  className="mt-4"
                  onClick={() => window.location.reload()}
                >
                  Try Again
                </Button>
              </div>
            ) : organizations.length === 0 ? (
              <div className="text-center py-8">
                <p className="text-muted-foreground">You don&apos;t have any organizations yet.</p>
                <Button
                  className="mt-4"
                  onClick={() => router.push("/auth/organization/create")}
                >
                  Create an Organization
                </Button>
              </div>
            ) : (
              <div className="space-y-4">
                {organizations.map((org) => (
                  <button
                    key={org.id}
                    className={`w-full flex items-center justify-between p-4 rounded-lg border cursor-pointer transition-colors text-left ${
                      selectedOrgId === org.id
                        ? "border-primary bg-primary/5"
                        : "border-border hover:border-primary/50"
                    }`}
                    onClick={() => setSelectedOrgId(org.id)}
                    aria-pressed={selectedOrgId === org.id}
                    type="button"
                  >
                    <div>
                      <h3 className="font-medium">{org.name}</h3>
                      <p className="text-sm text-muted-foreground">{org.slug}</p>
                    </div>
                    {selectedOrgId === org.id && (
                      <ArrowRight className="h-5 w-5 text-primary" />
                    )}
                  </button>
                ))}
              </div>
            )}
          </CardContent>
          {organizations.length > 0 && (
            <CardFooter className="flex justify-between">
              <Button
                variant="outline"
                onClick={() => router.push("/auth/organization/create")}
              >
                Create New
              </Button>
              <Button
                onClick={handleContinue}
                disabled={!selectedOrgId || isSubmitting}
              >
                {isSubmitting ? "Loading..." : "Continue"}
              </Button>
            </CardFooter>
          )}
        </Card>
        
        <div className="text-center">
          <Link href="/auth/logout" className="text-sm text-muted-foreground hover:text-primary">
            Sign out
          </Link>
        </div>
      </div>
    </div>
  )
}
