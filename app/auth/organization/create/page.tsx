"use client"

import { useState } from "react"
import { use<PERSON><PERSON><PERSON> } from "next/navigation"
import { MapPin } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import Link from "next/link"

export default function CreateOrganizationPage() {
  const router = useRouter()
  const [name, setName] = useState("")
  const [slug, setSlug] = useState("")
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  // Generate a slug from the organization name
  const handleNameChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value
    setName(value)
    
    // Generate a slug from the name
    const generatedSlug = value
      .toLowerCase()
      .replace(/[^a-z0-9]+/g, "-")
      .replace(/^-|-$/g, "")
    
    setSlug(generatedSlug)
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsLoading(true)
    setError(null)

    try {
      // Validate inputs
      if (!name) {
        throw new Error("Organization name is required")
      }

      if (!slug) {
        throw new Error("Organization slug is required")
      }

      // Create the organization
      const response = await fetch("/api/organizations", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ name, slug }),
      })

      const data = await response.json()

      if (!response.ok) {
        throw new Error(data.error || "Failed to create organization")
      }

      // Redirect to the dashboard
      router.push("/")
    } catch (err) {
      setError(err instanceof Error ? err.message : "An unexpected error occurred")
      console.error(err)
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <div className="flex min-h-screen items-center justify-center bg-muted/40 p-4">
      <div className="w-full max-w-md space-y-6">
        <div className="flex flex-col items-center space-y-2 text-center">
          <div className="flex items-center gap-2">
            <MapPin className="h-6 w-6 text-primary" />
            <span className="text-xl font-bold">Scene-o-matic</span>
          </div>
          <h1 className="text-2xl font-bold">Create an Organization</h1>
          <p className="text-muted-foreground">Set up your production company</p>
        </div>
        
        <Card>
          <CardHeader>
            <CardTitle>Organization Details</CardTitle>
            <CardDescription>Enter your organization information</CardDescription>
          </CardHeader>
          <CardContent>
            <form onSubmit={handleSubmit}>
              <div className="grid gap-4">
                <div className="grid gap-2">
                  <Label htmlFor="name">Organization Name</Label>
                  <Input 
                    id="name" 
                    placeholder="Acme Productions" 
                    value={name}
                    onChange={handleNameChange}
                    required 
                  />
                </div>
                <div className="grid gap-2">
                  <Label htmlFor="slug">Organization Slug</Label>
                  <Input 
                    id="slug" 
                    placeholder="acme-productions" 
                    value={slug}
                    onChange={(e) => setSlug(e.target.value)}
                    required 
                  />
                  <p className="text-xs text-muted-foreground">
                    This will be used in URLs and cannot be changed later.
                  </p>
                </div>
                {error && (
                  <div className="text-sm text-red-500">{error}</div>
                )}
                <Button type="submit" className="w-full" disabled={isLoading}>
                  {isLoading ? "Creating..." : "Create Organization"}
                </Button>
              </div>
            </form>
          </CardContent>
          <CardFooter className="flex justify-center">
            <Link href="/" className="text-sm text-muted-foreground hover:text-primary">
              Cancel and return to dashboard
            </Link>
          </CardFooter>
        </Card>
      </div>
    </div>
  )
}
