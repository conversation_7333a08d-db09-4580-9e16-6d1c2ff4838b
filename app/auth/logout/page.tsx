"use client"

import { useEffect } from "react"
import { useRouter } from "next/navigation"
import { Loader2 } from "lucide-react"
import { useAuth } from "@/hooks/use-auth"

export default function LogoutPage() {
  const router = useRouter()
  const { logout } = useAuth()

  useEffect(() => {
    async function handleLogout() {
      try {
        await logout()
        // Redirect to login page after logout
        router.push("/auth/login")
      } catch (error) {
        console.error("Logout error:", error)
        // If logout fails, still redirect to login page
        router.push("/auth/login")
      }
    }

    handleLogout()
  }, [logout, router])

  return (
    <div className="flex min-h-screen items-center justify-center bg-muted/40">
      <div className="flex flex-col items-center gap-4">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
        <p className="text-lg font-medium">Signing out...</p>
      </div>
    </div>
  )
}
