import Link from "next/link"
import { But<PERSON> } from "@/components/ui/button"
import { ArrowRight, CheckCircle, MapPin, Film, Calendar, ClipboardCheck, Users } from "lucide-react"

export default function HomePage() {
  return (
    <div className="flex flex-col min-h-screen">
      {/* Navigation */}
      <header className="border-b bg-background">
        <div className="container flex h-16 items-center justify-between px-4 md:px-6">
          <div className="flex items-center gap-2">
            <MapPin className="h-6 w-6 text-primary" />
            <span className="text-xl font-bold">Scene-o-matic</span>
          </div>
          <nav className="hidden md:flex items-center gap-6">
            <Link href="#features" className="text-sm font-medium hover:underline underline-offset-4">
              Features
            </Link>
            <Link href="#use-cases" className="text-sm font-medium hover:underline underline-offset-4">
              Use Cases
            </Link>
            <Link href="#pricing" className="text-sm font-medium hover:underline underline-offset-4">
              Pricing
            </Link>
          </nav>
          <div className="flex items-center gap-4">
            <Link href="/auth/login" className="text-sm font-medium hover:underline underline-offset-4">
              Log in
            </Link>
            <Button asChild>
              <Link href="/auth/register">Get Started</Link>
            </Button>
          </div>
        </div>
      </header>

      <main className="flex-1">
        {/* Hero Section */}
        <section className="w-full py-12 md:py-24 lg:py-32 xl:py-48 bg-background">
          <div className="container px-4 md:px-6">
            <div className="grid gap-6 lg:grid-cols-2 lg:gap-12 xl:grid-cols-2">
              <div className="flex flex-col justify-center space-y-4">
                <div className="space-y-2">
                  <h1 className="text-3xl font-bold tracking-tighter sm:text-4xl md:text-5xl lg:text-6xl/none">
                    Streamline Location Management for Film Production
                  </h1>
                  <p className="max-w-[600px] text-muted-foreground md:text-xl">
                    Discover, organize, and manage filming locations with ease. The all-in-one platform for location
                    scouts, production managers, and film crews.
                  </p>
                </div>
                <div className="flex flex-col gap-2 min-[400px]:flex-row">
                  <Button size="lg" asChild>
                    <Link href="/auth/register">
                      Start Free Trial
                      <ArrowRight className="ml-2 h-4 w-4" />
                    </Link>
                  </Button>
                  <Button size="lg" variant="outline" asChild>
                    <Link href="#demo">Watch Demo</Link>
                  </Button>
                </div>
              </div>
              <div className="flex items-center justify-center">
                <div className="relative w-full h-[350px] md:h-[400px] lg:h-[500px] rounded-lg overflow-hidden shadow-xl">
                  <img
                    src="/film-location-dashboard.png"
                    alt="Scene-o-matic dashboard preview"
                    className="object-cover w-full h-full"
                  />
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* Features Section */}
        <section id="features" className="w-full py-12 md:py-24 lg:py-32 bg-muted/40">
          <div className="container px-4 md:px-6">
            <div className="flex flex-col items-center justify-center space-y-4 text-center">
              <div className="space-y-2">
                <div className="inline-block rounded-lg bg-primary px-3 py-1 text-sm text-primary-foreground">
                  Features
                </div>
                <h2 className="text-3xl font-bold tracking-tighter md:text-4xl">
                  Everything You Need for Location Management
                </h2>
                <p className="max-w-[900px] text-muted-foreground md:text-xl/relaxed lg:text-base/relaxed xl:text-xl/relaxed">
                  Our comprehensive platform helps you manage every aspect of location scouting and management.
                </p>
              </div>
            </div>
            <div className="grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-3 lg:gap-12 mt-8">
              <div className="flex flex-col items-center space-y-2 border rounded-lg p-6 bg-background shadow-sm">
                <div className="p-2 bg-primary/10 rounded-full">
                  <MapPin className="h-6 w-6 text-primary" />
                </div>
                <h3 className="text-xl font-bold">Interactive Location Maps</h3>
                <p className="text-muted-foreground text-center">
                  Visualize all your locations on interactive maps with custom filters and clustering.
                </p>
              </div>
              <div className="flex flex-col items-center space-y-2 border rounded-lg p-6 bg-background shadow-sm">
                <div className="p-2 bg-primary/10 rounded-full">
                  <Film className="h-6 w-6 text-primary" />
                </div>
                <h3 className="text-xl font-bold">Scene Planning</h3>
                <p className="text-muted-foreground text-center">
                  Connect scenes to locations and collaborate on finding the perfect shooting spots.
                </p>
              </div>
              <div className="flex flex-col items-center space-y-2 border rounded-lg p-6 bg-background shadow-sm">
                <div className="p-2 bg-primary/10 rounded-full">
                  <Calendar className="h-6 w-6 text-primary" />
                </div>
                <h3 className="text-xl font-bold">Scheduling & Availability</h3>
                <p className="text-muted-foreground text-center">
                  Manage location availability, booking, and scheduling in one place.
                </p>
              </div>
              <div className="flex flex-col items-center space-y-2 border rounded-lg p-6 bg-background shadow-sm">
                <div className="p-2 bg-primary/10 rounded-full">
                  <ClipboardCheck className="h-6 w-6 text-primary" />
                </div>
                <h3 className="text-xl font-bold">Permit Management</h3>
                <p className="text-muted-foreground text-center">
                  Track permit applications, approvals, and requirements for each location.
                </p>
              </div>
              <div className="flex flex-col items-center space-y-2 border rounded-lg p-6 bg-background shadow-sm">
                <div className="p-2 bg-primary/10 rounded-full">
                  <Users className="h-6 w-6 text-primary" />
                </div>
                <h3 className="text-xl font-bold">Team Collaboration</h3>
                <p className="text-muted-foreground text-center">
                  Collaborate with your entire production team with role-based permissions.
                </p>
              </div>
              <div className="flex flex-col items-center space-y-2 border rounded-lg p-6 bg-background shadow-sm">
                <div className="p-2 bg-primary/10 rounded-full">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="24"
                    height="24"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    className="h-6 w-6 text-primary"
                  >
                    <path d="M14.5 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7.5L14.5 2z" />
                    <polyline points="14 2 14 8 20 8" />
                  </svg>
                </div>
                <h3 className="text-xl font-bold">Document Storage</h3>
                <p className="text-muted-foreground text-center">
                  Store and organize all location-related documents, photos, and contracts.
                </p>
              </div>
            </div>
          </div>
        </section>

        {/* Use Cases Section */}
        <section id="use-cases" className="w-full py-12 md:py-24 lg:py-32 bg-background">
          <div className="container px-4 md:px-6">
            <div className="flex flex-col items-center justify-center space-y-4 text-center">
              <div className="space-y-2">
                <div className="inline-block rounded-lg bg-primary px-3 py-1 text-sm text-primary-foreground">
                  Use Cases
                </div>
                <h2 className="text-3xl font-bold tracking-tighter md:text-4xl">Perfect For Every Production</h2>
                <p className="max-w-[900px] text-muted-foreground md:text-xl/relaxed lg:text-base/relaxed xl:text-xl/relaxed">
                  From indie films to major productions, Scene-o-matic helps teams of all sizes.
                </p>
              </div>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mt-12">
              <div className="flex flex-col space-y-4">
                <h3 className="text-2xl font-bold">Feature Films</h3>
                <ul className="space-y-2">
                  <li className="flex items-start gap-2">
                    <CheckCircle className="h-5 w-5 text-primary shrink-0 mt-0.5" />
                    <span>Manage hundreds of potential locations across multiple shooting days</span>
                  </li>
                  <li className="flex items-start gap-2">
                    <CheckCircle className="h-5 w-5 text-primary shrink-0 mt-0.5" />
                    <span>Coordinate large teams with specialized roles and permissions</span>
                  </li>
                  <li className="flex items-start gap-2">
                    <CheckCircle className="h-5 w-5 text-primary shrink-0 mt-0.5" />
                    <span>Track complex permitting requirements and deadlines</span>
                  </li>
                </ul>
              </div>
              <div className="flex flex-col space-y-4">
                <h3 className="text-2xl font-bold">TV Series</h3>
                <ul className="space-y-2">
                  <li className="flex items-start gap-2">
                    <CheckCircle className="h-5 w-5 text-primary shrink-0 mt-0.5" />
                    <span>Reuse locations across multiple episodes and seasons</span>
                  </li>
                  <li className="flex items-start gap-2">
                    <CheckCircle className="h-5 w-5 text-primary shrink-0 mt-0.5" />
                    <span>Maintain continuity with detailed location documentation</span>
                  </li>
                  <li className="flex items-start gap-2">
                    <CheckCircle className="h-5 w-5 text-primary shrink-0 mt-0.5" />
                    <span>Schedule recurring shoots efficiently</span>
                  </li>
                </ul>
              </div>
              <div className="flex flex-col space-y-4">
                <h3 className="text-2xl font-bold">Commercials</h3>
                <ul className="space-y-2">
                  <li className="flex items-start gap-2">
                    <CheckCircle className="h-5 w-5 text-primary shrink-0 mt-0.5" />
                    <span>Quickly find and secure locations for tight production schedules</span>
                  </li>
                  <li className="flex items-start gap-2">
                    <CheckCircle className="h-5 w-5 text-primary shrink-0 mt-0.5" />
                    <span>Share location options with clients and stakeholders</span>
                  </li>
                  <li className="flex items-start gap-2">
                    <CheckCircle className="h-5 w-5 text-primary shrink-0 mt-0.5" />
                    <span>Track budget allocations for each location</span>
                  </li>
                </ul>
              </div>
              <div className="flex flex-col space-y-4">
                <h3 className="text-2xl font-bold">Independent Productions</h3>
                <ul className="space-y-2">
                  <li className="flex items-start gap-2">
                    <CheckCircle className="h-5 w-5 text-primary shrink-0 mt-0.5" />
                    <span>Affordable plans for smaller teams and budgets</span>
                  </li>
                  <li className="flex items-start gap-2">
                    <CheckCircle className="h-5 w-5 text-primary shrink-0 mt-0.5" />
                    <span>Simplify location management with limited resources</span>
                  </li>
                  <li className="flex items-start gap-2">
                    <CheckCircle className="h-5 w-5 text-primary shrink-0 mt-0.5" />
                    <span>Focus on creativity while keeping organized</span>
                  </li>
                </ul>
              </div>
            </div>
          </div>
        </section>

        {/* Testimonials Section */}
        <section className="w-full py-12 md:py-24 lg:py-32 bg-muted/40">
          <div className="container px-4 md:px-6">
            <div className="flex flex-col items-center justify-center space-y-4 text-center">
              <div className="space-y-2">
                <div className="inline-block rounded-lg bg-primary px-3 py-1 text-sm text-primary-foreground">
                  Testimonials
                </div>
                <h2 className="text-3xl font-bold tracking-tighter md:text-4xl">Trusted by Production Teams</h2>
                <p className="max-w-[900px] text-muted-foreground md:text-xl/relaxed lg:text-base/relaxed xl:text-xl/relaxed">
                  See what our customers have to say about Scene-o-matic.
                </p>
              </div>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mt-12">
              <div className="flex flex-col p-6 bg-background rounded-lg border shadow-sm">
                <div className="flex items-center space-x-4 mb-4">
                  <div className="rounded-full bg-primary/10 p-1 h-12 w-12 flex items-center justify-center">
                    <span className="text-lg font-bold text-primary">JD</span>
                  </div>
                  <div>
                    <h4 className="font-bold">Jane Doe</h4>
                    <p className="text-sm text-muted-foreground">Location Manager, Horizon Films</p>
                  </div>
                </div>
                <p className="text-muted-foreground">
                  "Scene-o-matic has revolutionized how we manage locations. What used to take days now takes hours, and
                  our team collaboration has never been better."
                </p>
              </div>
              <div className="flex flex-col p-6 bg-background rounded-lg border shadow-sm">
                <div className="flex items-center space-x-4 mb-4">
                  <div className="rounded-full bg-primary/10 p-1 h-12 w-12 flex items-center justify-center">
                    <span className="text-lg font-bold text-primary">MS</span>
                  </div>
                  <div>
                    <h4 className="font-bold">Michael Smith</h4>
                    <p className="text-sm text-muted-foreground">Production Coordinator, Series Plus</p>
                  </div>
                </div>
                <p className="text-muted-foreground">
                  "The mapping features alone are worth the subscription. Being able to visualize all our locations and
                  filter by scene requirements has saved us countless hours."
                </p>
              </div>
              <div className="flex flex-col p-6 bg-background rounded-lg border shadow-sm">
                <div className="flex items-center space-x-4 mb-4">
                  <div className="rounded-full bg-primary/10 p-1 h-12 w-12 flex items-center justify-center">
                    <span className="text-lg font-bold text-primary">AL</span>
                  </div>
                  <div>
                    <h4 className="font-bold">Amanda Lee</h4>
                    <p className="text-sm text-muted-foreground">Director, Independent Films</p>
                  </div>
                </div>
                <p className="text-muted-foreground">
                  "As an indie filmmaker, I never thought I could afford such a powerful tool. The pricing is fair and
                  the features have helped us look much more professional to our clients."
                </p>
              </div>
            </div>
          </div>
        </section>

        {/* Pricing Section */}
        <section id="pricing" className="w-full py-12 md:py-24 lg:py-32 bg-background">
          <div className="container px-4 md:px-6">
            <div className="flex flex-col items-center justify-center space-y-4 text-center">
              <div className="space-y-2">
                <div className="inline-block rounded-lg bg-primary px-3 py-1 text-sm text-primary-foreground">
                  Pricing
                </div>
                <h2 className="text-3xl font-bold tracking-tighter md:text-4xl">Plans for Every Production Size</h2>
                <p className="max-w-[900px] text-muted-foreground md:text-xl/relaxed lg:text-base/relaxed xl:text-xl/relaxed">
                  Choose the plan that works best for your team and production needs.
                </p>
              </div>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mt-12">
              <div className="flex flex-col p-6 bg-background rounded-lg border shadow-sm">
                <div className="space-y-2 mb-4">
                  <h3 className="text-2xl font-bold">Indie</h3>
                  <p className="text-muted-foreground">Perfect for small productions and indie filmmakers</p>
                </div>
                <div className="mb-4">
                  <span className="text-4xl font-bold">$29</span>
                  <span className="text-muted-foreground">/month</span>
                </div>
                <ul className="space-y-2 mb-8 flex-1">
                  <li className="flex items-center gap-2">
                    <CheckCircle className="h-5 w-5 text-primary shrink-0" />
                    <span>Up to 3 team members</span>
                  </li>
                  <li className="flex items-center gap-2">
                    <CheckCircle className="h-5 w-5 text-primary shrink-0" />
                    <span>50 locations</span>
                  </li>
                  <li className="flex items-center gap-2">
                    <CheckCircle className="h-5 w-5 text-primary shrink-0" />
                    <span>5 projects</span>
                  </li>
                  <li className="flex items-center gap-2">
                    <CheckCircle className="h-5 w-5 text-primary shrink-0" />
                    <span>Basic map features</span>
                  </li>
                  <li className="flex items-center gap-2">
                    <CheckCircle className="h-5 w-5 text-primary shrink-0" />
                    <span>5GB storage</span>
                  </li>
                </ul>
                <Button size="lg" variant="outline" className="w-full">
                  Start Free Trial
                </Button>
              </div>
              <div className="flex flex-col p-6 bg-primary text-primary-foreground rounded-lg border shadow-sm relative">
                <div className="absolute -top-4 left-0 right-0 mx-auto w-fit bg-primary text-primary-foreground px-3 py-1 rounded-full text-sm font-medium">
                  Most Popular
                </div>
                <div className="space-y-2 mb-4">
                  <h3 className="text-2xl font-bold">Professional</h3>
                  <p className="text-primary-foreground/80">For growing production companies</p>
                </div>
                <div className="mb-4">
                  <span className="text-4xl font-bold">$99</span>
                  <span className="text-primary-foreground/80">/month</span>
                </div>
                <ul className="space-y-2 mb-8 flex-1">
                  <li className="flex items-center gap-2">
                    <CheckCircle className="h-5 w-5 shrink-0" />
                    <span>Up to 15 team members</span>
                  </li>
                  <li className="flex items-center gap-2">
                    <CheckCircle className="h-5 w-5 shrink-0" />
                    <span>Unlimited locations</span>
                  </li>
                  <li className="flex items-center gap-2">
                    <CheckCircle className="h-5 w-5 shrink-0" />
                    <span>20 projects</span>
                  </li>
                  <li className="flex items-center gap-2">
                    <CheckCircle className="h-5 w-5 shrink-0" />
                    <span>Advanced map features</span>
                  </li>
                  <li className="flex items-center gap-2">
                    <CheckCircle className="h-5 w-5 shrink-0" />
                    <span>50GB storage</span>
                  </li>
                  <li className="flex items-center gap-2">
                    <CheckCircle className="h-5 w-5 shrink-0" />
                    <span>Permit management</span>
                  </li>
                </ul>
                <Button size="lg" className="w-full bg-background text-primary hover:bg-background/90">
                  Start Free Trial
                </Button>
              </div>
              <div className="flex flex-col p-6 bg-background rounded-lg border shadow-sm">
                <div className="space-y-2 mb-4">
                  <h3 className="text-2xl font-bold">Enterprise</h3>
                  <p className="text-muted-foreground">For major studios and production houses</p>
                </div>
                <div className="mb-4">
                  <span className="text-4xl font-bold">$299</span>
                  <span className="text-muted-foreground">/month</span>
                </div>
                <ul className="space-y-2 mb-8 flex-1">
                  <li className="flex items-center gap-2">
                    <CheckCircle className="h-5 w-5 text-primary shrink-0" />
                    <span>Unlimited team members</span>
                  </li>
                  <li className="flex items-center gap-2">
                    <CheckCircle className="h-5 w-5 text-primary shrink-0" />
                    <span>Unlimited locations</span>
                  </li>
                  <li className="flex items-center gap-2">
                    <CheckCircle className="h-5 w-5 text-primary shrink-0" />
                    <span>Unlimited projects</span>
                  </li>
                  <li className="flex items-center gap-2">
                    <CheckCircle className="h-5 w-5 text-primary shrink-0" />
                    <span>Custom map features</span>
                  </li>
                  <li className="flex items-center gap-2">
                    <CheckCircle className="h-5 w-5 text-primary shrink-0" />
                    <span>500GB storage</span>
                  </li>
                  <li className="flex items-center gap-2">
                    <CheckCircle className="h-5 w-5 text-primary shrink-0" />
                    <span>Priority support</span>
                  </li>
                  <li className="flex items-center gap-2">
                    <CheckCircle className="h-5 w-5 text-primary shrink-0" />
                    <span>Custom integrations</span>
                  </li>
                </ul>
                <Button size="lg" variant="outline" className="w-full">
                  Contact Sales
                </Button>
              </div>
            </div>
          </div>
        </section>

        {/* CTA Section */}
        <section id="demo" className="w-full py-12 md:py-24 lg:py-32 bg-primary text-primary-foreground">
          <div className="container px-4 md:px-6">
            <div className="flex flex-col items-center justify-center space-y-4 text-center">
              <div className="space-y-2">
                <h2 className="text-3xl font-bold tracking-tighter md:text-4xl/tight">
                  Ready to Transform Your Location Management?
                </h2>
                <p className="mx-auto max-w-[600px] md:text-xl/relaxed lg:text-base/relaxed xl:text-xl/relaxed">
                  Join thousands of production teams already using Scene-o-matic to streamline their workflows.
                </p>
              </div>
              <div className="flex flex-col gap-2 min-[400px]:flex-row">
                <Button size="lg" className="bg-background text-primary hover:bg-background/90" asChild>
                  <Link href="/auth/register">Start Your Free 14-Day Trial</Link>
                </Button>
                <Button size="lg" variant="outline" className="border-primary-foreground" asChild>
                  <Link href="/contact">Schedule a Demo</Link>
                </Button>
              </div>
            </div>
          </div>
        </section>
      </main>

      {/* Footer */}
      <footer className="border-t bg-background">
        <div className="container flex flex-col gap-6 py-8 md:py-12 px-4 md:px-6">
          <div className="grid grid-cols-1 gap-8 sm:grid-cols-2 md:grid-cols-4 lg:grid-cols-5">
            <div className="flex flex-col gap-2">
              <div className="flex items-center gap-2">
                <MapPin className="h-6 w-6 text-primary" />
                <span className="text-lg font-bold">Scene-o-matic</span>
              </div>
              <p className="text-sm text-muted-foreground">
                The complete location management platform for film and media production.
              </p>
            </div>
            <div className="flex flex-col gap-2">
              <h3 className="font-medium">Product</h3>
              <nav className="flex flex-col gap-2">
                <Link href="#features" className="text-sm hover:underline underline-offset-4">
                  Features
                </Link>
                <Link href="#pricing" className="text-sm hover:underline underline-offset-4">
                  Pricing
                </Link>
                <Link href="#" className="text-sm hover:underline underline-offset-4">
                  Integrations
                </Link>
                <Link href="#" className="text-sm hover:underline underline-offset-4">
                  Changelog
                </Link>
              </nav>
            </div>
            <div className="flex flex-col gap-2">
              <h3 className="font-medium">Resources</h3>
              <nav className="flex flex-col gap-2">
                <Link href="#" className="text-sm hover:underline underline-offset-4">
                  Documentation
                </Link>
                <Link href="#" className="text-sm hover:underline underline-offset-4">
                  Guides
                </Link>
                <Link href="#" className="text-sm hover:underline underline-offset-4">
                  Support
                </Link>
                <Link href="#" className="text-sm hover:underline underline-offset-4">
                  API
                </Link>
              </nav>
            </div>
            <div className="flex flex-col gap-2">
              <h3 className="font-medium">Company</h3>
              <nav className="flex flex-col gap-2">
                <Link href="#" className="text-sm hover:underline underline-offset-4">
                  About
                </Link>
                <Link href="#" className="text-sm hover:underline underline-offset-4">
                  Blog
                </Link>
                <Link href="#" className="text-sm hover:underline underline-offset-4">
                  Careers
                </Link>
                <Link href="#" className="text-sm hover:underline underline-offset-4">
                  Contact
                </Link>
              </nav>
            </div>
            <div className="flex flex-col gap-2">
              <h3 className="font-medium">Legal</h3>
              <nav className="flex flex-col gap-2">
                <Link href="#" className="text-sm hover:underline underline-offset-4">
                  Terms
                </Link>
                <Link href="#" className="text-sm hover:underline underline-offset-4">
                  Privacy
                </Link>
                <Link href="#" className="text-sm hover:underline underline-offset-4">
                  Cookies
                </Link>
                <Link href="#" className="text-sm hover:underline underline-offset-4">
                  Licenses
                </Link>
              </nav>
            </div>
          </div>
          <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
            <p className="text-xs text-muted-foreground">© 2023 Scene-o-matic. All rights reserved.</p>
            <div className="flex gap-4">
              <Link href="#" className="text-muted-foreground hover:text-foreground">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="24"
                  height="24"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  className="h-5 w-5"
                >
                  <path d="M22 4s-.7 2.1-2 3.4c1.6 10-9.4 17.3-18 11.6 2.2.1 4.4-.6 6-2C3 15.5.5 9.6 3 5c2.2 2.6 5.6 4.1 9 4-.9-4.2 4-6.6 7-3.8 1.1 0 3-1.2 3-1.2z" />
                </svg>
                <span className="sr-only">Twitter</span>
              </Link>
              <Link href="#" className="text-muted-foreground hover:text-foreground">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="24"
                  height="24"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  className="h-5 w-5"
                >
                  <rect width="20" height="20" x="2" y="2" rx="5" ry="5" />
                  <path d="M16 11.37A4 4 0 1 1 12.63 8 4 4 0 0 1 16 11.37z" />
                  <line x1="17.5" x2="17.51" y1="6.5" y2="6.5" />
                </svg>
                <span className="sr-only">Instagram</span>
              </Link>
              <Link href="#" className="text-muted-foreground hover:text-foreground">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="24"
                  height="24"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  className="h-5 w-5"
                >
                  <path d="M18 2h-3a5 5 0 0 0-5 5v3H7v4h3v8h4v-8h3l1-4h-4V7a1 1 0 0 1 1-1h3z" />
                </svg>
                <span className="sr-only">Facebook</span>
              </Link>
              <Link href="#" className="text-muted-foreground hover:text-foreground">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="24"
                  height="24"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  className="h-5 w-5"
                >
                  <path d="M16 8a6 6 0 0 1 6 6v7h-4v-7a2 2 0 0 0-2-2 2 2 0 0 0-2 2v7h-4v-7a6 6 0 0 1 6-6z" />
                  <rect width="4" height="12" x="2" y="9" />
                  <circle cx="4" cy="4" r="2" />
                </svg>
                <span className="sr-only">LinkedIn</span>
              </Link>
            </div>
          </div>
        </div>
      </footer>
    </div>
  )
}
