@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans:
    system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
  --font-mono: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, monospace;
}

body {
  font-family: var(--font-sans);
}

@layer base {
  :root {
    /* Base colors */
    --background: 0 0% 100%; /* white */
    --foreground: 0 0% 0%; /* black */

    /* Card colors */
    --card: 0 0% 100%; /* white */
    --card-foreground: 0 0% 0%; /* black */

    /* Popover colors */
    --popover: 0 0% 100%; /* white */
    --popover-foreground: 0 0% 0%; /* black */

    /* Primary colors - Neon Blue */
    --primary: 240 100% 57%; /* neon blue (#2323FF) */
    --primary-foreground: 0 0% 100%; /* white */
    --primary-dark: 240 100% 47%; /* darker neon blue */

    /* Secondary colors */
    --secondary: 220 14% 96%; /* gray-100 */
    --secondary-foreground: 0 0% 0%; /* black */

    /* Muted colors */
    --muted: 220 14% 96%; /* gray-100 */
    --muted-foreground: 220 9% 46%; /* gray-500 */

    /* Accent colors - Neon Green */
    --accent: 108 100% 51%; /* neon green (#16FF00) */
    --accent-foreground: 0 0% 0%; /* black */

    /* Destructive colors */
    --destructive: 0 84% 60%; /* red-600 */
    --destructive-foreground: 0 0% 98%; /* white */

    /* Border and input colors */
    --border: 220 13% 91%; /* gray-200 */
    --border-medium: 216 12% 84%; /* gray-300 */
    --input: 220 13% 91%; /* gray-200 */

    /* Ring color for focus states */
    --ring: 240 100% 57%; /* neon blue (#2323FF) */

    /* Status colors */
    --status-green-bg: 108 100% 95%; /* neon green bg */
    --status-green-text: 108 100% 25%; /* neon green text */
    --status-yellow-bg: 55 100% 95%; /* neon yellow bg */
    --status-yellow-text: 55 100% 25%; /* neon yellow text */
    --status-red-bg: 0 100% 98%; /* red-50 */
    --status-red-text: 0 74% 42%; /* red-700 */
    --status-blue-bg: 240 100% 95%; /* neon blue bg */
    --status-blue-text: 240 100% 40%; /* neon blue text */

    /* Futuristic colors */
    --neon-blue: 240 100% 57%; /* #2323FF */
    --neon-green: 108 100% 51%; /* #16FF00 */
    --neon-pink: 332 92% 54%; /* #F61981 */
    --neon-yellow: 55 100% 50%; /* #FFED00 */
    --deep-black: 0 0% 0%; /* #000000 */

    /* Chart colors */
    --chart-1: 332 92% 54%; /* neon pink (#F61981) */
    --chart-2: 108 100% 51%; /* neon green (#16FF00) */
    --chart-3: 240 100% 57%; /* neon blue (#2323FF) */
    --chart-4: 55 100% 50%; /* neon yellow (#FFED00) */
    --chart-5: 0 0% 0%; /* deep black (#000000) */

    /* Sidebar - light mode: charcoal */
    --sidebar-bg: 215 28% 17%; /* charcoal (#1F2937) */
    --sidebar-text: 0 0% 100%; /* white */

    /* Border radius */
    --radius: 0.5rem;
  }

  .dark {
    /* Base colors */
    --background: 0 0% 0%; /* deep black */
    --foreground: 0 0% 100%; /* white */

    /* Card colors */
    --card: 0 0% 5%; /* near black */
    --card-foreground: 0 0% 100%; /* white */

    /* Popover colors */
    --popover: 0 0% 5%; /* near black */
    --popover-foreground: 0 0% 100%; /* white */

    /* Primary colors - Neon Blue */
    --primary: 240 100% 57%; /* neon blue (#2323FF) */
    --primary-foreground: 0 0% 100%; /* white */
    --primary-dark: 240 100% 67%; /* lighter neon blue */

    /* Secondary colors */
    --secondary: 0 0% 10%; /* dark gray */
    --secondary-foreground: 0 0% 100%; /* white */

    /* Muted colors */
    --muted: 0 0% 10%; /* dark gray */
    --muted-foreground: 0 0% 70%; /* light gray */

    /* Accent colors - Neon Green */
    --accent: 108 100% 51%; /* neon green (#16FF00) */
    --accent-foreground: 0 0% 0%; /* black */

    /* Destructive colors */
    --destructive: 0 63% 31%; /* red-800 */
    --destructive-foreground: 0 0% 100%; /* white */

    /* Border and input colors */
    --border: 0 0% 15%; /* dark gray */
    --border-medium: 0 0% 20%; /* medium gray */
    --input: 0 0% 15%; /* dark gray */

    /* Ring color for focus states */
    --ring: 240 100% 57%; /* neon blue (#2323FF) */

    /* Status colors - darker variants for dark mode */
    --status-green-bg: 108 100% 15%; /* darker neon green */
    --status-green-text: 108 100% 80%; /* lighter neon green */
    --status-yellow-bg: 55 100% 15%; /* darker neon yellow */
    --status-yellow-text: 55 100% 80%; /* lighter neon yellow */
    --status-red-bg: 332 92% 15%; /* darker neon pink */
    --status-red-text: 332 92% 80%; /* lighter neon pink */
    --status-blue-bg: 240 100% 15%; /* darker neon blue */
    --status-blue-text: 240 100% 80%; /* lighter neon blue */

    /* Futuristic colors */
    --neon-blue: 240 100% 57%; /* #2323FF */
    --neon-green: 108 100% 51%; /* #16FF00 */
    --neon-pink: 332 92% 54%; /* #F61981 */
    --neon-yellow: 55 100% 50%; /* #FFED00 */
    --deep-black: 0 0% 0%; /* #000000 */

    /* Chart colors - adjusted for dark mode */
    --chart-1: 332 92% 54%; /* neon pink (#F61981) */
    --chart-2: 108 100% 51%; /* neon green (#16FF00) */
    --chart-3: 240 100% 57%; /* neon blue (#2323FF) */
    --chart-4: 55 100% 50%; /* neon yellow (#FFED00) */
    --chart-5: 0 0% 100%; /* white */

    /* Sidebar - dark mode: black */
    --sidebar-bg: 0 0% 0%; /* black (#000000) */
    --sidebar-text: 0 0% 100%; /* white */
  }
}

@layer base {
  * {
    border-color: hsl(var(--border));
  }
  body {
    @apply bg-background text-foreground;
  }
}
