"use client"

import type { ReactNode } from "react"
import { Theme<PERSON>rovider as NextThemesProvider } from "next-themes"

interface ThemeProviderClientProps {
  children: ReactNode
}

export default function ThemeProviderClient({ children }: ThemeProviderClientProps) {
  return (
    <NextThemesProvider attribute="class" defaultTheme="system" enableSystem disableTransitionOnChange>
      {children}
    </NextThemesProvider>
  )
}
