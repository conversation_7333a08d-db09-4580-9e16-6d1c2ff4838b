"use client"

import type { <PERSON>actNode } from "react"
import { Theme<PERSON>rovider } from "next-themes"
import { StytchB2BProvider } from '@stytch/nextjs/b2b'
import { createStytchB2BUIClient } from '@stytch/nextjs/b2b/ui'
import { AuthProvider } from "@/providers/auth-provider"
import { OrganizationProvider } from "@/providers/organization-provider"
import { OnboardingProvider } from "@/providers/onboarding-provider"
import { NotificationProvider } from "@/providers/notification-provider"
import { MapProvider } from "@/providers/map-provider"
// Import the new QueryProvider
import { QueryProvider } from './query-provider'; 

// Stytch client configuration
const stytchOptions = {
    cookieOptions: {
      opaqueTokenCookieName: "stytch_session",
      jwtCookieName: "stytch_session_jwt",
      path: "/",
      availableToSubdomains: false,
      domain: "",
    },
    sessionDurationMinutes: 60 * 24, // 1 day (1440 minutes) - reduced to comply with live project limits
    // Add this for development to bypass domain restrictions
    // Remove this in production or after registering localhost in the Stytch dashboard
    overrideHostedDomainVerification: process.env.NODE_ENV === 'development'
}

const publicToken = process.env.NEXT_PUBLIC_STYTCH_PUBLIC_TOKEN
if (!publicToken) {
  throw new Error("Missing NEXT_PUBLIC_STYTCH_PUBLIC_TOKEN environment variable")
}

const stytchClient = createStytchB2BUIClient(
  publicToken,
  stytchOptions
)

interface AllClientProvidersProps {
  children: ReactNode
}

export default function AllClientProviders({ children }: AllClientProvidersProps) {
  return (
    // Wrap everything inside QueryProvider
    <QueryProvider> 
      <ThemeProvider attribute="class" defaultTheme="system" enableSystem disableTransitionOnChange>
        <StytchB2BProvider stytch={stytchClient}>
          <AuthProvider>
            <OrganizationProvider>
              <OnboardingProvider>
                <NotificationProvider>
                  <MapProvider>
                    {children}
                  </MapProvider>
                </NotificationProvider>
              </OnboardingProvider>
            </OrganizationProvider>
          </AuthProvider>
        </StytchB2BProvider>
      </ThemeProvider>
    </QueryProvider>
  )
}
