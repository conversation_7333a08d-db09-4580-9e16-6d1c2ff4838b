"use client" // Add "use client" directive

import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { useState, useEffect, lazy, Suspense } from 'react'; // Added useEffect

// Dynamically import DevTools only in development
const ReactQueryDevtools = process.env.NODE_ENV === 'development'
  ? lazy(() => import('@tanstack/react-query-devtools').then(mod => ({ default: mod.ReactQueryDevtools })))
  : () => null; // This will render null on the server and during initial client render

export function QueryProvider({ children }: { children: React.ReactNode }) {
  const [queryClient] = useState(
    () =>
      new QueryClient({
        defaultOptions: {
          queries: {
            staleTime: 60 * 1000, // 1 minute
            gcTime: 5 * 60 * 1000, // 5 minutes (cacheTime renamed to gcTime in v5)
            refetchOnWindowFocus: false,
            retry: 1,
          },
        },
      })
  );
  const [showDevtools, setShowDevtools] = useState(false);

  useEffect(() => {
    // This effect runs only on the client, after initial mount
    setShowDevtools(true);
  }, []);

  return (
    <QueryClientProvider client={queryClient}>
      {children}
      {/* Only include Devtools in development AND after client-side mount */}
      {showDevtools && process.env.NODE_ENV === 'development' && (
        <Suspense fallback={null}>
          <ReactQueryDevtools initialIsOpen={false} />
        </Suspense>
      )}
    </QueryClientProvider>
  );
}
