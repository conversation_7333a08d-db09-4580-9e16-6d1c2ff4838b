"use client"

import type { ReactNode } from "react"
import { AuthProvider } from "@/providers/auth-provider";
import { OrganizationProvider } from "@/providers/organization-provider";
import { NotificationProvider } from "@/providers/notification-provider";
// import StytchProvider from "./StytchProvider"; // Removed - Stytch context provided by AllClientProviders
import { QueryClient, QueryClientProvider } from "@tanstack/react-query"; // Import QueryClient and Provider
import { useState } from "react"; // Import useState for client instance

interface ClientProvidersProps {
  children: ReactNode;
}

export default function ClientProviders({ children }: ClientProvidersProps) {
  // Create a client instance (ensure it's only created once per render)
  const [queryClient] = useState(() => new QueryClient());

  return (
    <QueryClientProvider client={queryClient}> {/* Wrap with QueryClientProvider */}
      {/* StytchProvider removed - context provided by AllClientProviders */}
      <AuthProvider>
        <OrganizationProvider>
          <NotificationProvider>
            {children}
          </NotificationProvider>
        </OrganizationProvider>
      </AuthProvider>
    </QueryClientProvider>
  );
}
