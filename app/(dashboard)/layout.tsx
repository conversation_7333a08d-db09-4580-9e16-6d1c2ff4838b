import type { ReactNode } from "react";
import { DashboardHeader } from "./_components/dashboard-header";
import { DashboardSidebar } from "./_components/dashboard-sidebar";
import { OnboardingModal } from "@/components/onboarding/onboarding-modal";
import ClientProviders from "../providers/ClientProviders"; // Import ClientProviders

export default function DashboardLayout({ children }: { children: ReactNode }) {
  return (
    <div className="flex min-h-screen">
      <OnboardingModal />
      {/* Wrap Sidebar, Header, and main content with ClientProviders */}
      <ClientProviders>
        <DashboardSidebar />
        {/* Add left margin on medium screens and above to account for the fixed sidebar */}
        <div className="flex flex-col flex-1 md:ml-64">
          <DashboardHeader />
          <main className="flex-1 p-6">{children}</main>
        </div>
      </ClientProviders>
    </div>
  )
}
