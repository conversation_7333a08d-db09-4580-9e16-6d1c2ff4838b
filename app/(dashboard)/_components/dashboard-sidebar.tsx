"use client"

import { useState } from "react"
import Link from "next/link"
import { usePathname } from "next/navigation"
import { cn } from "@/lib/utils"
import { dashboardNavigation } from "@/lib/config/navigation"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Sheet, SheetContent, SheetTrigger } from "@/components/ui/sheet"
import { Menu } from "lucide-react"
import { useOrganization } from "@/hooks/use-organization"
import { usePermissions, hasRequiredRole } from "@/hooks/usePermissions"; // Import permission hooks

export function DashboardSidebar() {
  const pathname = usePathname()
  const [open, setOpen] = useState(false)
  // Organization context is used in the nested components only

  return (
    <>
      {/* Mobile sidebar */}
      <Sheet open={open} onOpenChange={setOpen}>
        <SheetTrigger asChild className="md:hidden">
          <Button variant="ghost" size="icon" className="md:hidden">
            <Menu className="h-5 w-5" />
            <span className="sr-only">Toggle menu</span>
          </Button>
        </SheetTrigger>
        <SheetContent side="left" className="p-0">
          <MobileSidebar pathname={pathname} onNavigate={() => setOpen(false)} />
        </SheetContent>
      </Sheet>

      {/* Desktop sidebar */}
      <div className="hidden md:flex md:w-64 md:flex-col md:fixed md:inset-y-0">
        <DesktopSidebar pathname={pathname} />
      </div>
    </>
  )
}

function MobileSidebar({ pathname, onNavigate }: { pathname: string; onNavigate: () => void }) {
  const { organization } = useOrganization()
  const orgSlug = organization?.slug || "default"
  const userRoles = usePermissions();
  // console.log('Mobile Sidebar Roles:', userRoles); // Log roles - Removed

  // Filter navigation items based on user roles
  const accessibleNavItems = dashboardNavigation.filter(item =>
    !item.requiredRoles || hasRequiredRole(userRoles, item.requiredRoles)
  );

  return (
    <div className="flex h-full flex-col bg-[#1F2937] dark:bg-black text-white">
      <div className="p-4 border-b border-white/20">
        <Link href={`/organizations/${orgSlug}`} className="flex flex-col">
          <div className="flex items-center gap-2">
            <div className="flex h-8 w-8 items-center justify-center rounded-md bg-neon-blue text-white shadow-[0_0_10px_rgba(35,35,255,0.7)]">
              <span className="text-sm font-bold">SM</span>
            </div>
            <span className="text-lg font-bold text-white">Scene-o-matic</span>
          </div>
          <span className="text-xs text-white/80 mt-1 ml-10 italic">Your Locations Start Here</span>
        </Link>
      </div>
      <ScrollArea className="flex-1 p-4">
        <nav className="flex flex-col gap-2">
          {/* Map over the filtered navigation items */}
          {accessibleNavItems.map((item) => {
            const href = `/organizations/${orgSlug}${item.href}`
            const isActive = pathname === href || (item.href !== "" && pathname.startsWith(`${href}/`))

            return (
              <Link
                key={item.href}
                href={href}
                onClick={onNavigate}
                className={cn(
                  "group flex items-center gap-3 rounded-md px-3 py-2.5 text-sm font-medium transition-colors",
                  isActive
                    ? "bg-neon-green text-deep-black shadow-[0_0_10px_rgba(22,255,0,0.7)] hover:shadow-[0_0_15px_rgba(22,255,0,0.9)]"
                    : "text-white/70 hover:bg-neon-green/10 hover:text-neon-green hover:shadow-[0_0_5px_rgba(22,255,0,0.5)]",
                )}
              >
                <item.icon className={cn("h-5 w-5 transition-all", isActive ? "text-deep-black" : "text-white/70 group-hover:text-neon-green group-hover:shadow-[0_0_8.75px_rgba(22,255,0,1.0)]")} />
                <span>{item.title}</span>
              </Link>
            )
          })}
        </nav>
      </ScrollArea>
      {/* Conditionally render Settings link based on role */}
      {hasRequiredRole(userRoles, ['admin']) && (
        <div className="border-t border-white/10 p-4">
          <div className="flex items-center justify-center">
            <Link href={`/organizations/${orgSlug}/settings`} onClick={onNavigate}>
              <Button variant="ghost" className="text-white/70 hover:text-white hover:bg-white/10 text-xs">
                Settings
              </Button>
            </Link>
          </div>
        </div>
      )}
    </div>
  )
}

// Let's also ensure the sidebar has the correct z-index and positioning

function DesktopSidebar({ pathname }: { pathname: string }) {
  const { organization } = useOrganization()
  const orgSlug = organization?.slug || "default"
  const userRoles = usePermissions();
  // console.log('Desktop Sidebar Roles:', userRoles); // Log roles - Removed

  // Filter navigation items based on user roles
  const accessibleNavItems = dashboardNavigation.filter(item =>
    !item.requiredRoles || hasRequiredRole(userRoles, item.requiredRoles)
  );

  return (
    <div className="flex h-full flex-col border-r bg-[#1F2937] dark:bg-black text-white z-30">
      <div className="p-4 border-b border-white/20">
        <Link href={`/organizations/${orgSlug}`} className="flex flex-col">
          <div className="flex items-center gap-2">
            <div className="flex h-8 w-8 items-center justify-center rounded-md bg-neon-blue text-white shadow-[0_0_10px_rgba(35,35,255,0.7)]">
              <span className="text-sm font-bold">SM</span>
            </div>
            <span className="text-lg font-bold text-white">Scene-o-matic</span>
          </div>
          <span className="text-xs text-white/80 mt-1 ml-10 italic">Your Locations Start Here</span>
        </Link>
      </div>
      <ScrollArea className="flex-1 p-4">
        <nav className="flex flex-col gap-2">
          {/* Map over the filtered navigation items */}
          {accessibleNavItems.map((item) => {
            const href = `/organizations/${orgSlug}${item.href}`
            const isActive = pathname === href || (item.href !== "" && pathname.startsWith(`${href}/`))

            return (
              <Link
                key={item.href}
                href={href}
                className={cn(
                  "group flex items-center gap-3 rounded-md px-3 py-2.5 text-sm font-medium transition-colors",
                  isActive
                    ? "bg-neon-green text-deep-black shadow-[0_0_10px_rgba(22,255,0,0.7)] hover:shadow-[0_0_15px_rgba(22,255,0,0.9)]"
                    : "text-white/70 hover:bg-neon-green/10 hover:text-neon-green hover:shadow-[0_0_5px_rgba(22,255,0,0.5)]",
                )}
              >
                <item.icon className={cn("h-5 w-5 transition-all", isActive ? "text-deep-black" : "text-white/70 group-hover:text-neon-green group-hover:shadow-[0_0_8.75px_rgba(22,255,0,1.0)]")} />
                <span>{item.title}</span>
              </Link>
            )
          })}
        </nav>
      </ScrollArea>
      {/* Conditionally render Settings link based on role */}
      {hasRequiredRole(userRoles, ['admin']) && (
        <div className="border-t border-white/10 p-4">
          <div className="flex items-center justify-center">
            <Link href={`/organizations/${orgSlug}/settings`}>
              <Button variant="ghost" className="text-white/70 hover:text-white hover:bg-white/10 text-xs">
                Settings
              </Button>
            </Link>
          </div>
        </div>
      )}
    </div>
  )
}
