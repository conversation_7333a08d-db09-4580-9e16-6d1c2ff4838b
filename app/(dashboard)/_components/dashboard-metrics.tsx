"use client"

import type React from "react"

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card"
import { MapPin, FileText, CheckSquare, Users } from "lucide-react"

type MetricCardProps = {
  title: string
  value: string | number
  description?: string
  icon: React.ElementType
  trend?: {
    value: number
    isPositive: boolean
  }
}

function MetricCard({ title, value, description, icon: Icon, trend }: MetricCardProps) {
  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between pb-2">
        <CardTitle className="text-sm font-medium">{title}</CardTitle>
        <Icon className="h-4 w-4 text-muted-foreground" />
      </CardHeader>
      <CardContent>
        <div className="text-2xl font-bold">{value}</div>
        {description && <p className="text-xs text-muted-foreground">{description}</p>}
        {trend && (
          <div className="flex items-center pt-1">
            <span className={trend.isPositive ? "text-emerald-500 text-xs" : "text-rose-500 text-xs"}>
              {trend.isPositive ? "+" : ""}
              {trend.value}%
            </span>
            <span className="text-xs text-muted-foreground ml-1">from last month</span>
          </div>
        )}
      </CardContent>
    </Card>
  )
}

type DashboardMetricsProps = {
  metrics: {
    locations: number
    documents: number
    tasks: number
    teamMembers: number
  }
}

export function DashboardMetrics({ metrics }: DashboardMetricsProps) {
  return (
    <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
      <MetricCard
        title="Total Locations"
        value={metrics.locations}
        icon={MapPin}
        trend={{ value: 12, isPositive: true }}
      />
      <MetricCard title="Documents" value={metrics.documents} icon={FileText} trend={{ value: 8, isPositive: true }} />
      <MetricCard
        title="Active Tasks"
        value={metrics.tasks}
        icon={CheckSquare}
        trend={{ value: 5, isPositive: false }}
      />
      <MetricCard
        title="Team Members"
        value={metrics.teamMembers}
        icon={Users}
        trend={{ value: 3, isPositive: true }}
      />
    </div>
  )
}
