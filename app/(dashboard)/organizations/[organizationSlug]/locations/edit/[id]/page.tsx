import { DashboardShell } from "@/components/dashboard/dashboard-shell"
import React from "react"
import { LocationEditForm } from "@/components/locations/location-edit-form"

export default function LocationEditPage({ params }: { params: Promise<{ id: string, organizationSlug: string }> }) {
  // Properly unwrap params using React.use()
  const unwrappedParams = React.use(params)
  const locationId = unwrappedParams.id
  const organizationSlug = unwrappedParams.organizationSlug
  
  return (
    <DashboardShell>
      <div className="flex items-center justify-between mb-6">
        <h1 className="text-2xl font-bold tracking-tight">Edit Location</h1>
      </div>
      <LocationEditForm 
        locationId={locationId} 
        organizationSlug={organizationSlug} 
      />
    </DashboardShell>
  )
}
