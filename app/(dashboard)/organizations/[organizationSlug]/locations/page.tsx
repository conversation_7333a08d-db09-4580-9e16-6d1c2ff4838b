"use client";

import { Suspense } from "react";
import { LocationsContent } from "./locations-content";
import { LocationsPageSkeleton } from "@/components/locations/locations-page-skeleton";

interface LocationsPageProps {
  params: Promise<{
    organizationSlug: string;
  }>;
}

export default function LocationsPage({ params }: LocationsPageProps) {
  return (
    <Suspense fallback={<LocationsPageSkeleton />}>
      <LocationsContent params={params} />
    </Suspense>
  );
}
