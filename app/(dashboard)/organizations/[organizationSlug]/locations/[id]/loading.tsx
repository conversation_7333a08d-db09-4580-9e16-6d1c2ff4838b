import { DashboardShell } from "@/components/dashboard/dashboard-shell";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Skeleton } from "@/components/ui/skeleton";
import { Ta<PERSON>, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Edit, MoreHorizontal } from "lucide-react";

export default function Loading() {
  return (
    <DashboardShell>
      <div className="flex justify-between items-center mb-6">
        <div>
          <Skeleton className="h-8 w-64 mb-2" />
          <Skeleton className="h-4 w-48" />
        </div>
        <div className="flex items-center gap-2">
          <Button variant="outline" disabled>
            <Edit className="mr-2 h-4 w-4" />
            Edit Location
          </Button>
          <Button variant="outline" size="icon" disabled>
            <MoreHorizontal className="h-4 w-4" />
          </Button>
        </div>
      </div>

      <div className="grid gap-6 md:grid-cols-7">
        <div className="md:col-span-5 space-y-6">
          <div className="rounded-lg border bg-card text-card-foreground shadow">
            <div className="aspect-video relative">
              <Skeleton className="h-full w-full rounded-t-lg" />
            </div>
            <div className="p-6">
              <div className="flex justify-between items-start mb-4">
                <div>
                  <Skeleton className="h-6 w-48 mb-2" />
                  <Skeleton className="h-4 w-64" />
                </div>
                <Skeleton className="h-6 w-20" />
              </div>
              <Skeleton className="h-20 w-full mb-4" />
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4 pt-2">
                {Array.from({ length: 8 }).map((_, i) => (
                  <div key={i} className="space-y-1">
                    <Skeleton className="h-4 w-24" />
                    <Skeleton className="h-5 w-20" />
                  </div>
                ))}
              </div>
            </div>
          </div>

          <Tabs defaultValue="gallery" className="space-y-4">
            <TabsList>
              <TabsTrigger value="gallery" disabled>Gallery</TabsTrigger>
              <TabsTrigger value="map" disabled>Map</TabsTrigger>
              <TabsTrigger value="documents" disabled>Documents</TabsTrigger>
              <TabsTrigger value="notes" disabled>Notes</TabsTrigger>
            </TabsList>
            <TabsContent value="gallery">
              <Skeleton className="h-[300px] w-full rounded-md" />
            </TabsContent>
          </Tabs>
        </div>

        <div className="md:col-span-2 space-y-6">
          {/* Location Contact Card */}
          <div className="rounded-lg border bg-card text-card-foreground shadow">
            <div className="p-6">
              <Skeleton className="h-5 w-32 mb-4" />
              <div className="space-y-4">
                <div className="flex items-center gap-4">
                  <Skeleton className="h-10 w-10 rounded-full" />
                  <div>
                    <Skeleton className="h-4 w-32 mb-1" />
                    <Skeleton className="h-3 w-24" />
                  </div>
                </div>
                <div className="space-y-2">
                  <Skeleton className="h-4 w-full" />
                  <Skeleton className="h-4 w-full" />
                </div>
                <Skeleton className="h-9 w-full" />
              </div>
            </div>
          </div>

          {/* Shooting Schedule Card */}
          <div className="rounded-lg border bg-card text-card-foreground shadow">
            <div className="p-6">
              <Skeleton className="h-5 w-40 mb-4" />
              <div className="space-y-4">
                {Array.from({ length: 3 }).map((_, i) => (
                  <div key={i} className="border rounded-md p-3">
                    <div className="flex items-center gap-2 mb-1">
                      <Skeleton className="h-4 w-4 rounded-full" />
                      <Skeleton className="h-4 w-24" />
                    </div>
                    <Skeleton className="h-3 w-full mb-1" />
                    <Skeleton className="h-5 w-16" />
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* Project Card */}
          <div className="rounded-lg border bg-card text-card-foreground shadow">
            <div className="p-6">
              <Skeleton className="h-5 w-16 mb-4" />
              <div className="space-y-4">
                <div className="flex items-center gap-4">
                  <Skeleton className="h-10 w-10 rounded-md" />
                  <div>
                    <Skeleton className="h-4 w-32 mb-1" />
                    <Skeleton className="h-3 w-24" />
                  </div>
                </div>
                <div className="border-t pt-4">
                  <Skeleton className="h-3 w-32 mb-1" />
                  <Skeleton className="h-4 w-full mb-1" />
                  <Skeleton className="h-4 w-3/4" />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </DashboardShell>
  );
}
