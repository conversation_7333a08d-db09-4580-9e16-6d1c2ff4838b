import { DashboardShell } from "@/components/dashboard/dashboard-shell"
import { LocationDetailsClient } from "@/components/locations/location-details-client"
import React from "react"

export default function LocationDetailPage({ params }: { params: Promise<{ id: string, organizationSlug: string }> }) {
  // Properly unwrap params using React.use()
  const unwrappedParams = React.use(params)
  const locationId = unwrappedParams.id
  const organizationSlug = unwrappedParams.organizationSlug
  
  return (
    <DashboardShell>
      <LocationDetailsClient 
        locationId={locationId} 
        organizationSlug={organizationSlug} 
      />
    </DashboardShell>
  )
}
