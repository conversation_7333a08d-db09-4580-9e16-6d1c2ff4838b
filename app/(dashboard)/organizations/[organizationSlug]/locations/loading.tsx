import { DashboardShell } from "@/components/dashboard/dashboard-shell";
import { DashboardHeader } from "@/components/dashboard/dashboard-header";
import { Button } from "@/components/ui/button";
import { Skeleton } from "@/components/ui/skeleton";
import { Tabs, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Plus, Grid3X3, List } from "lucide-react";

export default function Loading() {
  return (
    <DashboardShell>
      <DashboardHeader title="Locations" description="Manage and organize your filming locations">
        <Button disabled>
          <Plus className="mr-2 h-4 w-4" />
          Add New Location
        </Button>
      </DashboardHeader>

      <div className="flex flex-col md:flex-row gap-4 mb-6">
        <div className="relative flex-1">
          <Skeleton className="h-10 w-full" />
        </div>
        <Skeleton className="h-10 w-24" />
        <Tabs defaultValue="grid" className="w-[120px]">
          <TabsList>
            <TabsTrigger value="grid" className="px-3" disabled>
              <Grid3X3 className="h-4 w-4" />
            </TabsTrigger>
            <TabsTrigger value="list" className="px-3" disabled>
              <List className="h-4 w-4" />
            </TabsTrigger>
          </TabsList>
        </Tabs>
      </div>

      <Tabs defaultValue="grid" className="space-y-4">
        <TabsContent value="grid" className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
            {Array.from({ length: 6 }).map((_, i) => (
              <div key={i} className="h-full overflow-hidden rounded-lg border bg-card text-card-foreground shadow">
                <Skeleton className="aspect-video w-full" />
                <div className="p-4">
                  <div className="flex items-center justify-between">
                    <Skeleton className="h-5 w-1/2" />
                    <Skeleton className="h-5 w-16" />
                  </div>
                  <Skeleton className="mt-2 h-4 w-full" />
                  <Skeleton className="mt-1 h-4 w-3/4" />
                </div>
                <div className="border-t p-4 pt-2">
                  <div className="flex justify-between">
                    <Skeleton className="h-4 w-16" />
                    <Skeleton className="h-4 w-24" />
                  </div>
                </div>
              </div>
            ))}
          </div>
        </TabsContent>
        <TabsContent value="list" className="space-y-4">
          <div className="rounded-md border">
            <div className="p-1">
              <Skeleton className="h-[320px] w-full" />
            </div>
          </div>
        </TabsContent>
      </Tabs>
    </DashboardShell>
  );
}
