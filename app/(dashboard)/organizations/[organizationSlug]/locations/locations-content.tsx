"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs";
import { Plus, Search, Filter, Grid3X3, List, Loader2 } from "lucide-react";
import { LocationGrid } from "@/components/locations/location-grid";
import { LocationList } from "@/components/locations/location-list";
import { DashboardShell } from "@/components/dashboard/dashboard-shell";
import { DashboardHeader } from "@/components/dashboard/dashboard-header";
import { useState } from "react";
import React from "react";
import { useLocations } from "@/hooks/use-locations";
import { useDebounce } from "@/hooks/use-debounce";
import { Pagination } from "@/components/ui/pagination";
import dynamic from 'next/dynamic';

// Lazy load the modals
const FilterModal = dynamic(
  () => import('@/components/locations/filter-modal').then(mod => ({ default: mod.FilterModal })),
  { ssr: false, loading: () => <div className="hidden">Loading...</div> }
);

const AddLocationModal = dynamic(
  () => import('@/components/locations/add-location-modal').then(mod => ({ default: mod.AddLocationModal })),
  { ssr: false, loading: () => <div className="hidden">Loading...</div> }
);

interface LocationsContentProps {
  params: Promise<{
    organizationSlug: string;
  }>;
}

export function LocationsContent({ params }: LocationsContentProps) {
  const unwrappedParams = React.use(params);
  const { organizationSlug } = unwrappedParams;
  const [searchQuery, setSearchQuery] = useState("");
  const [viewMode, setViewMode] = useState("grid");
  const [page, setPage] = useState(1);
  const pageSize = 12;
  const [isFilterModalOpen, setIsFilterModalOpen] = useState(false);
  const [isAddLocationModalOpen, setIsAddLocationModalOpen] = useState(false);
  const [filters, setFilters] = useState({
    status: "",
    type: "",
    projectId: "",
  });
  
  // Debounce search query to prevent excessive API calls
  const debouncedSearch = useDebounce(searchQuery, 500);
  
  // Fetch locations with filters
  const { 
    data, 
    isLoading, 
    error
  } = useLocations({
    search: debouncedSearch,
    status: filters.status,
    type: filters.type,
    projectId: filters.projectId,
    page,
    pageSize, // Use the defined pageSize constant (12)
    useCommonFilter: false, // Disable common filter to ensure we get all locations
  });
  
  // Extract locations and pagination info from data
  const locations = data?.locations || [];
  const totalLocations = data?.total || 0;
  const totalPages = data?.totalPages || Math.ceil(totalLocations / pageSize);

  // Handle filter changes
  const handleFilterChange = (newFilters: {
    status: string;
    type: string;
    projectId: string;
  }) => {
    setFilters(newFilters);
    setPage(1); // Reset to first page when filters change
  };

  return (
    <DashboardShell>
      <DashboardHeader
        title="Locations"
        description="Manage and organize your filming locations"
      >
        <Button onClick={() => setIsAddLocationModalOpen(true)}>
          <Plus className="mr-2 h-4 w-4" />
          Add New Location
        </Button>
      </DashboardHeader>

      <div className="flex flex-col md:flex-row gap-4 mb-6 mt-6">
        <div className="relative flex-1">
          <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input 
            type="search" 
            placeholder="Search locations..." 
            className="w-full pl-8"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
        </div>
        <Button 
          variant="outline" 
          className="gap-1"
          onClick={() => setIsFilterModalOpen(true)}
        >
          <Filter className="h-4 w-4" />
          Filters
        </Button>
        <Tabs 
          defaultValue={viewMode} 
          value={viewMode} 
          onValueChange={setViewMode} 
          className="w-[120px]"
        >
          <TabsList>
            <TabsTrigger value="grid" className="px-3">
              <Grid3X3 className="h-4 w-4" />
            </TabsTrigger>
            <TabsTrigger value="list" className="px-3">
              <List className="h-4 w-4" />
            </TabsTrigger>
          </TabsList>
        </Tabs>
      </div>

      {isLoading ? (
        <div className="flex items-center justify-center py-10">
          <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
        </div>
      ) : error ? (
        <div className="flex flex-col items-center justify-center py-10">
          <p className="text-destructive">Error loading locations</p>
          <Button 
            variant="outline" 
            className="mt-4"
            onClick={() => window.location.reload()}
          >
            Try Again
          </Button>
        </div>
      ) : locations.length === 0 ? (
        <div className="flex flex-col items-center justify-center py-10">
          <p className="text-muted-foreground">No locations found</p>
          <Button className="mt-4" onClick={() => setIsAddLocationModalOpen(true)}>
            <Plus className="mr-2 h-4 w-4" />
            Add New Location
          </Button>
        </div>
      ) : (
        <>
          <Tabs 
            defaultValue={viewMode} 
            value={viewMode} 
            onValueChange={setViewMode} 
            className="space-y-4"
          >
          <TabsContent value="grid" className="space-y-4">
            <LocationGrid 
              locations={locations} 
              organizationSlug={organizationSlug} 
              isLoading={isLoading} 
            />
          </TabsContent>
          <TabsContent value="list" className="space-y-4">
            <LocationList 
              locations={locations} 
              organizationSlug={organizationSlug} 
              isLoading={isLoading} 
            />
          </TabsContent>
          </Tabs>
          
          {totalPages > 1 && (
            <div className="flex justify-center mt-6">
              <Pagination
                currentPage={page}
                totalPages={totalPages}
                onPageChange={setPage}
              />
            </div>
          )}
        </>
      )}
      
      {isAddLocationModalOpen && (
        <AddLocationModal
          isOpen={isAddLocationModalOpen}
          onClose={() => setIsAddLocationModalOpen(false)}
        />
      )}
      
      {isFilterModalOpen && (
        <FilterModal
          isOpen={isFilterModalOpen}
          onClose={() => setIsFilterModalOpen(false)}
          filters={filters}
          onFilterChange={handleFilterChange}
        />
      )}
    </DashboardShell>
  );
}
