"use client"

import type React from "react"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Textarea } from "@/components/ui/textarea"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { addNoteToSceneAction, deleteSceneNoteAction } from "@/modules/scene/actions"
import { formatRelativeTime } from "@/lib/utils/date"
import { getInitials } from "@/lib/utils/string"
import { Send, Trash2 } from "lucide-react"
import type { Scene } from "@/modules/scene/model"

type SceneDiscussionProps = {
  scene: Scene & {
    sceneNotes?: any[]
  }
}

export function SceneDiscussion({ scene }: SceneDiscussionProps) {
  const [content, setContent] = useState("")
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!content.trim()) return

    setError(null)
    setIsSubmitting(true)

    try {
      const result = await addNoteToSceneAction({
        sceneId: scene.id,
        content,
      })

      if (result.success) {
        setContent("")
      } else {
        setError(result.error || "Failed to add note")
      }
    } catch (error) {
      console.error("Error adding note:", error)
      setError("An unexpected error occurred")
    } finally {
      setIsSubmitting(false)
    }
  }

  const handleDeleteNote = async (noteId: string) => {
    try {
      await deleteSceneNoteAction(noteId, scene.id)
    } catch (error) {
      console.error("Error deleting note:", error)
    }
  }

  return (
    <div className="space-y-6">
      <div className="border rounded-lg overflow-hidden">
        <div className="p-4 bg-muted/50 border-b">
          <h3 className="font-medium">Team Discussion</h3>
          <p className="text-sm text-muted-foreground">
            Discuss this scene with your team members. All comments are visible to everyone with access to this project.
          </p>
        </div>

        <div className="p-4 max-h-[400px] overflow-y-auto space-y-4">
          {scene.sceneNotes?.length === 0 ? (
            <div className="text-center py-8">
              <p className="text-muted-foreground">No comments yet. Be the first to start the discussion!</p>
            </div>
          ) : (
            scene.sceneNotes?.map((note) => (
              <div key={note.id} className="flex gap-3 group">
                <Avatar className="h-8 w-8">
                  <AvatarImage src={note.author?.avatar || "/placeholder.svg"} alt={note.author?.name || "User"} />
                  <AvatarFallback>{getInitials(note.author?.name || note.author?.email || "User")}</AvatarFallback>
                </Avatar>
                <div className="flex-1">
                  <div className="flex items-center gap-2">
                    <span className="font-medium text-sm">
                      {note.author?.name || note.author?.email || "Unknown User"}
                    </span>
                    <span className="text-xs text-muted-foreground">{formatRelativeTime(note.createdAt)}</span>
                  </div>
                  <div className="mt-1 text-sm">{note.content}</div>
                </div>
                <Button
                  variant="ghost"
                  size="icon"
                  className="opacity-0 group-hover:opacity-100 transition-opacity"
                  onClick={() => handleDeleteNote(note.id)}
                >
                  <Trash2 className="h-4 w-4" />
                  <span className="sr-only">Delete</span>
                </Button>
              </div>
            ))
          )}
        </div>

        <div className="p-4 border-t">
          <form onSubmit={handleSubmit} className="space-y-2">
            <Textarea
              value={content}
              onChange={(e) => setContent(e.target.value)}
              placeholder="Add a comment..."
              rows={3}
            />
            {error && <p className="text-sm text-red-500">{error}</p>}
            <div className="flex justify-end">
              <Button type="submit" disabled={!content.trim() || isSubmitting}>
                {isSubmitting ? "Sending..." : "Send"}
                <Send className="ml-2 h-4 w-4" />
              </Button>
            </div>
          </form>
        </div>
      </div>
    </div>
  )
}
