"use client"

import { Label } from "@/components/ui/label"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON>ooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Trigger } from "@/components/ui/tabs"
import { Badge } from "@/components/ui/badge"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { Textarea } from "@/components/ui/textarea"
import { Search, MapPin, Grid, List, Plus, ThumbsUp, ThumbsDown, Check, Info } from "lucide-react"
import { LocationClientView } from "./location-client-view"
import { MapView } from "@/components/maps/map-view"
import { addLocationToSceneAction, selectLocationForSceneAction, voteForLocationAction } from "@/modules/scene/actions"
import type { Scene } from "@/modules/scene/model"
import type { Location } from "@/modules/location/model"

type SceneLocationSelectionProps = {
  scene: Scene & {
    finalLocation?: any
    sceneLocations?: any[]
  }
  projectLocations: Location[]
}

export function SceneLocationSelection({ scene, projectLocations }: SceneLocationSelectionProps) {
  const [viewMode, setViewMode] = useState<"grid" | "list">("grid")
  const [searchQuery, setSearchQuery] = useState("")
  const [selectedLocation, setSelectedLocation] = useState<Location | null>(null)
  const [showAddDialog, setShowAddDialog] = useState(false)
  const [notes, setNotes] = useState("")
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [error, setError] = useState<string | null>(null)

  // Filter scene locations based on search query
  const filteredSceneLocations = scene.sceneLocations?.filter((sceneLocation) => {
    const location = sceneLocation.location
    if (!location) return false

    const query = searchQuery.toLowerCase()
    return (
      location.name.toLowerCase().includes(query) ||
      location.address.formatted?.toLowerCase().includes(query) ||
      location.address.city.toLowerCase().includes(query) ||
      location.address.state.toLowerCase().includes(query) ||
      location.type.toLowerCase().includes(query)
    )
  })

  // Filter project locations based on search query and exclude those already added to the scene
  const sceneLocationIds = scene.sceneLocations?.map((sl) => sl.locationId) || []
  const filteredProjectLocations = projectLocations.filter((location) => {
    if (sceneLocationIds.includes(location.id)) return false

    const query = searchQuery.toLowerCase()
    return (
      location.name.toLowerCase().includes(query) ||
      location.address.formatted?.toLowerCase().includes(query) ||
      location.address.city.toLowerCase().includes(query) ||
      location.address.state.toLowerCase().includes(query) ||
      location.type.toLowerCase().includes(query)
    )
  })

  // Get marker color based on location type
  const getMarkerColor = (type: string) => {
    switch (type) {
      case "interior":
        return "#3b82f6" // blue
      case "exterior":
        return "#f97316" // orange
      case "studio":
        return "#a855f7" // purple
      case "house":
      case "apartment":
        return "#10b981" // emerald
      case "office":
        return "#0ea5e9" // sky
      case "warehouse":
        return "#f59e0b" // amber
      case "retail":
      case "restaurant":
      case "bar":
        return "#ec4899" // pink
      case "hotel":
        return "#8b5cf6" // violet
      case "park":
      case "beach":
      case "forest":
        return "#22c55e" // green
      case "urban":
        return "#64748b" // slate
      case "rural":
        return "#84cc16" // lime
      case "industrial":
        return "#94a3b8" // gray
      case "historic":
        return "#b45309" // amber-800
      case "modern":
        return "#0f766e" // teal-700
      default:
        return "#6b7280" // gray
    }
  }

  // Handle adding a location to the scene
  const handleAddLocation = async () => {
    if (!selectedLocation) return

    setError(null)
    setIsSubmitting(true)

    try {
      const result = await addLocationToSceneAction({
        sceneId: scene.id,
        locationId: selectedLocation.id,
        notes,
        isSelected: false,
      })

      if (result.success) {
        setShowAddDialog(false)
        setSelectedLocation(null)
        setNotes("")
      } else {
        setError(result.error || "Failed to add location to scene")
      }
    } catch (error) {
      console.error("Error adding location to scene:", error)
      setError("An unexpected error occurred")
    } finally {
      setIsSubmitting(false)
    }
  }

  // Handle selecting a location for the scene
  const handleSelectLocation = async (sceneLocationId: string) => {
    setError(null)
    setIsSubmitting(true)

    try {
      const result = await selectLocationForSceneAction(sceneLocationId, scene.id)

      if (!result.success) {
        setError(result.error || "Failed to select location for scene")
      }
    } catch (error) {
      console.error("Error selecting location for scene:", error)
      setError("An unexpected error occurred")
    } finally {
      setIsSubmitting(false)
    }
  }

  // Handle voting for a location
  const handleVote = async (sceneLocationId: string, vote: "up" | "down" | "neutral") => {
    try {
      await voteForLocationAction(
        {
          sceneLocationId,
          vote,
        },
        scene.id,
      )
    } catch (error) {
      console.error("Error voting for location:", error)
    }
  }

  // Get vote counts for a scene location
  const getVoteCounts = (sceneLocation: any) => {
    const votes = sceneLocation.votes || []
    const upVotes = votes.filter((v: any) => v.vote === "up").length
    const downVotes = votes.filter((v: any) => v.vote === "down").length
    return { upVotes, downVotes, total: votes.length }
  }

  // Check if the user has voted for a location
  const getUserVote = (sceneLocation: any) => {
    const votes = sceneLocation.votes || []
    // In a real app, you'd use the current user's ID
    // For now, we'll just return null
    return null
  }

  return (
    <div className="space-y-6">
      <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
        <div className="relative flex-1">
          <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            type="search"
            placeholder="Search locations..."
            className="pl-8"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
        </div>
        <div className="flex items-center gap-2">
          <Button variant={viewMode === "grid" ? "default" : "outline"} size="icon" onClick={() => setViewMode("grid")}>
            <Grid className="h-4 w-4" />
          </Button>
          <Button variant={viewMode === "list" ? "default" : "outline"} size="icon" onClick={() => setViewMode("list")}>
            <List className="h-4 w-4" />
          </Button>
        </div>
      </div>

      <Tabs defaultValue="scene-locations">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="scene-locations">Scene Locations</TabsTrigger>
          <TabsTrigger value="project-locations">Project Locations</TabsTrigger>
        </TabsList>

        <TabsContent value="scene-locations" className="pt-4">
          {filteredSceneLocations?.length === 0 ? (
            <div className="text-center p-8 border rounded-lg bg-muted/20">
              <MapPin className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
              <h3 className="text-lg font-medium mb-2">No Locations Added</h3>
              <p className="text-muted-foreground mb-4">
                Add potential locations for this scene from the Project Locations tab.
              </p>
            </div>
          ) : viewMode === "grid" ? (
            <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
              {filteredSceneLocations?.map((sceneLocation) => {
                const location = sceneLocation.location
                const voteCounts = getVoteCounts(sceneLocation)
                const userVote = getUserVote(sceneLocation)
                return (
                  <Card key={sceneLocation.id} className="overflow-hidden">
                    <div className="aspect-video relative">
                      <MapView locations={[location]} height="100%" interactive={false} showControls={false} />
                      {sceneLocation.isSelected && (
                        <div className="absolute top-2 right-2">
                          <Badge className="bg-green-500 text-white">Selected</Badge>
                        </div>
                      )}
                    </div>
                    <CardHeader className="pb-2">
                      <div className="flex justify-between items-start">
                        <CardTitle className="text-lg">{location.name}</CardTitle>
                        <Badge variant="outline" className="capitalize">
                          {location.type}
                        </Badge>
                      </div>
                    </CardHeader>
                    <CardContent className="space-y-2">
                      <p className="text-sm text-muted-foreground">
                        {location.address.formatted ||
                          `${location.address.street}, ${location.address.city}, ${location.address.state} ${location.address.postalCode}`}
                      </p>
                      {sceneLocation.notes && (
                        <div className="text-sm bg-muted p-2 rounded-md">
                          <p className="font-medium">Notes:</p>
                          <p>{sceneLocation.notes}</p>
                        </div>
                      )}
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-4">
                          <div className="flex items-center gap-1">
                            <ThumbsUp
                              className={`h-4 w-4 ${userVote === "up" ? "text-green-500" : "text-muted-foreground"}`}
                            />
                            <span className="text-xs">{voteCounts.upVotes}</span>
                          </div>
                          <div className="flex items-center gap-1">
                            <ThumbsDown
                              className={`h-4 w-4 ${userVote === "down" ? "text-red-500" : "text-muted-foreground"}`}
                            />
                            <span className="text-xs">{voteCounts.downVotes}</span>
                          </div>
                        </div>
                        <Dialog>
                          <DialogTrigger asChild>
                            <Button variant="ghost" size="sm">
                              <Info className="h-4 w-4 mr-1" />
                              Details
                            </Button>
                          </DialogTrigger>
                          <DialogContent className="max-w-3xl">
                            <LocationClientView location={location} />
                          </DialogContent>
                        </Dialog>
                      </div>
                    </CardContent>
                    <CardFooter className="flex justify-between bg-muted/50 p-2">
                      <div className="flex gap-2">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleVote(sceneLocation.id, "up")}
                          className={userVote === "up" ? "text-green-500" : ""}
                        >
                          <ThumbsUp className="h-4 w-4 mr-1" />
                          Like
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleVote(sceneLocation.id, "down")}
                          className={userVote === "down" ? "text-red-500" : ""}
                        >
                          <ThumbsDown className="h-4 w-4 mr-1" />
                          Dislike
                        </Button>
                      </div>
                      {!sceneLocation.isSelected && (
                        <Button
                          variant="default"
                          size="sm"
                          onClick={() => handleSelectLocation(sceneLocation.id)}
                          disabled={isSubmitting}
                        >
                          <Check className="h-4 w-4 mr-1" />
                          Select
                        </Button>
                      )}
                    </CardFooter>
                  </Card>
                )
              })}
            </div>
          ) : (
            <div className="space-y-2">
              {filteredSceneLocations?.map((sceneLocation) => {
                const location = sceneLocation.location
                const voteCounts = getVoteCounts(sceneLocation)
                const userVote = getUserVote(sceneLocation)
                return (
                  <div
                    key={sceneLocation.id}
                    className="flex items-center gap-4 p-3 border rounded-md hover:bg-muted/50"
                  >
                    <div
                      className="h-10 w-10 rounded-full flex items-center justify-center"
                      style={{ backgroundColor: getMarkerColor(location.type) }}
                    >
                      <MapPin className="h-5 w-5 text-white" />
                    </div>
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center gap-2">
                        <h3 className="font-medium truncate">{location.name}</h3>
                        {sceneLocation.isSelected && <Badge className="bg-green-500 text-white">Selected</Badge>}
                      </div>
                      <p className="text-sm text-muted-foreground truncate">
                        {location.address.formatted ||
                          `${location.address.street}, ${location.address.city}, ${location.address.state}`}
                      </p>
                    </div>
                    <div className="flex items-center gap-2">
                      <div className="flex items-center gap-1">
                        <ThumbsUp
                          className={`h-4 w-4 ${userVote === "up" ? "text-green-500" : "text-muted-foreground"}`}
                        />
                        <span className="text-xs">{voteCounts.upVotes}</span>
                      </div>
                      <div className="flex items-center gap-1">
                        <ThumbsDown
                          className={`h-4 w-4 ${userVote === "down" ? "text-red-500" : "text-muted-foreground"}`}
                        />
                        <span className="text-xs">{voteCounts.downVotes}</span>
                      </div>
                    </div>
                    <div className="flex items-center gap-2">
                      <Dialog>
                        <DialogTrigger asChild>
                          <Button variant="ghost" size="sm">
                            <Info className="h-4 w-4" />
                            <span className="sr-only">Details</span>
                          </Button>
                        </DialogTrigger>
                        <DialogContent className="max-w-3xl">
                          <LocationClientView location={location} />
                        </DialogContent>
                      </Dialog>
                      {!sceneLocation.isSelected && (
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleSelectLocation(sceneLocation.id)}
                          disabled={isSubmitting}
                        >
                          <Check className="h-4 w-4" />
                          <span className="sr-only">Select</span>
                        </Button>
                      )}
                    </div>
                  </div>
                )
              })}
            </div>
          )}
        </TabsContent>

        <TabsContent value="project-locations" className="pt-4">
          {filteredProjectLocations.length === 0 ? (
            <div className="text-center p-8 border rounded-lg bg-muted/20">
              <MapPin className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
              <h3 className="text-lg font-medium mb-2">No Matching Locations</h3>
              <p className="text-muted-foreground mb-4">
                {searchQuery
                  ? "No locations match your search criteria."
                  : "No additional locations available in this project."}
              </p>
            </div>
          ) : viewMode === "grid" ? (
            <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
              {filteredProjectLocations.map((location) => (
                <Card key={location.id} className="overflow-hidden">
                  <div className="aspect-video relative">
                    <MapView locations={[location]} height="100%" interactive={false} showControls={false} />
                  </div>
                  <CardHeader className="pb-2">
                    <div className="flex justify-between items-start">
                      <CardTitle className="text-lg">{location.name}</CardTitle>
                      <Badge variant="outline" className="capitalize">
                        {location.type}
                      </Badge>
                    </div>
                  </CardHeader>
                  <CardContent className="space-y-2">
                    <p className="text-sm text-muted-foreground">
                      {location.address.formatted ||
                        `${location.address.street}, ${location.address.city}, ${location.address.state} ${location.address.postalCode}`}
                    </p>
                  </CardContent>
                  <CardFooter className="flex justify-between bg-muted/50 p-2">
                    <Dialog>
                      <DialogTrigger asChild>
                        <Button variant="ghost" size="sm">
                          <Info className="h-4 w-4 mr-1" />
                          Details
                        </Button>
                      </DialogTrigger>
                      <DialogContent className="max-w-3xl">
                        <LocationClientView location={location} />
                      </DialogContent>
                    </Dialog>
                    <Dialog
                      open={showAddDialog && selectedLocation?.id === location.id}
                      onOpenChange={(open) => {
                        if (!open) {
                          setSelectedLocation(null)
                        }
                        setShowAddDialog(open)
                      }}
                    >
                      <DialogTrigger asChild>
                        <Button variant="default" size="sm" onClick={() => setSelectedLocation(location)}>
                          <Plus className="h-4 w-4 mr-1" />
                          Add to Scene
                        </Button>
                      </DialogTrigger>
                      <DialogContent>
                        <DialogHeader>
                          <DialogTitle>Add Location to Scene</DialogTitle>
                          <DialogDescription>Add this location as a potential option for this scene.</DialogDescription>
                        </DialogHeader>
                        <div className="py-4">
                          <div className="mb-4">
                            <h3 className="font-medium">{location.name}</h3>
                            <p className="text-sm text-muted-foreground">
                              {location.address.formatted ||
                                `${location.address.street}, ${location.address.city}, ${location.address.state}`}
                            </p>
                          </div>
                          <div className="space-y-2">
                            <Label htmlFor="notes">Notes (optional)</Label>
                            <Textarea
                              id="notes"
                              value={notes}
                              onChange={(e) => setNotes(e.target.value)}
                              placeholder="Add any notes about why this location might be suitable for this scene"
                              rows={4}
                            />
                          </div>
                          {error && <p className="text-sm text-red-500 mt-2">{error}</p>}
                        </div>
                        <DialogFooter>
                          <Button
                            variant="outline"
                            onClick={() => {
                              setShowAddDialog(false)
                              setSelectedLocation(null)
                            }}
                          >
                            Cancel
                          </Button>
                          <Button onClick={handleAddLocation} disabled={isSubmitting}>
                            {isSubmitting ? "Adding..." : "Add Location"}
                          </Button>
                        </DialogFooter>
                      </DialogContent>
                    </Dialog>
                  </CardFooter>
                </Card>
              ))}
            </div>
          ) : (
            <div className="space-y-2">
              {filteredProjectLocations.map((location) => (
                <div key={location.id} className="flex items-center gap-4 p-3 border rounded-md hover:bg-muted/50">
                  <div
                    className="h-10 w-10 rounded-full flex items-center justify-center"
                    style={{ backgroundColor: getMarkerColor(location.type) }}
                  >
                    <MapPin className="h-5 w-5 text-white" />
                  </div>
                  <div className="flex-1 min-w-0">
                    <h3 className="font-medium truncate">{location.name}</h3>
                    <p className="text-sm text-muted-foreground truncate">
                      {location.address.formatted ||
                        `${location.address.street}, ${location.address.city}, ${location.address.state}`}
                    </p>
                  </div>
                  <div className="flex items-center gap-2">
                    <Dialog>
                      <DialogTrigger asChild>
                        <Button variant="ghost" size="sm">
                          <Info className="h-4 w-4" />
                          <span className="sr-only">Details</span>
                        </Button>
                      </DialogTrigger>
                      <DialogContent className="max-w-3xl">
                        <LocationClientView location={location} />
                      </DialogContent>
                    </Dialog>
                    <Dialog
                      open={showAddDialog && selectedLocation?.id === location.id}
                      onOpenChange={(open) => {
                        if (!open) {
                          setSelectedLocation(null)
                        }
                        setShowAddDialog(open)
                      }}
                    >
                      <DialogTrigger asChild>
                        <Button variant="ghost" size="sm" onClick={() => setSelectedLocation(location)}>
                          <Plus className="h-4 w-4" />
                          <span className="sr-only">Add</span>
                        </Button>
                      </DialogTrigger>
                      <DialogContent>
                        <DialogHeader>
                          <DialogTitle>Add Location to Scene</DialogTitle>
                          <DialogDescription>Add this location as a potential option for this scene.</DialogDescription>
                        </DialogHeader>
                        <div className="py-4">
                          <div className="mb-4">
                            <h3 className="font-medium">{location.name}</h3>
                            <p className="text-sm text-muted-foreground">
                              {location.address.formatted ||
                                `${location.address.street}, ${location.address.city}, ${location.address.state}`}
                            </p>
                          </div>
                          <div className="space-y-2">
                            <Label htmlFor="notes">Notes (optional)</Label>
                            <Textarea
                              id="notes"
                              value={notes}
                              onChange={(e) => setNotes(e.target.value)}
                              placeholder="Add any notes about why this location might be suitable for this scene"
                              rows={4}
                            />
                          </div>
                          {error && <p className="text-sm text-red-500 mt-2">{error}</p>}
                        </div>
                        <DialogFooter>
                          <Button
                            variant="outline"
                            onClick={() => {
                              setShowAddDialog(false)
                              setSelectedLocation(null)
                            }}
                          >
                            Cancel
                          </Button>
                          <Button onClick={handleAddLocation} disabled={isSubmitting}>
                            {isSubmitting ? "Adding..." : "Add Location"}
                          </Button>
                        </DialogFooter>
                      </DialogContent>
                    </Dialog>
                  </div>
                </div>
              ))}
            </div>
          )}
        </TabsContent>
      </Tabs>
    </div>
  )
}
