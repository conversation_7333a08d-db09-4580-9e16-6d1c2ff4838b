"use client"

import type React from "react"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import {
  <PERSON>alog,
  DialogContent,
  DialogDescription,
  <PERSON><PERSON><PERSON><PERSON>er,
  <PERSON><PERSON><PERSON>eader,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>rigger,
} from "@/components/ui/dialog"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Checkbox } from "@/components/ui/checkbox"
import { Badge } from "@/components/ui/badge"
import { Clock, Home, Building2, Edit, Calendar, Film, MapPin } from "lucide-react"
import { editScene } from "@/modules/scene/actions"
import { SCENE_STATUSES, TIME_OF_DAY } from "@/modules/shared/constants"
import { capitalize } from "@/lib/utils/string"
import { formatDate } from "@/lib/utils/date"
import type { Scene } from "@/modules/scene/model"
import { MapView } from "@/components/maps/map-view"

type SceneDetailProps = {
  scene: Scene & {
    finalLocation?: any
    project?: any
    creator?: any
    sceneLocations?: any[]
    sceneNotes?: any[]
  }
}

export function SceneDetail({ scene }: SceneDetailProps) {
  const [isEditing, setIsEditing] = useState(false)
  const [formData, setFormData] = useState({
    name: scene.name,
    description: scene.description || "",
    sceneNumber: scene.sceneNumber || "",
    timeOfDay: scene.timeOfDay || "",
    interior: scene.interior,
    exterior: scene.exterior,
    status: scene.status,
  })
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target
    setFormData((prev) => ({ ...prev, [name]: value }))
  }

  const handleSelectChange = (name: string, value: string) => {
    setFormData((prev) => ({ ...prev, [name]: value }))
  }

  const handleCheckboxChange = (name: string, checked: boolean) => {
    setFormData((prev) => ({ ...prev, [name]: checked }))
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setError(null)
    setIsSubmitting(true)

    try {
      const result = await editScene(scene.id, formData)

      if (result.success) {
        setIsEditing(false)
      } else {
        setError(result.error || "Failed to update scene")
      }
    } catch (error) {
      console.error("Error updating scene:", error)
      setError("An unexpected error occurred")
    } finally {
      setIsSubmitting(false)
    }
  }

  const getStatusBadgeClasses = (status: string) => {
    switch (status) {
      case "planning":
        return "bg-blue-100 text-blue-800"
      case "scouting":
        return "bg-amber-100 text-amber-800"
      case "selected":
        return "bg-green-100 text-green-800"
      case "permitted":
        return "bg-purple-100 text-purple-800"
      case "scheduled":
        return "bg-indigo-100 text-indigo-800"
      case "completed":
        return "bg-gray-100 text-gray-800"
      default:
        return "bg-gray-100 text-gray-800"
    }
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div className="flex items-center gap-2">
          <Badge className={getStatusBadgeClasses(scene.status)}>{capitalize(scene.status)}</Badge>
          {scene.project && (
            <Badge variant="outline" className="text-muted-foreground">
              {scene.project.name}
            </Badge>
          )}
        </div>
        <Dialog open={isEditing} onOpenChange={setIsEditing}>
          <DialogTrigger asChild>
            <Button variant="outline" size="sm">
              <Edit className="h-4 w-4 mr-2" />
              Edit Scene
            </Button>
          </DialogTrigger>
          <DialogContent className="sm:max-w-[500px]">
            <form onSubmit={handleSubmit}>
              <DialogHeader>
                <DialogTitle>Edit Scene</DialogTitle>
                <DialogDescription>Update scene details.</DialogDescription>
              </DialogHeader>
              <div className="grid gap-4 py-4">
                <div className="grid grid-cols-4 gap-4">
                  <div className="col-span-1">
                    <Label htmlFor="sceneNumber">Scene #</Label>
                    <Input
                      id="sceneNumber"
                      name="sceneNumber"
                      value={formData.sceneNumber}
                      onChange={handleChange}
                      placeholder="1A"
                    />
                  </div>
                  <div className="col-span-3">
                    <Label htmlFor="name">Scene Name</Label>
                    <Input
                      id="name"
                      name="name"
                      value={formData.name}
                      onChange={handleChange}
                      placeholder="Scene name"
                      required
                    />
                  </div>
                </div>
                <div className="grid gap-2">
                  <Label htmlFor="description">Description</Label>
                  <Textarea
                    id="description"
                    name="description"
                    value={formData.description}
                    onChange={handleChange}
                    placeholder="Scene description"
                    rows={3}
                  />
                </div>
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="timeOfDay">Time of Day</Label>
                    <Select
                      value={formData.timeOfDay}
                      onValueChange={(value) => handleSelectChange("timeOfDay", value)}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Select time of day" />
                      </SelectTrigger>
                      <SelectContent>
                        {TIME_OF_DAY.map((time) => (
                          <SelectItem key={time} value={time}>
                            {capitalize(time)}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                  <div>
                    <Label htmlFor="status">Status</Label>
                    <Select value={formData.status} onValueChange={(value) => handleSelectChange("status", value)}>
                      <SelectTrigger>
                        <SelectValue placeholder="Select status" />
                      </SelectTrigger>
                      <SelectContent>
                        {SCENE_STATUSES.map((status) => (
                          <SelectItem key={status} value={status}>
                            {capitalize(status)}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                </div>
                <div className="flex space-x-4">
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="interior"
                      checked={formData.interior}
                      onCheckedChange={(checked) => handleCheckboxChange("interior", checked === true)}
                    />
                    <Label htmlFor="interior">Interior</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="exterior"
                      checked={formData.exterior}
                      onCheckedChange={(checked) => handleCheckboxChange("exterior", checked === true)}
                    />
                    <Label htmlFor="exterior">Exterior</Label>
                  </div>
                </div>
                {error && <p className="text-sm text-red-500">{error}</p>}
              </div>
              <DialogFooter>
                <Button type="button" variant="outline" onClick={() => setIsEditing(false)}>
                  Cancel
                </Button>
                <Button type="submit" disabled={isSubmitting}>
                  {isSubmitting ? "Saving..." : "Save Changes"}
                </Button>
              </DialogFooter>
            </form>
          </DialogContent>
        </Dialog>
      </div>

      <div className="grid gap-6 md:grid-cols-2">
        <Card>
          <CardHeader>
            <CardTitle>Scene Information</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-1">
              <p className="text-sm font-medium">Description</p>
              <p className="text-sm text-muted-foreground">{scene.description || "No description provided"}</p>
            </div>

            <div className="flex flex-wrap gap-2">
              {scene.timeOfDay && (
                <div className="flex items-center gap-1 bg-muted px-2 py-1 rounded-md text-sm">
                  <Clock className="h-4 w-4" />
                  <span>{capitalize(scene.timeOfDay)}</span>
                </div>
              )}
              {scene.interior && (
                <div className="flex items-center gap-1 bg-muted px-2 py-1 rounded-md text-sm">
                  <Home className="h-4 w-4" />
                  <span>Interior</span>
                </div>
              )}
              {scene.exterior && (
                <div className="flex items-center gap-1 bg-muted px-2 py-1 rounded-md text-sm">
                  <Building2 className="h-4 w-4" />
                  <span>Exterior</span>
                </div>
              )}
            </div>

            <div className="space-y-1">
              <p className="text-sm font-medium">Created</p>
              <div className="flex items-center gap-2 text-sm text-muted-foreground">
                <Calendar className="h-4 w-4" />
                <span>{formatDate(scene.createdAt)}</span>
                {scene.creator && <span>by {scene.creator.name || scene.creator.email}</span>}
              </div>
            </div>

            <div className="space-y-1">
              <p className="text-sm font-medium">Project</p>
              <div className="flex items-center gap-2 text-sm text-muted-foreground">
                <Film className="h-4 w-4" />
                <span>{scene.project?.name || "Unknown Project"}</span>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Selected Location</CardTitle>
          </CardHeader>
          <CardContent>
            {scene.finalLocation ? (
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <MapPin className="h-4 w-4 text-primary" />
                    <span className="font-medium">{scene.finalLocation.name}</span>
                  </div>
                  <Badge variant="outline" className="bg-green-50 text-green-700">
                    Selected
                  </Badge>
                </div>
                <div className="text-sm text-muted-foreground">
                  {scene.finalLocation.address.formatted ||
                    `${scene.finalLocation.address.street}, ${scene.finalLocation.address.city}, ${scene.finalLocation.address.state} ${scene.finalLocation.address.postalCode}`}
                </div>
                <div className="h-[200px] w-full rounded-md overflow-hidden border">
                  <MapView locations={[scene.finalLocation]} height="200px" interactive={false} showControls={false} />
                </div>
              </div>
            ) : (
              <div className="flex flex-col items-center justify-center h-[200px] text-center">
                <MapPin className="h-8 w-8 text-muted-foreground mb-2" />
                <p className="text-muted-foreground">No location selected yet</p>
                <p className="text-xs text-muted-foreground mt-1">
                  Go to the Locations tab to select a location for this scene
                </p>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
