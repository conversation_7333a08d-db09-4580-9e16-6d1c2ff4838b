"use client"

import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/tabs"
import { Badge } from "@/components/ui/badge"
import { MapView } from "@/components/maps/map-view"
import { Clock, Home, Building2, Camera } from "lucide-react"
import type { Location } from "@/modules/location/model"

type LocationClientViewProps = {
  location: Location
}

export function LocationClientView({ location }: LocationClientViewProps) {
  // Get marker color based on location type
  const getMarkerColor = (type: string) => {
    switch (type) {
      case "interior":
        return "#3b82f6" // blue
      case "exterior":
        return "#f97316" // orange
      case "studio":
        return "#a855f7" // purple
      case "house":
      case "apartment":
        return "#10b981" // emerald
      case "office":
        return "#0ea5e9" // sky
      case "warehouse":
        return "#f59e0b" // amber
      case "retail":
      case "restaurant":
      case "bar":
        return "#ec4899" // pink
      case "hotel":
        return "#8b5cf6" // violet
      case "park":
      case "beach":
      case "forest":
        return "#22c55e" // green
      case "urban":
        return "#64748b" // slate
      case "rural":
        return "#84cc16" // lime
      case "industrial":
        return "#94a3b8" // gray
      case "historic":
        return "#b45309" // amber-800
      case "modern":
        return "#0f766e" // teal-700
      default:
        return "#6b7280" // gray
    }
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <h2 className="text-xl font-bold">{location.name}</h2>
          <Badge variant="outline" className="capitalize">
            {location.type}
          </Badge>
        </div>
      </div>

      <Tabs defaultValue="info">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="info">Information</TabsTrigger>
          <TabsTrigger value="map">Map</TabsTrigger>
          <TabsTrigger value="gallery">Gallery</TabsTrigger>
        </TabsList>

        <TabsContent value="info" className="space-y-4 py-4">
          <div className="space-y-2">
            <h3 className="font-medium">Location Details</h3>
            <p className="text-sm text-muted-foreground">{location.description || "No description provided"}</p>
          </div>

          <div className="space-y-2">
            <h3 className="font-medium">Address</h3>
            <p className="text-sm text-muted-foreground">
              {location.address.formatted ||
                `${location.address.street}, ${location.address.city}, ${location.address.state} ${location.address.postalCode}, ${location.address.country}`}
            </p>
          </div>

          <div className="flex flex-wrap gap-2">
            {location.interior && (
              <div className="flex items-center gap-1 bg-muted px-2 py-1 rounded-md text-sm">
                <Home className="h-4 w-4" />
                <span>Interior</span>
              </div>
            )}
            {location.exterior && (
              <div className="flex items-center gap-1 bg-muted px-2 py-1 rounded-md text-sm">
                <Building2 className="h-4 w-4" />
                <span>Exterior</span>
              </div>
            )}
            {location.goldenMorningStart && location.goldenMorningEnd && (
              <div className="flex items-center gap-1 bg-muted px-2 py-1 rounded-md text-sm">
                <Clock className="h-4 w-4" />
                <span>
                  Morning Golden Hour: {location.goldenMorningStart} - {location.goldenMorningEnd}
                </span>
              </div>
            )}
            {location.goldenEveningStart && location.goldenEveningEnd && (
              <div className="flex items-center gap-1 bg-muted px-2 py-1 rounded-md text-sm">
                <Clock className="h-4 w-4" />
                <span>
                  Evening Golden Hour: {location.goldenEveningStart} - {location.goldenEveningEnd}
                </span>
              </div>
            )}
          </div>

          {location.locationFeatures && location.locationFeatures.length > 0 && (
            <div className="space-y-2">
              <h3 className="font-medium">Features</h3>
              <div className="flex flex-wrap gap-1">
                {location.locationFeatures.map((feature, index) => (
                  <Badge key={index} variant="outline" className="bg-slate-50">
                    {feature}
                  </Badge>
                ))}
              </div>
            </div>
          )}

          {location.locationTags && location.locationTags.length > 0 && (
            <div className="space-y-2">
              <h3 className="font-medium">Tags</h3>
              <div className="flex flex-wrap gap-1">
                {location.locationTags.map((tag, index) => (
                  <Badge key={index} variant="outline" className="bg-blue-50 text-blue-800">
                    {tag}
                  </Badge>
                ))}
              </div>
            </div>
          )}

          {location.accessibility && (
            <div className="space-y-2">
              <h3 className="font-medium">Accessibility</h3>
              <p className="text-sm text-muted-foreground">{location.accessibility}</p>
            </div>
          )}

          {location.parkingInfo && (
            <div className="space-y-2">
              <h3 className="font-medium">Parking Information</h3>
              <p className="text-sm text-muted-foreground">{location.parkingInfo}</p>
            </div>
          )}

          {location.restrictions && (
            <div className="space-y-2">
              <h3 className="font-medium">Restrictions</h3>
              <p className="text-sm text-muted-foreground">{location.restrictions}</p>
            </div>
          )}
        </TabsContent>

        <TabsContent value="map" className="py-4">
          <div className="h-[400px] w-full rounded-md overflow-hidden border">
            <MapView locations={[location]} height="400px" />
          </div>
        </TabsContent>

        <TabsContent value="gallery" className="py-4">
          {location.media && location.media.length > 0 ? (
            <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
              {location.media.map((media) => (
                <div key={media.id} className="aspect-video relative rounded-md overflow-hidden">
                  <img
                    src={media.thumbnailUrl || media.url}
                    alt={media.title || location.name}
                    className="object-cover w-full h-full"
                  />
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center p-8 border rounded-lg bg-muted/20">
              <Camera className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
              <h3 className="text-lg font-medium mb-2">No Images Available</h3>
              <p className="text-muted-foreground">This location doesn't have any images yet.</p>
            </div>
          )}
        </TabsContent>
      </Tabs>
    </div>
  )
}
