import { getScene } from "@/modules/scene/service"
import { getLocationsByProject } from "@/modules/location/service"
import { SceneDetail } from "./_components/scene-detail"
import { SceneLocationSelection } from "./_components/scene-location-selection"
import { SceneDiscussion } from "./_components/scene-discussion"
import { Ta<PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs"
import { Button } from "@/components/ui/button"
import { ArrowLeft } from "lucide-react"
import Link from "next/link"
import { notFound } from "next/navigation"

export default async function ScenePage({ params }: { params: { id: string } }) {
  try {
    const scene = await getScene(params.id)
    const projectLocations = await getLocationsByProject(scene.projectId)

    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Button variant="outline" size="icon" asChild>
              <Link href={`/projects/${scene.projectId}`}>
                <ArrowLeft className="h-4 w-4" />
              </Link>
            </Button>
            <h1 className="text-3xl font-bold tracking-tight">
              {scene.sceneNumber && <span className="text-muted-foreground mr-2">{scene.sceneNumber}</span>}
              {scene.name}
            </h1>
          </div>
        </div>

        <Tabs defaultValue="details" className="w-full">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="details">Scene Details</TabsTrigger>
            <TabsTrigger value="locations">Locations</TabsTrigger>
            <TabsTrigger value="discussion">Discussion</TabsTrigger>
          </TabsList>
          <TabsContent value="details" className="py-4">
            <SceneDetail scene={scene} />
          </TabsContent>
          <TabsContent value="locations" className="py-4">
            <SceneLocationSelection scene={scene} projectLocations={projectLocations} />
          </TabsContent>
          <TabsContent value="discussion" className="py-4">
            <SceneDiscussion scene={scene} />
          </TabsContent>
        </Tabs>
      </div>
    )
  } catch (error) {
    console.error("Error loading scene:", error)
    return notFound()
  }
}
