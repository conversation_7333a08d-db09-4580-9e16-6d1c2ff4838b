import { Suspense } from "react"
import { dehydrate, HydrationBoundary, QueryClient } from "@tanstack/react-query"
import { notificationKeys } from "@/hooks/use-notification-data"
import { MapPageClient } from "@/app/(dashboard)/organizations/[organizationSlug]/map/map-client"
import { MapPageSkeleton } from "@/components/maps/map-page-skeleton"

// Server component with optimized parallel data loading
export default async function MapsPage({ params }: { params: { organizationSlug: string } }) {
  // Await params to ensure it's fully resolved
  const resolvedParams = await Promise.resolve(params)
  const { organizationSlug } = resolvedParams
  
  // Create a new QueryClient for server
  const queryClient = new QueryClient()
  
  // Base API URL for all requests
  const baseUrl = process.env.NEXT_PUBLIC_API_URL || ''
  
  // Cache configuration for different data types
  const cacheConfig = {
    // Use short cache for dynamic data
    dynamic: { cache: 'no-store' as RequestCache },
    // Use longer cache for static/semi-static data
    static: { 
      next: { 
        revalidate: 3600 // 1 hour cache for static data
      } 
    }
  }
  
  // Prefetch all required data in parallel for optimal performance
  await Promise.all([
    // Prefetch combined initial map data (organization, locations, views, favorites)
    queryClient.prefetchQuery({
      queryKey: ['initial-map-data', organizationSlug],
      queryFn: async () => {
        const res = await fetch(`${baseUrl}/api/map/initial-data?organizationSlug=${organizationSlug}`, cacheConfig.dynamic)
        if (!res.ok) throw new Error(`Failed to fetch initial map data for: ${organizationSlug} (${res.status} ${res.statusText})`)
        try {
          return await res.json()
        } catch (error) {
          console.error("Error parsing JSON response:", error);
          throw new Error("Invalid JSON response from initial map data API");
        }
      },
    }),
    
    // Prefetch Mapbox token (static data that rarely changes)
    queryClient.prefetchQuery({
      queryKey: ['/api/mapbox-token'],
      queryFn: async () => {
        const res = await fetch(`${baseUrl}/api/mapbox-token`, cacheConfig.static)
        if (!res.ok) throw new Error(`Failed to fetch Mapbox token: ${res.status} ${res.statusText}`)
        try {
          return await res.json()
        } catch (error) {
          console.error("Error parsing JSON response:", error);
          throw new Error("Invalid JSON response from Mapbox token API");
        }
      },
    }),
    
    // Prefetch notification count
    queryClient.prefetchQuery({
      queryKey: notificationKeys.count(),
      queryFn: async () => {
        const res = await fetch(`${baseUrl}/api/notifications/count`, cacheConfig.dynamic)
        if (!res.ok) throw new Error(`Failed to fetch notification count: ${res.status} ${res.statusText}`)
        try {
          return await res.json()
        } catch (error) {
          console.error("Error parsing JSON response:", error);
          throw new Error("Invalid JSON response from notification count API");
        }
      },
    }),
    
    // Prefetch notification preferences
    queryClient.prefetchQuery({
      queryKey: notificationKeys.preferences(),
      queryFn: async () => {
        const res = await fetch(`${baseUrl}/api/notifications/preferences`, cacheConfig.dynamic)
        if (!res.ok) throw new Error(`Failed to fetch notification preferences: ${res.status} ${res.statusText}`)
        try {
          return await res.json()
        } catch (error) {
          console.error("Error parsing JSON response:", error);
          throw new Error("Invalid JSON response from notification preferences API");
        }
      },
    }),
  ])
  
  return (
    <HydrationBoundary state={dehydrate(queryClient)}>
      <Suspense fallback={<MapPageSkeleton />}>
        <MapPageClient organizationSlug={organizationSlug} />
      </Suspense>
    </HydrationBoundary>
  )
}
