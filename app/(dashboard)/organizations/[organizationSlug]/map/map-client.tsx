"use client"

import { useState, useEffect, useRef } from "react" // Removed useCallback
import mapboxgl from "mapbox-gl"
import "mapbox-gl/dist/mapbox-gl.css"
import { DashboardShell } from "@/components/dashboard/dashboard-shell"
import { Dash<PERSON>Header } from "@/components/dashboard/dashboard-header"
import { Button } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON>Content, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Input } from "@/components/ui/input"
import { Plus, Search, Layers, MapPin, Save, Share, BookOpen, Loader2, AlertCircle, Download } from "lucide-react"
import { AddLocationModal } from "@/components/locations/add-location-modal"
import { SaveViewModal } from "@/components/maps/save-view-modal"
import { ViewSelectorModal } from "@/components/maps/view-selector-modal"
import { ShareMapModal } from "@/components/maps/share-map-modal"
import { ExportMapModal } from "@/components/maps/export-map-modal"
import { FullScreenMap } from "@/components/maps/full-screen-map"
import { LocationDetailModal } from "@/components/maps/location-detail-modal" // Added
import { MapLayers } from "@/components/maps/map-layers"
import { MapLocations } from "@/components/maps/map-locations"
// MapProvider removed
import { MapPagination } from "@/components/maps/map-pagination"
import { useInitialMapData } from "@/hooks/use-initial-map-data"
import { useOrganization } from "@/hooks/use-organization"
import { useQuery } from "@tanstack/react-query"
import type { Location } from "@/modules/location/model"

// Extended Location type with selected property
interface LocationWithSelected extends Location {
  selected?: boolean;
}

interface MapPageClientProps {
  organizationSlug: string
}

export function MapPageClient({ organizationSlug }: MapPageClientProps) {
  // Define all state hooks at the top level to avoid conditional hook calls
  const [isFullScreen, setIsFullScreen] = useState(false)
  const [currentPage, setCurrentPage] = useState(1)
  const [totalPages, setTotalPages] = useState(1)
  const [isSaveViewModalOpen, setIsSaveViewModalOpen] = useState(false)
  const [isViewSelectorModalOpen, setIsViewSelectorModalOpen] = useState(false)
  const [isShareMapModalOpen, setIsShareMapModalOpen] = useState(false)
  const [isExportMapModalOpen, setIsExportMapModalOpen] = useState(false)
  const [isAddLocationModalOpen, setIsAddLocationModalOpen] = useState(false)
  const [selectedLocation, setSelectedLocation] = useState<LocationWithSelected | null>(null)
  // const [isLocationDetailModalOpen, setIsLocationDetailModalOpen] = useState(false) // Removed
  const [locationForModalTrigger, setLocationForModalTrigger] = useState<LocationWithSelected | null>(null);

  const mapContainerRef = useRef<HTMLDivElement>(null)
  const mapRef = useRef<mapboxgl.Map | null>(null)
  const markersRef = useRef<{ [key: string]: mapboxgl.Marker }>({})
  
  // Use the combined data hook for better performance
  const { 
    data: initialData, 
    isLoading: isLoadingInitialData,
    error: initialDataError
  } = useInitialMapData(organizationSlug)
  
  // Get organization data at the component level
  const { organization } = useOrganization()
  
  // Use React Query for Mapbox token with proper queryFn
  const { data: mapboxData, isLoading: isLoadingMapboxToken } = useQuery<{ token: string }>({
    queryKey: ['/api/mapbox-token'],
    queryFn: async () => {
      const response = await fetch('/api/mapbox-token');
      if (!response.ok) {
        throw new Error(`Failed to fetch Mapbox token: ${response.status} ${response.statusText}`);
      }
      try {
        return await response.json();
      } catch (error) {
        console.error("Error parsing JSON response:", error);
        throw new Error("Invalid JSON response from Mapbox token API");
      }
    }
  })
  
  // Extract data from the combined response
  const locations = (initialData?.locations || []) as LocationWithSelected[]
  // const views = initialData?.views || [] // Removed unused views variable
  
  // Handle pagination changes from MapLocations component
  const handlePaginationChange = (page: number, total: number) => {
    setCurrentPage(page)
    setTotalPages(total)
  }

  // Effect for initializing and updating the map - MOVED HERE, BEFORE EARLY RETURNS
  useEffect(() => {
    // Ensure mapboxData and locations are available before proceeding with map logic
    if (!mapboxData?.token || !locations) {
        // If map is already initialized, we might still want to clear markers if locations become undefined
        if (mapRef.current && !locations) {
            Object.values(markersRef.current).forEach(marker => marker.remove());
            markersRef.current = {};
        }
        return;
    }

    if (!mapContainerRef.current) return; // Container ref must be available

    if (!mapRef.current) { // Initialize map only if it hasn't been initialized
      mapboxgl.accessToken = mapboxData.token
      mapRef.current = new mapboxgl.Map({
        container: mapContainerRef.current,
        style: "mapbox://styles/mapbox/streets-v12",
        center: [-98.5795, 39.8283],
        zoom: 3,
      })

      mapRef.current.addControl(new mapboxgl.NavigationControl(), "top-right")

      mapRef.current.on('load', () => {
        if (locations.length > 0) {
          const bounds = new mapboxgl.LngLatBounds();
          locations.forEach(location => {
            if (location.coordinates?.longitude && location.coordinates?.latitude) {
              bounds.extend([location.coordinates.longitude, location.coordinates.latitude]);
            }
          });
          if (!bounds.isEmpty()) {
            mapRef.current?.fitBounds(bounds, { padding: 50, maxZoom: 15 });
          }
        }
      });
    }

    // Add/Update markers
    // This part runs on subsequent renders if locations array changes
    if (mapRef.current) { // Ensure map is initialized before trying to add markers
      const newMarkers: { [key: string]: mapboxgl.Marker } = {};
      locations.forEach(location => {
        if (location.coordinates?.longitude && location.coordinates?.latitude && location.id) {
          let marker = markersRef.current[location.id];
          const currentIterationLocation = location; // Capture the location for this specific iteration

          if (!marker) {
            marker = new mapboxgl.Marker()
              .setLngLat([currentIterationLocation.coordinates.longitude, currentIterationLocation.coordinates.latitude])
              .addTo(mapRef.current!);
            
            marker.getElement().addEventListener('click', () => {
              // Use the captured location from this iteration's scope
              if (currentIterationLocation) {
                setLocationForModalTrigger(currentIterationLocation); // This will trigger the useEffect below
              }
            });
          } else {
            // If marker exists, ensure it's on the map (it might have been removed if locations list was temporarily empty)
             if (!marker.getElement().parentNode && mapRef.current) {
                marker.addTo(mapRef.current);
            }
            marker.setLngLat([location.coordinates.longitude, location.coordinates.latitude]);
          }
          newMarkers[location.id] = marker;
        }
      });

      // Remove old markers that are no longer in the locations list
      Object.keys(markersRef.current).forEach(markerId => {
        if (!newMarkers[markerId]) {
          markersRef.current[markerId].remove();
          delete markersRef.current[markerId]; // Remove from ref
        }
      });
      markersRef.current = newMarkers;
    }
    
    // Resize handler
    const handleResize = () => {
      mapRef.current?.resize();
    };
    window.addEventListener('resize', handleResize);

    return () => { // Cleanup
      window.removeEventListener('resize', handleResize);
      // Optional: Full map cleanup when the component unmounts
      // if (mapRef.current) {
      //   mapRef.current.remove();
      //   mapRef.current = null;
      // }
    }
  }, [mapboxData?.token, locations]) // Dependencies for the map effect

  // Effect to set selectedLocation when a marker is clicked (via locationForModalTrigger)
  useEffect(() => {
    if (locationForModalTrigger) {
      setSelectedLocation(locationForModalTrigger);
      // setIsLocationDetailModalOpen(true); // Removed: Modal visibility is now tied to selectedLocation
      setLocationForModalTrigger(null); // Reset trigger
    }
  }, [locationForModalTrigger]);

  // Show loading state if data is still loading
  if (isLoadingInitialData || isLoadingMapboxToken) {
    return (
      <div className="flex items-center justify-center h-screen">
        <div className="flex flex-col items-center gap-2">
          <Loader2 className="h-8 w-8 animate-spin text-neon-blue" />
          <p className="text-sm text-muted-foreground">Loading map data...</p>
        </div>
      </div>
    )
  }
  
  // Show error state if there was an error loading data
  if (initialDataError) {
    return (
      <div className="flex items-center justify-center h-screen">
        <div className="flex flex-col items-center gap-2 max-w-md text-center">
          <div className="p-3 rounded-full bg-red-100">
            <AlertCircle className="text-red-600" />
          </div>
          <h3 className="text-lg font-medium">Error Loading Map Data</h3>
          <p className="text-sm text-muted-foreground">
            {initialDataError instanceof Error ? initialDataError.message : 'An unknown error occurred'}
          </p>
          <Button
            variant="outline"
            className="mt-4"
            onClick={() => window.location.reload()}
          >
            Try Again
          </Button>
        </div>
      </div>
    )
  }

  return (
      <DashboardShell>
        <DashboardHeader title="Map">
          <div className="flex items-center gap-2">
          <div className="flex items-center gap-2">
            <Button variant="outline" onClick={() => setIsExportMapModalOpen(true)}>
              <Download className="mr-2 h-4 w-4" />
              Export
            </Button>
            <Button variant="outline" onClick={() => setIsShareMapModalOpen(true)}>
              <Share className="mr-2 h-4 w-4" />
              Share
            </Button>
            <Button onClick={() => setIsAddLocationModalOpen(true)}>
              <Plus className="mr-2 h-4 w-4" />
              Add Location
            </Button>
          </div>
          </div>
        </DashboardHeader>

        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <div className="md:col-span-1 space-y-6">
            <Card>
              <CardContent className="p-4 space-y-4">
                <div className="relative">
                  <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                  <Input type="search" placeholder="Search locations..." className="w-full pl-8" />
                </div>

                <Tabs defaultValue="locations" className="w-full">
                  <TabsList className="w-full">
                    <TabsTrigger
                      value="locations"
                      className="flex-1 transition-all data-[state=active]:shadow-[0_0_10px_rgba(35,35,255,0.5)] data-[state=active]:border-neon-blue"
                    >
                      <MapPin className="mr-2 h-4 w-4" />
                      Locations
                    </TabsTrigger>
                    <TabsTrigger
                      value="layers"
                      className="flex-1 transition-all data-[state=active]:shadow-[0_0_10px_rgba(35,35,255,0.5)] data-[state=active]:border-neon-blue"
                    >
                      <Layers className="mr-2 h-4 w-4" />
                      Layers
                    </TabsTrigger>
                  </TabsList>
                  <TabsContent value="locations" className="mt-4 space-y-4">
                    <MapLocations
                      height="350px"
                      showPagination={false}
                      onPaginationChange={handlePaginationChange}
                      locations={locations} // Added locations prop
                      isLoading={isLoadingInitialData} // Added isLoading prop
                      selectedLocationId={selectedLocation?.id} // Pass selectedLocationId for highlighting
                      onLocationSelect={(locId: string) => {
                        const loc = locations.find(l => l.id === locId);
                        if (loc && mapRef.current && loc.coordinates?.longitude && loc.coordinates?.latitude) {
                          setSelectedLocation(loc); // Update selected location state
                          mapRef.current.flyTo({
                            center: [loc.coordinates.longitude, loc.coordinates.latitude],
                            zoom: 14
                          });
                          // Do NOT open modal here, only fly to location and update selection
                        }
                      }}
                    />
                  </TabsContent>
                  <TabsContent value="layers" className="mt-4 space-y-4">
                    <MapLayers />
                  </TabsContent>
                </Tabs>

                <div className="pt-2 space-y-4">
                  <div className="flex gap-2">
                    <Button
                      variant="outline"
                      size="sm"
                      className="flex-1"
                      onClick={() => setIsSaveViewModalOpen(true)}
                    >
                      <Save className="mr-2 h-4 w-4" />
                      Save View
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      className="flex-1"
                      onClick={() => setIsViewSelectorModalOpen(true)}
                    >
                      <BookOpen className="mr-2 h-4 w-4" />
                      Load View
                    </Button>
                  </div>
                  
                  {/* External pagination controls */}
                  <MapPagination
                    currentPage={currentPage}
                    totalPages={totalPages}
                    onPageChange={setCurrentPage}
                  />
                </div>
              </CardContent>
            </Card>
          </div>

          <div className="md:col-span-3 flex flex-col h-[calc(100vh-13rem)]">
            <Card className="overflow-hidden flex-1 h-full relative">
              <CardContent className="p-0 h-full w-full absolute inset-0">
                <div ref={mapContainerRef} className="w-full h-full" />
              </CardContent>
            </Card>
          </div>
        </div>

        {isFullScreen && (
          <FullScreenMap
            onClose={() => setIsFullScreen(false)}
            // FullScreenMap might need direct map instance or simplified props
            // For now, assuming it can work independently or will be refactored later
          />
        )}
        
        {/* Modals */}
        <SaveViewModal
          isOpen={isSaveViewModalOpen}
          onClose={() => setIsSaveViewModalOpen(false)}
        />
        
        <ViewSelectorModal
          isOpen={isViewSelectorModalOpen}
          onClose={() => setIsViewSelectorModalOpen(false)}
        />
        
        <ShareMapModal
          isOpen={isShareMapModalOpen}
          onClose={() => setIsShareMapModalOpen(false)}
        />
        
        <ExportMapModal
          isOpen={isExportMapModalOpen}
          onClose={() => setIsExportMapModalOpen(false)}
          locations={locations}
          selectedLocations={locations.filter((loc: LocationWithSelected) => loc.selected)}
          onExport={async (options) => {
            if (!organization) {
              throw new Error('Organization not found');
            }
            try {
              const response = await fetch('/api/map/export', {
                method: 'POST',
                headers: {
                  'Content-Type': 'application/json',
                  'X-Stytch-Org-Id': organization.id || '',
                },
                body: JSON.stringify({
                  organizationId: organization.id,
                  locationIds: options.includeAllLocations
                    ? []
                    : locations.filter((loc: LocationWithSelected) => loc.selected).map((loc: LocationWithSelected) => loc.id),
                  options,
                }),
              });
              if (!response.ok) {
                throw new Error('Failed to export map');
              }
              const data = await response.json();
              return data.url;
            } catch (error) {
              console.error('Export error:', error);
              throw error;
            }
          }}
        />
        
        <AddLocationModal
          isOpen={isAddLocationModalOpen}
          onClose={() => setIsAddLocationModalOpen(false)}
        />

        {/* Modal visibility is now directly controlled by selectedLocation state */}
        <LocationDetailModal
          open={!!selectedLocation} // Modal is open if selectedLocation is not null
          onOpenChange={(isOpen) => {
            if (!isOpen) {
              setSelectedLocation(null); // Close modal by clearing selectedLocation
            }
            // If isOpen is true, it means the modal is trying to open itself via internal mechanisms,
            // but we primarily control its visibility via the `open` prop tied to `selectedLocation`.
            // This handler ensures that if the modal's own close button is clicked, our state updates.
          }}
          location={selectedLocation || undefined} // Ensure undefined is passed if selectedLocation is null
        />
      </DashboardShell>
  )
}
