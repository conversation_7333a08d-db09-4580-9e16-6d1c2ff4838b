"use client"

import { useState } from "react"
import { DashboardShell } from "@/components/dashboard/dashboard-shell"
import { DashboardHeader } from "@/components/dashboard/dashboard-header"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>rigger } from "@/components/ui/tabs"
import { Input } from "@/components/ui/input"
import { Plus, Search, Layers, MapPin, Save, Share, BookOpen, Loader2, AlertCircle } from "lucide-react"
import { SaveViewModal } from "@/components/maps/save-view-modal"
import { ViewSelectorModal } from "@/components/maps/view-selector-modal"
import { ShareMapModal } from "@/components/maps/share-map-modal"
import { FullMap } from "@/components/maps/full-map"
import { FullScreenMap } from "@/components/maps/full-screen-map"
import { MapLayers } from "@/components/maps/map-layers"
import { MapLocations } from "@/components/maps/map-locations"
import { MapProvider } from "@/providers/map-provider"
import { MapPagination } from "@/components/maps/map-pagination"
import { useInitialMapData } from "@/hooks/use-initial-map-data"
import { useQuery } from "@tanstack/react-query"

interface MapPageClientProps {
  organizationSlug: string
}

export function MapPageClient({ organizationSlug }: MapPageClientProps) {
  // Define all state hooks at the top level to avoid conditional hook calls
  const [isFullScreen, setIsFullScreen] = useState(false)
  const [currentPage, setCurrentPage] = useState(1)
  const [totalPages, setTotalPages] = useState(1)
  const [isSaveViewModalOpen, setIsSaveViewModalOpen] = useState(false)
  const [isViewSelectorModalOpen, setIsViewSelectorModalOpen] = useState(false)
  const [isShareMapModalOpen, setIsShareMapModalOpen] = useState(false)
  
  // Use the combined data hook for better performance
  const { 
    data: initialData, 
    isLoading: isLoadingInitialData,
    error: initialDataError
  } = useInitialMapData(organizationSlug)
  
  // Use React Query for Mapbox token with proper queryFn
  const { data: mapboxData, isLoading: isLoadingMapboxToken } = useQuery<{ token: string }>({ 
    queryKey: ['/api/mapbox-token'],
    queryFn: async () => {
      const response = await fetch('/api/mapbox-token');
      if (!response.ok) {
        throw new Error('Failed to fetch Mapbox token');
      }
      return response.json();
    }
  })
  
  // Extract data from the combined response for use in MapProvider
  const locations = initialData?.locations || []
  const views = initialData?.views || []
  
  // Handle pagination changes from MapLocations component
  const handlePaginationChange = (page: number, total: number) => {
    setCurrentPage(page)
    setTotalPages(total)
  }
  
  // Show loading state if data is still loading
  if (isLoadingInitialData || isLoadingMapboxToken) {
    return (
      <div className="flex items-center justify-center h-screen">
        <div className="flex flex-col items-center gap-2">
          <Loader2 className="h-8 w-8 animate-spin text-neon-blue" />
          <p className="text-sm text-muted-foreground">Loading map data...</p>
        </div>
      </div>
    )
  }
  
  // Show error state if there was an error loading data
  if (initialDataError) {
    return (
      <div className="flex items-center justify-center h-screen">
        <div className="flex flex-col items-center gap-2 max-w-md text-center">
          <div className="p-3 rounded-full bg-red-100">
            <AlertCircle className="text-red-600" />
          </div>
          <h3 className="text-lg font-medium">Error Loading Map Data</h3>
          <p className="text-sm text-muted-foreground">
            {initialDataError instanceof Error ? initialDataError.message : 'An unknown error occurred'}
          </p>
          <Button 
            variant="outline" 
            className="mt-4"
            onClick={() => window.location.reload()}
          >
            Try Again
          </Button>
        </div>
      </div>
    )
  }

  return (
    <MapProvider 
      initialLocations={locations} 
      initialViews={views}
      mapboxToken={mapboxData?.token}
    >
      <DashboardShell>
        <DashboardHeader title="Map">
          <div className="flex items-center gap-2">
          <Button variant="outline" onClick={() => setIsShareMapModalOpen(true)}>
            <Share className="mr-2 h-4 w-4" />
            Share
          </Button>
            <Button>
              <Plus className="mr-2 h-4 w-4" />
              Add Location
            </Button>
          </div>
        </DashboardHeader>

        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <div className="md:col-span-1 space-y-6">
            <Card>
              <CardContent className="p-4 space-y-4">
                <div className="relative">
                  <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                  <Input type="search" placeholder="Search locations..." className="w-full pl-8" />
                </div>

                <Tabs defaultValue="locations" className="w-full">
                  <TabsList className="w-full">
                    <TabsTrigger 
                      value="locations" 
                      className="flex-1 transition-all data-[state=active]:shadow-[0_0_10px_rgba(35,35,255,0.5)] data-[state=active]:border-neon-blue"
                    >
                      <MapPin className="mr-2 h-4 w-4" />
                      Locations
                    </TabsTrigger>
                    <TabsTrigger 
                      value="layers" 
                      className="flex-1 transition-all data-[state=active]:shadow-[0_0_10px_rgba(35,35,255,0.5)] data-[state=active]:border-neon-blue"
                    >
                      <Layers className="mr-2 h-4 w-4" />
                      Layers
                    </TabsTrigger>
                  </TabsList>
                  <TabsContent value="locations" className="mt-4 space-y-4">
                    <MapLocations 
                      height="350px" 
                      showPagination={false} 
                      onPaginationChange={handlePaginationChange} 
                    />
                  </TabsContent>
                  <TabsContent value="layers" className="mt-4 space-y-4">
                    <MapLayers />
                  </TabsContent>
                </Tabs>

                <div className="pt-2 space-y-4">
                  <div className="flex gap-2">
                    <Button 
                      variant="outline" 
                      size="sm" 
                      className="flex-1"
                      onClick={() => setIsSaveViewModalOpen(true)}
                    >
                      <Save className="mr-2 h-4 w-4" />
                      Save View
                    </Button>
                    <Button 
                      variant="outline" 
                      size="sm" 
                      className="flex-1"
                      onClick={() => setIsViewSelectorModalOpen(true)}
                    >
                      <BookOpen className="mr-2 h-4 w-4" />
                      Load View
                    </Button>
                  </div>
                  
                  {/* External pagination controls */}
                  <MapPagination 
                    currentPage={currentPage}
                    totalPages={totalPages}
                    onPageChange={setCurrentPage}
                  />
                </div>
              </CardContent>
            </Card>
          </div>

          <div className="md:col-span-3">
            <Card className="overflow-hidden">
              <CardContent className="p-0">
                <FullMap onFullScreen={() => setIsFullScreen(true)} />
              </CardContent>
            </Card>
          </div>
        </div>

        {isFullScreen && (
          <FullScreenMap onClose={() => setIsFullScreen(false)} />
        )}
        
        {/* Modals */}
        <SaveViewModal 
          isOpen={isSaveViewModalOpen} 
          onClose={() => setIsSaveViewModalOpen(false)} 
        />
        
        <ViewSelectorModal 
          isOpen={isViewSelectorModalOpen} 
          onClose={() => setIsViewSelectorModalOpen(false)} 
        />
        
        <ShareMapModal
          isOpen={isShareMapModalOpen}
          onClose={() => setIsShareMapModalOpen(false)}
        />
      </DashboardShell>
    </MapProvider>
  )
}
