import { DashboardShell } from "@/components/dashboard/dashboard-shell"
import { DashboardHeader } from "@/components/dashboard/dashboard-header"
import { But<PERSON> } from "@/components/ui/button"
import { Skeleton } from "@/components/ui/skeleton"
import { Card, CardContent } from "@/components/ui/card"
import { Tabs, Tabs<PERSON>ist, TabsTrigger } from "@/components/ui/tabs"
import { Plus, Layers, MapPin, Share } from "lucide-react"

export default function Loading() {
  return (
    <DashboardShell>
      <DashboardHeader title="Interactive Map" description="Visualize and manage your filming locations">
        <div className="flex items-center gap-2">
          <Button disabled variant="outline">
            <Share className="mr-2 h-4 w-4" />
            Share
          </Button>
          <Button disabled>
            <Plus className="mr-2 h-4 w-4" />
            Add Location
          </Button>
        </div>
      </DashboardHeader>

      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div className="md:col-span-1 space-y-6">
          <Card>
            <CardContent className="p-4 space-y-4">
              <div className="relative">
                <Skeleton className="h-10 w-full" />
              </div>

              <div className="flex items-center justify-between">
                <Skeleton className="h-9 w-full" />
              </div>

              <Tabs defaultValue="locations" className="w-full">
                <TabsList className="w-full">
                  <TabsTrigger value="locations" className="flex-1" disabled>
                    <MapPin className="mr-2 h-4 w-4" />
                    Locations
                  </TabsTrigger>
                  <TabsTrigger value="layers" className="flex-1" disabled>
                    <Layers className="mr-2 h-4 w-4" />
                    Layers
                  </TabsTrigger>
                </TabsList>
                <div className="mt-4 space-y-4">
                  {[
                    { id: "location-skeleton-1", name: "Location 1" },
                    { id: "location-skeleton-2", name: "Location 2" },
                    { id: "location-skeleton-3", name: "Location 3" }
                  ].map((item) => (
                    <div key={item.id} className="flex items-center justify-between rounded-md border p-3">
                      <div className="space-y-2">
                        <Skeleton className="h-4 w-40" />
                        <Skeleton className="h-3 w-24" />
                      </div>
                      <Skeleton className="h-6 w-6 rounded-full" />
                    </div>
                  ))}
                </div>
              </Tabs>

              <div className="pt-2 space-y-2">
                <Skeleton className="h-9 w-full" />
                <Skeleton className="h-9 w-full" />
              </div>
            </CardContent>
          </Card>
        </div>

        <div className="md:col-span-3">
          <Card className="overflow-hidden">
            <CardContent className="p-0">
              <Skeleton className="h-[500px] w-full" />
            </CardContent>
          </Card>
        </div>
      </div>
    </DashboardShell>
  )
}
