import { DashboardShell } from "@/components/dashboard/dashboard-shell";
import { DashboardHeader } from "@/components/dashboard/dashboard-header";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Tabs, TabsContent, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Plus, Search, Filter, Grid3X3, List, FolderOpen } from "lucide-react";
import { DocumentGrid } from "@/components/documents/document-grid";
import { DocumentList } from "@/components/documents/document-list";
import { DocumentFolders } from "@/components/documents/document-folders";

export default function DocumentsPage() {
  return (
    <DashboardShell>
      <DashboardHeader title="Documents" description="Manage your production documents and files">
        <Button>
          <Plus className="mr-2 h-4 w-4" />
          Upload Document
        </Button>
      </DashboardHeader>

      <div className="flex flex-col md:flex-row gap-4 mb-6">
        <div className="relative flex-1">
          <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input type="search" placeholder="Search documents..." className="w-full pl-8" />
        </div>
        <Button variant="outline" className="gap-1">
          <Filter className="h-4 w-4" />
          Filters
        </Button>
        <Tabs defaultValue="grid" className="w-[180px]">
          <TabsList>
            <TabsTrigger value="grid" className="px-3">
              <Grid3X3 className="h-4 w-4" />
            </TabsTrigger>
            <TabsTrigger value="list" className="px-3">
              <List className="h-4 w-4" />
            </TabsTrigger>
            <TabsTrigger value="folders" className="px-3">
              <FolderOpen className="h-4 w-4" />
            </TabsTrigger>
          </TabsList>
        </Tabs>
      </div>

      <Tabs defaultValue="grid" className="space-y-4">
        <TabsContent value="grid" className="space-y-4">
          <DocumentGrid />
        </TabsContent>
        <TabsContent value="list" className="space-y-4">
          <DocumentList />
        </TabsContent>
        <TabsContent value="folders" className="space-y-4">
          <DocumentFolders />
        </TabsContent>
      </Tabs>
    </DashboardShell>
  );
}
