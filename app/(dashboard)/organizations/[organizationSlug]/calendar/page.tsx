"use client";

import React from 'react';
import { DashboardHeader } from "@/components/dashboard/dashboard-header";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { PlusIcon } from "lucide-react";
import { Calendar } from "@/components/ui/calendar";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";

// This is a placeholder page - in a real implementation, this would fetch calendar events from the API
export default function CalendarPage() {
  // Mock data for demonstration
  const events = [
    {
      id: "1",
      title: "Location Scout - Downtown",
      date: new Date(2025, 3, 25, 10, 0), // April 25, 2025, 10:00 AM
      type: "scouting",
      projectId: "project1",
      projectName: "Summer Feature",
    },
    {
      id: "2",
      title: "Permit Application Deadline",
      date: new Date(2025, 3, 28, 17, 0), // April 28, 2025, 5:00 PM
      type: "deadline",
      projectId: "project1",
      projectName: "Summer Feature",
    },
    {
      id: "3",
      title: "Tech Scout - Beach Location",
      date: new Date(2025, 4, 2, 9, 0), // May 2, 2025, 9:00 AM
      type: "scouting",
      projectId: "project2",
      projectName: "Commercial Shoot",
    },
  ];

  // Get today's date
  const today = new Date();

  // Function to get events for a specific day
  const getEventsForDay = (day: Date) => {
    return events.filter(
      (event) =>
        event.date.getDate() === day.getDate() &&
        event.date.getMonth() === day.getMonth() &&
        event.date.getFullYear() === day.getFullYear()
    );
  };

  // Get today's events
  const todayEvents = getEventsForDay(today);

  // Function to get event type badge variant
  const getEventTypeVariant = (type: string) => {
    switch (type) {
      case "scouting":
        return "default";
      case "shooting":
        return "destructive";
      case "deadline":
        return "secondary";
      default:
        return "outline";
    }
  };

  return (
    <div className="flex-1 space-y-4 p-4 md:p-8 pt-6">
      <DashboardHeader
        title="Calendar"
        description="View and manage your production schedule."
      >
        <Button>
          <PlusIcon className="mr-2 h-4 w-4" />
          Add Event
        </Button>
      </DashboardHeader>

      <div className="grid gap-4 md:grid-cols-2">
        <Card>
          <CardHeader>
            <CardTitle>Production Calendar</CardTitle>
          </CardHeader>
          <CardContent>
            <Calendar
              mode="single"
              selected={today}
              className="rounded-md border"
              modifiers={{
                event: (date: Date) => getEventsForDay(date).length > 0,
              }}
              modifiersClassNames={{
                event: "bg-primary/10 font-bold",
              }}
            />
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Today's Events</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {todayEvents.length === 0 ? (
                <div className="text-center text-sm text-muted-foreground">
                  No events scheduled for today.
                </div>
              ) : (
                todayEvents.map((event) => (
                  <div
                    key={event.id}
                    className="flex items-center justify-between rounded-md border p-3"
                  >
                    <div>
                      <div className="font-medium">{event.title}</div>
                      <div className="text-xs text-muted-foreground">
                        {event.date.toLocaleTimeString([], {
                          hour: "2-digit",
                          minute: "2-digit",
                        })}{" "}
                        &bull; {event.projectName}
                      </div>
                    </div>
                    <Badge variant={getEventTypeVariant(event.type)}>
                      {event.type.charAt(0).toUpperCase() + event.type.slice(1)}
                    </Badge>
                  </div>
                ))
              )}
            </div>
          </CardContent>
        </Card>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Upcoming Events</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {events
              .filter((event) => event.date > today)
              .sort((a, b) => a.date.getTime() - b.date.getTime())
              .slice(0, 5) // Limit to 5 upcoming events
              .map((event) => (
                <div
                  key={event.id}
                  className="flex items-center justify-between rounded-md border p-3"
                >
                  <div>
                    <div className="font-medium">{event.title}</div>
                    <div className="text-xs text-muted-foreground">
                      {event.date.toLocaleDateString()} at{" "}
                      {event.date.toLocaleTimeString([], {
                        hour: "2-digit",
                        minute: "2-digit",
                      })}{" "}
                      &bull; {event.projectName}
                    </div>
                  </div>
                  <Badge variant={getEventTypeVariant(event.type)}>
                    {event.type.charAt(0).toUpperCase() + event.type.slice(1)}
                  </Badge>
                </div>
              ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
