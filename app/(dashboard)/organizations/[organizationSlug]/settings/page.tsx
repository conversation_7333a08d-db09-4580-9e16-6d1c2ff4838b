"use client"

/* eslint-disable @typescript-eslint/no-explicit-any */

import { DashboardShell } from "@/components/dashboard/dashboard-shell"
import { DashboardHeader } from "@/components/dashboard/dashboard-header"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { OnboardingButton } from "@/components/onboarding/onboarding-button"
import { Separator } from "@/components/ui/separator"
import React from "react"

// Define the type for the params object
type Params = {
  organizationSlug: string
}

interface OrganizationSettingsPageProps {
  params: Params | Promise<Params>
}

export default function OrganizationSettingsPage({ params }: OrganizationSettingsPageProps) {
  // Use React.use() to unwrap the params Promise
  // This is the recommended approach for Next.js 15.3.1
  // biome-ignore lint/suspicious/noExplicitAny: needed for React.use() compatibility
  const resolvedParams = React.use(params as any) as Params
  const organizationSlug = resolvedParams.organizationSlug
  
  return (
    <DashboardShell>
      <DashboardHeader title="Settings" description="Manage your organization settings">
        <OnboardingButton />
      </DashboardHeader>
      
      <Tabs defaultValue="general" className="space-y-4">
        <TabsList>
          <TabsTrigger value="general">General</TabsTrigger>
          <TabsTrigger value="appearance">Appearance</TabsTrigger>
          <TabsTrigger value="notifications">Notifications</TabsTrigger>
          <TabsTrigger value="help">Help & Support</TabsTrigger>
        </TabsList>
        
        <TabsContent value="general" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>General Settings</CardTitle>
              <CardDescription>
                Manage your organization&apos;s general settings
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <h3 className="text-sm font-medium">Organization ID</h3>
                <p className="text-sm text-muted-foreground">{organizationSlug}</p>
              </div>
              <Separator />
              <div>
                <h3 className="text-sm font-medium">Product Tour</h3>
                <p className="text-sm text-muted-foreground mb-2">
                  Take a guided tour of the platform&apos;s features
                </p>
                <OnboardingButton variant="secondary" size="sm" />
              </div>
            </CardContent>
          </Card>
        </TabsContent>
        
        <TabsContent value="appearance" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Appearance Settings</CardTitle>
              <CardDescription>
                Customize the appearance of your workspace
              </CardDescription>
            </CardHeader>
            <CardContent>
              <p className="text-sm text-muted-foreground">
                Appearance settings coming soon.
              </p>
            </CardContent>
          </Card>
        </TabsContent>
        
        <TabsContent value="notifications" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Notification Settings</CardTitle>
              <CardDescription>
                Configure how you receive notifications
              </CardDescription>
            </CardHeader>
            <CardContent>
              <p className="text-sm text-muted-foreground">
                Notification settings coming soon.
              </p>
            </CardContent>
          </Card>
        </TabsContent>
        
        <TabsContent value="help" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Help & Support</CardTitle>
              <CardDescription>
                Get help with using the platform
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <h3 className="text-sm font-medium">Product Tour</h3>
                <p className="text-sm text-muted-foreground mb-2">
                  Take a guided tour of the platform&apos;s features
                </p>
                <OnboardingButton variant="secondary" size="sm" />
              </div>
              <Separator />
              <div>
                <h3 className="text-sm font-medium">Contact Support</h3>
                <p className="text-sm text-muted-foreground">
                  Email us at <span className="text-primary"><EMAIL></span>
                </p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </DashboardShell>
  )
}
