"use client";

import { useEffect } from "react";
import { useParams } from "next/navigation";
import { prefetchLocations, prefetchCommonMetadata } from "@/lib/prefetch";

export default function OrganizationLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const params = useParams();
  const organizationSlug = params.organizationSlug as string;

  useEffect(() => {
    // Prefetch data when the organization layout mounts
    if (organizationSlug) {
      prefetchLocations(organizationSlug);
      prefetchCommonMetadata(organizationSlug);
    }
  }, [organizationSlug]);

  return <>{children}</>;
}
