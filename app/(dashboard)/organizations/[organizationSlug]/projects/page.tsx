"use client";

import { DashboardShell } from "@/components/dashboard/dashboard-shell";
import { DashboardHeader } from "@/components/dashboard/dashboard-header";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs";
import { Plus, Search, Filter, Calendar, List, Grid3X3 } from "lucide-react";
import { ProjectGrid } from "@/components/projects/project-grid";
import { ProjectList } from "@/components/projects/project-list";
import { ProjectCalendar } from "@/components/projects/project-calendar";
import Link from "next/link";
import { useState } from "react";
import React from "react"; // Keep React import if needed elsewhere
import { useParams } from "next/navigation"; // Import useParams
// import { useOrganization } from "@/hooks/use-organization"; // No longer needed directly for ID
import { useQuery } from "@tanstack/react-query";
import { type Project as ProjectModel } from "@/modules/project/model";
import type { Organization as OrganizationModel } from "@/modules/organization/model"; // Keep type keyword
import { Skeleton } from "@/components/ui/skeleton";
import { Terminal } from "lucide-react"; // Keep Terminal icon
import { usePermissions } from "@/hooks/use-permissions"; // Import the permissions hook
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogClose, // Added DialogClose
} from "@/components/ui/dialog";

// Removed local Project type alias

// Remove ProjectsPageProps interface if params prop is no longer used
// interface ProjectsPageProps {
//   params: Promise<{
//     organizationSlug: string;
//   }>;
// }

// Removed unused mockProjects array

export default function ProjectsPage(/* Remove params prop */) {
  const params = useParams<{ organizationSlug: string }>(); // Use useParams hook
  const organizationSlug = params?.organizationSlug; // Get slug from params object
  const { hasAnyRole, isLoadingPermissions } = usePermissions(); // Use the permissions hook
  const [searchQuery, setSearchQuery] = useState("");
  const [viewMode, setViewMode] = useState("grid");
  const [projectToArchive, setProjectToArchive] = useState<ProjectModel | null>(null);

  // Determine if the user can create projects
  const canCreateProject = hasAnyRole(['admin', 'manager']);

  // 1. Fetch organization details (including internal ID) by slug
  const { 
    data: organizationData, 
    isLoading: isOrgLoading, 
    error: orgError 
  } = useQuery<OrganizationModel>({
    queryKey: ['organization', organizationSlug],
    queryFn: async () => {
      const response = await fetch(`/api/organizations/slug/${organizationSlug}`);
      if (!response.ok) {
        if (response.status === 404) {
          throw new Error(`Organization with slug '${organizationSlug}' not found.`);
        }
        throw new Error('Failed to fetch organization details');
      }
      return response.json();
    },
    enabled: !!organizationSlug, // Only run if slug is available
    staleTime: Number.POSITIVE_INFINITY, // Use Number.POSITIVE_INFINITY instead of Infinity
    retry: false, // Don't retry on 404
  });

  // 2. Fetch projects using TanStack Query (will modify this next)
  const { 
    data: projects = [],
    isLoading: isProjectsLoading,
    error: projectsError,
  } = useQuery<ProjectModel[]>({
    // Use the internal organizationData.id from the first query
    queryKey: ['projects', organizationData?.id, searchQuery], 
    queryFn: async () => {
      // Ensure we have the internal ID before fetching
      if (!organizationData?.id) return []; 
      
      const url = `/api/organizations/${organizationData.id}/projects${searchQuery ? `?search=${encodeURIComponent(searchQuery)}` : ''}`;
      const response = await fetch(url);
      if (!response.ok) {
        // Handle 404 specifically if needed, though the API should return empty array or 500
        throw new Error('Failed to fetch projects');
      }
      return response.json();
    },
    // Enable this query only after the organization ID has been successfully fetched
    enabled: !!organizationData?.id, 
    staleTime: 5 * 60 * 1000,
  });

  // Note: Client-side filtering is removed as it's now handled by the API via searchQuery

  const handleArchiveClick = (project: ProjectModel) => { // Use ProjectModel
    setProjectToArchive(project);
  };

  const handleConfirmArchive = async () => {
    // Use organizationData.id for consistency if needed in mutation
    if (!projectToArchive || !organizationData?.id) return; 

    // TODO: Implement actual API call/server action to update project status
    // Example: await archiveProjectMutation.mutateAsync({ projectId: projectToArchive.id, organizationId: organizationData.id });
    console.log(`Archiving project: ${projectToArchive.name} (ID: ${projectToArchive.id})`);
    
    // For now, just close modal and potentially refetch data
    // await refetch(); // Refetch data after mutation (if using mutation hook)
    setProjectToArchive(null); // Close modal
  };

  const isLoading = isOrgLoading || isProjectsLoading;

  // Render Loading State
  if (isLoading) {
    return (
      <DashboardShell>
        <DashboardHeader
          title="Projects"
          description="Manage your production projects"
        >
          {/* Conditionally render skeleton based on permission loading state */}
          {isLoadingPermissions ? (
             <Skeleton className="h-10 w-32" />
          ) : canCreateProject ? (
             <Skeleton className="h-10 w-32" /> // Keep skeleton if loading projects, actual button shown later
          ) : null}
        </DashboardHeader>
        <div className="flex flex-col md:flex-row gap-4 mb-6 mt-6">
          <Skeleton className="h-10 flex-1" /> {/* Skeleton for Search */}
          <Skeleton className="h-10 w-24" /> {/* Skeleton for Filters */}
          <Skeleton className="h-10 w-[180px]" /> {/* Skeleton for View Tabs */}
        </div>
        {/* Skeleton for Grid/List - Use project ID or index if project data is available for keys, otherwise index is okay for skeleton */}
        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
          {[...Array(3)].map((_, i) => <Skeleton key={`skeleton-${i}`} className="h-60" />)} {/* Added prefix to key */}
        </div>
      </DashboardShell>
    );
  }

  // Render Error State (handle org error first)
  if (orgError) {
     return (
      <DashboardShell>
        <DashboardHeader
          title="Error"
          description="Could not load organization details."
        />
        <div className="mt-6 rounded-md border border-destructive bg-destructive/10 p-4 text-destructive">
          <div className="flex items-center gap-2">
            <Terminal className="h-4 w-4" />
            <h5 className="font-medium">Error Loading Organization</h5>
          </div>
          <p className="text-sm ml-6">
            {orgError.message || "An unknown error occurred."}
          </p>
        </div>
      </DashboardShell>
    );
  }
  // Handle projects error after org is loaded successfully
  if (projectsError) {
    return (
      <DashboardShell>
        <DashboardHeader 
          title="Projects"
          description="Manage your production projects"
        />
        {/* Replaced Alert with simple div for error message */}
        <div className="mt-6 rounded-md border border-destructive bg-destructive/10 p-4 text-destructive">
          <div className="flex items-center gap-2">
            <Terminal className="h-4 w-4" />
            <h5 className="font-medium">Error</h5>
          </div>
          <p className="text-sm ml-6">
            Failed to load projects. Please try again later.
          </p>
        </div>
      </DashboardShell>
    );
  }

  // Render Content (including Empty State)
  return (
    <DashboardShell>
      <DashboardHeader 
        title="Projects"
        description="Manage your production projects"
      >
        {/* Conditionally render the New Project button based on role */}
        {!isLoadingPermissions && canCreateProject && (
          <Link href={`/organizations/${organizationSlug}/projects/new`}>
            <Button>
              <Plus className="mr-2 h-4 w-4" />
              New Project
            </Button>
          </Link>
        )}
      </DashboardHeader>

      <div className="flex flex-col md:flex-row gap-4 mb-6 mt-6">
        <div className="relative flex-1">
          <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input 
            type="search" 
            placeholder="Search projects..." 
            className="w-full pl-8"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
        </div>
        <Button variant="outline" className="gap-1">
          <Filter className="h-4 w-4" />
          Filters
        </Button>
        <Tabs 
          defaultValue={viewMode} 
          value={viewMode} 
          onValueChange={setViewMode} 
          className="w-[180px]"
        >
          <TabsList>
            <TabsTrigger value="grid" className="px-3">
              <Grid3X3 className="h-4 w-4" />
            </TabsTrigger>
            <TabsTrigger value="list" className="px-3">
              <List className="h-4 w-4" />
            </TabsTrigger>
            <TabsTrigger value="calendar" className="px-3">
              <Calendar className="h-4 w-4" />
            </TabsTrigger>
          </TabsList>
        </Tabs>
      </div>

      {/* Conditional Rendering for Empty State */}
      {projects.length === 0 ? (
        // Replaced Alert with simple div for empty state message
        <div className="mt-6 rounded-md border border-border bg-background p-6 text-center text-muted-foreground">
           <Terminal className="h-6 w-6 mx-auto mb-2" /> {/* Larger icon */}
           <h5 className="font-medium mb-1">No Projects Found</h5>
           <p className="text-sm">
             {searchQuery
               ? "No projects match your search criteria."
               : "No projects created yet. Click 'New Project' to get started."}
           </p>
         </div>
      ) : (
        <Tabs
          defaultValue={viewMode} 
          value={viewMode} 
          onValueChange={setViewMode} 
          className="space-y-4"
        >
          <TabsContent value="grid" className="space-y-4">
            <ProjectGrid 
              projects={projects} // Use fetched projects directly
              organizationSlug={organizationSlug} 
              handleArchiveClick={handleArchiveClick} 
            />
          </TabsContent>
          <TabsContent value="list" className="space-y-4">
            <ProjectList 
              projects={projects} // Use fetched projects directly
              organizationSlug={organizationSlug} 
              handleArchiveClick={handleArchiveClick} 
            />
          </TabsContent>
          <TabsContent value="calendar" className="space-y-4">
            {/* TODO: Decide if Calendar view needs archive functionality */}
            <ProjectCalendar projects={projects} /> {/* Use fetched projects directly */}
          </TabsContent>
        </Tabs>
      )}

      {/* Archive Confirmation Dialog (remains the same) */}
      <Dialog open={!!projectToArchive} onOpenChange={(open) => !open && setProjectToArchive(null)}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Archive Project?</DialogTitle>
            <DialogDescription>
              {`Are you sure you want to archive the project "${projectToArchive?.name}"? This will mark it as completed.`}
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <DialogClose asChild>
              <Button variant="outline">Cancel</Button>
            </DialogClose>
            <Button variant="destructive" onClick={handleConfirmArchive}>
              Confirm Archive
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </DashboardShell>
  );
}
