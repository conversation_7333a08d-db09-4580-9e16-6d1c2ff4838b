"use client"

import { useState, use } from "react"
import { useRouter } from "next/navigation"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs"
import { ProjectBasicInfo } from "@/components/projects/project-create/project-basic-info"
import { ProjectDates } from "@/components/projects/project-create/project-dates"
import { ProjectTeam } from "@/components/projects/project-create/project-team"
import { ProjectBudget } from "@/components/projects/project-create/project-budget"
import { ProjectScenes } from "@/components/projects/project-create/project-scenes"
import {
  BasicInfoSkeleton,
  DatesSkeleton,
  TeamSkeleton,
  BudgetSkeleton,
} from "@/components/projects/project-create/loading-skeletons"
import { ScenesSkeleton } from "@/components/projects/project-create/project-scenes-skeleton"
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Save, Loader2 } from "lucide-react"

interface ProjectCreatePageProps {
  params: Promise<{
    organizationSlug?: string
  }>
}

export default function ProjectCreatePage({ params }: ProjectCreatePageProps) {
  const router = useRouter()
  const [activeTab, setActiveTab] = useState("basic-info")
  const [isLoading, setIsLoading] = useState(false)

  // Unwrap params using React.use() and ensure organizationSlug is never undefined
  const unwrappedParams = use(params)
  const organizationSlug = unwrappedParams?.organizationSlug || "default"

  const tabs = [
    { id: "basic-info", label: "Basic Info" },
    { id: "dates", label: "Dates" },
    { id: "team", label: "Team" },
    { id: "scenes", label: "Scenes" },
    { id: "budget", label: "Budget" },
  ]

  const handleTabChange = (value: string) => {
    setActiveTab(value)
  }

  const handleNext = () => {
    const currentIndex = tabs.findIndex((tab) => tab.id === activeTab)
    if (currentIndex < tabs.length - 1) {
      setActiveTab(tabs[currentIndex + 1].id)
    }
  }

  const handlePrevious = () => {
    const currentIndex = tabs.findIndex((tab) => tab.id === activeTab)
    if (currentIndex > 0) {
      setActiveTab(tabs[currentIndex - 1].id)
    }
  }

  const handleSaveAsDraft = () => {
    setIsLoading(true)
    // Simulate API call
    setTimeout(() => {
      setIsLoading(false)
      router.push(`/organizations/${organizationSlug}/projects`)
    }, 1500)
  }

  const handleCreateProject = () => {
    setIsLoading(true)
    // Simulate API call
    setTimeout(() => {
      setIsLoading(false)
      router.push(`/organizations/${organizationSlug}/projects`)
    }, 1500)
  }

  const handleCancel = () => {
    router.push(`/organizations/${organizationSlug}/projects`)
  }

  return (
    <div className="container py-6 space-y-6 max-w-5xl">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold tracking-tight">Create New Project</h1>
          <p className="text-muted-foreground">
            Fill out the details below to create a new project for your organization.
          </p>
        </div>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Project Details</CardTitle>
          <CardDescription>
            Complete each section to set up your project. You can save as a draft and come back later.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Tabs value={activeTab} onValueChange={handleTabChange}>
            <TabsList className="grid grid-cols-2 md:grid-cols-5 mb-6">
              {tabs.map((tab) => (
                <TabsTrigger key={tab.id} value={tab.id} disabled={isLoading}>
                  {tab.label}
                </TabsTrigger>
              ))}
            </TabsList>
            <TabsContent value="basic-info">{isLoading ? <BasicInfoSkeleton /> : <ProjectBasicInfo />}</TabsContent>
            <TabsContent value="dates">{isLoading ? <DatesSkeleton /> : <ProjectDates />}</TabsContent>
            <TabsContent value="team">
              {isLoading ? <TeamSkeleton /> : <ProjectTeam organizationSlug={organizationSlug} />}
            </TabsContent>
            <TabsContent value="scenes">{isLoading ? <ScenesSkeleton /> : <ProjectScenes />}</TabsContent>
            <TabsContent value="budget">{isLoading ? <BudgetSkeleton /> : <ProjectBudget />}</TabsContent>
          </Tabs>

          <div className="flex justify-between mt-8 pt-6 border-t">
            <div>
              <Button variant="outline" onClick={handleCancel} disabled={isLoading}>
                Cancel
              </Button>
            </div>
            <div className="flex gap-2">
              <Button variant="outline" onClick={handlePrevious} disabled={activeTab === "basic-info" || isLoading}>
                <ArrowLeft className="mr-2 h-4 w-4" />
                Previous
              </Button>
              <Button variant="outline" onClick={handleSaveAsDraft} disabled={isLoading}>
                {isLoading ? <Loader2 className="mr-2 h-4 w-4 animate-spin" /> : <Save className="mr-2 h-4 w-4" />}
                Save as Draft
              </Button>
              {activeTab === tabs[tabs.length - 1].id ? (
                <Button onClick={handleCreateProject} disabled={isLoading}>
                  {isLoading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                  Create Project
                </Button>
              ) : (
                <Button onClick={handleNext} disabled={isLoading}>
                  Next
                  <ArrowRight className="ml-2 h-4 w-4" />
                </Button>
              )}
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
