"use client"

import { useState, useEffect, use } from "react" // Added 'use' hook import
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { Button } from "@/components/ui/button"
import { ProjectMap } from "@/components/projects/project-map"
import { ProjectScenes } from "@/components/projects/project-scenes-list"
import { MapPin, Film, Calendar, ClipboardList, Settings, Users } from "lucide-react"

interface ProjectDashboardPageProps {
  params: Promise<{ // Params is now a Promise
    organizationSlug: string;
    projectId: string;
  }>;
}

export default function ProjectDashboardPage({ params }: ProjectDashboardPageProps) {
  // Unwrap the params promise using React.use()
  const resolvedParams = use(params);
  const { projectId } = resolvedParams; // Destructure after awaiting

  const [project, setProject] = useState<{
    id: string;
    name: string;
    description: string;
    status: string;
    organizationId: string;
  } | null>(null)
  const [loading, setLoading] = useState(true)

  // Access params directly (reverted from using React.use)
  useEffect(() => {
    // In a real implementation, this would fetch project data from an API
    const fetchProject = async () => {
      try {
        // Simulate API call
        setProject({
          id: projectId,
          name: "Sample Project",
          description: "A sample project for demonstration purposes",
          status: "active",
          organizationId: "org123",
        })
      } catch (error) {
        console.error("Error fetching project:", error)
      } finally {
        setLoading(false)
      }
    }

    fetchProject()
  }, [projectId])

  if (loading) {
    return (
      <div className="flex h-[calc(100vh-4rem)] items-center justify-center">
        <div className="text-center">
          <div className="animate-spin h-8 w-8 border-4 border-primary border-t-transparent rounded-full mx-auto" />
          <p className="mt-4 text-muted-foreground">Loading project...</p>
        </div>
      </div>
    )
  }

  if (!project) {
    return (
      <div className="flex h-[calc(100vh-4rem)] items-center justify-center">
        <div className="text-center">
          <h2 className="text-2xl font-bold">Project not found</h2>
          <p className="mt-2 text-muted-foreground">The project you&apos;re looking for doesn&apos;t exist or you don&apos;t have access to it.</p>
        </div>
      </div>
    )
  }

  return (
    <div className="container py-6 space-y-6">
      <div className="flex flex-col md:flex-row justify-between gap-4">
        <div>
          <h1 className="text-3xl font-bold">{project.name}</h1>
          <p className="text-muted-foreground">{project.description}</p>
        </div>
        <div className="flex items-start gap-2">
          <Button variant="outline">
            <Settings className="mr-2 h-4 w-4" />
            Settings
          </Button>
          <Button>
            <Film className="mr-2 h-4 w-4" />
            Add Scene
          </Button>
        </div>
      </div>

      <Tabs defaultValue="overview" className="space-y-4">
        <TabsList className="grid grid-cols-5 md:w-fit">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="scenes">Scenes</TabsTrigger>
          <TabsTrigger value="locations">Locations</TabsTrigger>
          <TabsTrigger value="schedule">Schedule</TabsTrigger>
          <TabsTrigger value="team">Team</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="md:col-span-2">
              <Card className="h-full">
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <MapPin className="mr-2 h-5 w-5" />
                    Project Locations
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="h-[500px]">
                    <ProjectMap projectId={projectId} />
                  </div>
                </CardContent>
              </Card>
            </div>

            <div>
              <Card className="h-full">
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <Film className="mr-2 h-5 w-5" />
                    Scenes Overview
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <Card>
                      <CardContent className="p-4 text-center">
                        <p className="text-3xl font-bold">12</p>
                        <p className="text-sm text-muted-foreground">Total Scenes</p>
                      </CardContent>
                    </Card>
                    <Card>
                      <CardContent className="p-4 text-center">
                        <p className="text-3xl font-bold">8</p>
                        <p className="text-sm text-muted-foreground">Locations Needed</p>
                      </CardContent>
                    </Card>
                    <Card>
                      <CardContent className="p-4 text-center">
                        <p className="text-3xl font-bold">5</p>
                        <p className="text-sm text-muted-foreground">Locations Secured</p>
                      </CardContent>
                    </Card>
                    <Card>
                      <CardContent className="p-4 text-center">
                        <p className="text-3xl font-bold">3</p>
                        <p className="text-sm text-muted-foreground">Pending Approval</p>
                      </CardContent>
                    </Card>
                  </div>

                  <div className="space-y-2">
                    <h3 className="font-medium">Recent Activity</h3>
                    <div className="space-y-2">
                      <div className="flex items-start gap-2 text-sm">
                        <div className="h-2 w-2 mt-1.5 rounded-full bg-green-500" />
                        <div>
                          <p>Location approved for Scene 3</p>
                          <p className="text-xs text-muted-foreground">2 hours ago</p>
                        </div>
                      </div>
                      <div className="flex items-start gap-2 text-sm">
                        <div className="h-2 w-2 mt-1.5 rounded-full bg-blue-500" />
                        <div>
                          <p>New location added for Scene 5</p>
                          <p className="text-xs text-muted-foreground">Yesterday</p>
                        </div>
                      </div>
                      <div className="flex items-start gap-2 text-sm">
                        <div className="h-2 w-2 mt-1.5 rounded-full bg-orange-500" />
                        <div>
                          <p>Scene 7 scheduled for next week</p>
                          <p className="text-xs text-muted-foreground">2 days ago</p>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div className="pt-4">
                    <Button variant="outline" className="w-full">
                      <ClipboardList className="mr-2 h-4 w-4" />
                      View Full Activity Log
                    </Button>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Calendar className="mr-2 h-5 w-5" />
                  Upcoming Schedule
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex items-center justify-between border-b pb-2">
                    <div>
                      <p className="font-medium">Scene 4: Downtown Chase</p>
                      <p className="text-sm text-muted-foreground">Downtown Financial District</p>
                    </div>
                    <div className="text-right">
                      <p className="font-medium">Jun 15, 2025</p>
                      <p className="text-sm text-muted-foreground">8:00 AM - 12:00 PM</p>
                    </div>
                  </div>
                  <div className="flex items-center justify-between border-b pb-2">
                    <div>
                      <p className="font-medium">Scene 2: Apartment Confrontation</p>
                      <p className="text-sm text-muted-foreground">Downtown Loft</p>
                    </div>
                    <div className="text-right">
                      <p className="font-medium">Jun 18, 2025</p>
                      <p className="text-sm text-muted-foreground">1:00 PM - 7:00 PM</p>
                    </div>
                  </div>
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="font-medium">Scene 8: Police Station Briefing</p>
                      <p className="text-sm text-muted-foreground">Police Station Set</p>
                    </div>
                    <div className="text-right">
                      <p className="font-medium">Jun 20, 2025</p>
                      <p className="text-sm text-muted-foreground">9:00 AM - 12:00 PM</p>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Users className="mr-2 h-5 w-5" />
                  Team Members
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex items-center justify-between border-b pb-2">
                    <div className="flex items-center gap-3">
                      <div className="h-8 w-8 rounded-full bg-primary/10 flex items-center justify-center">
                        <span className="text-xs font-medium">JD</span>
                      </div>
                      <div>
                        <p className="font-medium">John Doe</p>
                        <p className="text-xs text-muted-foreground">Project Manager</p>
                      </div>
                    </div>
                    <Button variant="ghost" size="sm">
                      Contact
                    </Button>
                  </div>
                  <div className="flex items-center justify-between border-b pb-2">
                    <div className="flex items-center gap-3">
                      <div className="h-8 w-8 rounded-full bg-primary/10 flex items-center justify-center">
                        <span className="text-xs font-medium">SJ</span>
                      </div>
                      <div>
                        <p className="font-medium">Sarah Johnson</p>
                        <p className="text-xs text-muted-foreground">Location Scout</p>
                      </div>
                    </div>
                    <Button variant="ghost" size="sm">
                      Contact
                    </Button>
                  </div>
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      <div className="h-8 w-8 rounded-full bg-primary/10 flex items-center justify-center">
                        <span className="text-xs font-medium">MR</span>
                      </div>
                      <div>
                        <p className="font-medium">Mike Rodriguez</p>
                        <p className="text-xs text-muted-foreground">Director</p>
                      </div>
                    </div>
                    <Button variant="ghost" size="sm">
                      Contact
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="scenes">
          <ProjectScenes projectId={projectId} />
        </TabsContent>

        <TabsContent value="locations">
          <div className="text-center py-12">
            <h3 className="text-lg font-medium">Locations Tab Content</h3>
            <p className="text-muted-foreground">This tab will show all locations for the project.</p>
          </div>
        </TabsContent>

        <TabsContent value="schedule">
          <div className="text-center py-12">
            <h3 className="text-lg font-medium">Schedule Tab Content</h3>
            <p className="text-muted-foreground">This tab will show the project schedule.</p>
          </div>
        </TabsContent>

        <TabsContent value="team">
          <div className="text-center py-12">
            <h3 className="text-lg font-medium">Team Tab Content</h3>
            <p className="text-muted-foreground">This tab will show the project team members.</p>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  )
}
