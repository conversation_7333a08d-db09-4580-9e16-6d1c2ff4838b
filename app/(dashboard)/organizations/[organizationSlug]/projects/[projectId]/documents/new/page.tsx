import React from 'react';

// Placeholder for Document Upload (Screen 34)
// Path: /organizations/[organizationSlug]/projects/[projectId]/documents/new

interface DocumentUploadPageProps {
  params: {
    organizationSlug: string;
    projectId: string;
  };
}

const DocumentUploadPage: React.FC<DocumentUploadPageProps> = ({ params }) => {
  return (
    <div>
      <h1>Upload New Document</h1>
      <p>Organization Slug: {params.organizationSlug}</p>
      <p>Project ID: {params.projectId}</p>
      {/* TODO: Implement Document Upload UI based on specs */}
    </div>
  );
};

export default DocumentUploadPage;
