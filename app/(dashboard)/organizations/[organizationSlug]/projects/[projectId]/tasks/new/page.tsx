import React from 'react';

// Placeholder for Task Creation (Screen 39)
// Path: /organizations/[organizationSlug]/projects/[projectId]/tasks/new

interface TaskCreationPageProps {
  params: {
    organizationSlug: string;
    projectId: string;
  };
}

const TaskCreationPage: React.FC<TaskCreationPageProps> = ({ params }) => {
  return (
    <div>
      <h1>Create New Task</h1>
      <p>Organization Slug: {params.organizationSlug}</p>
      <p>Project ID: {params.projectId}</p>
      {/* TODO: Implement Task Creation Form UI based on specs */}
    </div>
  );
};

export default TaskCreationPage;
