import React from 'react';

// Placeholder for Project Tasks List (Screen 38)
// Path: /organizations/[organizationSlug]/projects/[projectId]/tasks

interface ProjectTasksPageProps {
  params: {
    organizationSlug: string;
    projectId: string;
  };
}

const ProjectTasksPage: React.FC<ProjectTasksPageProps> = ({ params }) => {
  return (
    <div>
      <h1>Project Tasks List</h1>
      <p>Organization Slug: {params.organizationSlug}</p>
      <p>Project ID: {params.projectId}</p>
      {/* TODO: Implement Project Tasks List UI based on specs */}
    </div>
  );
};

export default ProjectTasksPage;
