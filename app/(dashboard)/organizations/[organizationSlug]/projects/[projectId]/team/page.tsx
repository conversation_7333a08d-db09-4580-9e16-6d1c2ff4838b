import React from 'react';

// Placeholder for Project Team (Screen 22)
// Path: /organizations/[organizationSlug]/projects/[projectId]/team

interface ProjectTeamPageProps {
  params: {
    organizationSlug: string;
    projectId: string;
  };
}

const ProjectTeamPage: React.FC<ProjectTeamPageProps> = ({ params }) => {
  return (
    <div>
      <h1>Project Team</h1>
      <p>Organization Slug: {params.organizationSlug}</p>
      <p>Project ID: {params.projectId}</p>
      {/* TODO: Implement Project Team UI based on specs */}
    </div>
  );
};

export default ProjectTeamPage;
