import React from 'react';

// Placeholder for Mobile Location Capture (Screen 53)
// Path: /organizations/[organizationSlug]/projects/[projectId]/locations/capture

interface MobileLocationCapturePageProps {
  params: {
    organizationSlug: string;
    projectId: string;
  };
}

const MobileLocationCapturePage: React.FC<MobileLocationCapturePageProps> = ({ params }) => {
  return (
    <div>
      <h1>Mobile Location Capture</h1>
      <p>Organization Slug: {params.organizationSlug}</p>
      <p>Project ID: {params.projectId}</p>
      {/* TODO: Implement Mobile Location Capture UI based on specs */}
    </div>
  );
};

export default MobileLocationCapturePage;
