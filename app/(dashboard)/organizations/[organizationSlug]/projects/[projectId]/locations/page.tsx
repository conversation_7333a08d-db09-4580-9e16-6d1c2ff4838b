import React from 'react';

// Placeholder for Project Locations List (Screen 25)
// Path: /organizations/[organizationSlug]/projects/[projectId]/locations

interface ProjectLocationsPageProps {
  params: {
    organizationSlug: string;
    projectId: string;
  };
}

const ProjectLocationsPage: React.FC<ProjectLocationsPageProps> = ({ params }) => {
  return (
    <div>
      <h1>Project Locations List</h1>
      <p>Organization Slug: {params.organizationSlug}</p>
      <p>Project ID: {params.projectId}</p>
      {/* TODO: Implement Project Locations List UI based on specs */}
    </div>
  );
};

export default ProjectLocationsPage;
