import React from 'react';

// Placeholder for Location Creation (Screen 26)
// Path: /organizations/[organizationSlug]/projects/[projectId]/locations/new

interface LocationCreationPageProps {
  params: {
    organizationSlug: string;
    projectId: string;
  };
}

const LocationCreationPage: React.FC<LocationCreationPageProps> = ({ params }) => {
  return (
    <div>
      <h1>Create New Location</h1>
      <p>Organization Slug: {params.organizationSlug}</p>
      <p>Project ID: {params.projectId}</p>
      {/* TODO: Implement Location Creation Form UI based on specs */}
    </div>
  );
};

export default LocationCreationPage;
