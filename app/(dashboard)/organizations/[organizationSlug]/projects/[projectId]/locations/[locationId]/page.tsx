import React from 'react';

// Placeholder for Location Details (Screen 27)
// Path: /organizations/[organizationSlug]/projects/[projectId]/locations/[locationId]

interface LocationDetailsPageProps {
  params: {
    organizationSlug: string;
    projectId: string;
    locationId: string;
  };
}

const LocationDetailsPage: React.FC<LocationDetailsPageProps> = ({ params }) => {
  return (
    <div>
      <h1>Location Details</h1>
      <p>Organization Slug: {params.organizationSlug}</p>
      <p>Project ID: {params.projectId}</p>
      <p>Location ID: {params.locationId}</p>
      {/* TODO: Implement Location Details UI based on specs */}
    </div>
  );
};

export default LocationDetailsPage;
