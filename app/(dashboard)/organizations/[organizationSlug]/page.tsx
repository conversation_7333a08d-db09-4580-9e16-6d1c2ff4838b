"use client"; // Mark as client component because we're using hooks

import { DashboardShell } from "@/components/dashboard/dashboard-shell";
import { DashboardHeader } from "@/components/dashboard/dashboard-header";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Briefcase, CalendarDays, MapPin, Plus, Users } from "lucide-react"; // Added Briefcase
import Link from "next/link";
import React from "react";
import { OnboardingTrigger } from "@/components/onboarding/onboarding-trigger";
import { usePermissions, hasRequiredRole } from "@/hooks/usePermissions";

// Define props to accept params from dynamic route segment
interface OrganizationDashboardPageProps {
  params: Promise<{
    organizationSlug: string;
  }>;
}

export default function OrganizationDashboardPage({ params }: OrganizationDashboardPageProps) {
  // Always use React.use() to unwrap params in Next.js 15
  const { organizationSlug } = React.use(params);

  // TODO: Fetch dynamic data based on organizationSlug
  // const { data: stats, isLoading: isLoadingStats } = useQuery(...)
  // const { data: recentProjects, isLoading: isLoadingProjects } = useQuery(...)
  // const { data: recentLocations, isLoading: isLoadingLocations } = useQuery(...)
  // const { data: activityFeed, isLoading: isLoadingActivity } = useQuery(...)

  const userRoles = usePermissions();
  const canCreateProject = hasRequiredRole(userRoles, ['admin', 'manager']);

  return (
    <DashboardShell>
      <OnboardingTrigger organizationSlug={organizationSlug} />
      {/* Changed 'heading' prop to 'title' */}
      <DashboardHeader title="Dashboard" description="Overview of your production activities">
        {/* Conditionally render the New Project button based on role */}
        {canCreateProject && (
          <Link href={`/organizations/${organizationSlug}/projects/new`}>
            <Button>
              <Plus className="mr-2 h-4 w-4" />
              New Project
            </Button>
          </Link>
        )}
      </DashboardHeader>

      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Projects</CardTitle>
            {/* Replaced SVG with Briefcase icon */}
            <Briefcase className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            {/* TODO: Replace with dynamic data */}
            <div className="text-2xl font-bold">12</div>
            <p className="text-xs text-muted-foreground">+2 from last month</p> {/* TODO: Replace with dynamic data */}
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Locations</CardTitle>
            <MapPin className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            {/* TODO: Replace with dynamic data */}
            <div className="text-2xl font-bold">34</div>
            <p className="text-xs text-muted-foreground">+8 from last month</p> {/* TODO: Replace with dynamic data */}
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Team Members</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            {/* TODO: Replace with dynamic data */}
            <div className="text-2xl font-bold">16</div>
            <p className="text-xs text-muted-foreground">+3 from last month</p> {/* TODO: Replace with dynamic data */}
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Upcoming Shoots</CardTitle>
            <CalendarDays className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            {/* TODO: Replace with dynamic data */}
            <div className="text-2xl font-bold">5</div>
            <p className="text-xs text-muted-foreground">Next: Jun 12, 2025</p> {/* TODO: Replace with dynamic data */}
          </CardContent>
        </Card>
      </div>

      <Tabs defaultValue="recent" className="space-y-4">
        <TabsList>
          <TabsTrigger value="recent">Recent Projects</TabsTrigger>
          <TabsTrigger value="locations">Recent Locations</TabsTrigger>
          <TabsTrigger value="activity">Activity</TabsTrigger>
        </TabsList>
        <TabsContent value="recent" className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
            {/* TODO: Replace placeholder map with real data and use unique project IDs for keys */}
            {Array.from({ length: 3 }).map((_, i) => (
              // biome-ignore lint/suspicious/noArrayIndexKey: <explanation>Placeholder data, replace with unique ID when fetching real data</explanation>
              <Card key={i}>
                <CardHeader className="pb-2">
                  <CardTitle>Project {i + 1}</CardTitle> {/* TODO: Replace with dynamic data */}
                  <CardDescription>Last updated: {new Date().toLocaleDateString()}</CardDescription> {/* TODO: Replace with dynamic data */}
                </CardHeader>
                <CardContent>
                  <div className="text-sm">
                    <p>Status: In Progress</p>
                    <p>Locations: 4</p>
                    <p>Team: 8 members</p>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
          <div className="flex justify-center">
            {/* Use dynamic organizationSlug in the link */}
            <Link href={`/organizations/${organizationSlug}/projects`}>
              <Button variant="outline">View All Projects</Button>
            </Link>
          </div>
        </TabsContent>
        <TabsContent value="locations" className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
            {/* TODO: Replace placeholder map with real data and use unique location IDs for keys */}
            {Array.from({ length: 3 }).map((_, i) => (
              // biome-ignore lint/suspicious/noArrayIndexKey: <explanation>Placeholder data, replace with unique ID when fetching real data</explanation>
              <Card key={i}>
                <CardHeader className="pb-2">
                  <CardTitle>Location {i + 1}</CardTitle> {/* TODO: Replace with dynamic data */}
                  <CardDescription>Added: {new Date().toLocaleDateString()}</CardDescription> {/* TODO: Replace with dynamic data */}
                </CardHeader>
                <CardContent>
                  <div className="text-sm">
                    <p>Type: Exterior</p>
                    <p>Status: Approved</p>
                    <p>Project: Project {i + 2}</p>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
          <div className="flex justify-center">
            {/* Use dynamic organizationSlug in the link */}
            <Link href={`/organizations/${organizationSlug}/locations`}>
              <Button variant="outline">View All Locations</Button>
            </Link>
          </div>
        </TabsContent>
        <TabsContent value="activity" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Recent Activity</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {/* TODO: Replace placeholder map with real data and use unique activity IDs for keys */}
                {Array.from({ length: 5 }).map((_, i) => (
                  // biome-ignore lint/suspicious/noArrayIndexKey: <explanation>Placeholder data, replace with unique ID when fetching real data</explanation>
                  <div key={i} className="flex items-start gap-4 border-b pb-4 last:border-0">
                    <div className="rounded-full bg-primary/10 p-2">
                      <Users className="h-4 w-4" /> {/* TODO: Use appropriate icon based on activity type */}
                    </div>
                    <div>
                      {/* TODO: Replace with dynamic data */}
                      <p className="text-sm font-medium">
                        User {i + 1} updated Project {i + 1}
                      </p>
                      <p className="text-xs text-muted-foreground">{new Date().toLocaleString()}</p> {/* TODO: Replace with dynamic data */}
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </DashboardShell>
  )
}

// Removed duplicate default export
