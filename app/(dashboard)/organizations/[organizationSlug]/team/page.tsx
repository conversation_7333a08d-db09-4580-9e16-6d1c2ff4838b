"use client"; // Mark as client component

import React from 'react';
import { DashboardHeader } from "@/components/dashboard/dashboard-header";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { PlusIcon, MailIcon, PhoneIcon } from "lucide-react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Avatar } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { usePermissions, hasRequiredRole } from "@/hooks/usePermissions"; // Import permission hooks
import { Pencil } from "lucide-react"; // Import Pencil icon
import { EditMemberModal } from "@/components/team/edit-member-modal"; // Import the modal
import { useOrganization } from "@/hooks/use-organization"; // Import organization hook
import { useQueryClient } from "@tanstack/react-query"; // Import query client for cache invalidation
import { toast } from "sonner"; // Import toast for feedback

// This is a placeholder page - in a real implementation, this would fetch team data from the API
export default function TeamPage() {
  const userRoles = usePermissions();
  const canAddMember = hasRequiredRole(userRoles, ['admin']);
  const { organization } = useOrganization(); // Get organization context
  const queryClient = useQueryClient(); // Get query client instance

  // TODO: Replace mock data with actual data fetching using TanStack Query
  // Example: const { data: teamMembers, isLoading, error } = useQuery({ queryKey: ['team', organization?.id], queryFn: fetchTeamMembers });
  const teamMembers = [
    {
      id: "1",
      name: "John Doe",
      email: "<EMAIL>",
      phone: "+****************",
      role: "Location Manager",
      avatar: "",
      projects: ["Summer Feature", "Commercial Shoot"],
    },
    {
      id: "2",
      name: "Jane Smith",
      email: "<EMAIL>",
      phone: "+****************",
      role: "Location Scout",
      avatar: "",
      projects: ["Summer Feature"],
    },
    {
      id: "3",
      name: "Robert Johnson",
      email: "<EMAIL>",
      phone: "+****************",
      role: "Production Manager",
      avatar: "",
      projects: ["Commercial Shoot", "Documentary Series"],
    },
    {
      id: "4",
      name: "Emily Davis",
      email: "<EMAIL>",
      phone: "+****************",
      role: "Location Scout",
      avatar: "",
      projects: ["Documentary Series"],
    },
  ];

  // Function to get role badge variant
  const getRoleBadgeVariant = (role: string) => {
    switch (role) {
      case "Location Manager":
        return "default";
      case "Production Manager":
        return "secondary";
      case "Location Scout":
        return "outline";
      default:
        return "outline";
    }
  };

  // Function to handle saving the updated role via API
  const handleSaveMemberRole = async (memberId: string, newRole: string) => {
    if (!organization?.id) {
      toast.error("Organization context is missing.");
      throw new Error("Organization context is missing.");
    }

    console.log(`Saving role for member ${memberId} to ${newRole} in org ${organization.id}`);
    try {
      const response = await fetch(`/api/organizations/${organization.id}/members/${memberId}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ roles: [newRole] }), // Send roles as an array
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || `Failed to update role: ${response.statusText}`);
      }

      // On success: show toast and invalidate queries to refetch team data
      toast.success("Member role updated successfully!");
      // TODO: Adjust query key if necessary based on actual data fetching implementation
      await queryClient.invalidateQueries({ queryKey: ['team', organization.id] });

    } catch (error) {
      console.error("Failed to save member role:", error);
      toast.error(`Failed to update role: ${error instanceof Error ? error.message : 'Unknown error'}`);
      // Re-throw the error so the modal can display it
      throw error;
    }
  };


  return (
    <div className="flex-1 space-y-4 p-4 md:p-8 pt-6">
      <DashboardHeader
        title="Team"
        description="Manage your production team members."
      >
        {/* Conditionally render Add Team Member button */}
        {canAddMember && (
          <Button>
            {/* TODO: Implement invite/add member functionality */}
            <PlusIcon className="mr-2 h-4 w-4" />
            Add Team Member
          </Button>
        )}
      </DashboardHeader>

      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        {/* TODO: Use unique key like member.id when using real data */}
        {teamMembers.slice(0, 4).map((member) => (
          // biome-ignore lint/suspicious/noArrayIndexKey: <explanation>Using member.id for mock data, but confirm uniqueness with real data</explanation>
          <Card key={member.id}>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <div className="flex items-center space-x-2">
                <Avatar className="h-9 w-9">
                  <span className="text-xs font-medium">
                    {member.name
                      .split(" ")
                      .map((n) => n[0])
                      .join("")}
                  </span>
                </Avatar>
                <div>
                  <CardTitle className="text-sm font-medium">
                    {member.name}
                  </CardTitle>
                  <CardDescription className="text-xs">
                    {member.role}
                  </CardDescription>
                </div>
              </div>
              <Badge variant={getRoleBadgeVariant(member.role)}>
                {member.role}
              </Badge>
            </CardHeader>
            <CardContent>
              <div className="text-xs text-muted-foreground">
                <div className="flex items-center space-x-1">
                  <MailIcon className="h-3 w-3" />
                  <span>{member.email}</span>
                </div>
                <div className="flex items-center space-x-1 mt-1">
                  <PhoneIcon className="h-3 w-3" />
                  <span>{member.phone}</span>
                </div>
                <div className="mt-2">
                  <span className="font-medium text-xs">Projects: </span>
                  {member.projects.join(", ")}
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Team Members</CardTitle>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Name</TableHead>
                <TableHead>Role</TableHead>
                <TableHead>Email</TableHead>
                <TableHead>Phone</TableHead>
                <TableHead>Projects</TableHead>
                <TableHead className="text-right">Actions</TableHead> {/* Add Actions column */}
              </TableRow>
            </TableHeader>
            <TableBody>
              {/* TODO: Use unique key like member.id when using real data */}
              {teamMembers.map((member) => (
                // biome-ignore lint/suspicious/noArrayIndexKey: <explanation>Using member.id for mock data, but confirm uniqueness with real data</explanation>
                <TableRow key={member.id}>
                  <TableCell className="font-medium">{member.name}</TableCell>
                  <TableCell>
                    <Badge variant={getRoleBadgeVariant(member.role)}>
                      {member.role}
                    </Badge>
                  </TableCell>
                  <TableCell>{member.email}</TableCell>
                  <TableCell>{member.phone}</TableCell>
                  <TableCell>{member.projects.join(", ")}</TableCell>
                  <TableCell className="text-right">
                    {/* Conditionally render Edit Member Modal trigger for admins */}
                    {hasRequiredRole(userRoles, ['admin']) && (
                      <EditMemberModal
                        member={{
                          id: member.id,
                          name: member.name,
                          email: member.email,
                          // TODO: Ensure this 'role' matches Stytch role IDs ('admin', 'manager', etc.)
                          // This mock data uses display names, which might need adjustment.
                          // For now, we'll pass it as is, but the modal expects Stytch IDs.
                          // Ensure the role passed matches Stytch role IDs ('admin', 'manager', etc.)
                          // The mock data uses display names ("Location Manager"), which needs correction.
                          // For now, we'll pass a placeholder or attempt a basic mapping.
                          // Ideally, fetch data should return the correct Stytch role ID.
                          role: member.role.toLowerCase().replace(' ', '_'), // Basic attempt to map - NEEDS VERIFICATION
                        }}
                        onSave={handleSaveMemberRole} // Pass the actual save handler
                        triggerButton={
                          <Button variant="ghost" size="icon" className="h-8 w-8">
                            <Pencil className="h-4 w-4" />
                            <span className="sr-only">Edit Member</span>
                          </Button>
                        }
                      />
                    )}
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
    </div>
  );
}
