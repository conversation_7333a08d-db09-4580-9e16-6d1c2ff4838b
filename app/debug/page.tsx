"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"

export default function DebugPage() {
  const [email, setEmail] = useState("")
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [success, setSuccess] = useState(false)
  const [response, setResponse] = useState<Record<string, unknown> | null>(null)

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsLoading(true)
    setError(null)
    setSuccess(false)
    setResponse(null)

    try {
      console.log("Debug page: Sending request to debug API route")
      const response = await fetch("/api/auth/debug", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ email }),
      })

      const data = await response.json()
      console.log("Debug page: Response from debug API route:", data)

      if (!response.ok) {
        throw new Error(data.error || "Failed to send debug magic link")
      }

      setSuccess(true)
      setResponse(data)
    } catch (err) {
      console.error("Debug page error:", err)
      setError(err instanceof Error ? err.message : "An unexpected error occurred")
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <div className="flex min-h-screen items-center justify-center bg-muted/40 p-4">
      <div className="w-full max-w-md space-y-6">
        <Card>
          <CardHeader>
            <CardTitle>Debug Stytch Authentication</CardTitle>
            <CardDescription>Send a magic link directly using the Stytch API</CardDescription>
          </CardHeader>
          <CardContent>
            {success ? (
              <div className="space-y-4">
                <div className="text-center py-4">
                  <p className="text-green-600 font-medium mb-2">Magic link sent successfully!</p>
                  <p className="text-sm text-muted-foreground">
                    Check your email at {email}
                  </p>
                </div>
                <div className="border p-4 rounded-md bg-muted/20">
                  <p className="font-medium mb-2">API Response:</p>
                  <pre className="text-xs overflow-auto whitespace-pre-wrap">
                    {JSON.stringify(response, null, 2)}
                  </pre>
                </div>
                <Button 
                  className="w-full" 
                  onClick={() => {
                    setSuccess(false)
                    setResponse(null)
                  }}
                >
                  Try Again
                </Button>
              </div>
            ) : (
              <form onSubmit={handleSubmit}>
                <div className="grid gap-4">
                  <div className="grid gap-2">
                    <Label htmlFor="email">Email</Label>
                    <Input 
                      id="email" 
                      type="email" 
                      placeholder="<EMAIL>" 
                      value={email}
                      onChange={(e) => setEmail(e.target.value)}
                      required 
                    />
                  </div>
                  {error && (
                    <div className="text-sm text-red-500">{error}</div>
                  )}
                  <Button type="submit" className="w-full" disabled={isLoading}>
                    {isLoading ? "Sending..." : "Send Debug Magic Link"}
                  </Button>
                </div>
              </form>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
