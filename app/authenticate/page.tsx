"use client"

import { useEffect, useState } from "react"
import { useR<PERSON>er, useSearchParams } from "next/navigation"
import { MapPin } from "lucide-react"
import { Card, CardContent } from "@/components/ui/card"
import { StytchB2B } from '@stytch/nextjs/b2b'
import { AuthFlowType, B2BProducts } from '@stytch/vanilla-js/b2b'
import type { StytchEvent, StytchError } from '@stytch/vanilla-js'
import { useStytchMember } from "@stytch/nextjs/b2b"
import { authConfig } from "@/lib/config/auth"

export default function AuthenticatePage() {
  const router = useRouter()
  const searchParams = useSearchParams()
  const { member, isInitialized } = useStytchMember()
  const [isLoading, setIsLoading] = useState(true)
  
  // Get the base URL from the environment or use the current origin
  const baseUrl = process.env.NEXT_PUBLIC_APP_URL || 
    (typeof window !== 'undefined' ? window.location.origin : 'http://localhost:3000')
  
  // Check if there's a token in the URL - use the exact parameter names that Stytch expects
  const token = searchParams.get("token") || 
                searchParams.get("stytch_token") || 
                searchParams.get("stytch_token_magic_links") ||
                searchParams.get("discovery_magic_links_token")
  
  
  // Log token information for debugging
  useEffect(() => {
    if (token) {
      console.log(`Token found in URL: ${token.substring(0, 10)}...`)
      console.log("Token type:", searchParams.get("stytch_token_type") || "unknown")
      console.log("Redirect type:", searchParams.get("stytch_redirect_type") || "unknown")
    } else {
      console.log("No token found in URL. Search params:", Object.fromEntries(searchParams.entries()))
    }
  }, [token, searchParams])
  
  // Configure the StytchB2B component
  const config = {
    products: [B2BProducts.emailMagicLinks],
    sessionOptions: { sessionDurationMinutes: 60 * 24 }, // 1 day (1440 minutes) - reduced to comply with live project limits
    authFlowType: AuthFlowType.Discovery,
    emailMagicLinksOptions: {
      loginRedirectURL: `${baseUrl}${authConfig.callbackUrl}`,
      loginExpirationMinutes: 30,
      signupRedirectURL: `${baseUrl}${authConfig.callbackUrl}`,
      signupExpirationMinutes: 30,
      discoveryRedirectURL: `${baseUrl}${authConfig.callbackUrl}`,
    },
    style: {
      fontFamily: 'inherit',
      width: '100%',
      borderRadius: '0.375rem',
      primaryColor: 'hsl(var(--primary))',
      primaryTextColor: 'hsl(var(--primary-foreground))',
    }
  }
  
  // If user is already logged in, redirect to organization selection
  useEffect(() => {
    if (isInitialized && member) {
      router.replace("/auth/organization/select")
    } else if (isInitialized) {
      setIsLoading(false)
    }
  }, [member, isInitialized, router])
  
  return (
    <div className="flex min-h-screen items-center justify-center bg-muted/40 p-4">
      <div className="w-full max-w-md space-y-6">
        <div className="flex flex-col items-center space-y-2 text-center">
          <div className="flex items-center gap-2">
            <MapPin className="h-6 w-6 text-primary" />
            <span className="text-xl font-bold">Scene-o-matic</span>
          </div>
          <h1 className="text-2xl font-bold">Authenticating...</h1>
          <p className="text-muted-foreground">Please wait while we verify your identity</p>
        </div>
        
        <Card>
          <CardContent className="pt-6">
{isLoading ? "Loading..." : <StytchB2B config={config} callbacks={{ onEvent: (event: StytchEvent) => { console.log("Stytch event:", event); if (event.type.includes("AUTHENTICATED")) { console.log("Authentication successful, redirecting to organization selection"); router.replace("/auth/organization/select"); } }, onError: (error: StytchError) => { console.error("Stytch error:", error); } }} />}
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
