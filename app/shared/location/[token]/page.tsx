import { SharedLocationView } from "@/components/locations/shared-location-view"
import type { Metadata } from "next"

interface SharedLocationPageProps {
  params: {
    token: string
  }
}

export const metadata: Metadata = {
  title: "Shared Location | Scene-o-matic",
  description: "View a shared location from Scene-o-matic",
}

export default function SharedLocationPage({ params }: SharedLocationPageProps) {
  return <SharedLocationView token={params.token} />
}
