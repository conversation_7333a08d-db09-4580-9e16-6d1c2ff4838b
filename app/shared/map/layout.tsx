import type { <PERSON>ada<PERSON> } from "next"
import { Inter } from "next/font/google"
import { Toaster } from "sonner"
import "@/app/globals.css"

const inter = Inter({ subsets: ["latin"] })

export const metadata: Metadata = {
  title: {
    default: "Scene-o-matic Shared Map",
    template: "%s | Scene-o-matic",
  },
  description: "View a shared map from Scene-o-matic",
}

export default function SharedMapLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="en">
      <body className={inter.className}>
        <Toaster position="top-right" />
        <main>{children}</main>
      </body>
    </html>
  )
}
