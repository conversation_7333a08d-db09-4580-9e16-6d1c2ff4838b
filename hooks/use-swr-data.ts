import useSWR from 'swr';

// Define a reusable fetcher function
const fetcher = async (url: string) => {
  const res = await fetch(url);
  if (!res.ok) {
    const errorInfo = await res.text();
    throw new Error(`An error occurred while fetching the data from ${url}: ${res.statusText} - ${errorInfo}`);
  }
  return res.json();
};

// Hook to fetch Mapbox token using SWR
export function useMapboxToken() {
  // Use a stable key for the Mapbox token endpoint
  const key = '/api/mapbox-token'; 

  return useSWR<any, Error>( // TODO: Replace any with specific token response type
    key, 
    fetcher, 
    {
      revalidateOnFocus: false,
      revalidateIfStale: false,
      revalidateOnReconnect: false,
      // Cache token for a long duration (e.g., 24 hours) as it rarely changes
      dedupingInterval: 24 * 60 * 60 * 1000, 
      // Optional: Set error retry options if needed
      // onErrorRetry: (error, key, config, revalidate, { retryCount }) => { ... }
    }
  );
}

// Hook to fetch user favorites using SWR
export function useFavorites() {
  // Use a stable key for the favorites endpoint
  const key = '/api/favorites'; 

  return useSWR<any, Error>( // TODO: Replace any with specific favorites response type
    key, 
    fetcher, 
    {
      revalidateOnFocus: false, // Don't refetch just on focus
      // Cache for 5 minutes, SWR will handle stale-while-revalidate based on this
      dedupingInterval: 5 * 60 * 1000, 
      // Optional: Refresh periodically if needed
      // refreshInterval: 10 * 60 * 1000, // e.g., every 10 minutes
    }
  );
}
