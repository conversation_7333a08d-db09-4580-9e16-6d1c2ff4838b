"use client";

import { useAuth } from "./use-auth";

/**
 * Custom hook to check user permissions based on their roles.
 * Relies on the user object from AuthContext containing a 'roles' array.
 */
export function usePermissions() {
  const { user, isLoading } = useAuth();

  /**
   * Checks if the currently authenticated user has a specific role.
   *
   * @param role The role string to check for (e.g., 'admin', 'manager').
   * @returns True if the user has the role, false otherwise. Returns false if loading or no user.
   */
  const hasRole = (role: string): boolean => {
    if (isLoading || !user || !user.roles) {
      return false;
    }
    // Ensure case-insensitive comparison if needed, but Stytch roles are typically lowercase
    return user.roles.includes(role);
  };

  /**
   * Checks if the currently authenticated user has *any* of the specified roles.
   *
   * @param roles An array of role strings to check for.
   * @returns True if the user has at least one of the specified roles, false otherwise.
   */
  const hasAnyRole = (roles: string[]): boolean => {
     if (isLoading || !user || !user.roles) {
      return false;
    }
    // Assign to a constant after the check to help TypeScript narrow the type
    const currentRoles = user.roles;
    return roles.some(role => currentRoles.includes(role));
  }

  return {
    userRoles: user?.roles ?? [], // Return the roles array directly
    hasRole,
    hasAnyRole,
    isLoadingPermissions: isLoading, // Expose loading state
  };
}
