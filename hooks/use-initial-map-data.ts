import { useQuery } from '@tanstack/react-query';
import { useOrganization } from './use-organization-data';

/**
 * Custom hook to fetch initial map data from the combined endpoint
 * This reduces multiple API calls by fetching all required data in parallel
 */
export function useInitialMapData(organizationSlug: string) {
  // Get organization data from the organization hook
  const { data: organizationData } = useOrganization(organizationSlug);
  
  // Use React Query to fetch initial map data
  return useQuery({
    queryKey: ['initial-map-data', organizationSlug],
    queryFn: async () => {
      const res = await fetch(`/api/map/initial-data?organizationSlug=${organizationSlug}`);
      
      if (!res.ok) {
        const errorText = await res.text();
        throw new Error(`Failed to fetch initial map data: ${res.statusText} - ${errorText}`);
      }
      
      return res.json();
    },
    // Only run if we have an organization slug
    enabled: !!organizationSlug,
    // Cache for 1 minute (matching the server-side cache)
    staleTime: 60 * 1000,
    // Retry failed requests up to 2 times
    retry: 2,
    // Don't refetch on window focus to reduce unnecessary API calls
    refetchOnWindowFocus: false,
    // Use the organization data as initial data if available
    placeholderData: organizationData ? {
      organization: organizationData,
      locations: [],
      views: [],
      favorites: []
    } : undefined,
  });
}
