"use client"

import { useQuery } from "@tanstack/react-query"
import { useOrganization } from "./use-organization"
import type { Location } from "@/modules/location/model"

interface LocationsQueryParams {
  status?: string;
  type?: string;
  projectId?: string;
  search?: string;
  page?: number;
  pageSize?: number;
}

interface LocationsResponse {
  locations: Location[];
  total: number;
  page: number;
  pageSize: number;
  totalPages: number;
}

export function useLocations(params: LocationsQueryParams = {}) {
  const { organization } = useOrganization()
  const organizationId = organization?.id

  return useQuery<LocationsResponse>({
    queryKey: ['locations', organizationId, params],
    queryFn: async () => {
      // Build query string from params
      const queryParams = new URLSearchParams()
      if (params.status) queryParams.append('status', params.status)
      if (params.type) queryParams.append('type', params.type)
      if (params.projectId) queryParams.append('projectId', params.projectId)
      if (params.search) queryParams.append('search', params.search)
      if (params.page) queryParams.append('page', params.page.toString())
      if (params.pageSize) queryParams.append('pageSize', params.pageSize.toString())
      
      const queryString = queryParams.toString()
      const url = `/api/locations${queryString ? `?${queryString}` : ''}`
      
      const response = await fetch(url)
      if (!response.ok) {
        throw new Error('Failed to fetch locations')
      }
      
      const data = await response.json()
      
      // If the API doesn't return the expected structure, transform it
      if (!data.locations) {
        return {
          locations: data,
          total: data.length,
          page: params.page || 1,
          pageSize: params.pageSize || data.length,
          totalPages: 1
        }
      }
      
      return data
    },
    enabled: !!organizationId,
    staleTime: 60 * 1000, // 1 minute
  })
}
