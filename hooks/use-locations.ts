"use client"

import { useQuery } from "@tanstack/react-query"
import { useOrganization } from "./use-organization"
import type { Location } from "@/modules/location/model"

interface LocationsQueryParams {
  status?: string;
  type?: string;
  projectId?: string;
  search?: string;
  page?: number;
  pageSize?: number;
  useCommonFilter?: boolean; // Flag to use common filter endpoint
}

interface LocationsResponse {
  locations: Location[];
  total: number;
  page: number;
  pageSize: number;
  totalPages: number;
  filter?: string;
}

interface LocationMetadata {
  types: string[];
  statuses: string[];
  totalCount: number;
  countByStatus: Record<string, number>;
  countByType: Record<string, number>;
}

// Helper function to determine if we can use a common filter
function getCommonFilterName(params: LocationsQueryParams): string | null {
  // If search is provided, we can't use common filter
  if (params.search) return null;
  
  // If page is not 1, we can't use common filter
  if (params.page && params.page !== 1) return null;
  
  // If multiple filters are provided, we can't use common filter
  const filterCount = [params.status, params.type, params.projectId].filter(Boolean).length;
  if (filterCount > 1) return null;
  
  // Check for common filters
  if (params.status === "pending") return "pending";
  if (params.status === "approved") return "approved";
  if (params.status === "rejected") return "rejected";
  if (params.status === "secured") return "secured";
  if (params.type === "studio") return "studio";
  if (params.type === "outdoor") return "outdoor";
  if (params.type === "residential") return "residential";
  if (params.type === "commercial") return "commercial";
  
  // If no filters are provided, use the "all" filter
  if (filterCount === 0 && (!params.page || params.page === 1)) return "all";
  
  return null;
}

export function useLocations(params: LocationsQueryParams = {}) {
  const { organization } = useOrganization();
  const organizationId = organization?.id;
  const organizationSlug = organization?.slug;
  
  // Determine if we can use a common filter
  const commonFilter = params.useCommonFilter !== false ? getCommonFilterName(params) : null;

  // Fetch metadata (types, statuses, counts)
  const metadataQuery = useQuery<LocationMetadata>({
    queryKey: ['locations-metadata', organizationId],
    queryFn: async () => {
      const response = await fetch(`/api/locations/metadata`, {
        headers: {
          'X-Stytch-Org-Id': organizationSlug || '',
        }
      });
      
      if (!response.ok) {
        throw new Error('Failed to fetch location metadata');
      }
      
      return response.json();
    },
    enabled: !!organizationId && !!organizationSlug,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });

  // Fetch locations data
  const locationsQuery = useQuery<LocationsResponse>({
    queryKey: ['locations', organizationId, params, commonFilter],
    queryFn: async () => {
      let url: string;
      
      // If we can use a common filter, use the common filter endpoint
      if (commonFilter) {
        url = `/api/locations/common/${commonFilter}`;
      } else {
        // Otherwise, use the regular endpoint with query params
        const queryParams = new URLSearchParams();
        if (params.status) queryParams.append('status', params.status);
        if (params.type) queryParams.append('type', params.type);
        if (params.projectId) queryParams.append('projectId', params.projectId);
        if (params.search) queryParams.append('search', params.search);
        if (params.page) queryParams.append('page', params.page.toString());
        if (params.pageSize) queryParams.append('pageSize', params.pageSize.toString());
        
        const queryString = queryParams.toString();
        url = `/api/locations${queryString ? `?${queryString}` : ''}`;
      }
      
      const response = await fetch(url, {
        headers: {
          'X-Stytch-Org-Id': organizationSlug || '',
        }
      });
      
      if (!response.ok) {
        throw new Error('Failed to fetch locations');
      }
      
      const data = await response.json();
      
      // If the API doesn't return the expected structure, transform it
      if (!data.locations) {
        return {
          locations: data,
          total: data.length,
          page: params.page || 1,
          pageSize: params.pageSize || data.length,
          totalPages: 1
        };
      }
      
      return data;
    },
    enabled: !!organizationId && !!organizationSlug,
    staleTime: 60 * 1000, // 1 minute
  });

  return {
    ...locationsQuery,
    metadata: metadataQuery.data,
    isMetadataLoading: metadataQuery.isLoading,
    isMetadataError: metadataQuery.isError,
  };
}
