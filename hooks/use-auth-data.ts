import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';

// Query keys for better cache management
export const authKeys = {
  all: ['auth'] as const,
  user: () => [...authKeys.all, 'user'] as const,
  session: () => [...authKeys.all, 'session'] as const, // Keep session key if needed elsewhere
};

// Custom fetcher function (optional, but good practice)
const fetcher = async (url: string) => {
  const res = await fetch(url);
  if (!res.ok) {
    const errorInfo = await res.text();
    throw new Error(`Failed to fetch ${url}: ${res.statusText} - ${errorInfo}`);
  }
  return res.json();
};

export function useUser() {
  return useQuery({
    queryKey: authKeys.user(),
    queryFn: () => fetcher('/api/auth/me'), // Use fetcher
    // Don't refetch on window focus for user data
    refetchOnWindowFocus: false,
    // Keep cached for 10 minutes
    staleTime: 10 * 60 * 1000, 
    gcTime: 15 * 60 * 1000, // Garbage collect after 15 mins (slightly longer than staleTime)
    retry: 1, // Retry once on error
  });
}

export function useLogout() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: async () => {
      const res = await fetch('/api/auth/logout', { method: 'POST' });
      if (!res.ok) {
         const errorInfo = await res.text();
         throw new Error(`Failed to logout: ${res.statusText} - ${errorInfo}`);
      }
      // No need to return res.json() if the logout endpoint doesn't return a body
      return; 
    },
    onSuccess: () => {
      console.log("Logout successful, invalidating auth queries.");
      // Invalidate all queries under the 'auth' key on logout
      queryClient.invalidateQueries({ queryKey: authKeys.all });
      // Optionally, redirect user or clear other state here if needed
    },
    onError: (error) => {
       console.error("Logout mutation failed:", error);
       // Handle logout error (e.g., show notification)
    }
  });
}
