"use client"

import { useState, useEffect } from "react"
import { useDebounce } from "./use-debounce"
import { useOrganization } from "./use-organization"
import type { Location } from "@/modules/location/model"
import type { MapViewSettings, MapFilter } from "@/modules/map/model"
import { defaultMapViewSettings } from "@/modules/map/model"

export function useMapLocations(initialLocations: Location[] = []) {
  const { organization } = useOrganization()
  const [locations, setLocations] = useState<Location[]>(initialLocations)
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [filters, setFilters] = useState<MapFilter>({})
  const debouncedFilters = useDebounce(filters, 500)

  useEffect(() => {
    if (!organization) {
      setLocations(initialLocations)
      return
    }

    async function fetchLocations() {
      try {
        setIsLoading(true)

        // Build query parameters
        const params = new URLSearchParams()
        params.append("organizationId", organization.id)

        if (debouncedFilters.locationTypes?.length) {
          params.append("locationTypes", debouncedFilters.locationTypes.join(","))
        }

        if (debouncedFilters.projectIds?.length) {
          params.append("projectIds", debouncedFilters.projectIds.join(","))
        }

        if (debouncedFilters.searchQuery) {
          params.append("searchQuery", debouncedFilters.searchQuery)
        }

        const response = await fetch(`/api/map?${params.toString()}`)

        if (!response.ok) {
          throw new Error("Failed to fetch locations")
        }

        const data = await response.json()
        setLocations(data)
        setError(null)
      } catch (err) {
        console.error("Failed to fetch locations:", err)
        setError("Failed to load locations")
      } finally {
        setIsLoading(false)
      }
    }

    // Only fetch if we have filters or if initialLocations is empty
    if (Object.keys(debouncedFilters).length > 0 || initialLocations.length === 0) {
      fetchLocations()
    }
  }, [organization, debouncedFilters, initialLocations])

  return {
    locations,
    isLoading,
    error,
    filters,
    setFilters,
  }
}

export function useMapViewSettings() {
  const [viewSettings, setViewSettings] = useState<MapViewSettings>(defaultMapViewSettings)

  const saveViewSettings = async (settings: MapViewSettings) => {
    try {
      const response = await fetch("/api/map/settings", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(settings),
      })

      if (!response.ok) {
        throw new Error("Failed to save map settings")
      }

      return true
    } catch (error) {
      console.error("Error saving map settings:", error)
      return false
    }
  }

  return {
    viewSettings,
    setViewSettings,
    saveViewSettings,
  }
}
