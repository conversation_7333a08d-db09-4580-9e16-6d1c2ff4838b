import { 
  useQuery, 
  useInfiniteQuery, 
  useMutation, 
  useQueryClient,
  type InfiniteData // Import InfiniteData type
  // Removed unused QueryKey import
} from '@tanstack/react-query';

// TODO: Define proper types for API responses, filters, viewData
// type LocationType = { id: string; name: string; /* ... other properties */ };
// type MapViewType = { id: string; name: string; /* ... other properties */ };
// type PaginationType = { currentPage: number; totalPages: number; totalCount: number; };
// type MapLocationsResponseType = { locations: LocationType[]; pagination: PaginationType; };
// type MapViewsResponseType = { views: MapViewType[]; };
// type FilterType = { [key: string]: any }; 
// type ViewData = { id?: string; organizationSlug: string; /* ... other view properties */ };

// Query keys for map data
export const mapKeys = {
  all: ['map'] as const,
  locations: (orgSlug: string, filters?: any) => // TODO: Replace any with FilterType
    [...mapKeys.all, 'locations', orgSlug, ...(filters ? [JSON.stringify(filters)] : [])] as const,
  views: (orgSlug: string) => [...mapKeys.all, 'views', orgSlug] as const,
  view: (viewId: string) => [...mapKeys.all, 'view', viewId] as const,
};

// Hook for fetching map locations with infinite scrolling
export function useMapLocations(
  orgSlug: string | undefined | null, 
  filters?: any, // TODO: Replace any with FilterType
  pageSize = 50
) {
  // Define query key type explicitly if needed for generics
  // type MapLocationsQueryKey = ReturnType<typeof mapKeys.locations>;

  return useInfiniteQuery<
    any, // TODO: Replace any with MapLocationsResponseType
    Error, 
    InfiniteData<any>, // TODO: Replace any with MapLocationsResponseType
    // MapLocationsQueryKey, // Use explicit key type
    readonly (string | Record<string, any>)[], // Default key type if explicit one is not used
    number // Type for pageParam
  >({
    queryKey: mapKeys.locations(orgSlug ?? 'INVALID_SLUG', filters), 
    queryFn: async ({ pageParam }: { pageParam: number }) => { // Type pageParam
      const params = new URLSearchParams({
        page: pageParam.toString(),
        pageSize: pageSize.toString(),
      });
      if (filters) {
        for (const [key, value] of Object.entries(filters)) { 
          if (value !== undefined && value !== null) { 
            params.append(key, String(value)); 
          }
        }
      }
      
      if (!orgSlug) {
         // This should ideally not be reached if 'enabled' is false
         throw new Error("Organization slug is required to fetch map locations.");
      }
      params.append('organizationSlug', orgSlug); 
      
      const res = await fetch(`/api/map?${params}`);
      if (!res.ok) {
         const errorInfo = await res.text();
         throw new Error(`Failed to fetch map locations: ${res.statusText} - ${errorInfo}`);
      }
      return res.json();
    },
    initialPageParam: 1, // Must be inside the options object
    getNextPageParam: (lastPage) => { // Must be inside the options object
      const pagination = lastPage?.pagination; // TODO: Adjust based on actual API response structure
      if (!pagination) return undefined; 
      const { currentPage, totalPages } = pagination;
      return currentPage < totalPages ? currentPage + 1 : undefined;
    },
    staleTime: 2 * 60 * 1000, // Must be inside the options object
    gcTime: 5 * 60 * 1000, // Must be inside the options object
    enabled: !!orgSlug, // Must be inside the options object
  });
}

// Hook for fetching saved map views
export function useMapViews(orgSlug: string | undefined | null) {
  return useQuery<any, Error>({ // TODO: Replace any with MapViewsResponseType
    queryKey: mapKeys.views(orgSlug ?? 'INVALID_SLUG'), 
    queryFn: async () => {
       if (!orgSlug) {
          // Should not run if enabled is false, but good practice
          throw new Error("Organization slug is required to fetch map views.");
       }
       const res = await fetch(`/api/map/views?organizationSlug=${orgSlug}`); 
       if (!res.ok) {
          const errorInfo = await res.text();
          throw new Error(`Failed to fetch map views: ${res.statusText} - ${errorInfo}`);
       }
       return res.json();
    },
    staleTime: 5 * 60 * 1000, 
    gcTime: 10 * 60 * 1000, 
    enabled: !!orgSlug, 
  });
}

// Hook for saving (creating/updating) a map view
export function useSaveMapView() {
  const queryClient = useQueryClient();
  
  return useMutation<any, Error, any>({ // TODO: Replace any with specific types: SuccessResponse, Error, ViewData
    mutationFn: async (viewData) => { // TODO: Replace any with ViewData type
      const isUpdate = !!viewData.id; 
      const url = isUpdate ? `/api/map/views/${viewData.id}` : '/api/map/views';
      const method = isUpdate ? 'PUT' : 'POST';

      const res = await fetch(url, {
        method: method,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(viewData),
      });
      
      if (!res.ok) {
         const errorInfo = await res.text();
         throw new Error(`Failed to save map view: ${res.statusText} - ${errorInfo}`);
      }
      return res.json();
    },
    onSuccess: (data, variables) => { // variables type is ViewData
      if (variables.organizationSlug) {
         console.log("SaveMapView successful, invalidating views for:", variables.organizationSlug);
         queryClient.invalidateQueries({ 
           queryKey: mapKeys.views(variables.organizationSlug) 
         });
         if (variables.id) {
            // Update the cache for the specific view
            queryClient.setQueryData(mapKeys.view(variables.id), data); 
         }
      } else {
         console.warn("SaveMapView succeeded but no organizationSlug provided in variables to invalidate views query.");
      }
    },
    onError: (error) => {
       console.error("SaveMapView mutation failed:", error);
    }
  });
}

// Hook for deleting a map view
export function useDeleteMapView() {
  const queryClient = useQueryClient();

  // Define type for mutation variables
  type DeleteMapViewVariables = { viewId: string; organizationSlug: string };

  return useMutation<{ success: boolean }, Error, DeleteMapViewVariables>({ 
    mutationFn: async ({ viewId }) => { // Destructure viewId, organizationSlug is available in variables in onSuccess/onError
      const res = await fetch(`/api/map/views/${viewId}`, { method: 'DELETE' });

      if (!res.ok) {
        const errorInfo = await res.text();
        throw new Error(`Failed to delete map view: ${res.statusText} - ${errorInfo}`);
      }
      return { success: true }; 
    },
    onSuccess: (data, variables) => { // variables type is DeleteMapViewVariables
      // organizationSlug is now correctly typed in variables
      console.log("DeleteMapView successful, invalidating views for:", variables.organizationSlug);
      queryClient.invalidateQueries({ 
        queryKey: mapKeys.views(variables.organizationSlug) 
      });
      queryClient.removeQueries({ queryKey: mapKeys.view(variables.viewId) });
    },
     onError: (error) => {
       console.error("DeleteMapView mutation failed:", error);
    }
  });
}
