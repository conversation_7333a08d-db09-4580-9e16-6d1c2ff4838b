import { useQuery } from '@tanstack/react-query';

// Optional: Define a reusable fetcher if not already defined globally
const fetcher = async (url: string) => {
  const res = await fetch(url);
  if (!res.ok) {
    const errorInfo = await res.text();
    throw new Error(`Failed to fetch ${url}: ${res.statusText} - ${errorInfo}`);
  }
  return res.json();
};

// Query keys for organization data
export const organizationKeys = {
  all: ['organizations'] as const,
  lists: () => [...organizationKeys.all, 'list'] as const,
  detail: (slug: string) => [...organizationKeys.all, 'detail', slug] as const,
  members: (slug: string) => [...organizationKeys.all, 'members', slug] as const,
};

// Hook to fetch list of organizations (user belongs to)
export function useOrganizations() {
  return useQuery({
    queryKey: organizationKeys.lists(),
    queryFn: () => fetcher('/api/organizations'),
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
  });
}

// Hook to fetch details for a specific organization
export function useOrganization(slug: string | undefined | null) { // Allow undefined/null slug
  return useQuery({
    // Pass slug directly or a default value; enabled flag prevents fetch if slug is null/undefined
    queryKey: organizationKeys.detail(slug ?? 'INVALID_SLUG'), 
    queryFn: () => fetcher(`/api/organizations/${slug}`),
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
    enabled: !!slug, // Only run the query if slug is truthy
  });
}

// Hook to fetch members for a specific organization
export function useOrganizationMembers(slug: string | undefined | null) { // Allow undefined/null slug
  return useQuery({
    // Pass slug directly or a default value
    queryKey: organizationKeys.members(slug ?? 'INVALID_SLUG'), 
    queryFn: () => fetcher(`/api/organizations/${slug}/members`),
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
    enabled: !!slug, // Only run the query if slug is truthy
  });
}
