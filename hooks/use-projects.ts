"use client"

import { useState, useEffect } from "react"
import { useOrganization } from "./use-organization"

type Project = {
  id: string
  name: string
  description?: string
  status: string
}

export function useProjects() {
  const { organization } = useOrganization()
  const [projects, setProjects] = useState<Project[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    if (!organization) {
      setProjects([])
      setIsLoading(false)
      return
    }

    async function fetchProjects() {
      try {
        setIsLoading(true)

        // This would be replaced with an actual API call
        // const response = await fetch(`/api/organizations/${organization.id}/projects`);
        // const data = await response.json();

        // Mock data for now
        const mockProjects = [
          {
            id: "1",
            name: "Downtown Renovation",
            description: "Renovation of downtown office buildings",
            status: "in-progress",
          },
          {
            id: "2",
            name: "Warehouse Expansion",
            description: "Expansion of warehouse facilities",
            status: "planning",
          },
        ]

        setProjects(mockProjects)
        setError(null)
      } catch (err) {
        console.error("Failed to fetch projects:", err)
        setError("Failed to load projects")
      } finally {
        setIsLoading(false)
      }
    }

    fetchProjects()
  }, [organization])

  return { projects, isLoading, error }
}
