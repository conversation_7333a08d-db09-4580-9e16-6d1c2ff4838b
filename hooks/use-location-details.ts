"use client"

import { useQuery } from "@tanstack/react-query"
import type { Location } from "@/modules/location/model"

export function useLocationDetails(locationId: string | undefined | null) {
  return useQuery<Location>({
    queryKey: ['location', locationId],
    queryFn: async () => {
      if (!locationId) {
        throw new Error('Location ID is required')
      }
      
      const response = await fetch(`/api/locations/${locationId}`)
      if (!response.ok) {
        throw new Error('Failed to fetch location details')
      }
      
      return response.json()
    },
    enabled: !!locationId,
    staleTime: 60 * 1000, // 1 minute
  })
}
