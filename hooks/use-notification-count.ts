import { useState, useEffect, useCallback } from 'react';
import { useDebounce } from './use-debounce'; // Correct relative path

export function useNotificationCount() {
  const [count, setCount] = useState(0);
  const [loading, setLoading] = useState(true);
  const [lastUpdated, setLastUpdated] = useState<Date | null>(null);
  
  // State to trigger fetch, debounced below
  const [fetchTrigger, setFetchTrigger] = useState(0); 
  const debouncedFetchTrigger = useDebounce(fetchTrigger, 5000); // 5 second debounce

  const fetchCount = useCallback(async () => {
    console.log("useNotificationCount: Fetching count...");
    try {
      setLoading(true);
      const response = await fetch('/api/notifications/count');
      if (!response.ok) {
        throw new Error(`Failed to fetch notification count: ${response.statusText}`);
      }
      const data = await response.json();
      console.log("useNotificationCount: Count fetched:", data.count);
      setCount(data.count);
      setLastUpdated(new Date());
    } catch (error) {
      console.error('useNotificationCount: Error fetching notification count:', error);
      // Optionally set an error state here
    } finally {
      setLoading(false);
    }
  }, []); // No dependencies needed for fetch itself
  
  // Trigger fetch when debounced value changes
  useEffect(() => {
    // Fetch immediately on mount (debouncedFetchTrigger is initially 0)
    // and whenever the debounced trigger changes after mount.
    console.log("useNotificationCount: Debounced trigger changed, fetching count.");
    fetchCount();
  }, [debouncedFetchTrigger, fetchCount]); // Depend on debounced trigger and fetch function
  
  // Function to manually trigger a refresh by incrementing the trigger state
  const refreshCount = useCallback(() => {
    console.log("useNotificationCount: Refresh triggered.");
    setFetchTrigger(prev => prev + 1);
  }, []);
  
  return { count, loading, lastUpdated, refreshCount };
}
