// hooks/usePermissions.ts
import { useStytchMemberSession } from '@stytch/nextjs/b2b'; // Import the B2B hook

// Define an interface for the expected structure of the custom claim
interface SceneomaticRolesClaim {
  roles?: string[];
  // Add other properties if needed, e.g., custom_roles, user_id, organization_id
}

/**
 * Custom hook to retrieve the current member's roles from the Stytch B2B session.
 *
 * @returns {string[]} An array of role strings assigned to the member, or an empty array if no session or roles are found.
 */
export function usePermissions() {
  // Use the B2B hook to get the member session and initialization status
  const { session, isInitialized } = useStytchMemberSession();

  // Return empty array if session is not yet initialized
  if (!isInitialized) {
    return [];
  }

  // Access roles from the nested structure within custom_claims using type assertion
  const rolesClaim = session?.custom_claims?.["https://sceneomatic.io/roles"] as SceneomaticRolesClaim | undefined;
  const roles = rolesClaim?.roles ?? [];

  // Ensure roles is always an array of strings
  if (!Array.isArray(roles) || !roles.every(role => typeof role === 'string')) {
    console.warn('usePermissions: Roles found in session custom_claims["https://sceneomatic.io/roles"].roles are not a valid string array.', rolesClaim);
    return [];
  }

  return roles as string[];
}

/**
 * Helper function to check if the user has at least one of the required roles.
 * Can be used directly in components after getting roles from usePermissions.
 *
 * @param {string[]} userRoles - The roles the user currently has (from usePermissions).
 * @param {string | string[]} requiredRoles - The role or roles required for the action/view.
 * @returns {boolean} True if the user has at least one of the required roles, false otherwise.
 */
export function hasRequiredRole(userRoles: string[], requiredRoles: string | string[]): boolean {
  const required = Array.isArray(requiredRoles) ? requiredRoles : [requiredRoles];
  if (required.length === 0) {
    return true; // No specific role required
  }
  return userRoles.some(role => required.includes(role));
}
