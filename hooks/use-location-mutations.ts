"use client"

import { useMutation, useQueryClient } from "@tanstack/react-query"
import { useOrganization } from "./use-organization"
import { useAuth } from "./use-auth"
import { toast } from "sonner"
import type { Location } from "@/modules/location/model"

export function useLocationMutations() {
  const queryClient = useQueryClient()
  const { organization } = useOrganization()
  const { user } = useAuth()
  const organizationId = organization?.id
  const memberId = user?.member_id // Use the correct member_id property from Stytch Member object

  const createLocation = useMutation({
    mutationFn: async (locationData: Partial<Location>) => {
      const response = await fetch('/api/locations', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          // Add this header for RBAC checks
          'X-Stytch-Org-Id': organizationId || '',
          'X-Stytch-Member-Id': memberId || '',
        },
        body: JSON.stringify({
          ...locationData,
          organizationId,
        }),
      })
      
      if (!response.ok) {
        const errorData = await response.json().catch(() => null)
        throw new Error(errorData?.error || 'Failed to create location')
      }
      
      return response.json()
    },
    onSuccess: () => {
      // Invalidate locations query to refetch data
      queryClient.invalidateQueries({ queryKey: ['locations', organizationId] })
      toast.success('Location created successfully')
    },
    onError: (error: Error) => {
      toast.error(error.message)
    }
  })

  const updateLocation = useMutation({
    mutationFn: async ({ id, data }: { id: string; data: Partial<Location> }) => {
      // Only include organizationId if it's a valid UUID
      const updatedData = { ...data };
      
      // Don't add organizationId to the data - it should be handled by the API
      // The organizationId is already sent in the headers for RBAC checks
      
      const response = await fetch(`/api/locations/${id}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
          // Add these headers for RBAC checks
          'X-Stytch-Org-Id': organizationId || '',
          'X-Stytch-Member-Id': memberId || '',
        },
        body: JSON.stringify(updatedData),
      })
      
      if (!response.ok) {
        const errorData = await response.json().catch(() => null)
        throw new Error(errorData?.error || 'Failed to update location')
      }
      
      return response.json()
    },
    onSuccess: (_, variables) => {
      // Invalidate specific location query and locations list
      queryClient.invalidateQueries({ queryKey: ['location', variables.id] })
      queryClient.invalidateQueries({ queryKey: ['locations', organizationId] })
      toast.success('Location updated successfully')
    },
    onError: (error: Error) => {
      toast.error(error.message)
    }
  })

  const deleteLocation = useMutation({
    mutationFn: async (id: string) => {
      const response = await fetch(`/api/locations/${id}`, {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
          // Add these headers for RBAC checks
          'X-Stytch-Org-Id': organizationId || '',
          'X-Stytch-Member-Id': memberId || '',
        },
      })
      
      if (!response.ok) {
        const errorData = await response.json().catch(() => null)
        throw new Error(errorData?.error || 'Failed to delete location')
      }
      
      return true
    },
    onSuccess: (_, id) => {
      // Invalidate specific location query and locations list
      queryClient.invalidateQueries({ queryKey: ['location', id] })
      queryClient.invalidateQueries({ queryKey: ['locations', organizationId] })
      toast.success('Location deleted successfully')
    },
    onError: (error: Error) => {
      toast.error(error.message)
    }
  })

  const approveLocation = useMutation({
    mutationFn: async (id: string) => {
      const response = await fetch(`/api/locations/${id}/approve`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          // Add these headers for RBAC checks
          'X-Stytch-Org-Id': organizationId || '',
          'X-Stytch-Member-Id': memberId || '',
        },
      })
      
      if (!response.ok) {
        const errorData = await response.json().catch(() => null)
        throw new Error(errorData?.error || 'Failed to approve location')
      }
      
      return response.json()
    },
    onSuccess: (_, id) => {
      // Invalidate specific location query and locations list
      queryClient.invalidateQueries({ queryKey: ['location', id] })
      queryClient.invalidateQueries({ queryKey: ['locations', organizationId] })
      toast.success('Location approved successfully')
    },
    onError: (error: Error) => {
      toast.error(error.message)
    }
  })

  const rejectLocation = useMutation({
    mutationFn: async ({ id, reason }: { id: string; reason?: string }) => {
      const response = await fetch(`/api/locations/${id}/reject`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          // Add these headers for RBAC checks
          'X-Stytch-Org-Id': organizationId || '',
          'X-Stytch-Member-Id': memberId || '',
        },
        body: JSON.stringify({ reason }),
      })
      
      if (!response.ok) {
        const errorData = await response.json().catch(() => null)
        throw new Error(errorData?.error || 'Failed to reject location')
      }
      
      return response.json()
    },
    onSuccess: (_, variables) => {
      // Invalidate specific location query and locations list
      queryClient.invalidateQueries({ queryKey: ['location', variables.id] })
      queryClient.invalidateQueries({ queryKey: ['locations', organizationId] })
      toast.success('Location rejected successfully')
    },
    onError: (error: Error) => {
      toast.error(error.message)
    }
  })

  return {
    createLocation,
    updateLocation,
    deleteLocation,
    approveLocation,
    rejectLocation,
  }
}
