import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';

// TODO: Define proper types for API responses
// type NotificationType = { id: string; message: string; /* ... */ };
// type NotificationPreferenceType = { id: string; enabled: boolean; /* ... */ };
// type NotificationsResponseType = { notifications: NotificationType[]; pagination?: any };
// type NotificationCountResponseType = { count: number; };
// type NotificationPreferencesResponseType = { preferences: NotificationPreferenceType[]; };

// Optional: Define a reusable fetcher if not already defined globally
const fetcher = async (url: string) => {
  const res = await fetch(url);
  if (!res.ok) {
    const errorInfo = await res.text();
    throw new Error(`Failed to fetch ${url}: ${res.statusText} - ${errorInfo}`);
  }
  return res.json();
};

// Query keys for notification data
export const notificationKeys = {
  all: ['notifications'] as const,
  lists: () => [...notificationKeys.all, 'list'] as const, // Key for paginated lists
  list: (page?: number) => // Key for a specific page
    [...notificationKeys.lists(), ...(page ? [page.toString()] : [])] as const,
  count: () => [...notificationKeys.all, 'count'] as const,
  preferences: () => [...notificationKeys.all, 'preferences'] as const,
};

// Hook to fetch notifications (paginated example)
export function useNotificationsList(page = 1, pageSize = 10) { // Renamed to avoid conflict if non-paginated exists
  return useQuery<any, Error>({ // TODO: Replace any with NotificationsResponseType
    queryKey: notificationKeys.list(page),
    queryFn: () => fetcher(`/api/notifications?page=${page}&pageSize=${pageSize}`),
    staleTime: 1 * 60 * 1000, // 1 minute
    gcTime: 5 * 60 * 1000, // 5 minutes
  });
}

// Hook to fetch notification count
export function useNotificationCountQuery() { // Renamed to avoid conflict with previous hook
  return useQuery<any, Error>({ // TODO: Replace any with NotificationCountResponseType
    queryKey: notificationKeys.count(),
    queryFn: () => fetcher('/api/notifications/count'),
    // Shorter stale time for count as it changes frequently
    staleTime: 30 * 1000, // 30 seconds
    gcTime: 5 * 60 * 1000, // 5 minutes
    refetchInterval: 2 * 60 * 1000, // Refetch every 2 minutes
  });
}

// Hook to fetch notification preferences
export function useNotificationPreferencesQuery() { // Renamed to avoid conflict
  return useQuery<any, Error>({ // TODO: Replace any with NotificationPreferencesResponseType
    queryKey: notificationKeys.preferences(),
    queryFn: () => fetcher('/api/notifications/preferences'),
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
  });
}

// Hook to mark a single notification as read
export function useMarkNotificationAsRead() {
  const queryClient = useQueryClient();
  
  return useMutation<any, Error, string>({ // SuccessResponse, Error, notificationId
    mutationFn: async (notificationId) => {
      // API endpoint might be PUT or POST based on design
      const res = await fetch(`/api/notifications/${notificationId}/read`, { 
        method: 'PUT', // Assuming PUT based on previous provider code
      });
      
      if (!res.ok) {
         const errorInfo = await res.text();
         throw new Error(`Failed to mark notification as read: ${res.statusText} - ${errorInfo}`);
      }
      return res.json(); // Or return success: true if no body
    },
    onSuccess: (data, notificationId) => {
      console.log(`MarkAsRead successful for ${notificationId}, invalidating lists and count.`);
      // Invalidate all notification lists and the count
      queryClient.invalidateQueries({ queryKey: notificationKeys.lists() });
      queryClient.invalidateQueries({ queryKey: notificationKeys.count() });
      // Optionally update the specific notification in the cache if needed
    },
     onError: (error, notificationId) => {
       console.error(`MarkAsRead mutation failed for ${notificationId}:`, error);
    }
  });
}

// Hook to mark all notifications as read
export function useMarkAllNotificationsAsRead() {
  const queryClient = useQueryClient();
  
  return useMutation<any, Error, void>({ // SuccessResponse, Error, void (no variables needed)
    mutationFn: async () => {
      // API endpoint might be PUT or POST
      const res = await fetch('/api/notifications/read-all', { 
        method: 'PUT', // Assuming PUT based on previous provider code
      });
      
      if (!res.ok) {
         const errorInfo = await res.text();
         throw new Error(`Failed to mark all notifications as read: ${res.statusText} - ${errorInfo}`);
      }
      return res.json(); // Or return success: true
    },
    onSuccess: () => {
      console.log("MarkAllAsRead successful, invalidating lists and count.");
      // Invalidate all notification lists
      queryClient.invalidateQueries({ queryKey: notificationKeys.lists() });
      // Optimistically update count query data to zero
      queryClient.setQueryData(notificationKeys.count(), { count: 0 }); // TODO: Ensure structure matches API response
    },
     onError: (error) => {
       console.error("MarkAllAsRead mutation failed:", error);
    }
  });
}

// Hook to update notification preferences (bulk update example)
export function useUpdateNotificationPreferences() {
  const queryClient = useQueryClient();

  // Define type for mutation variables
  // TODO: Define PreferenceUpdateItem type properly
  type UpdatePreferencesVariables = { preferences: { notificationType: string; inApp: boolean; email: boolean }[] }; 

  return useMutation<any, Error, UpdatePreferencesVariables>({ // SuccessResponse, Error, VariablesType
    mutationFn: async (variables) => {
      const res = await fetch('/api/notifications/preferences', {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(variables), // Send the whole object { preferences: [...] }
      });

      if (!res.ok) {
        const errorInfo = await res.text();
        throw new Error(`Failed to update preferences: ${res.statusText} - ${errorInfo}`);
      }
      return res.json();
    },
    // Add underscore prefix to unused parameters
    onSuccess: (_data, _variables) => { 
      console.log("UpdatePreferences successful, invalidating preferences query.");
      // Invalidate the preferences query to refetch
      queryClient.invalidateQueries({ queryKey: notificationKeys.preferences() });
      // Optionally, update the cache directly with the response data
      // queryClient.setQueryData(notificationKeys.preferences(), data); 
    },
    onError: (error) => {
       console.error("UpdatePreferences mutation failed:", error);
    }
  });
}
