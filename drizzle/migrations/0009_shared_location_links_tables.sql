CREATE TABLE IF NOT EXISTS "shared_location_links" (
  "id" UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  "organization_id" UUID NOT NULL,
  "token" TEXT NOT NULL UNIQUE,
  "location_id" UUID NOT NULL REFERENCES "locations"("id"),
  "view_mode" TEXT NOT NULL DEFAULT 'client',
  "password_hash" TEXT,
  "expires_at" TIMESTAMP,
  "created_by" UUID NOT NULL REFERENCES "users"("id"),
  "created_at" TIMESTAMP NOT NULL DEFAULT now(),
  "updated_at" TIMESTAMP NOT NULL DEFAULT now()
);

CREATE TABLE IF NOT EXISTS "shared_location_link_access_logs" (
  "id" UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  "shared_location_link_id" UUID NOT NULL REFERENCES "shared_location_links"("id") ON DELETE CASCADE,
  "ip_address" TEXT,
  "user_agent" TEXT,
  "accessed_at" TIMESTAMP NOT NULL DEFAULT now()
);

CREATE INDEX IF NOT EXISTS "shared_location_links_organization_id_idx" ON "shared_location_links" ("organization_id");
CREATE INDEX IF NOT EXISTS "shared_location_links_location_id_idx" ON "shared_location_links" ("location_id");
CREATE INDEX IF NOT EXISTS "shared_location_links_token_idx" ON "shared_location_links" ("token");
CREATE INDEX IF NOT EXISTS "shared_location_link_access_logs_shared_location_link_id_idx" ON "shared_location_link_access_logs" ("shared_location_link_id");
