-- Create map_views table
CREATE TABLE map_views (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  organization_id UUID NOT NULL REFERENCES organizations(id),
  name VARCHAR(255) NOT NULL,
  description TEXT,
  filters JSONB,
  view_settings JSONB,
  created_by UUID NOT NULL REFERENCES users(id),
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
  deleted_at TIMESTAMP WITH TIME ZONE
);

-- <PERSON>reate map_view_locations junction table
CREATE TABLE map_view_locations (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  map_view_id UUID NOT NULL REFERENCES map_views(id),
  location_id UUID NOT NULL REFERENCES locations(id),
  UNIQUE(map_view_id, location_id)
);

-- Add indexes for better performance
CREATE INDEX idx_map_views_organization_id ON map_views(organization_id);
CREATE INDEX idx_map_views_created_by ON map_views(created_by);
CREATE INDEX idx_map_view_locations_map_view_id ON map_view_locations(map_view_id);
CREATE INDEX idx_map_view_locations_location_id ON map_view_locations(location_id);
