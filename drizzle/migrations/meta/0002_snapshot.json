{"version": "5", "dialect": "pg", "id": "0002_notification_tables", "prevId": "0001_polar_integration", "tables": {"notifications": {"name": "notifications", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "type": {"name": "type", "type": "notification_type", "primaryKey": false, "notNull": true}, "title": {"name": "title", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "message": {"name": "message", "type": "text", "primaryKey": false, "notNull": true}, "status": {"name": "status", "type": "notification_status", "primaryKey": false, "notNull": true, "default": "'unread'"}, "priority": {"name": "priority", "type": "notification_priority", "primaryKey": false, "notNull": true, "default": "'medium'"}, "recipient_id": {"name": "recipient_id", "type": "uuid", "primaryKey": false, "notNull": true}, "organization_id": {"name": "organization_id", "type": "uuid", "primaryKey": false, "notNull": true}, "project_id": {"name": "project_id", "type": "uuid", "primaryKey": false, "notNull": false}, "location_id": {"name": "location_id", "type": "uuid", "primaryKey": false, "notNull": false}, "document_id": {"name": "document_id", "type": "uuid", "primaryKey": false, "notNull": false}, "task_id": {"name": "task_id", "type": "uuid", "primaryKey": false, "notNull": false}, "actor_id": {"name": "actor_id", "type": "uuid", "primaryKey": false, "notNull": false}, "metadata": {"name": "metadata", "type": "jsonb", "primaryKey": false, "notNull": false, "default": "'{}'::jsonb"}, "is_read": {"name": "is_read", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "read_at": {"name": "read_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "expires_at": {"name": "expires_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"notifications_recipient_id_users_id_fk": {"name": "notifications_recipient_id_users_id_fk", "tableFrom": "notifications", "tableTo": "users", "columnsFrom": ["recipient_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "notifications_organization_id_organizations_id_fk": {"name": "notifications_organization_id_organizations_id_fk", "tableFrom": "notifications", "tableTo": "organizations", "columnsFrom": ["organization_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "notifications_project_id_projects_id_fk": {"name": "notifications_project_id_projects_id_fk", "tableFrom": "notifications", "tableTo": "projects", "columnsFrom": ["project_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}, "notifications_location_id_locations_id_fk": {"name": "notifications_location_id_locations_id_fk", "tableFrom": "notifications", "tableTo": "locations", "columnsFrom": ["location_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}, "notifications_document_id_documents_id_fk": {"name": "notifications_document_id_documents_id_fk", "tableFrom": "notifications", "tableTo": "documents", "columnsFrom": ["document_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}, "notifications_task_id_tasks_id_fk": {"name": "notifications_task_id_tasks_id_fk", "tableFrom": "notifications", "tableTo": "tasks", "columnsFrom": ["task_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}, "notifications_actor_id_users_id_fk": {"name": "notifications_actor_id_users_id_fk", "tableFrom": "notifications", "tableTo": "users", "columnsFrom": ["actor_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "notification_preferences": {"name": "notification_preferences", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "user_id": {"name": "user_id", "type": "uuid", "primaryKey": false, "notNull": true}, "organization_id": {"name": "organization_id", "type": "uuid", "primaryKey": false, "notNull": true}, "notification_type": {"name": "notification_type", "type": "notification_type", "primaryKey": false, "notNull": true}, "in_app": {"name": "in_app", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "email": {"name": "email", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"notification_preferences_user_id_users_id_fk": {"name": "notification_preferences_user_id_users_id_fk", "tableFrom": "notification_preferences", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "notification_preferences_organization_id_organizations_id_fk": {"name": "notification_preferences_organization_id_organizations_id_fk", "tableFrom": "notification_preferences", "tableTo": "organizations", "columnsFrom": ["organization_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}}, "enums": {"notification_type": {"name": "notification_type", "values": {"project_created": "project_created", "project_updated": "project_updated", "project_deleted": "project_deleted", "location_created": "location_created", "location_updated": "location_updated", "location_deleted": "location_deleted", "location_approved": "location_approved", "document_uploaded": "document_uploaded", "document_updated": "document_updated", "document_deleted": "document_deleted", "task_created": "task_created", "task_updated": "task_updated", "task_completed": "task_completed", "task_assigned": "task_assigned", "member_added": "member_added", "member_removed": "member_removed", "member_role_changed": "member_role_changed", "subscription_created": "subscription_created", "subscription_updated": "subscription_updated", "subscription_canceled": "subscription_canceled", "payment_succeeded": "payment_succeeded", "payment_failed": "payment_failed", "comment_added": "comment_added", "system_notification": "system_notification"}}, "notification_status": {"name": "notification_status", "values": {"unread": "unread", "read": "read", "archived": "archived"}}, "notification_priority": {"name": "notification_priority", "values": {"low": "low", "medium": "medium", "high": "high", "urgent": "urgent"}}}, "schemas": {}, "_meta": {"schemas": {}, "tables": {}, "columns": {}}}