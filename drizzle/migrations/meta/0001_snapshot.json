{"id": "0001_polar_integration", "prevId": "966cc822-d225-42f9-a6a8-b888c541cadf", "version": "5", "dialect": "pg", "tables": {"organizations": {"name": "organizations", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "slug": {"name": "slug", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true, "unique": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "logo": {"name": "logo", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "stytch_organization_id": {"name": "stytch_organization_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false, "unique": true}, "polar_customer_id": {"name": "polar_customer_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false, "unique": true}, "subscription_tier": {"name": "subscription_tier", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false, "default": "'basic'"}, "subscription_status": {"name": "subscription_status", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false}, "subscription_valid_until": {"name": "subscription_valid_until", "type": "timestamp", "primaryKey": false, "notNull": false}, "max_projects": {"name": "max_projects", "type": "integer", "primaryKey": false, "notNull": false, "default": 5}, "max_locations_per_project": {"name": "max_locations_per_project", "type": "integer", "primaryKey": false, "notNull": false, "default": 50}, "max_team_members": {"name": "max_team_members", "type": "integer", "primaryKey": false, "notNull": false, "default": 5}, "max_storage": {"name": "max_storage", "type": "integer", "primaryKey": false, "notNull": false, "default": 10}, "metadata": {"name": "metadata", "type": "jsonb", "primaryKey": false, "notNull": false}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": false, "default": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "deleted_at": {"name": "deleted_at", "type": "timestamp", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "subscriptions": {"name": "subscriptions", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "organization_id": {"name": "organization_id", "type": "uuid", "primaryKey": false, "notNull": true}, "polar_subscription_id": {"name": "polar_subscription_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true, "unique": true}, "tier": {"name": "tier", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true}, "status": {"name": "status", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true}, "current_period_start": {"name": "current_period_start", "type": "timestamp", "primaryKey": false, "notNull": true}, "current_period_end": {"name": "current_period_end", "type": "timestamp", "primaryKey": false, "notNull": true}, "cancel_at_period_end": {"name": "cancel_at_period_end", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "metadata": {"name": "metadata", "type": "jsonb", "primaryKey": false, "notNull": false}, "quantity": {"name": "quantity", "type": "integer", "primaryKey": false, "notNull": false, "default": 1}}, "indexes": {}, "foreignKeys": {"subscriptions_organization_id_organizations_id_fk": {"name": "subscriptions_organization_id_organizations_id_fk", "tableFrom": "subscriptions", "tableTo": "organizations", "columnsFrom": ["organization_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "subscription_invoices": {"name": "subscription_invoices", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "organization_id": {"name": "organization_id", "type": "uuid", "primaryKey": false, "notNull": true}, "subscription_id": {"name": "subscription_id", "type": "uuid", "primaryKey": false, "notNull": false}, "polar_invoice_id": {"name": "polar_invoice_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true, "unique": true}, "amount": {"name": "amount", "type": "integer", "primaryKey": false, "notNull": true}, "currency": {"name": "currency", "type": "<PERSON><PERSON><PERSON>(10)", "primaryKey": false, "notNull": false, "default": "'usd'"}, "status": {"name": "status", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true}, "invoice_url": {"name": "invoice_url", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "invoice_number": {"name": "invoice_number", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false}, "invoice_date": {"name": "invoice_date", "type": "timestamp", "primaryKey": false, "notNull": true}, "paid_at": {"name": "paid_at", "type": "timestamp", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"subscription_invoices_organization_id_organizations_id_fk": {"name": "subscription_invoices_organization_id_organizations_id_fk", "tableFrom": "subscription_invoices", "tableTo": "organizations", "columnsFrom": ["organization_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "subscription_invoices_subscription_id_subscriptions_id_fk": {"name": "subscription_invoices_subscription_id_subscriptions_id_fk", "tableFrom": "subscription_invoices", "tableTo": "subscriptions", "columnsFrom": ["subscription_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}}, "enums": {}, "schemas": {}, "_meta": {"columns": {}, "schemas": {}, "tables": {}}}