{"id": "966cc822-d225-42f9-a6a8-b888c541cadf", "prevId": "00000000-0000-0000-0000-000000000000", "version": "5", "dialect": "pg", "tables": {"organization_user_roles": {"name": "organization_user_roles", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "organization_id": {"name": "organization_id", "type": "uuid", "primaryKey": false, "notNull": true}, "user_id": {"name": "user_id", "type": "uuid", "primaryKey": false, "notNull": true}, "role": {"name": "role", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"organization_user_roles_organization_id_organizations_id_fk": {"name": "organization_user_roles_organization_id_organizations_id_fk", "tableFrom": "organization_user_roles", "tableTo": "organizations", "columnsFrom": ["organization_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "organization_user_roles_user_id_users_id_fk": {"name": "organization_user_roles_user_id_users_id_fk", "tableFrom": "organization_user_roles", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "organizations": {"name": "organizations", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "slug": {"name": "slug", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "logo": {"name": "logo", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "stytch_organization_id": {"name": "stytch_organization_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "metadata": {"name": "metadata", "type": "jsonb", "primaryKey": false, "notNull": false}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": false, "default": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "deleted_at": {"name": "deleted_at", "type": "timestamp", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"organizations_slug_unique": {"name": "organizations_slug_unique", "nullsNotDistinct": false, "columns": ["slug"]}, "organizations_stytch_organization_id_unique": {"name": "organizations_stytch_organization_id_unique", "nullsNotDistinct": false, "columns": ["stytch_organization_id"]}}}, "project_user_roles": {"name": "project_user_roles", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "project_id": {"name": "project_id", "type": "uuid", "primaryKey": false, "notNull": true}, "user_id": {"name": "user_id", "type": "uuid", "primaryKey": false, "notNull": true}, "role": {"name": "role", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"project_user_roles_project_id_projects_id_fk": {"name": "project_user_roles_project_id_projects_id_fk", "tableFrom": "project_user_roles", "tableTo": "projects", "columnsFrom": ["project_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "project_user_roles_user_id_users_id_fk": {"name": "project_user_roles_user_id_users_id_fk", "tableFrom": "project_user_roles", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "projects": {"name": "projects", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "status": {"name": "status", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true}, "organization_id": {"name": "organization_id", "type": "uuid", "primaryKey": false, "notNull": true}, "created_by": {"name": "created_by", "type": "uuid", "primaryKey": false, "notNull": false}, "start_date": {"name": "start_date", "type": "timestamp", "primaryKey": false, "notNull": false}, "end_date": {"name": "end_date", "type": "timestamp", "primaryKey": false, "notNull": false}, "budget": {"name": "budget", "type": "jsonb", "primaryKey": false, "notNull": false}, "client_name": {"name": "client_name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "client_contact": {"name": "client_contact", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "production_type": {"name": "production_type", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false}, "metadata": {"name": "metadata", "type": "jsonb", "primaryKey": false, "notNull": false}, "is_archived": {"name": "is_archived", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "deleted_at": {"name": "deleted_at", "type": "timestamp", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"projects_organization_id_organizations_id_fk": {"name": "projects_organization_id_organizations_id_fk", "tableFrom": "projects", "tableTo": "organizations", "columnsFrom": ["organization_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "projects_created_by_users_id_fk": {"name": "projects_created_by_users_id_fk", "tableFrom": "projects", "tableTo": "users", "columnsFrom": ["created_by"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "locations": {"name": "locations", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "address": {"name": "address", "type": "jsonb", "primaryKey": false, "notNull": true}, "coordinates": {"name": "coordinates", "type": "jsonb", "primaryKey": false, "notNull": true}, "type": {"name": "type", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true}, "project_id": {"name": "project_id", "type": "uuid", "primaryKey": false, "notNull": true}, "organization_id": {"name": "organization_id", "type": "uuid", "primaryKey": false, "notNull": true}, "metadata": {"name": "metadata", "type": "jsonb", "primaryKey": false, "notNull": false}, "area": {"name": "area", "type": "numeric(10, 2)", "primaryKey": false, "notNull": false}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": false, "default": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "deleted_at": {"name": "deleted_at", "type": "timestamp", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"locations_project_id_projects_id_fk": {"name": "locations_project_id_projects_id_fk", "tableFrom": "locations", "tableTo": "projects", "columnsFrom": ["project_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "locations_organization_id_organizations_id_fk": {"name": "locations_organization_id_organizations_id_fk", "tableFrom": "locations", "tableTo": "organizations", "columnsFrom": ["organization_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "documents": {"name": "documents", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "file_url": {"name": "file_url", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "file_type": {"name": "file_type", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true}, "file_size": {"name": "file_size", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false}, "project_id": {"name": "project_id", "type": "uuid", "primaryKey": false, "notNull": false}, "location_id": {"name": "location_id", "type": "uuid", "primaryKey": false, "notNull": false}, "organization_id": {"name": "organization_id", "type": "uuid", "primaryKey": false, "notNull": true}, "uploaded_by_id": {"name": "uploaded_by_id", "type": "uuid", "primaryKey": false, "notNull": false}, "metadata": {"name": "metadata", "type": "jsonb", "primaryKey": false, "notNull": false}, "is_archived": {"name": "is_archived", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "deleted_at": {"name": "deleted_at", "type": "timestamp", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"documents_project_id_projects_id_fk": {"name": "documents_project_id_projects_id_fk", "tableFrom": "documents", "tableTo": "projects", "columnsFrom": ["project_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "documents_location_id_locations_id_fk": {"name": "documents_location_id_locations_id_fk", "tableFrom": "documents", "tableTo": "locations", "columnsFrom": ["location_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "documents_organization_id_organizations_id_fk": {"name": "documents_organization_id_organizations_id_fk", "tableFrom": "documents", "tableTo": "organizations", "columnsFrom": ["organization_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "documents_uploaded_by_id_users_id_fk": {"name": "documents_uploaded_by_id_users_id_fk", "tableFrom": "documents", "tableTo": "users", "columnsFrom": ["uploaded_by_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "tasks": {"name": "tasks", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "title": {"name": "title", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "status": {"name": "status", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true}, "priority": {"name": "priority", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true}, "due_date": {"name": "due_date", "type": "date", "primaryKey": false, "notNull": false}, "project_id": {"name": "project_id", "type": "uuid", "primaryKey": false, "notNull": false}, "location_id": {"name": "location_id", "type": "uuid", "primaryKey": false, "notNull": false}, "organization_id": {"name": "organization_id", "type": "uuid", "primaryKey": false, "notNull": true}, "assigned_to_id": {"name": "assigned_to_id", "type": "uuid", "primaryKey": false, "notNull": false}, "created_by_id": {"name": "created_by_id", "type": "uuid", "primaryKey": false, "notNull": false}, "metadata": {"name": "metadata", "type": "jsonb", "primaryKey": false, "notNull": false}, "is_completed": {"name": "is_completed", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "completed_at": {"name": "completed_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "deleted_at": {"name": "deleted_at", "type": "timestamp", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"tasks_project_id_projects_id_fk": {"name": "tasks_project_id_projects_id_fk", "tableFrom": "tasks", "tableTo": "projects", "columnsFrom": ["project_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "tasks_location_id_locations_id_fk": {"name": "tasks_location_id_locations_id_fk", "tableFrom": "tasks", "tableTo": "locations", "columnsFrom": ["location_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "tasks_organization_id_organizations_id_fk": {"name": "tasks_organization_id_organizations_id_fk", "tableFrom": "tasks", "tableTo": "organizations", "columnsFrom": ["organization_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "tasks_assigned_to_id_users_id_fk": {"name": "tasks_assigned_to_id_users_id_fk", "tableFrom": "tasks", "tableTo": "users", "columnsFrom": ["assigned_to_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "tasks_created_by_id_users_id_fk": {"name": "tasks_created_by_id_users_id_fk", "tableFrom": "tasks", "tableTo": "users", "columnsFrom": ["created_by_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "users": {"name": "users", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "email": {"name": "email", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "avatar": {"name": "avatar", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "stytch_user_id": {"name": "stytch_user_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "metadata": {"name": "metadata", "type": "jsonb", "primaryKey": false, "notNull": false}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": false, "default": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "deleted_at": {"name": "deleted_at", "type": "timestamp", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"users_email_unique": {"name": "users_email_unique", "nullsNotDistinct": false, "columns": ["email"]}, "users_stytch_user_id_unique": {"name": "users_stytch_user_id_unique", "nullsNotDistinct": false, "columns": ["stytch_user_id"]}}}}, "enums": {}, "schemas": {}, "_meta": {"columns": {}, "schemas": {}, "tables": {}}}