-- Add created_by column to locations table if it doesn't exist
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1
        FROM information_schema.columns
        WHERE table_name = 'locations'
        AND column_name = 'created_by'
    ) THEN
        ALTER TABLE "locations" ADD COLUMN "created_by" UUID REFERENCES "users"("id");
    END IF;
END $$;

-- Update existing records to have a default user if needed
-- This assumes there's at least one user in the system
-- If there's no user, this will be a no-op
UPDATE "locations"
SET "created_by" = (SELECT "id" FROM "users" ORDER BY "created_at" LIMIT 1)
WHERE "created_by" IS NULL;

-- Add created_by column to shared_location_links table if it doesn't exist
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1
        FROM information_schema.columns
        WHERE table_name = 'shared_location_links'
        AND column_name = 'created_by'
    ) THEN
        ALTER TABLE "shared_location_links" ADD COLUMN "created_by" UUID REFERENCES "users"("id");
    END IF;
END $$;

-- Update existing records to have a default user if needed
UPDATE "shared_location_links"
SET "created_by" = (SELECT "id" FROM "users" ORDER BY "created_at" LIMIT 1)
WHERE "created_by" IS NULL;
