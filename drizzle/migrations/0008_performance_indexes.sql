-- Add indexes for frequently queried fields to improve query performance

-- Map view indexes
CREATE INDEX IF NOT EXISTS idx_map_views_organization_id ON map_views (organization_id);
CREATE INDEX IF NOT EXISTS idx_map_views_created_by ON map_views (created_by);
CREATE INDEX IF NOT EXISTS idx_map_views_deleted_at ON map_views (deleted_at) WHERE deleted_at IS NULL;
CREATE INDEX IF NOT EXISTS idx_map_views_org_deleted ON map_views (organization_id, deleted_at) WHERE deleted_at IS NULL;

-- Map view locations indexes
CREATE INDEX IF NOT EXISTS idx_map_view_locations_map_view_id ON map_view_locations (map_view_id);
CREATE INDEX IF NOT EXISTS idx_map_view_locations_location_id ON map_view_locations (location_id);

-- Shared map links indexes
CREATE INDEX IF NOT EXISTS idx_shared_map_links_token ON shared_map_links (token);
CREATE INDEX IF NOT EXISTS idx_shared_map_links_organization_id ON shared_map_links (organization_id);
CREATE INDEX IF NOT EXISTS idx_shared_map_links_expires_at ON shared_map_links (expires_at);

-- Location indexes
CREATE INDEX IF NOT EXISTS idx_locations_organization_id ON locations (organization_id);
CREATE INDEX IF NOT EXISTS idx_locations_project_id ON locations (project_id);
CREATE INDEX IF NOT EXISTS idx_locations_deleted_at ON locations (deleted_at) WHERE deleted_at IS NULL;
CREATE INDEX IF NOT EXISTS idx_locations_status ON locations (status);
CREATE INDEX IF NOT EXISTS idx_locations_type ON locations (type);
CREATE INDEX IF NOT EXISTS idx_locations_org_deleted ON locations (organization_id, deleted_at) WHERE deleted_at IS NULL;
CREATE INDEX IF NOT EXISTS idx_locations_org_project ON locations (organization_id, project_id);

-- Create GIN index for JSONB coordinates queries
CREATE INDEX IF NOT EXISTS idx_locations_coordinates_gin ON locations USING GIN (coordinates);

-- Notification indexes
CREATE INDEX IF NOT EXISTS idx_notifications_recipient_id ON notifications (recipient_id);
CREATE INDEX IF NOT EXISTS idx_notifications_organization_id ON notifications (organization_id);
CREATE INDEX IF NOT EXISTS idx_notifications_status ON notifications (status);
CREATE INDEX IF NOT EXISTS idx_notifications_type ON notifications (type);
CREATE INDEX IF NOT EXISTS idx_notifications_created_at ON notifications (created_at DESC);
CREATE INDEX IF NOT EXISTS idx_notifications_recipient_org ON notifications (recipient_id, organization_id);
CREATE INDEX IF NOT EXISTS idx_notifications_recipient_org_status ON notifications (recipient_id, organization_id, status);

-- Notification preferences indexes
CREATE INDEX IF NOT EXISTS idx_notification_preferences_user_id ON notification_preferences (user_id);
CREATE INDEX IF NOT EXISTS idx_notification_preferences_organization_id ON notification_preferences (organization_id);
CREATE INDEX IF NOT EXISTS idx_notification_preferences_notification_type ON notification_preferences (notification_type);
CREATE INDEX IF NOT EXISTS idx_notification_preferences_user_org ON notification_preferences (user_id, organization_id);
CREATE INDEX IF NOT EXISTS idx_notification_preferences_user_org_type ON notification_preferences (user_id, organization_id, notification_type);
