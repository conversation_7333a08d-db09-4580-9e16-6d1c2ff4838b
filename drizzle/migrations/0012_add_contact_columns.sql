-- Add contact_name column to locations table if it doesn't exist
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1
        FROM information_schema.columns
        WHERE table_name = 'locations'
        AND column_name = 'contact_name'
    ) THEN
        ALTER TABLE "locations" ADD COLUMN "contact_name" TEXT;
    END IF;
END $$;

-- Add contact_email column to locations table if it doesn't exist
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1
        FROM information_schema.columns
        WHERE table_name = 'locations'
        AND column_name = 'contact_email'
    ) THEN
        ALTER TABLE "locations" ADD COLUMN "contact_email" TEXT;
    END IF;
END $$;

-- Add contact_phone column to locations table if it doesn't exist
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1
        FROM information_schema.columns
        WHERE table_name = 'locations'
        AND column_name = 'contact_phone'
    ) THEN
        ALTER TABLE "locations" ADD COLUMN "contact_phone" TEXT;
    END IF;
END $$;

-- Add location_size column to locations table if it doesn't exist
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1
        FROM information_schema.columns
        WHERE table_name = 'locations'
        AND column_name = 'location_size'
    ) THEN
        ALTER TABLE "locations" ADD COLUMN "location_size" TEXT;
    END IF;
END $$;

-- Add location_features column to locations table if it doesn't exist
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1
        FROM information_schema.columns
        WHERE table_name = 'locations'
        AND column_name = 'location_features'
    ) THEN
        ALTER TABLE "locations" ADD COLUMN "location_features" JSONB;
    END IF;
END $$;

-- Add location_tags column to locations table if it doesn't exist
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1
        FROM information_schema.columns
        WHERE table_name = 'locations'
        AND column_name = 'location_tags'
    ) THEN
        ALTER TABLE "locations" ADD COLUMN "location_tags" JSONB;
    END IF;
END $$;

-- Add accessibility column to locations table if it doesn't exist
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1
        FROM information_schema.columns
        WHERE table_name = 'locations'
        AND column_name = 'accessibility'
    ) THEN
        ALTER TABLE "locations" ADD COLUMN "accessibility" TEXT;
    END IF;
END $$;

-- Add parking_info column to locations table if it doesn't exist
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1
        FROM information_schema.columns
        WHERE table_name = 'locations'
        AND column_name = 'parking_info'
    ) THEN
        ALTER TABLE "locations" ADD COLUMN "parking_info" TEXT;
    END IF;
END $$;

-- Add hourly_rate column to locations table if it doesn't exist
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1
        FROM information_schema.columns
        WHERE table_name = 'locations'
        AND column_name = 'hourly_rate'
    ) THEN
        ALTER TABLE "locations" ADD COLUMN "hourly_rate" JSONB;
    END IF;
END $$;

-- Add daily_rate column to locations table if it doesn't exist
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1
        FROM information_schema.columns
        WHERE table_name = 'locations'
        AND column_name = 'daily_rate'
    ) THEN
        ALTER TABLE "locations" ADD COLUMN "daily_rate" JSONB;
    END IF;
END $$;

-- Add permits_required column to locations table if it doesn't exist
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1
        FROM information_schema.columns
        WHERE table_name = 'locations'
        AND column_name = 'permits_required'
    ) THEN
        ALTER TABLE "locations" ADD COLUMN "permits_required" BOOLEAN NOT NULL DEFAULT FALSE;
    END IF;
END $$;

-- Add restrictions column to locations table if it doesn't exist
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1
        FROM information_schema.columns
        WHERE table_name = 'locations'
        AND column_name = 'restrictions'
    ) THEN
        ALTER TABLE "locations" ADD COLUMN "restrictions" TEXT;
    END IF;
END $$;

-- Add golden_morning_start column to locations table if it doesn't exist
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1
        FROM information_schema.columns
        WHERE table_name = 'locations'
        AND column_name = 'golden_morning_start'
    ) THEN
        ALTER TABLE "locations" ADD COLUMN "golden_morning_start" TEXT;
    END IF;
END $$;

-- Add golden_morning_end column to locations table if it doesn't exist
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1
        FROM information_schema.columns
        WHERE table_name = 'locations'
        AND column_name = 'golden_morning_end'
    ) THEN
        ALTER TABLE "locations" ADD COLUMN "golden_morning_end" TEXT;
    END IF;
END $$;

-- Add golden_evening_start column to locations table if it doesn't exist
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1
        FROM information_schema.columns
        WHERE table_name = 'locations'
        AND column_name = 'golden_evening_start'
    ) THEN
        ALTER TABLE "locations" ADD COLUMN "golden_evening_start" TEXT;
    END IF;
END $$;

-- Add golden_evening_end column to locations table if it doesn't exist
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1
        FROM information_schema.columns
        WHERE table_name = 'locations'
        AND column_name = 'golden_evening_end'
    ) THEN
        ALTER TABLE "locations" ADD COLUMN "golden_evening_end" TEXT;
    END IF;
END $$;
