-- Add Polar-related fields to organizations table
ALTER TABLE "organizations" 
ADD COLUMN IF NOT EXISTS "polar_customer_id" varchar(255) UNIQUE,
ADD COLUMN IF NOT EXISTS "subscription_tier" varchar(50) DEFAULT 'basic',
ADD COLUMN IF NOT EXISTS "subscription_status" varchar(50),
ADD COLUMN IF NOT EXISTS "subscription_valid_until" timestamp,
ADD COLUMN IF NOT EXISTS "max_projects" integer DEFAULT 5,
ADD COLUMN IF NOT EXISTS "max_locations_per_project" integer DEFAULT 50,
ADD COLUMN IF NOT EXISTS "max_team_members" integer DEFAULT 5,
ADD COLUMN IF NOT EXISTS "max_storage" integer DEFAULT 10;

-- Create subscriptions table for Polar integration
CREATE TABLE IF NOT EXISTS "subscriptions" (
  "id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
  "organization_id" uuid NOT NULL REFERENCES "organizations"("id") ON DELETE CASCADE,
  "polar_subscription_id" varchar(255) UNIQUE NOT NULL,
  "tier" varchar(50) NOT NULL,
  "status" varchar(50) NOT NULL,
  "current_period_start" timestamp NOT NULL,
  "current_period_end" timestamp NOT NULL,
  "cancel_at_period_end" boolean DEFAULT false,
  "created_at" timestamp DEFAULT now() NOT NULL,
  "updated_at" timestamp DEFAULT now() NOT NULL,
  "metadata" jsonb,
  "quantity" integer DEFAULT 1
);

-- Create subscription_invoices table
CREATE TABLE IF NOT EXISTS "subscription_invoices" (
  "id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
  "organization_id" uuid NOT NULL REFERENCES "organizations"("id") ON DELETE CASCADE,
  "subscription_id" uuid REFERENCES "subscriptions"("id") ON DELETE SET NULL,
  "polar_invoice_id" varchar(255) UNIQUE NOT NULL,
  "amount" integer NOT NULL,
  "currency" varchar(10) DEFAULT 'usd',
  "status" varchar(50) NOT NULL,
  "invoice_url" varchar(255),
  "invoice_number" varchar(100),
  "invoice_date" timestamp NOT NULL,
  "paid_at" timestamp
);

-- Add metadata to track migration
INSERT INTO "_drizzle_migrations" (id, hash, created_at)
VALUES ('0001_polar_integration', 'polar_integration_migration', NOW())
ON CONFLICT DO NOTHING;
