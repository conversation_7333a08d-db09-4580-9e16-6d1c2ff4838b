CREATE TABLE IF NOT EXISTS "location_media" (
  "id" UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  "location_id" UUID NOT NULL REFERENCES "locations"("id"),
  "url" TEXT NOT NULL,
  "thumbnail_url" TEXT,
  "type" TEXT NOT NULL,
  "title" TEXT,
  "description" TEXT,
  "uploaded_by" UUID REFERENCES "users"("id"),
  "uploaded_at" TIMESTAMP NOT NULL DEFAULT NOW(),
  "metadata" JSONB,
  "file_size" INTEGER,
  "width" INTEGER,
  "height" INTEGER,
  "is_public" BOOLEAN NOT NULL DEFAULT TRUE,
  "ordering" INTEGER NOT NULL DEFAULT 0
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS "idx_location_media_location_id" ON "location_media"("location_id");
CREATE INDEX IF NOT EXISTS "idx_location_media_uploaded_by" ON "location_media"("uploaded_by");
CREATE INDEX IF NOT EXISTS "idx_location_media_type" ON "location_media"("type");
