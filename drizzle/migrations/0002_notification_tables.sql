-- Create notification_type enum
CREATE TYPE "notification_type" AS ENUM (
  'project_created',
  'project_updated',
  'project_deleted',
  'location_created',
  'location_updated',
  'location_deleted',
  'location_approved',
  'document_uploaded',
  'document_updated',
  'document_deleted',
  'task_created',
  'task_updated',
  'task_completed',
  'task_assigned',
  'member_added',
  'member_removed',
  'member_role_changed',
  'subscription_created',
  'subscription_updated',
  'subscription_canceled',
  'payment_succeeded',
  'payment_failed',
  'comment_added',
  'system_notification'
);

-- Create notification_status enum
CREATE TYPE "notification_status" AS ENUM (
  'unread',
  'read',
  'archived'
);

-- Create notification_priority enum
CREATE TYPE "notification_priority" AS ENUM (
  'low',
  'medium',
  'high',
  'urgent'
);

-- Create notifications table
CREATE TABLE IF NOT EXISTS "notifications" (
  "id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
  "type" notification_type NOT NULL,
  "title" varchar(255) NOT NULL,
  "message" text NOT NULL,
  "status" notification_status DEFAULT 'unread' NOT NULL,
  "priority" notification_priority DEFAULT 'medium' NOT NULL,
  "recipient_id" uuid NOT NULL REFERENCES "users"("id") ON DELETE CASCADE,
  "organization_id" uuid NOT NULL REFERENCES "organizations"("id") ON DELETE CASCADE,
  "project_id" uuid REFERENCES "projects"("id") ON DELETE SET NULL,
  "location_id" uuid REFERENCES "locations"("id") ON DELETE SET NULL,
  "document_id" uuid REFERENCES "documents"("id") ON DELETE SET NULL,
  "task_id" uuid REFERENCES "tasks"("id") ON DELETE SET NULL,
  "actor_id" uuid REFERENCES "users"("id") ON DELETE SET NULL,
  "metadata" jsonb DEFAULT '{}',
  "is_read" boolean DEFAULT false NOT NULL,
  "read_at" timestamp,
  "expires_at" timestamp,
  "created_at" timestamp DEFAULT now() NOT NULL,
  "updated_at" timestamp DEFAULT now() NOT NULL
);

-- Create notification_preferences table
CREATE TABLE IF NOT EXISTS "notification_preferences" (
  "id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
  "user_id" uuid NOT NULL REFERENCES "users"("id") ON DELETE CASCADE,
  "organization_id" uuid NOT NULL REFERENCES "organizations"("id") ON DELETE CASCADE,
  "notification_type" notification_type NOT NULL,
  "in_app" boolean DEFAULT true NOT NULL,
  "email" boolean DEFAULT true NOT NULL,
  "created_at" timestamp DEFAULT now() NOT NULL,
  "updated_at" timestamp DEFAULT now() NOT NULL
);

-- Add metadata to track migration
INSERT INTO "_drizzle_migrations" (id, hash, created_at)
VALUES ('0002_notification_tables', 'notification_tables_migration', NOW())
ON CONFLICT DO NOTHING;
