-- Create shared_map_links table
CREATE TABLE shared_map_links (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  organization_id UUID NOT NULL REFERENCES organizations(id),
  token VARCHAR(255) NOT NULL UNIQUE,
  map_view_id UUID REFERENCES map_views(id),
  filters <PERSON><PERSON><PERSON><PERSON>,
  view_settings JSONB,
  password_hash VARCHAR(255),
  expires_at TIMESTAMP WITH TIME ZONE,
  created_by UUID NOT NULL REFERENCES users(id),
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW()
);

-- Create shared_map_link_locations junction table
CREATE TABLE shared_map_link_locations (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  shared_map_link_id UUID NOT NULL REFERENCES shared_map_links(id),
  location_id UUID NOT NULL REFERENCES locations(id),
  UNIQUE(shared_map_link_id, location_id)
);

-- Create shared_map_link_access_logs table for tracking access
CREATE TABLE shared_map_link_access_logs (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  shared_map_link_id UUID NOT NULL REFERENCES shared_map_links(id),
  ip_address VARCHAR(45),
  user_agent TEXT,
  accessed_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW()
);

-- Add indexes for better performance
CREATE INDEX idx_shared_map_links_organization_id ON shared_map_links(organization_id);
CREATE INDEX idx_shared_map_links_token ON shared_map_links(token);
CREATE INDEX idx_shared_map_links_created_by ON shared_map_links(created_by);
CREATE INDEX idx_shared_map_link_locations_shared_map_link_id ON shared_map_link_locations(shared_map_link_id);
CREATE INDEX idx_shared_map_link_locations_location_id ON shared_map_link_locations(location_id);
CREATE INDEX idx_shared_map_link_access_logs_shared_map_link_id ON shared_map_link_access_logs(shared_map_link_id);
