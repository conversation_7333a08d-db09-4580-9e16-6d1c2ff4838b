import { NextResponse } from "next/server";
import type { NextRequest } from "next/server";
import type {
  MemberSession,
  B2BSessionsAuthenticateResponse,
} from "stytch";
// Import async client and cache utils
import { getStytchB2BClientAsync } from "@/lib/stytch-b2b"; 
import { getCachedAuthData, cacheAuthData } from "@/lib/utils/auth-cache";

// Define an interface for the expected JWT auth response structure
// based on previous TS errors, as SDK exports seem inconsistent here.
interface B2BJwtAuthResponse {
  member_session: MemberSession;
  session_jwt: string;
  // Add other potential fields if known, e.g., request_id, status_code
}

// Paths that *don't* require authentication
const publicPaths = [
  // UI Paths
  "/auth/login",
  "/auth/register",
  "/authenticate", // Assuming this is a UI page
  "/auth/authenticate", // Assuming this is a UI page
  // API Paths (Public)
  "/api/auth/login",
  "/api/auth/authenticate",
  "/api/webhooks/stytch", // Add other webhooks if any
  "/api/webhooks/polar", // Added based on file structure
  // Add other truly public paths like /terms, /privacy etc. if they exist
]

// Paths that require authentication but *don't* require organization selection
const authNoOrgRequiredPaths = [
  "/auth/organization/create", // UI path
  "/auth/organization/select", // UI path
  "/api/organizations", // API for creating/listing orgs
  "/api/auth/logout", // API for logging out (needs auth)
  "/api/auth/me", // API to get current user (needs auth)
]

export async function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl
  const headers = new Headers(request.headers)

  // 1. Handle Root Path
  if (pathname === "/") {
    // Check if the user is authenticated
    const sessionJwt = request.cookies.get("stytch_session_jwt")?.value
    const sessionToken = request.cookies.get("stytch_session")?.value

    // Use the relevant token/jwt for cache check and potential auth call
    const activeToken = sessionJwt || sessionToken; 

    if (activeToken) {
      try {
        let memberId: string | undefined;
        let organizationId: string | undefined;
        let roles: string[] = []; // Initialize roles

        // --- Cache Check ---
        const cachedData = getCachedAuthData(activeToken);
        if (cachedData) {
          console.log("Middleware (Root): Cache hit.");
          memberId = cachedData.userId;
          organizationId = cachedData.organizationId;
          // Roles are not typically cached with basic auth data, skip setting roles header from cache
        } else {
          console.log("Middleware (Root): Cache miss, authenticating.");
          const stytchClient = await getStytchB2BClientAsync(); // Use async client
          let session: MemberSession | null = null;
          // Just need to authenticate to confirm validity for redirect
          if (sessionJwt) {
            const jwtSessionResponse = (await stytchClient.sessions.authenticateJwt({ session_jwt: sessionJwt })) as B2BJwtAuthResponse;
            session = jwtSessionResponse.member_session;
          } else if (sessionToken) {
            const tokenSessionResponse: B2BSessionsAuthenticateResponse = await stytchClient.sessions.authenticate({ session_token: sessionToken });
            session = tokenSessionResponse.member_session;
          }

          if (!session) throw new Error("Root path authentication failed.");

          memberId = session.member_id;
          organizationId = session.organization_id;
          roles = session.roles || []; // Get roles from session if available

          // Cache the data
          cacheAuthData(activeToken, memberId, organizationId);
          console.log("Middleware (Root): Data cached.");
        }
        
        // If authenticated (cache or API), redirect to organization selection flow
        // We don't need to set headers here as we are redirecting immediately
        return NextResponse.redirect(new URL("/auth/organization/select", request.url));
      } catch (error: unknown) { // Add type to error
        // Invalid session, treat as unauthenticated
        console.error("Root path session validation error:", error);
        // Clear potentially invalid cookies
        const response = NextResponse.next();
        response.cookies.delete("stytch_session_jwt")
        response.cookies.delete("stytch_session")
        response.cookies.delete("current_organization") // Also clear org context
        return response
      }
    }
    // Not authenticated, show landing page
    return NextResponse.next()
  }

  // 2. Check if Path is Public
  if (publicPaths.some((path) => pathname === path || pathname.startsWith(`${path}/`))) {
    return NextResponse.next()
  }

  // 3. Check for Session Cookies
  const sessionJwt = request.cookies.get("stytch_session_jwt")?.value
  const sessionToken = request.cookies.get("stytch_session")?.value

  if (!sessionJwt && !sessionToken) {
    // Not authenticated, redirect to login
    return NextResponse.redirect(new URL(`/auth/login?callbackUrl=${encodeURIComponent(pathname)}`, request.url))
  }

  // 4. Authenticate Session & Extract Data (with Cache)
  let memberId: string | undefined;
  let organizationId: string | undefined;
  let roles: string[] = []; // Initialize roles
  const activeToken = sessionJwt || sessionToken; // Use the available token/jwt

  try {
    // --- Cache Check ---
    const cachedData = activeToken ? getCachedAuthData(activeToken) : undefined;
    if (cachedData) {
       console.log("Middleware: Cache hit.");
       memberId = cachedData.userId;
       organizationId = cachedData.organizationId;
       // Roles are not cached, need to fetch if required, or rely on session below if cache miss
    } else if (activeToken) {
      console.log("Middleware: Cache miss, authenticating.");
      const stytchClient = await getStytchB2BClientAsync(); // Use async client
      let session: MemberSession | null = null;

      if (sessionJwt) {
        const jwtSessionResponse = (await stytchClient.sessions.authenticateJwt({ session_jwt: sessionJwt })) as B2BJwtAuthResponse;
        session = jwtSessionResponse.member_session;
      } else if (sessionToken) {
        const tokenSessionResponse: B2BSessionsAuthenticateResponse = await stytchClient.sessions.authenticate({ session_token: sessionToken });
        session = tokenSessionResponse.member_session;
      }

      if (!session) {
        throw new Error("Authentication successful but no session returned.");
      }

      memberId = session.member_id;
      organizationId = session.organization_id;
      roles = session.roles || []; // Get roles from session

      // Cache the data
      cacheAuthData(activeToken, memberId, organizationId);
      console.log("Middleware: Data cached.");
    } else {
       // This case should technically be caught by step 3, but handle defensively
       throw new Error("No active session token or JWT found for authentication.");
    }

    // 5. Attach Session Info to Headers for Downstream Use
    if (!memberId || !organizationId) {
       // Should not happen if cache or auth worked
       throw new Error("Failed to resolve memberId or organizationId after auth check.");
    }
    headers.set("X-Stytch-Member-Id", memberId);
    headers.set("X-Stytch-Org-Id", organizationId);
    // Only set roles header if we got them from a fresh auth call
    if (roles.length > 0) {
       headers.set("X-Stytch-Roles", JSON.stringify(roles));
    }
    // Pass the original token/jwt used for authentication
    if (sessionJwt) {
      headers.set("X-Stytch-Session-JWT", sessionJwt);
    } else if (sessionToken) {
      headers.set("X-Stytch-Session-Token", sessionToken);
    }

  } catch (error: unknown) { // Add type to error
    console.error("Middleware authentication error:", error);
    // Clear potentially invalid cookies and redirect to login
    const response = NextResponse.redirect(
      new URL(`/auth/login?callbackUrl=${encodeURIComponent(pathname)}`, request.url)
    );
    response.cookies.delete("stytch_session_jwt");
    response.cookies.delete("stytch_session")
    response.cookies.delete("current_organization")
    return response
  }

  // 6. Check Organization Selection Requirement
  const requiresOrgSelection = !authNoOrgRequiredPaths.some((path) => pathname === path || pathname.startsWith(`${path}/`));

  // Use the organizationId resolved from cache or API call
  if (requiresOrgSelection && !organizationId) { 
     // Should not happen if Stytch B2B is set up correctly and auth worked
     console.warn(`User ${memberId} authenticated but resolved organizationId is missing. Redirecting to org select.`);
     return NextResponse.redirect(new URL("/auth/organization/select", request.url));
  }

  // If the path requires org selection, also check the cookie as a fallback/consistency check?
  // Or rely solely on the session.organization_id which is more authoritative.
  // Let's rely on session.organization_id for now.

  // 7. Proceed with Added Headers
  return NextResponse.next({
    request: {
      headers: headers,
    },
  })
}

export const config = {
  matcher: ["/((?!_next/static|_next/image|favicon.ico).*)"],
}
