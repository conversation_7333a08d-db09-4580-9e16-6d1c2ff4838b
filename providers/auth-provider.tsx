"use client"

import {
  createContext,
  useContext,
  useEffect,
  useState,
  useMemo,
  useCallback,
  type ReactNode,
} from "react"
// Use correct imports based on original file and library structure
import { useStytchMember } from "@stytch/nextjs/b2b" 
// Try importing Member type from vanilla-js/b2b again
import type { Member, Organization } from "@stytch/vanilla-js/b2b" 
// Import the client-side UI client getter
import { getStytchB2BClient } from "@/lib/stytch-b2b-client" 

type AuthContextType = {
  user: Member | null // Use Member type from vanilla-js/b2b
  organization: Organization | null 
  loading: boolean
  error: Error | null
  isInitialized: boolean // Status from Stytch hooks
  isAuthenticated: boolean // Status from Stytch hooks
  // refreshUser removed as fetchApiUser is removed
  logout: () => Promise<void>
};

export const AuthContext = createContext<AuthContextType | undefined>(undefined)

export function AuthProvider({ children }: { children: ReactNode }) {
  // Explicitly type the destructured variables
  const { member, isInitialized: memberInitialized }: { member: Member | null; isInitialized: boolean } = useStytchMember()
  
  // State - Removed apiUser state
  const [organization, setOrganization] = useState<Organization | null>(null)
  const [loading, setLoading] = useState(true) // Combined loading state
  const [error, setError] = useState<Error | null>(null)

  const isInitialized = memberInitialized
  const isAuthenticated = !!member

  // Removed fetchApiUser function

  // Simplified initialization status handling
  useEffect(() => {
    console.log(`AuthProvider: Initializing... MemberInit: ${memberInitialized}`)
    if (isInitialized) {
      console.log(`AuthProvider: Initialized. Authenticated: ${isAuthenticated}`)
      if (!isAuthenticated) {
        // Initialized but not authenticated
        setOrganization(null) // Clear org if not authenticated
      }
      // Set loading false once Stytch hooks are initialized
      setLoading(false) 
    } else {
      // Still initializing Stytch hooks
      setLoading(true)
    }
  }, [isInitialized, isAuthenticated, memberInitialized]) 

  // Fetch organization details when member data is available
  useEffect(() => {
    let isMounted = true
    console.log(`AuthProvider: Member/Org Check. Initialized: ${isInitialized}, Member: ${!!member}`)
    // Ensure initialized and member exists before fetching org
    if (isInitialized && member && member.organization_id) { 
      console.log("AuthProvider: Member has organization ID:", member.organization_id)
      const stytchClient = getStytchB2BClient() // Use the client-side UI client
      if (stytchClient) {
        console.log("AuthProvider: Stytch client available, fetching organization details")
        // Corrected .get() call - no arguments expected for client-side UI client
        stytchClient.organization.get() 
          .then((response) => {
             // Assuming response structure might be the Organization object itself, or null/undefined.
             // Need to check actual response or documentation.
             // Also handle potential null response.
            const orgData = response; // Assume response is the org object or null/undefined
            if (isMounted && orgData) {
              console.log("AuthProvider: Organization details fetched:", orgData)
              setOrganization(orgData) // Set the response directly
              setError(null); // Clear previous errors on success
            } else if (isMounted) {
              console.log("AuthProvider: Fetched null or empty organization data")
              setOrganization(null);
            }
          })
          .catch((err) => {
            if (isMounted) {
              console.error("AuthProvider: Failed to fetch organization:", err)
              setOrganization(null)
              setError(err instanceof Error ? err : new Error(String(err)))
            }
          })
      } else {
        console.log("AuthProvider: Stytch client not available, cannot fetch organization")
      }
    } else if (isInitialized && !member) {
      // Initialized but no member (e.g., logged out state handled by previous useEffect)
      console.log("AuthProvider: Initialized but no member found for Org fetch.")
      setOrganization(null) // Ensure org is null if no member
    }
     return () => {
      isMounted = false
    }
  }, [member, isInitialized]) // Depend on member and initialization status

  // Removed refreshUser function

  // Function to handle logout - simplified
  const logout = useCallback(async () => {
    try {
      console.log("AuthProvider: Logging out")
      setLoading(true) // Indicate loading during logout process
      const response = await fetch('/api/auth/logout', { method: 'POST' })

      if (!response.ok) {
        throw new Error('Failed to logout via API')
      }

      console.log("AuthProvider: Logout API call successful")
      // State clearing (like setOrganization(null)) should happen naturally 
      // when Stytch hooks update (member becomes null)
      setError(null) 
    } catch (err) {
      console.error('AuthProvider: Error logging out:', err)
      setError(err instanceof Error ? err : new Error(String(err)))
      setLoading(false); // Ensure loading is false on error
    } 
    // Loading state will be set to false by the initialization useEffect when Stytch hooks update
  }, []);

  // Memoize context value - removed refreshUser
  const contextValue = useMemo(
    () => ({
      user: member, 
      organization,
      loading, // Use the unified loading state
      error,
      isInitialized,
      isAuthenticated,
      logout, // Only logout needed now
    }),
    // Update dependencies
    [member, organization, loading, error, isInitialized, isAuthenticated, logout] 
  );

  return (
    <AuthContext.Provider value={contextValue}>
      {children}
    </AuthContext.Provider>
  )
}

// Custom hook for using the auth context
export function useAuth() {
  const context = useContext(AuthContext)
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider')
  }
  return context
}

// Keep existing hook for compatibility if needed elsewhere
export function useAuthContext() {
  const context = useContext(AuthContext)
  if (context === undefined) {
    throw new Error("useAuthContext must be used within an AuthProvider")
  }
  return context
}
