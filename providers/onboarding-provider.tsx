"use client"

import { create<PERSON>ontext, useContext, useState } from "react"
import type { ReactNode } from "react"
import { OnboardingWelcome } from "@/components/onboarding/steps/onboarding-welcome"
import { OnboardingDashboard } from "@/components/onboarding/steps/onboarding-dashboard"
import { OnboardingProjects } from "@/components/onboarding/steps/onboarding-projects"
import { OnboardingLocations } from "@/components/onboarding/steps/onboarding-locations"
import { OnboardingMap } from "@/components/onboarding/steps/onboarding-map"
import { OnboardingTasks } from "@/components/onboarding/steps/onboarding-tasks"
import { OnboardingCalendar } from "@/components/onboarding/steps/onboarding-calendar"
import { OnboardingDocuments } from "@/components/onboarding/steps/onboarding-documents"
import { OnboardingTeam } from "@/components/onboarding/steps/onboarding-team"
import { OnboardingSettings } from "@/components/onboarding/steps/onboarding-settings"
import { OnboardingComplete } from "@/components/onboarding/steps/onboarding-complete"

// Define the onboarding steps
export const ONBOARDING_STEPS = [
  { id: "welcome", component: OnboardingWelcome, title: "Welcome" },
  { id: "dashboard", component: OnboardingDashboard, title: "Dashboard" },
  { id: "projects", component: OnboardingProjects, title: "Projects" },
  { id: "locations", component: OnboardingLocations, title: "Locations" },
  { id: "map", component: OnboardingMap, title: "Map View" },
  { id: "tasks", component: OnboardingTasks, title: "Tasks" },
  { id: "calendar", component: OnboardingCalendar, title: "Calendar" },
  { id: "documents", component: OnboardingDocuments, title: "Documents" },
  { id: "team", component: OnboardingTeam, title: "Team" },
  { id: "settings", component: OnboardingSettings, title: "Settings" },
  { id: "complete", component: OnboardingComplete, title: "Complete" },
]

// Define the context type
interface OnboardingContextType {
  isOpen: boolean
  currentStepIndex: number
  currentStep: typeof ONBOARDING_STEPS[0]
  totalSteps: number
  startOnboarding: () => void
  closeOnboarding: () => void
  nextStep: () => void
  prevStep: () => void
  goToStep: (stepIndex: number) => void
}

// Create the context with a default value
const OnboardingContext = createContext<OnboardingContextType | undefined>(undefined)

// Provider component
export function OnboardingProvider({ children }: { children: ReactNode }) {
  const [isOpen, setIsOpen] = useState(false)
  const [currentStepIndex, setCurrentStepIndex] = useState(0)
  
  const totalSteps = ONBOARDING_STEPS.length
  const currentStep = ONBOARDING_STEPS[currentStepIndex]
  
  // Start the onboarding flow
  const startOnboarding = () => {
    setCurrentStepIndex(0)
    setIsOpen(true)
  }
  
  // Close the onboarding flow
  const closeOnboarding = () => {
    setIsOpen(false)
  }
  
  // Navigate to the next step
  const nextStep = () => {
    if (currentStepIndex < totalSteps - 1) {
      setCurrentStepIndex(currentStepIndex + 1)
    } else {
      // If we're at the last step, close the onboarding
      closeOnboarding()
    }
  }
  
  // Navigate to the previous step
  const prevStep = () => {
    if (currentStepIndex > 0) {
      setCurrentStepIndex(currentStepIndex - 1)
    }
  }
  
  // Navigate to a specific step
  const goToStep = (stepIndex: number) => {
    if (stepIndex >= 0 && stepIndex < totalSteps) {
      setCurrentStepIndex(stepIndex)
    }
  }
  
  // Create the context value
  const contextValue: OnboardingContextType = {
    isOpen,
    currentStepIndex,
    currentStep,
    totalSteps,
    startOnboarding,
    closeOnboarding,
    nextStep,
    prevStep,
    goToStep,
  }
  
  return (
    <OnboardingContext.Provider value={contextValue}>
      {children}
    </OnboardingContext.Provider>
  )
}

// Custom hook to use the onboarding context
export function useOnboarding() {
  const context = useContext(OnboardingContext)
  
  if (context === undefined) {
    throw new Error("useOnboarding must be used within an OnboardingProvider")
  }
  
  return context
}
