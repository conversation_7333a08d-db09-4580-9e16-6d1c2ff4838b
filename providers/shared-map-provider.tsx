"use client"

import { createContext, useContext, useState, type ReactNode } from "react"
import type { MapViewSettings, MapFilter, ClusterSettings } from "@/modules/map/model"
import { defaultMapViewSettings, defaultClusterSettings } from "@/modules/map/model"
import type { Location } from "@/modules/location/model"

type SharedMapContextType = {
  // Data
  locations: Location[]
  
  // State
  isLoading: boolean
  error: string | null
  selectedLocation: Location | null
  
  // Settings
  viewSettings: MapViewSettings
  clusterSettings: ClusterSettings
  filters: MapFilter
  
  // Actions
  setSelectedLocation: (location: Location | null) => void
  setViewSettings: (settings: MapViewSettings) => void
  setClusterSettings: (settings: ClusterSettings) => void
}

const SharedMapContext = createContext<SharedMapContextType | undefined>(undefined)

export function SharedMapProvider({
  children,
  initialLocations = [],
  initialViewSettings = defaultMapViewSettings,
}: { 
  children: ReactNode
  initialLocations?: Location[]
  initialViewSettings?: MapViewSettings
}) {
  // Data states
  const [locations] = useState<Location[]>(initialLocations)
  
  // UI states
  const [isLoading] = useState(false)
  const [error] = useState<string | null>(null)
  const [selectedLocation, setSelectedLocation] = useState<Location | null>(null)
  
  // Settings
  const [viewSettings, setViewSettings] = useState<MapViewSettings>(initialViewSettings)
  const [clusterSettings, setClusterSettings] = useState<ClusterSettings>(defaultClusterSettings)
  const [filters] = useState<MapFilter>({})

  return (
    <SharedMapContext.Provider
      value={{
        // Data
        locations,
        
        // State
        isLoading,
        error,
        selectedLocation,
        
        // Settings
        viewSettings,
        clusterSettings,
        filters,
        
        // Actions
        setSelectedLocation,
        setViewSettings,
        setClusterSettings,
      }}
    >
      {children}
    </SharedMapContext.Provider>
  )
}

export function useSharedMap() {
  const context = useContext(SharedMapContext)
  if (context === undefined) {
    throw new Error("useSharedMap must be used within a SharedMapProvider")
  }
  return context
}
