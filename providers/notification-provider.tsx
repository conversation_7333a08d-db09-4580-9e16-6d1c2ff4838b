"use client"

import React, { create<PERSON>ontext, useContext, useState, useEffect, useC<PERSON>back, type ReactNode, useMemo } from "react"
import { useAuth } from "@/hooks/use-auth"
import { useOrganization } from "@/hooks/use-organization"
// Import the new hook
import { useNotificationCount } from "@/hooks/use-notification-count" 
// Apply import type
import type { NotificationWithRelations, NotificationPreference } from "@/modules/notification/model" 

// Updated Context Type
interface NotificationContextType {
  notifications: NotificationWithRelations[]
  unreadCount: number // Use unreadCount from hook
  preferences: NotificationPreference[]
  loading: boolean // Combined loading state
  loadingCount: boolean // Loading state specifically for count
  error: string | null
  fetchNotifications: () => Promise<void>
  refreshCount: () => void // Use refreshCount from hook
  // Update fetchPreferences return type in context
  fetchPreferences: (forceRefresh?: boolean) => Promise<NotificationPreference[] | null> 
  markAsRead: (id: string) => Promise<void>
  markAllAsRead: () => Promise<void>
  updatePreferences: (preferences: { notificationType: string; inApp: boolean; email: boolean }[]) => Promise<void>
  // initializePreferences might become internal logic
}

const NotificationContext = createContext<NotificationContextType | undefined>(undefined)

export function NotificationProvider({ children }: { children: ReactNode }) {
  const { user } = useAuth()
  const { organization } = useOrganization()
  // Use the new hook for count management
  const { count: unreadCount, loading: loadingCount, refreshCount } = useNotificationCount(); 
  
  const [notifications, setNotifications] = useState<NotificationWithRelations[]>([])
  // Removed notificationCount state
  const [preferences, setPreferences] = useState<NotificationPreference[]>([])
  // Use a more specific loading state, or combine if appropriate
  const [loadingNotifications, setLoadingNotifications] = useState<boolean>(false) 
  const [loadingPreferences, setLoadingPreferences] = useState<boolean>(false)
  const [error, setError] = useState<string | null>(null)

  // Combined loading state for context consumers
  const loading = loadingNotifications || loadingPreferences || loadingCount;

  // Fetch notifications
  const fetchNotifications = useCallback(async () => {
    // Add optional chaining for user and organization
    if (!user || !organization?.id) return 

    setLoadingNotifications(true) // Use correct setter
    setError(null)

    try {
      const response = await fetch("/api/notifications", {
        headers: {
          'X-Stytch-Org-Id': organization?.id || '',
          'X-Stytch-Member-Id': user?.member_id || ''
        }
      })
      
      if (!response.ok) {
        throw new Error(`Failed to fetch notifications: ${response.statusText}`)
      }
      
      const data = await response.json()
      setNotifications(data)
    } catch (err) {
      console.error("Error fetching notifications:", err)
      setError("Failed to fetch notifications")
    } finally {
      setLoadingNotifications(false) // Use correct setter
    }
  }, [user, organization])

  // Removed fetchNotificationCount function

  // Fetch notification preferences (with localStorage caching)
  const fetchPreferences = useCallback(async (forceRefresh = false): Promise<NotificationPreference[] | null> => {
    // Add optional chaining
    if (!user || !organization?.id) return null; 

    // Check localStorage first unless forcing refresh
    if (!forceRefresh) {
      try {
        const cachedPrefs = localStorage.getItem('notificationPreferences');
        const cachedTimestamp = localStorage.getItem('notificationPreferencesTimestamp');
        
        // Use cached data if it's less than 5 minutes old
        if (cachedPrefs && cachedTimestamp) {
          // Use Number.parseInt
          const timestamp = Number.parseInt(cachedTimestamp, 10); 
          const now = Date.now();
          
          if (now - timestamp < 5 * 60 * 1000) { // 5 minutes
            console.log("NotificationProvider: Using cached preferences.");
            const parsedPrefs = JSON.parse(cachedPrefs);
            setPreferences(parsedPrefs);
            return parsedPrefs; // Return cached data
          } 
          // Removed redundant else block as the if block returns
          console.log("NotificationProvider: Cache stale.");
        }
      } catch (e) {
        console.error("NotificationProvider: Error reading preferences from localStorage", e);
        localStorage.removeItem('notificationPreferences'); // Clear potentially corrupted cache
        localStorage.removeItem('notificationPreferencesTimestamp');
      }
    } else {
       console.log("NotificationProvider: Forcing preference refresh.");
    }

    // Fetch fresh data if cache is stale, missing, or refresh forced
    console.log("NotificationProvider: Fetching fresh preferences from API.");
    setLoadingPreferences(true); // Use specific loading state
    setError(null);

    try {
      const response = await fetch("/api/notifications/preferences", {
        headers: {
          'X-Stytch-Org-Id': organization?.id || '',
          'X-Stytch-Member-Id': user?.member_id || ''
        }
      });
      
      if (!response.ok) {
        throw new Error(`Failed to fetch notification preferences: ${response.statusText}`);
      }
      
      const data = await response.json();
      // Assuming API returns { preferences: [...] } based on optimization doc example
      const fetchedPreferences = data?.preferences || []; 
      setPreferences(fetchedPreferences);
      
      // Update cache
      try {
        localStorage.setItem('notificationPreferences', JSON.stringify(fetchedPreferences));
        localStorage.setItem('notificationPreferencesTimestamp', Date.now().toString());
        console.log("NotificationProvider: Updated preferences cache.");
      } catch (e) {
         console.error("NotificationProvider: Error writing preferences to localStorage", e);
      }
      return fetchedPreferences; // Return fetched data
    } catch (err) {
      console.error("Error fetching notification preferences:", err);
      setError("Failed to fetch notification preferences");
      return null; // Add return null in catch block
    } finally {
      setLoadingPreferences(false); // Use correct setter
    }
  }, [user, organization])

  // Mark notification as read
  const markAsRead = useCallback(async (id: string) => {
    // Add optional chaining
    if (!user || !organization?.id) return 

    try {
      const response = await fetch(`/api/notifications/${id}/read`, {
        method: "PUT",
        headers: {
          'X-Stytch-Org-Id': organization?.id || '',
          'X-Stytch-Member-Id': user?.member_id || ''
        }
      })
      
      if (!response.ok) {
        throw new Error(`Failed to mark notification as read: ${response.statusText}`)
      }
      
      // Update local state
      setNotifications(prev => 
        prev.map(notification => 
          notification.id === id 
            ? { ...notification, status: "read", isRead: true, readAt: new Date() } 
            : notification
        )
      )
      
      // Refresh count using the hook's function
      refreshCount() 
    } catch (err) {
      console.error("Error marking notification as read:", err)
      setError("Failed to mark notification as read")
    }
  }, [user, organization, refreshCount]) // Use refreshCount dependency

  // Mark all notifications as read
  const markAllAsRead = useCallback(async () => {
    // Add optional chaining
    if (!user || !organization?.id) return 

    try {
      const response = await fetch("/api/notifications/read-all", {
        method: "PUT", // Should likely be POST or PUT based on API design
        headers: {
          'X-Stytch-Org-Id': organization?.id || '',
          'X-Stytch-Member-Id': user?.member_id || ''
        }
      })
      
      if (!response.ok) {
        throw new Error(`Failed to mark all notifications as read: ${response.statusText}`)
      }
      
      // Update local state
      setNotifications(prev => 
        prev.map(notification => 
          notification.status === "unread"
            ? { ...notification, status: "read", isRead: true, readAt: new Date() } 
            : notification
        )
      )
      
      // Refresh count using the hook's function
      refreshCount() 
    } catch (err) {
      console.error("Error marking all notifications as read:", err)
      setError("Failed to mark all notifications as read")
    }
  }, [user, organization, refreshCount]) // Use refreshCount dependency

  // Update notification preferences
  const updatePreferences = useCallback(async (preferencesData: { notificationType: string; inApp: boolean; email: boolean }[]) => {
    // Add optional chaining
    if (!user || !organization?.id) return 

    setLoadingPreferences(true) // Use correct setter
    setError(null)

    try {
      const response = await fetch("/api/notifications/preferences", {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
          'X-Stytch-Org-Id': organization?.id || '',
          'X-Stytch-Member-Id': user?.member_id || ''
        },
        body: JSON.stringify({
          preferences: preferencesData,
        }),
      })
      
      if (!response.ok) {
        throw new Error(`Failed to update notification preferences: ${response.statusText}`)
      }
      
      const data = await response.json()
      setPreferences(data)
    } catch (err) {
      console.error("Error updating notification preferences:", err)
      setError("Failed to update notification preferences")
    } finally {
      setLoadingPreferences(false) // Use correct setter
    }
  }, [user, organization])

  // Initialize notification preferences
  const initializePreferences = useCallback(async () => {
    // Add optional chaining
    if (!user || !organization?.id) return 

    setLoadingPreferences(true) // Use correct setter
    setError(null)

    try {
      const response = await fetch("/api/notifications/preferences", {
        method: "POST",
        headers: {
          'X-Stytch-Org-Id': organization?.id || '',
          'X-Stytch-Member-Id': user?.member_id || ''
        }
      })
      
      if (!response.ok) {
        throw new Error(`Failed to initialize notification preferences: ${response.statusText}`)
      }
      
      const data = await response.json()
      setPreferences(data)
    } catch (err) {
      console.error("Error initializing notification preferences:", err)
      setError("Failed to initialize notification preferences")
    } finally {
      setLoadingPreferences(false) // Use specific loading state
    }
  }, [user, organization])

  // Initial data fetching & Initialization Logic
  useEffect(() => {
    // Flag to prevent multiple initialization attempts per session/load
    const initializationKey = `notificationPreferencesInitialized_${user?.member_id}_${organization?.id}`;
    let alreadyAttempted = false;
    try {
      alreadyAttempted = localStorage.getItem(initializationKey) === 'true';
    } catch (e) {
      console.error("NotificationProvider: Error reading initialization flag from localStorage", e);
    }

    if (user && organization && !alreadyAttempted) {
      console.log("NotificationProvider: Attempting to fetch/initialize preferences.");
      // Fetch preferences (this checks cache first)
      fetchPreferences().then(prefs => {
        // Check if preferences were fetched successfully (from cache or API) and if they are empty
        if (prefs !== null && prefs.length === 0) {
          console.log("NotificationProvider: No preferences found, initializing...");
          initializePreferences(); // Call initialize if fetch returned empty array
        } else if (prefs === null) {
          console.log("NotificationProvider: Failed to fetch preferences during initialization check.");
          // Handle fetch error if needed, maybe retry later?
        } else {
           console.log("NotificationProvider: Preferences already exist or were fetched.");
        }
        // Mark initialization as attempted for this session/user/org
        try {
           localStorage.setItem(initializationKey, 'true');
        } catch (e) {
           console.error("NotificationProvider: Error setting initialization flag in localStorage", e);
        }
      });
    } else if (user && organization && alreadyAttempted) {
       console.log("NotificationProvider: Initialization already attempted this session.");
       // Still fetch preferences (will likely hit cache) in case they changed elsewhere
       fetchPreferences(); 
    }
  // Depend on user and organization to re-run if they change
  }, [user, organization, fetchPreferences, initializePreferences]); 

  // Removed polling useEffect for notification count

  // Wrap value in useMemo
  const value = useMemo(() => ({
    notifications,
    unreadCount, // Use value from hook
    preferences,
    loading, // Use combined loading state
    loadingCount, // Expose count loading state
    error,
    fetchNotifications,
    refreshCount, // Use function from hook
    fetchPreferences,
    markAsRead,
    markAllAsRead,
    updatePreferences,
    // initializePreferences removed from context, handled internally
  }), [
    notifications, 
    unreadCount, 
    preferences, 
    loading, 
    loadingCount, 
    error, 
    fetchNotifications, 
    refreshCount, 
    fetchPreferences, 
    markAsRead, 
    markAllAsRead, 
    updatePreferences
  ]);

  return (
    <NotificationContext.Provider value={value}>
      {children}
    </NotificationContext.Provider>
  )
}

export function useNotifications() {
  const context = useContext(NotificationContext)
  
  if (context === undefined) {
    throw new Error("useNotifications must be used within a NotificationProvider")
  }
  
  return context
}
