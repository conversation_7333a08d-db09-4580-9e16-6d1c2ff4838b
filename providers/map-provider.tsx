"use client"

// Import useMemo
import { createContext, useContext, useState, useEffect, useCallback, useMemo, type ReactNode } from "react" 
import { useOrganization } from "@/hooks/use-organization"
import { useAuth } from "@/hooks/use-auth"
import type { Location } from "@/modules/location/model"
import type { MapViewSettings, MapFilter, ClusterSettings } from "@/modules/map/model"
import { defaultMapViewSettings, defaultClusterSettings } from "@/modules/map/model"
import type { Project } from "@/modules/project/model"
import { toast } from "sonner"
// Import mapStorage
import { mapStorage } from "@/lib/utils/storage" 

type FavoriteList = {
  id: string
  name: string
  type: "project" | "scene" | "custom"
  projectId?: string
  sceneId?: string
  locations: {
    id: string
    name: string
    type: string
    status: string
    addedAt: string
  }[]
}

type SavedView = {
  id: string
  name: string
  description?: string
  filters: MapFilter
  viewSettings: MapViewSettings
}

type MapContextType = {
  // Data
  locations: Location[]
  projects: Project[]
  
  // State
  isLoading: boolean
  error: string | null
  selectedLocation: Location | null
  
  // Settings
  viewSettings: MapViewSettings
  clusterSettings: ClusterSettings
  filters: MapFilter // State for current filters
  mapboxToken?: string // Mapbox token for map initialization
  
  // Favorites
  favorites: FavoriteList[]
  isFavoritesLoading: boolean
  
  // Saved Views
  savedViews: SavedView[]
  isSavedViewsLoading: boolean
  
  // Actions
  setSelectedLocation: (location: Location | null) => void
  setViewSettings: (settings: MapViewSettings) => void
  setClusterSettings: (settings: ClusterSettings) => void
  setFilters: (filters: MapFilter) => void // Add setFilters to context type
  refreshLocations: () => Promise<unknown | null> // Updated return type
  
  // Favorites actions
  addLocationToFavorites: (locationId: string, listId: string) => Promise<boolean>
  createFavoriteList: (name: string, type: "project" | "scene" | "custom", projectId?: string, sceneId?: string) => Promise<FavoriteList | null>
  refreshFavorites: () => Promise<unknown | null> // Updated return type
  
  // Saved Views actions
  saveView: (name: string, description?: string) => Promise<SavedView | null>
  loadView: (viewId: string) => Promise<boolean>
  refreshSavedViews: () => Promise<unknown | null> // Updated return type
}

const MapContext = createContext<MapContextType | undefined>(undefined)

export function MapProvider({
  children,
  initialLocations = [],
  initialViews = [],
  mapboxToken,
}: { 
  children: ReactNode; 
  initialLocations?: Location[];
  initialViews?: SavedView[];
  mapboxToken?: string;
}) {
  const { organization } = useOrganization()
  const { user } = useAuth()
  
  // Data states
  const [locations, setLocations] = useState<Location[]>(initialLocations)
  const [projects, setProjects] = useState<Project[]>([])
  
  // UI states
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [selectedLocation, setSelectedLocation] = useState<Location | null>(null)
  
  // Settings
  const [viewSettings, setViewSettings] = useState<MapViewSettings>(defaultMapViewSettings) // TODO: Consider persisting viewSettings too
  const [clusterSettings, setClusterSettings] = useState<ClusterSettings>(defaultClusterSettings)
  // Initialize filters state lazily from localStorage once organization is available
  const [filters, setFiltersInternal] = useState<MapFilter>({}) 
  
  // Favorites
  const [favorites, setFavorites] = useState<FavoriteList[]>([])
  const [isFavoritesLoading, setIsFavoritesLoading] = useState(false)
  
  // Saved Views
  const [savedViews, setSavedViews] = useState<SavedView[]>(initialViews)
  const [isSavedViewsLoading, setIsSavedViewsLoading] = useState(false)

  // Fetch locations and projects
  const fetchLocations = useCallback(async () => {
    // Return type updated to match context type
    if (!organization) return null; 

    try {
      setIsLoading(true)
      setError(null)

      // Build query parameters
      const params = new URLSearchParams()
      
      if (filters.locationTypes?.length) {
        params.append("locationTypes", filters.locationTypes.join(","))
      }

      if (filters.projectIds?.length) {
        params.append("projectIds", filters.projectIds.join(","))
      }

      if (filters.searchQuery) {
        params.append("searchQuery", filters.searchQuery)
      }
      
      // Add new filter parameters
      if (filters.status?.length) {
        params.append("status", filters.status.join(","))
      }
      
      if (filters.dateFrom) {
        params.append("dateFrom", filters.dateFrom)
      }
      
      if (filters.dateTo) {
        params.append("dateTo", filters.dateTo)
      }
      
      if (filters.favoriteListId) {
        params.append("favoriteListId", filters.favoriteListId)
      }
      
      if (filters.locationTags?.length) {
        params.append("locationTags", filters.locationTags.join(","))
      }
      
      if (filters.locationFeatures?.length) {
        params.append("locationFeatures", filters.locationFeatures.join(","))
      }
      
      if (filters.permitsRequired !== undefined) {
        params.append("permitsRequired", filters.permitsRequired.toString())
      }
      
      if (filters.hourlyRateMin !== undefined) {
        params.append("hourlyRateMin", filters.hourlyRateMin.toString())
      }
      
      if (filters.hourlyRateMax !== undefined) {
        params.append("hourlyRateMax", filters.hourlyRateMax.toString())
      }
      
      if (filters.dailyRateMin !== undefined) {
        params.append("dailyRateMin", filters.dailyRateMin.toString())
      }
      
      if (filters.dailyRateMax !== undefined) {
        params.append("dailyRateMax", filters.dailyRateMax.toString())
      }

      // Add authentication headers
      const response = await fetch(`/api/map?${params.toString()}`, {
        headers: {
          'X-Stytch-Org-Id': organization.id || '',
          'X-Stytch-Member-Id': user?.member_id || '',
        }
      })

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}))
        throw new Error(errorData.error || "Failed to fetch locations")
      }

      const data = await response.json()
      
      // The API now returns both locations and projects
      setLocations(data.locations || [])
      setProjects(data.projects || [])
      
      return data // Return fetched data
    } catch (err) {
      console.error("Failed to fetch locations:", err)
      const errorMessage = err instanceof Error ? err.message : "Failed to load locations"
      setError(errorMessage)
      toast.error(errorMessage)
      return null
    } finally {
      setIsLoading(false)
    }
  }, [organization, filters, user?.member_id])

  // Function to update filters state and localStorage
  const setFilters = useCallback((newFilters: MapFilter) => {
    if (!organization?.id) return; // Need org id for storage key
    console.log("MapProvider: Setting filters", newFilters);
    setFiltersInternal(newFilters);
    mapStorage.setFilters(organization.id, newFilters);
  }, [organization?.id]); // Depend on org id

  // Effect to load initial filters from storage when organization becomes available
  useEffect(() => {
    if (organization?.id) {
      console.log("MapProvider: Organization available, loading initial filters for", organization.id);
      const storedFilters = mapStorage.getFilters(organization.id);
      if (storedFilters && Object.keys(storedFilters).length > 0) {
         console.log("MapProvider: Found stored filters", storedFilters);
         setFiltersInternal(storedFilters);
      } else {
         console.log("MapProvider: No stored filters found, using default empty object.");
         setFiltersInternal({}); // Ensure it's an empty object if nothing stored
      }
    }
  }, [organization?.id]); // Run only when org id changes
  
  // Fetch favorites
  const fetchFavorites = useCallback(async () => {
    // Return type updated to match context type
    if (!organization) return null; 
    
    try {
      setIsFavoritesLoading(true)
      
      // Add authentication headers
      const response = await fetch(`/api/favorites?organizationId=${organization.id}`, {
        headers: {
          'X-Stytch-Org-Id': organization.id || '',
        }
      })
      
      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}))
        throw new Error(errorData.error || "Failed to fetch favorites")
      }
      
      const data = await response.json()
      setFavorites(data || [])
      
      return data // Return fetched data
    } catch (err) {
      console.error("Failed to fetch favorites:", err)
      // Don't show toast for favorites loading error to avoid UI clutter
      return null
    } finally {
      setIsFavoritesLoading(false)
    }
  }, [organization])
  
  // Fetch saved views
  const fetchSavedViews = useCallback(async () => {
     // Return type updated to match context type
    if (!organization) return null;
    
    try {
      setIsSavedViewsLoading(true)
      
      // Add authentication headers
      const response = await fetch(`/api/map/views?organizationId=${organization.id}`, {
        headers: {
          'X-Stytch-Org-Id': organization.id || '',
        }
      })
      
      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}))
        throw new Error(errorData.error || "Failed to fetch saved views")
      }
      
      const data = await response.json()
      setSavedViews(data || [])
      
      return data // Return fetched data
    } catch (err) {
      console.error("Failed to fetch saved views:", err)
      // Don't show toast for saved views loading error to avoid UI clutter
      return null
    } finally {
      setIsSavedViewsLoading(false)
    }
  }, [organization])
  
  // Create favorite list
  const createFavoriteList = useCallback(async (
    name: string, 
    type: "project" | "scene" | "custom", 
    projectId?: string, 
    sceneId?: string
  ): Promise<FavoriteList | null> => { // Added useCallback and return type
    if (!organization) return null
    
    try {
      const response = await fetch('/api/favorites', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          organizationId: organization.id, name, type, projectId, sceneId,
        }),
      })
      
      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}))
        throw new Error(errorData.error || "Failed to create favorite list")
      }
      
      const data = await response.json()
      await fetchFavorites() // Refresh list after creation
      toast.success("Favorite list created")
      return data
    } catch (err) {
      console.error("Failed to create favorite list:", err)
      const errorMessage = err instanceof Error ? err.message : "Failed to create favorite list"
      toast.error(errorMessage)
      return null
    }
  }, [organization, fetchFavorites]); // Added dependencies
  
  // Add location to favorites
  const addLocationToFavorites = useCallback(async (locationId: string, listId: string): Promise<boolean> => { // Added useCallback and return type
    if (!organization) return false
    
    try {
      const response = await fetch('/api/favorites/add', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          organizationId: organization.id, locationId, listId,
        }),
      })
      
      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}))
        throw new Error(errorData.error || "Failed to add location to favorites")
      }
      
      await fetchFavorites() // Refresh list after adding
      toast.success("Location added to favorites")
      return true
    } catch (err) {
      console.error("Failed to add location to favorites:", err)
      const errorMessage = err instanceof Error ? err.message : "Failed to add location to favorites"
      toast.error(errorMessage)
      return false
    }
  }, [organization, fetchFavorites]); // Added dependencies
  
  // Save current view
  const saveView = useCallback(async (name: string, description?: string): Promise<SavedView | null> => { // Added useCallback and return type
    if (!organization) return null
    
    try {
      const response = await fetch('/api/map/views', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          organizationId: organization.id, name, description, filters, viewSettings,
          selectedLocationIds: selectedLocation ? [selectedLocation.id] : [],
        }),
      })
      
      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}))
        throw new Error(errorData.error || "Failed to save view")
      }
      
      const data = await response.json()
      await fetchSavedViews() // Refresh list after saving
      toast.success("View saved")
      return data
    } catch (err) {
      console.error("Failed to save view:", err)
      const errorMessage = err instanceof Error ? err.message : "Failed to save view"
      toast.error(errorMessage)
      return null
    }
  // Added dependencies
  }, [organization, filters, viewSettings, selectedLocation, fetchSavedViews]); 
  
  // Load saved view
  const loadView = useCallback(async (viewId: string): Promise<boolean> => { // Added useCallback and return type
    const view = savedViews.find(v => v.id === viewId)
    
    if (!view) {
      toast.error("View not found")
      return false
    }
    
    try {
      setViewSettings(view.viewSettings)
      setFilters(view.filters) // Use the memoized setFilters
      // No need to call fetchLocations here, the effect below will trigger it
      toast.success(`View "${view.name}" loaded`)
      return true
    } catch (err) {
      console.error("Failed to load view:", err)
      const errorMessage = err instanceof Error ? err.message : "Failed to load view"
      toast.error(errorMessage)
      return false
    }
  }, [savedViews, setFilters]); // Added dependencies

  // Fetch locations when organization changes
  useEffect(() => {
    if (organization) {
      // fetchLocations is already memoized with filters as a dependency
      fetchLocations() 
    }
  // fetchLocations is stable due to useCallback, filters change triggers refetch via dependency in fetchLocations
  }, [organization, fetchLocations]); 
  
  // Fetch favorites and saved views when organization changes
  useEffect(() => {
    if (organization) {
      fetchFavorites()
      fetchSavedViews()
    }
  }, [organization, fetchFavorites, fetchSavedViews])

  // Memoize the context value
  const contextValue = useMemo(() => ({
      locations,
      projects,
      isLoading,
      error,
      selectedLocation,
      viewSettings,
      clusterSettings,
      filters,
      mapboxToken, // Add mapboxToken to the context
      favorites,
      isFavoritesLoading,
      savedViews,
      isSavedViewsLoading,
      setSelectedLocation,
      setViewSettings,
      setClusterSettings,
      setFilters, 
      refreshLocations: fetchLocations, // Use memoized fetchLocations
      addLocationToFavorites, // Use memoized function
      createFavoriteList, // Use memoized function
      refreshFavorites: fetchFavorites, // Use memoized fetchFavorites
      saveView, // Use memoized function
      loadView, // Use memoized function
      refreshSavedViews: fetchSavedViews, // Use memoized fetchSavedViews
  }), [
      locations, projects, isLoading, error, selectedLocation, viewSettings, 
      clusterSettings, filters, mapboxToken, favorites, isFavoritesLoading, savedViews, 
      isSavedViewsLoading, setFilters, fetchLocations, addLocationToFavorites, 
      createFavoriteList, fetchFavorites, saveView, loadView, fetchSavedViews
      // Removed state setters from dependencies as they are stable
  ]);

  return (
    <MapContext.Provider value={contextValue}>
      {children}
    </MapContext.Provider>
  )
}

export function useMap() {
  const context = useContext(MapContext)
  if (context === undefined) {
    throw new Error("useMap must be used within a MapProvider")
  }
  return context
}
