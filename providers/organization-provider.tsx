"use client"

import { createContext, useEffect, useState, type <PERSON>actNode } from "react"
import { useAuth } from "./auth-provider"
import { useStytchMember } from "@stytch/nextjs/b2b"

type Organization = {
  id: string
  name: string
  slug: string
}

type OrganizationContextType = {
  organization: Organization | null
  setOrganization: (organization: Organization) => void
  organizations: Organization[]
  isLoading: boolean
  error: string | null
}

export const OrganizationContext = createContext<OrganizationContextType | undefined>(undefined)

export function OrganizationProvider({ children }: { children: ReactNode }) {
  const [organization, setOrganization] = useState<Organization | null>(null)
  const [organizations, setOrganizations] = useState<Organization[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  
  const { user } = useAuth()
  const { member, isInitialized: isMemberInitialized } = useStytchMember()

  // Load organizations when the user is authenticated
  useEffect(() => {
    // Skip if there's no user or member is not initialized
    if (!user || !isMemberInitialized) {
      if (isMemberInitialized && !member) {
        // Reset state when user is not available
        setOrganizations([])
        setOrganization(null)
        setIsLoading(false)
      }
      return
    }
    
    async function loadOrganizations() {
      try {
        setIsLoading(true)

        // Fetch organizations from the API
        const response = await fetch("/api/organizations")
        
        if (!response.ok) {
          throw new Error("Failed to fetch organizations")
        }
        
        const data = await response.json()
        
        if (data.success && Array.isArray(data.data)) {
          setOrganizations(data.data)
          
          // Get current organization from cookie or user data
          const currentOrgId = user?.organizationId || 
                              document.cookie.split('; ')
                                .find(row => row.startsWith('current_organization='))
                                ?.split('=')[1]
          
          if (currentOrgId) {
            const currentOrg = data.data.find((org: Organization) => org.id === currentOrgId)
            if (currentOrg) {
              setOrganization(currentOrg)
            } else if (data.data.length > 0) {
              setOrganization(data.data[0])
            }
          } else if (data.data.length > 0) {
            setOrganization(data.data[0])
          }
          
          setError(null)
        } else {
          throw new Error("Invalid response format")
        }
      } catch (err) {
        console.error("Error loading organizations:", err)
        setError("Failed to load organizations")
      } finally {
        setIsLoading(false)
      }
    }

    loadOrganizations()
  }, [user, member, isMemberInitialized])

  // Handle organization switching
  const handleSetOrganization = async (org: Organization) => {
    try {
      // Call the API to switch organizations
      const response = await fetch(`/api/organizations/${org.id}/switch`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
      })
      
      if (!response.ok) {
        throw new Error("Failed to switch organization")
      }
      
      // Update the local state
      setOrganization(org)
    } catch (err) {
      console.error("Error switching organization:", err)
      setError("Failed to switch organization")
    }
  }

  return (
    <OrganizationContext.Provider
      value={{
        organization,
        setOrganization: handleSetOrganization,
        organizations,
        isLoading,
        error,
      }}
    >
      {children}
    </OrganizationContext.Provider>
  )
}
