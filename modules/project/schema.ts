import { relations } from "drizzle-orm";
import { pgTable, uuid, varchar, text, timestamp, jsonb, boolean, pgEnum } from "drizzle-orm/pg-core"; // Added pgEnum
import { createInsertSchema, createSelectSchema } from "drizzle-zod";

import { organizations } from "../organization/schema";
import { locations } from "../location/schema"
import { documents } from "../document/schema"
import { tasks } from "../task/schema";
import { users } from "../user/schema";

// Define project status enum values
export const PROJECT_STATUS = ["active", "draft", "planning", "on-hold", "completed", "archived"] as const;
export const projectStatusEnum = pgEnum("project_status", PROJECT_STATUS);

export const projects = pgTable("projects", {
  id: uuid("id").primaryKey().defaultRandom(),
  name: varchar("name", { length: 255 }).notNull(),
  description: text("description"),
  status: projectStatusEnum("status").notNull(), // Use pgEnum here
  organizationId: uuid("organization_id")
    .references(() => organizations.id, { onDelete: "cascade" })
    .notNull(),
  createdBy: uuid("created_by").references(() => users.id),
  startDate: timestamp("start_date"),
  endDate: timestamp("end_date"),
  budget: jsonb("budget"),
  clientName: varchar("client_name", { length: 255 }),
  clientContact: varchar("client_contact", { length: 255 }),
  productionType: varchar("production_type", { length: 100 }),
  metadata: jsonb("metadata").$type<Record<string, unknown>>(), // Changed any to unknown
  isArchived: boolean("is_archived").default(false),
  createdAt: timestamp("created_at").defaultNow().notNull(),
  updatedAt: timestamp("updated_at").defaultNow().notNull(),
  deletedAt: timestamp("deleted_at"),
})

export const projectsRelations = relations(projects, ({ one, many }) => ({
  organization: one(organizations, {
    fields: [projects.organizationId],
    references: [organizations.id],
  }),
  creator: one(users, {
    fields: [projects.createdBy],
    references: [users.id],
  }),
  locations: many(locations),
  documents: many(documents),
  tasks: many(tasks),
  members: many(projectUserRoles),
}))

export const projectUserRoles = pgTable("project_user_roles", {
  id: uuid("id").primaryKey().defaultRandom(),
  projectId: uuid("project_id")
    .references(() => projects.id, { onDelete: "cascade" })
    .notNull(),
  userId: uuid("user_id")
    .references(() => users.id, { onDelete: "cascade" })
    .notNull(),
  role: varchar("role", { length: 50 }).notNull(),
  createdAt: timestamp("created_at").defaultNow().notNull(),
  updatedAt: timestamp("updated_at").defaultNow().notNull(),
})

export const projectUserRolesRelations = relations(projectUserRoles, ({ one }) => ({
  project: one(projects, {
    fields: [projectUserRoles.projectId],
    references: [projects.id],
  }),
  user: one(users, {
    fields: [projectUserRoles.userId],
    references: [users.id],
  }),
}))

// Zod schemas for validation
export const insertProjectSchema = createInsertSchema(projects)
export const selectProjectSchema = createSelectSchema(projects)

export const createProjectSchema = insertProjectSchema.omit({
  id: true,
  createdAt: true,
  updatedAt: true,
  deletedAt: true,
})

export const updateProjectSchema = createProjectSchema.partial()

export const projectIdSchema = selectProjectSchema.pick({ id: true })

export const insertProjectUserRoleSchema = createInsertSchema(projectUserRoles)
export const selectProjectUserRoleSchema = createSelectSchema(projectUserRoles)

export const createProjectUserRoleSchema = insertProjectUserRoleSchema.omit({
  id: true,
  createdAt: true,
  updatedAt: true,
})
