import { db } from "@/lib/db";
import { projects, projectUserRoles } from "./schema";
import { eq, and, isNull, or, ilike } from "drizzle-orm"; // Added or, ilike
import { NotFoundError } from "../shared/errors";
import { type Project, createProjectSchema, updateProjectSchema } from "./model";

/**
 * Get projects for an organization, optionally filtering by search query.
 * @param organizationId The ID of the organization.
 * @param searchQuery Optional search string to filter projects by name or description.
 * @returns A promise resolving to an array of projects.
 */
export async function getProjects(organizationId: string, searchQuery?: string) {
  const conditions = [
    eq(projects.organizationId, organizationId),
    isNull(projects.deletedAt),
  ];

  if (searchQuery) {
    const searchPattern = `%${searchQuery}%`;
    conditions.push(
      or(
        ilike(projects.name, searchPattern),
        ilike(projects.description, searchPattern) 
      ) ?? eq(projects.id, '') // Add dummy condition if or returns undefined/null
    );
  }

  return db.query.projects.findMany({
    where: and(...conditions),
    orderBy: projects.createdAt,
  });
}

export async function getProject(id: string) {
  const project = await db.query.projects.findFirst({
    where: and(eq(projects.id, id), isNull(projects.deletedAt)),
    with: {
      members: {
        with: {
          user: true,
        },
      },
    },
  })

  if (!project) {
    throw new NotFoundError(`Project with ID ${id} not found`)
  }

  return project
}

export async function createProject(
  data: Omit<Project, "id" | "createdAt" | "updatedAt" | "deletedAt" | "isArchived">,
) {
  const validatedData = createProjectSchema.parse(data)

  const [project] = await db
    .insert(projects)
    .values({
      ...validatedData,
      isArchived: false,
    })
    .returning()

  return project
}

export async function updateProject(
  id: string,
  data: Partial<Omit<Project, "id" | "createdAt" | "updatedAt" | "deletedAt">>,
) {
  const validatedData = updateProjectSchema.parse(data)

  const [project] = await db
    .update(projects)
    .set({
      ...validatedData,
      updatedAt: new Date(),
    })
    .where(and(eq(projects.id, id), isNull(projects.deletedAt)))
    .returning()

  if (!project) {
    throw new NotFoundError(`Project with ID ${id} not found`)
  }

  return project
}

export async function deleteProject(id: string) {
  const [project] = await db
    .update(projects)
    .set({
      deletedAt: new Date(),
      updatedAt: new Date(),
    })
    .where(and(eq(projects.id, id), isNull(projects.deletedAt)))
    .returning()

  if (!project) {
    throw new NotFoundError(`Project with ID ${id} not found`)
  }

  return project
}

export async function addUserToProject(projectId: string, userId: string, role: string) {
  const [projectUserRole] = await db
    .insert(projectUserRoles)
    .values({
      projectId,
      userId,
      role,
    })
    .returning()

  return projectUserRole
}

export async function removeUserFromProject(projectId: string, userId: string) {
  const [projectUserRole] = await db
    .delete(projectUserRoles)
    .where(and(eq(projectUserRoles.projectId, projectId), eq(projectUserRoles.userId, userId)))
    .returning()

  if (!projectUserRole) {
    throw new NotFoundError(`User with ID ${userId} not found in project with ID ${projectId}`)
  }

  return projectUserRole
}
