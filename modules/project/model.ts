import { z } from "zod";
// import { PROJECT_STATUSES, USER_ROLES } from "../shared/constants"; // Removed old import
import { PROJECT_STATUS } from "./schema"; // Import from schema file
import { USER_ROLES } from "../shared/constants"; // Keep USER_ROLES import

// Project domain models
export type Project = {
  id: string
  name: string;
  description?: string;
  status: ProjectStatus; // Use the type derived below
  organizationId: string;
  createdBy?: string;
  startDate?: Date
  endDate?: Date
  budget?: number
  clientName?: string
  clientContact?: string;
  productionType?: string;
  metadata?: Record<string, unknown>; // Changed any to unknown
  isArchived: boolean;
  createdAt: Date;
  updatedAt: Date
  deletedAt?: Date | null
}

export type ProjectUserRole = {
  id: string
  projectId: string
  userId: string
  role: UserRole
  createdAt: Date
  updatedAt: Date;
};

export type ProjectWithMembers = Project & {
  members: Array<{
    userId: string
    role: UserRole
    user: {
      id: string
      name?: string
      email: string
      avatar?: string
    }
  }>;
};

export type ProjectStatus = (typeof PROJECT_STATUS)[number]; // Use PROJECT_STATUS from schema
export type UserRole = (typeof USER_ROLES)[number];

// Validation schemas
export const projectSchema = z.object({
  id: z.string().uuid(),
  name: z.string().min(1).max(255),
  description: z.string().optional(),
  status: z.enum(PROJECT_STATUS), // Use PROJECT_STATUS from schema
  organizationId: z.string().uuid(),
  createdBy: z.string().uuid().optional(),
  startDate: z.date().optional(),
  endDate: z.date().optional(),
  budget: z.number().optional(),
  clientName: z.string().optional(),
  clientContact: z.string().optional(),
  productionType: z.string().optional(),
  metadata: z.record(z.unknown()).optional(), // Changed any to unknown
  isArchived: z.boolean(),
  createdAt: z.date(),
  updatedAt: z.date(),
  deletedAt: z.date().nullable().optional(),
})

export const projectUserRoleSchema = z.object({
  id: z.string().uuid(),
  projectId: z.string().uuid(),
  userId: z.string().uuid(),
  role: z.enum(USER_ROLES), // Removed type assertion
  createdAt: z.date(),
  updatedAt: z.date(),
})

export const createProjectSchema = z.object({
  name: z.string().min(1).max(255),
  description: z.string().optional(),
  status: z.enum(PROJECT_STATUS), // Use PROJECT_STATUS from schema
  organizationId: z.string().uuid(),
  startDate: z.date().optional(),
  endDate: z.date().optional(),
  budget: z.number().optional(),
  clientName: z.string().optional(),
  clientContact: z.string().optional(),
  productionType: z.string().optional(),
  metadata: z.record(z.unknown()).optional(), // Changed any to unknown
});

export const updateProjectSchema = createProjectSchema.partial()

export const addUserToProjectSchema = z.object({
  userId: z.string().uuid(),
  role: z.enum(USER_ROLES), // Removed type assertion
});
