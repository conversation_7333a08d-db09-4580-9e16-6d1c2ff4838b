import { sql } from "drizzle-orm"

// Custom PostGIS types
export const point = (columnName: string) => sql`GEOGRAPHY(POINT, 4326)`.as(columnName)
export const polygon = (columnName: string) => sql`GEOGRAPHY(POLYGON, 4326)`.as(columnName)

// Helper functions for working with PostGIS
export function createPoint(latitude: number, longitude: number) {
  return sql`ST_SetSRID(ST_MakePoint(${longitude}, ${latitude}), 4326)::geography`
}

export function createPolygon(coordinates: [number, number][]) {
  // Format: 'POLYGON((lon1 lat1, lon2 lat2, ...))'
  const pointsString = coordinates.map(([lon, lat]) => `${lon} ${lat}`).join(", ")
  return sql`ST_SetSRID(ST_GeomFromText('POLYGON((${pointsString}))'), 4326)::geography`
}

export function getLatitude(point: any) {
  return sql`ST_Y(${point}::geometry)`
}

export function getLongitude(point: any) {
  return sql`ST_X(${point}::geometry)`
}

// Calculate distance between two points in meters
export function calculateDistance(point1: any, point2: any) {
  return sql`ST_Distance(${point1}, ${point2})`
}

// Find locations within a certain distance (in meters)
export function findLocationsWithinDistance(point: any, distance: number) {
  return sql`ST_DWithin(coordinates, ${point}, ${distance})`
}

// Check if a point is within a polygon
export function isPointInPolygon(point: any, polygon: any) {
  return sql`ST_Contains(${polygon}::geometry, ${point}::geometry)`
}
