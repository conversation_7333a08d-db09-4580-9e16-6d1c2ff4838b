import { z } from "zod"

export function validate<T extends z.ZodTypeAny>(schema: T, data: unknown): z.infer<T> {
  return schema.parse(data)
}

export const addressSchema = z.object({
  street: z.string(),
  city: z.string(),
  state: z.string(),
  postalCode: z.string(),
  country: z.string(),
  formatted: z.string().optional(),
})

export const coordinatesSchema = z.object({
  latitude: z.number(),
  longitude: z.number(),
})
