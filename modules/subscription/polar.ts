import { Polar } from "@polar-sh/sdk"

// Initialize Polar client
let polarClient: Polar | null = null

/**
 * Get the Polar client instance
 * @returns The Polar client
 */
export function getPolarClient(): Polar {
  if (!polarClient) {
    const accessToken = process.env.POLAR_ACCESS_TOKEN
    const server = process.env.POLAR_SERVER || "https://api.polar.sh"

    if (!accessToken) {
      throw new Error("Missing POLAR_ACCESS_TOKEN environment variable")
    }

    polarClient = new Polar({
      accessToken,
      server,
    })
  }

  return polarClient
}

/**
 * Get a customer by ID
 * @param customerId Polar customer ID
 * @returns The customer or null if not found
 */
export async function getCustomer(customerId: string) {
  try {
    const client = getPolarClient()
    return await client.customers.get(customerId)
  } catch (error) {
    console.error("Error getting Polar customer:", error)
    return null
  }
}

/**
 * Create a new customer
 * @param email Customer email
 * @param name Customer name
 * @returns The created customer or null if creation failed
 */
export async function createCustomer(email: string, name?: string) {
  try {
    const client = getPolarClient()
    return await client.customers.create({
      email,
      name,
    })
  } catch (error) {
    console.error("Error creating Polar customer:", error)
    return null
  }
}

/**
 * Get a subscription by ID
 * @param subscriptionId Polar subscription ID
 * @returns The subscription or null if not found
 */
export async function getSubscription(subscriptionId: string) {
  try {
    const client = getPolarClient()
    return await client.subscriptions.get(subscriptionId)
  } catch (error) {
    console.error("Error getting Polar subscription:", error)
    return null
  }
}

/**
 * Get all subscriptions for a customer
 * @param customerId Polar customer ID
 * @returns Array of subscriptions or empty array if none found
 */
export async function getCustomerSubscriptions(customerId: string) {
  try {
    const client = getPolarClient()
    const response = await client.subscriptions.list({
      customer_id: customerId,
    })
    return response.items || []
  } catch (error) {
    console.error("Error getting Polar customer subscriptions:", error)
    return []
  }
}

/**
 * Cancel a subscription
 * @param subscriptionId Polar subscription ID
 * @param cancelAtPeriodEnd Whether to cancel at the end of the current period
 * @returns The updated subscription or null if cancellation failed
 */
export async function cancelSubscription(subscriptionId: string, cancelAtPeriodEnd = true) {
  try {
    const client = getPolarClient()
    return await client.subscriptions.cancel(subscriptionId, {
      cancel_at_period_end: cancelAtPeriodEnd,
    })
  } catch (error) {
    console.error("Error canceling Polar subscription:", error)
    return null
  }
}

/**
 * Get an invoice by ID
 * @param invoiceId Polar invoice ID
 * @returns The invoice or null if not found
 */
export async function getInvoice(invoiceId: string) {
  try {
    const client = getPolarClient()
    return await client.invoices.get(invoiceId)
  } catch (error) {
    console.error("Error getting Polar invoice:", error)
    return null
  }
}

/**
 * Get all invoices for a customer
 * @param customerId Polar customer ID
 * @returns Array of invoices or empty array if none found
 */
export async function getCustomerInvoices(customerId: string) {
  try {
    const client = getPolarClient()
    const response = await client.invoices.list({
      customer_id: customerId,
    })
    return response.items || []
  } catch (error) {
    console.error("Error getting Polar customer invoices:", error)
    return []
  }
}

/**
 * Create a checkout session for a subscription
 * @param customerId Polar customer ID
 * @param planId Polar plan ID
 * @param successUrl URL to redirect to on successful checkout
 * @param cancelUrl URL to redirect to on cancelled checkout
 * @returns The checkout session URL or null if creation failed
 */
export async function createCheckoutSession(
  customerId: string,
  planId: string,
  successUrl: string,
  cancelUrl: string
) {
  try {
    const client = getPolarClient()
    const session = await client.checkout.createSession({
      customer_id: customerId,
      plan_id: planId,
      success_url: successUrl,
      cancel_url: cancelUrl,
    })
    return session.url
  } catch (error) {
    console.error("Error creating Polar checkout session:", error)
    return null
  }
}

/**
 * Create a customer portal session
 * @param customerId Polar customer ID
 * @param returnUrl URL to return to after the portal session
 * @returns The portal session URL or null if creation failed
 */
export async function createPortalSession(customerId: string, returnUrl: string) {
  try {
    const client = getPolarClient()
    const session = await client.portal.createSession({
      customer_id: customerId,
      return_url: returnUrl,
    })
    return session.url
  } catch (error) {
    console.error("Error creating Polar portal session:", error)
    return null
  }
}
