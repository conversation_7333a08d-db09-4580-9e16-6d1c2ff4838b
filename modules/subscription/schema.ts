import { relations } from "drizzle-orm";
import { pgTable, uuid, varchar, timestamp, jsonb, boolean, integer, pgEnum } from "drizzle-orm/pg-core"; // Added pgEnum
import { createInsertSchema, createSelectSchema } from "drizzle-zod";

import { organizations } from "../organization/schema";

// Subscription status enum values
export const SUBSCRIPTION_STATUS = ["active", "canceled", "incomplete", "incomplete_expired", "past_due", "trialing", "unpaid"] as const;

// Payment status enum values
export const PAYMENT_STATUS = ["pending", "paid", "failed", "refunded"] as const;

// Subscription tiers
export const SUBSCRIPTION_TIERS = ["basic", "professional", "enterprise"] as const;

// Define pgEnums
export const subscriptionTierEnum = pgEnum("subscription_tier", SUBSCRIPTION_TIERS);
export const subscriptionStatusEnum = pgEnum("subscription_status", SUBSCRIPTION_STATUS); // Added enum for subscription status
export const paymentStatusEnum = pgEnum("payment_status", PAYMENT_STATUS); // Added enum for payment status

// Subscriptions Table (for Polar integration)
export const subscriptions = pgTable("subscriptions", {
  id: uuid("id").primaryKey().defaultRandom(),
  organizationId: uuid("organization_id")
    .references(() => organizations.id, { onDelete: "cascade" })
    .notNull(),
  polarSubscriptionId: varchar("polar_subscription_id", { length: 255 }).unique().notNull(),
  tier: subscriptionTierEnum("tier").notNull(),
  status: subscriptionStatusEnum("status").notNull(), // Use pgEnum for subscription status
  currentPeriodStart: timestamp("current_period_start").notNull(),
  currentPeriodEnd: timestamp("current_period_end").notNull(),
  cancelAtPeriodEnd: boolean("cancel_at_period_end").default(false).notNull(), // Added notNull()
  createdAt: timestamp("created_at").defaultNow().notNull(),
  updatedAt: timestamp("updated_at").defaultNow().notNull(),
  metadata: jsonb("metadata").$type<Record<string, unknown>>().notNull().default({}), // Added notNull() and a default value
  quantity: integer("quantity").default(1).notNull() // Added notNull()
})

// Subscription Invoices Table
export const subscriptionInvoices = pgTable("subscription_invoices", {
  id: uuid("id").primaryKey().defaultRandom(),
  organizationId: uuid("organization_id")
    .references(() => organizations.id, { onDelete: "cascade" })
    .notNull(),
  subscriptionId: uuid("subscription_id")
    .references(() => subscriptions.id, { onDelete: "set null" }),
  polarInvoiceId: varchar("polar_invoice_id", { length: 255 }).unique().notNull(),
  amount: integer("amount").notNull(), // in cents
  currency: varchar("currency", { length: 10 }).default("usd").notNull(), // Added notNull()
  status: paymentStatusEnum("status").notNull(), // Use pgEnum for payment status
  invoiceUrl: varchar("invoice_url", { length: 255 }),
  invoiceNumber: varchar("invoice_number", { length: 100 }),
  invoiceDate: timestamp("invoice_date").notNull(),
  paidAt: timestamp("paid_at")
})

// Relations
export const subscriptionsRelations = relations(subscriptions, ({ one, many }) => ({
  organization: one(organizations, {
    fields: [subscriptions.organizationId],
    references: [organizations.id],
  }),
  invoices: many(subscriptionInvoices)
}))

export const subscriptionInvoicesRelations = relations(subscriptionInvoices, ({ one }) => ({
  organization: one(organizations, {
    fields: [subscriptionInvoices.organizationId],
    references: [organizations.id],
  }),
  subscription: one(subscriptions, {
    fields: [subscriptionInvoices.subscriptionId],
    references: [subscriptions.id],
  })
}))

// Zod schemas for validation
export const insertSubscriptionSchema = createInsertSchema(subscriptions)
export const selectSubscriptionSchema = createSelectSchema(subscriptions)

export const createSubscriptionSchema = insertSubscriptionSchema.omit({
  id: true,
  createdAt: true,
  updatedAt: true,
})

export const updateSubscriptionSchema = createSubscriptionSchema.partial()

export const insertSubscriptionInvoiceSchema = createInsertSchema(subscriptionInvoices)
export const selectSubscriptionInvoiceSchema = createSelectSchema(subscriptionInvoices)

export const createSubscriptionInvoiceSchema = insertSubscriptionInvoiceSchema.omit({
  id: true,
  // createdAt: true, // Removed non-existent field
  // updatedAt: true, // Removed non-existent field
});
