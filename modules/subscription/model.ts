import { z } from "zod"
import { SUBSCRIPTION_STATUS, SUBSCRIPTION_TIERS, PAYMENT_STATUS } from "./schema"

// Subscription domain models
export type Subscription = {
  id: string
  organizationId: string
  polarSubscriptionId: string
  tier: SubscriptionTier
  status: SubscriptionStatus
  currentPeriodStart: Date
  currentPeriodEnd: Date
  cancelAtPeriodEnd: boolean
  createdAt: Date
  updatedAt: Date
  metadata?: Record<string, unknown>
  quantity: number
}

export type SubscriptionInvoice = {
  id: string;
  organizationId: string;
  subscriptionId: string | null; // Changed from optional string to nullable string
  polarInvoiceId: string;
  amount: number;
  currency: string;
  status: PaymentStatus;
  invoiceUrl: string | null; // Changed from optional string to nullable string
  invoiceNumber: string | null; // Changed from optional string to nullable string
  invoiceDate: Date;
  paidAt: Date | null; // Changed from optional Date to nullable Date
};

export type SubscriptionTier = (typeof SUBSCRIPTION_TIERS)[number]
export type SubscriptionStatus = (typeof SUBSCRIPTION_STATUS)[number]
export type PaymentStatus = (typeof PAYMENT_STATUS)[number]

// Validation schemas
export const subscriptionSchema = z.object({
  id: z.string().uuid(),
  organizationId: z.string().uuid(),
  polarSubscriptionId: z.string(),
  tier: z.enum(SUBSCRIPTION_TIERS as unknown as [string, ...string[]]),
  status: z.enum(SUBSCRIPTION_STATUS as unknown as [string, ...string[]]),
  currentPeriodStart: z.date(),
  currentPeriodEnd: z.date(),
  cancelAtPeriodEnd: z.boolean(),
  createdAt: z.date(),
  updatedAt: z.date(),
  metadata: z.record(z.unknown()).optional(),
  quantity: z.number(),
})

export const subscriptionInvoiceSchema = z.object({
  id: z.string().uuid(),
  organizationId: z.string().uuid(),
  subscriptionId: z.string().uuid().nullable(), // Changed from optional() to nullable()
  polarInvoiceId: z.string(),
  amount: z.number(),
  currency: z.string(),
  status: z.enum(PAYMENT_STATUS as unknown as [string, ...string[]]),
  invoiceUrl: z.string().nullable(), // Changed from optional() to nullable()
  invoiceNumber: z.string().nullable(), // Changed from optional() to nullable()
  invoiceDate: z.date(),
  paidAt: z.date().nullable(), // Changed from optional() to nullable()
});

export const createSubscriptionSchema = z.object({
  organizationId: z.string().uuid(),
  polarSubscriptionId: z.string(),
  tier: z.enum(SUBSCRIPTION_TIERS as unknown as [string, ...string[]]),
  status: z.enum(SUBSCRIPTION_STATUS as unknown as [string, ...string[]]),
  currentPeriodStart: z.date(),
  currentPeriodEnd: z.date(),
  cancelAtPeriodEnd: z.boolean().optional(),
  metadata: z.record(z.unknown()).optional(),
  quantity: z.number().optional(),
})

export const updateSubscriptionSchema = createSubscriptionSchema.partial()

export const createSubscriptionInvoiceSchema = z.object({
  organizationId: z.string().uuid(),
  subscriptionId: z.string().uuid().nullable(), // Changed from optional() to nullable()
  polarInvoiceId: z.string(),
  amount: z.number(),
  currency: z.string().optional(),
  status: z.enum(PAYMENT_STATUS as unknown as [string, ...string[]]),
  invoiceUrl: z.string().nullable(), // Changed from optional() to nullable()
  invoiceNumber: z.string().nullable(), // Changed from optional() to nullable()
  invoiceDate: z.date(),
  paidAt: z.date().nullable(), // Changed from optional() to nullable()
});
