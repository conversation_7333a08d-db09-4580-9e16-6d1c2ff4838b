import { db } from "@/lib/db"
import { subscriptions, subscriptionInvoices } from "./schema"
import { eq } from "drizzle-orm"
import type { Subscription, SubscriptionInvoice } from "./model"

/**
 * Get a subscription by ID
 * @param id Subscription ID
 * @returns The subscription or null if not found
 */
export async function getSubscriptionById(id: string): Promise<Subscription | null> {
  const result = await db.select().from(subscriptions).where(eq(subscriptions.id, id)).limit(1)
  return result.length > 0 ? result[0] : null
}

/**
 * Get subscriptions for an organization
 * @param organizationId Organization ID
 * @returns Array of subscriptions
 */
export async function getSubscriptionsByOrganizationId(organizationId: string): Promise<Subscription[]> {
  return db.select().from(subscriptions).where(eq(subscriptions.organizationId, organizationId))
}

/**
 * Get a subscription by Polar subscription ID
 * @param polarSubscriptionId Polar subscription ID
 * @returns The subscription or null if not found
 */
export async function getSubscriptionByPolarId(polarSubscriptionId: string): Promise<Subscription | null> {
  const result = await db
    .select()
    .from(subscriptions)
    .where(eq(subscriptions.polarSubscriptionId, polarSubscriptionId))
    .limit(1)
  return result.length > 0 ? result[0] : null
}

/**
 * Create a new subscription
 * @param data Subscription data
 * @returns The created subscription
 */
export async function createSubscription(data: Omit<Subscription, "id" | "createdAt" | "updatedAt">): Promise<Subscription> {
  const result = await db.insert(subscriptions).values(data).returning()
  return result[0]
}

/**
 * Update a subscription
 * @param id Subscription ID
 * @param data Partial subscription data to update
 * @returns The updated subscription
 */
export async function updateSubscription(
  id: string,
  data: Partial<Omit<Subscription, "id" | "createdAt" | "updatedAt">>
): Promise<Subscription | null> {
  const result = await db.update(subscriptions).set(data).where(eq(subscriptions.id, id)).returning()
  return result.length > 0 ? result[0] : null
}

/**
 * Delete a subscription
 * @param id Subscription ID
 * @returns True if deleted, false if not found
 */
export async function deleteSubscription(id: string): Promise<boolean> {
  const result = await db.delete(subscriptions).where(eq(subscriptions.id, id)).returning({ id: subscriptions.id })
  return result.length > 0
}

/**
 * Get an invoice by ID
 * @param id Invoice ID
 * @returns The invoice or null if not found
 */
export async function getInvoiceById(id: string): Promise<SubscriptionInvoice | null> {
  const result = await db.select().from(subscriptionInvoices).where(eq(subscriptionInvoices.id, id)).limit(1)
  return result.length > 0 ? result[0] : null
}

/**
 * Get invoices for a subscription
 * @param subscriptionId Subscription ID
 * @returns Array of invoices
 */
export async function getInvoicesBySubscriptionId(subscriptionId: string): Promise<SubscriptionInvoice[]> {
  return db.select().from(subscriptionInvoices).where(eq(subscriptionInvoices.subscriptionId, subscriptionId))
}

/**
 * Get invoices for an organization
 * @param organizationId Organization ID
 * @returns Array of invoices
 */
export async function getInvoicesByOrganizationId(organizationId: string): Promise<SubscriptionInvoice[]> {
  return db.select().from(subscriptionInvoices).where(eq(subscriptionInvoices.organizationId, organizationId))
}

/**
 * Get an invoice by Polar invoice ID
 * @param polarInvoiceId Polar invoice ID
 * @returns The invoice or null if not found
 */
export async function getInvoiceByPolarId(polarInvoiceId: string): Promise<SubscriptionInvoice | null> {
  const result = await db
    .select()
    .from(subscriptionInvoices)
    .where(eq(subscriptionInvoices.polarInvoiceId, polarInvoiceId))
    .limit(1)
  return result.length > 0 ? result[0] : null
}

/**
 * Create a new invoice
 * @param data Invoice data
 * @returns The created invoice
 */
export async function createInvoice(data: Omit<SubscriptionInvoice, "id">): Promise<SubscriptionInvoice> {
  const result = await db.insert(subscriptionInvoices).values(data).returning()
  return result[0]
}

/**
 * Update an invoice
 * @param id Invoice ID
 * @param data Partial invoice data to update
 * @returns The updated invoice
 */
export async function updateInvoice(
  id: string,
  data: Partial<Omit<SubscriptionInvoice, "id">>
): Promise<SubscriptionInvoice | null> {
  const result = await db.update(subscriptionInvoices).set(data).where(eq(subscriptionInvoices.id, id)).returning()
  return result.length > 0 ? result[0] : null
}

/**
 * Delete an invoice
 * @param id Invoice ID
 * @returns True if deleted, false if not found
 */
export async function deleteInvoice(id: string): Promise<boolean> {
  const result = await db.delete(subscriptionInvoices).where(eq(subscriptionInvoices.id, id)).returning({ id: subscriptionInvoices.id })
  return result.length > 0
}
