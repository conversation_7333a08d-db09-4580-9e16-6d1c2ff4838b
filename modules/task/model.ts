import { z } from "zod"
import { TASK_PRIORITIES, TASK_STATUSES } from "../shared/constants"

// Task domain models
export type Task = {
  id: string
  title: string
  description?: string
  status: TaskStatus
  priority: TaskPriority
  dueDate?: Date
  projectId?: string
  locationId?: string
  organizationId: string
  assignedToId?: string
  createdById?: string
  metadata?: Record<string, any>
  isCompleted: boolean
  completedAt?: Date
  createdAt: Date
  updatedAt: Date
  deletedAt?: Date | null
}

export type TaskStatus = (typeof TASK_STATUSES)[number]
export type TaskPriority = (typeof TASK_PRIORITIES)[number]

// Validation schemas
export const taskSchema = z.object({
  id: z.string().uuid(),
  title: z.string().min(1).max(255),
  description: z.string().optional(),
  status: z.enum(TASK_STATUSES as [string, ...string[]]),
  priority: z.enum(TASK_PRIORITIES as [string, ...string[]]),
  dueDate: z.date().optional(),
  projectId: z.string().uuid().optional(),
  locationId: z.string().uuid().optional(),
  organizationId: z.string().uuid(),
  assignedToId: z.string().uuid().optional(),
  createdById: z.string().uuid().optional(),
  metadata: z.record(z.any()).optional(),
  isCompleted: z.boolean(),
  completedAt: z.date().optional(),
  createdAt: z.date(),
  updatedAt: z.date(),
  deletedAt: z.date().nullable().optional(),
})

export const createTaskSchema = z.object({
  title: z.string().min(1).max(255),
  description: z.string().optional(),
  status: z.enum(TASK_STATUSES as [string, ...string[]]),
  priority: z.enum(TASK_PRIORITIES as [string, ...string[]]),
  dueDate: z.date().optional(),
  projectId: z.string().uuid().optional(),
  locationId: z.string().uuid().optional(),
  organizationId: z.string().uuid(),
  assignedToId: z.string().uuid().optional(),
  metadata: z.record(z.any()).optional(),
})

export const updateTaskSchema = createTaskSchema.partial()
