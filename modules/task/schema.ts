import { relations } from "drizzle-orm"
import { pgTable, uuid, varchar, text, timestamp, jsonb, boolean, date } from "drizzle-orm/pg-core"
import { createInsertSchema, createSelectSchema } from "drizzle-zod"

import { projects } from "../project/schema"
import { locations } from "../location/schema"
import { organizations } from "../organization/schema"
import { users } from "../user/schema"

export const tasks = pgTable("tasks", {
  id: uuid("id").primaryKey().defaultRandom(),
  title: varchar("title", { length: 255 }).notNull(),
  description: text("description"),
  status: varchar("status", { length: 50 }).notNull(),
  priority: varchar("priority", { length: 50 }).notNull(),
  dueDate: date("due_date"),
  projectId: uuid("project_id").references(() => projects.id, { onDelete: "cascade" }),
  locationId: uuid("location_id").references(() => locations.id, { onDelete: "cascade" }),
  organizationId: uuid("organization_id")
    .references(() => organizations.id, { onDelete: "cascade" })
    .notNull(),
  assignedToId: uuid("assigned_to_id").references(() => users.id),
  createdById: uuid("created_by_id").references(() => users.id),
  metadata: jsonb("metadata").$type<Record<string, any>>(),
  isCompleted: boolean("is_completed").default(false),
  completedAt: timestamp("completed_at"),
  createdAt: timestamp("created_at").defaultNow().notNull(),
  updatedAt: timestamp("updated_at").defaultNow().notNull(),
  deletedAt: timestamp("deleted_at"),
})

export const tasksRelations = relations(tasks, ({ one }) => ({
  project: one(projects, {
    fields: [tasks.projectId],
    references: [projects.id],
    relationName: "project_tasks",
  }),
  location: one(locations, {
    fields: [tasks.locationId],
    references: [locations.id],
    relationName: "location_tasks",
  }),
  organization: one(organizations, {
    fields: [tasks.organizationId],
    references: [organizations.id],
    relationName: "organization_tasks",
  }),
  assignedTo: one(users, {
    fields: [tasks.assignedToId],
    references: [users.id],
    relationName: "assigned_tasks",
  }),
  createdBy: one(users, {
    fields: [tasks.createdById],
    references: [users.id],
    relationName: "created_tasks",
  }),
}))

// Zod schemas for validation
export const insertTaskSchema = createInsertSchema(tasks)
export const selectTaskSchema = createSelectSchema(tasks)

export const createTaskSchema = insertTaskSchema.omit({
  id: true,
  completedAt: true,
  createdAt: true,
  updatedAt: true,
  deletedAt: true,
})

export const updateTaskSchema = createTaskSchema.partial()

export const taskIdSchema = selectTaskSchema.pick({ id: true })
