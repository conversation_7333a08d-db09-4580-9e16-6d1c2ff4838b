import { z } from "zod"

// Location Media domain models
export type LocationMedia = {
  id: string
  locationId: string
  url: string
  thumbnailUrl?: string
  type: string
  title?: string
  description?: string
  uploadedBy?: string
  uploadedAt: Date
  metadata?: Record<string, any>
  fileSize?: number
  width?: number
  height?: number
  isPublic: boolean
  ordering: number
}

// Validation schemas
export const locationMediaSchema = z.object({
  id: z.string().uuid(),
  locationId: z.string().uuid(),
  url: z.string().url(),
  thumbnailUrl: z.string().url().optional(),
  type: z.string(),
  title: z.string().optional(),
  description: z.string().optional(),
  uploadedBy: z.string().uuid().optional(),
  uploadedAt: z.date(),
  metadata: z.record(z.any()).optional(),
  fileSize: z.number().optional(),
  width: z.number().optional(),
  height: z.number().optional(),
  isPublic: z.boolean().default(true),
  ordering: z.number().default(0),
})

export const createLocationMediaSchema = locationMediaSchema.omit({
  id: true,
  uploadedAt: true,
})

export const updateLocationMediaSchema = createLocationMediaSchema.partial()
