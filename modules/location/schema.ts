import { relations } from "drizzle-orm"
import { pgTable, uuid, text, timestamp, boolean, jsonb, integer } from "drizzle-orm/pg-core"
import { users } from "../user/schema"
import { projects } from "../project/schema"

// Locations table
export const locations = pgTable("locations", {
  id: uuid("id").primaryKey().defaultRandom(),
  name: text("name").notNull(),
  description: text("description"),
  address: jsonb("address").notNull(),
  coordinates: jsonb("coordinates").notNull(),
  boundary: jsonb("boundary"),
  type: text("type").notNull(),
  status: text("status").notNull().default("pending"),
  projectId: uuid("project_id").references(() => projects.id),
  organizationId: uuid("organization_id").notNull(),
  
  contactName: text("contact_name"),
  contactEmail: text("contact_email"),
  contactPhone: text("contact_phone"),
  
  locationSize: text("location_size"),
  locationFeatures: jsonb("location_features"),
  locationTags: jsonb("location_tags"),
  accessibility: text("accessibility"),
  parkingInfo: text("parking_info"),
  hourlyRate: jsonb("hourly_rate"),
  dailyRate: jsonb("daily_rate"),
  
  permitsRequired: boolean("permits_required").notNull().default(false),
  restrictions: text("restrictions"),
  approvedBy: uuid("approved_by").references(() => users.id),
  approvedAt: timestamp("approved_at"),
  
  goldenMorningStart: text("golden_morning_start"),
  goldenMorningEnd: text("golden_morning_end"),
  goldenEveningStart: text("golden_evening_start"),
  goldenEveningEnd: text("golden_evening_end"),
  
  metadata: jsonb("metadata"),
  isActive: boolean("is_active").notNull().default(true),
  createdBy: uuid("created_by").references(() => users.id),
  createdAt: timestamp("created_at").notNull().defaultNow(),
  updatedAt: timestamp("updated_at").notNull().defaultNow(),
  deletedAt: timestamp("deleted_at"),
})

// Relations for locations
export const locationsRelations = relations(locations, ({ one }) => ({
  creator: one(users, {
    fields: [locations.createdBy],
    references: [users.id],
  }),
  approver: one(users, {
    fields: [locations.approvedBy],
    references: [users.id],
  }),
  project: one(projects, {
    fields: [locations.projectId],
    references: [projects.id],
  }),
}))

// Shared location links table
export const sharedLocationLinks = pgTable("shared_location_links", {
  id: uuid("id").primaryKey().defaultRandom(),
  organizationId: uuid("organization_id").notNull(),
  token: text("token").notNull().unique(),
  locationId: uuid("location_id").notNull().references(() => locations.id),
  viewMode: text("view_mode").notNull().default("client"),
  passwordHash: text("password_hash"),
  expiresAt: timestamp("expires_at"),
  createdBy: uuid("created_by").notNull().references(() => users.id),
  createdAt: timestamp("created_at").notNull().defaultNow(),
  updatedAt: timestamp("updated_at").notNull().defaultNow(),
})

// Relations for shared location links
export const sharedLocationLinksRelations = relations(sharedLocationLinks, ({ one }) => ({
  creator: one(users, {
    fields: [sharedLocationLinks.createdBy],
    references: [users.id],
  }),
  location: one(locations, {
    fields: [sharedLocationLinks.locationId],
    references: [locations.id],
  }),
}))

// Shared location link access logs table
export const sharedLocationLinkAccessLogs = pgTable("shared_location_link_access_logs", {
  id: uuid("id").primaryKey().defaultRandom(),
  sharedLocationLinkId: uuid("shared_location_link_id").notNull().references(() => sharedLocationLinks.id),
  ipAddress: text("ip_address"),
  userAgent: text("user_agent"),
  accessedAt: timestamp("accessed_at").notNull().defaultNow(),
})

// Location media table
export const locationMedia = pgTable("location_media", {
  id: uuid("id").primaryKey().defaultRandom(),
  locationId: uuid("location_id").notNull().references(() => locations.id),
  url: text("url").notNull(),
  thumbnailUrl: text("thumbnail_url"),
  type: text("type").notNull(), // image, pdf, doc, etc.
  title: text("title"),
  description: text("description"),
  uploadedBy: uuid("uploaded_by").references(() => users.id),
  uploadedAt: timestamp("uploaded_at").notNull().defaultNow(),
  metadata: jsonb("metadata"),
  fileSize: integer("file_size"),
  width: integer("width"),
  height: integer("height"),
  isPublic: boolean("is_public").notNull().default(true),
  ordering: integer("ordering").notNull().default(0),
})

// Relations for location media
export const locationMediaRelations = relations(locationMedia, ({ one }) => ({
  location: one(locations, {
    fields: [locationMedia.locationId],
    references: [locations.id],
  }),
  uploader: one(users, {
    fields: [locationMedia.uploadedBy],
    references: [users.id],
  }),
}))
