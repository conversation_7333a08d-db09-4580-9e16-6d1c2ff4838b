"use server"

import { revalidatePath } from "next/cache"
import {
  createLocation,
  updateLocation,
  deleteLocation,
  approveLocation,
  rejectLocation,
  secureLocation,
  markLocationUnavailable,
} from "./service"
import type { Location } from "./model"
import { getCurrentUser } from "../auth/service"

export async function addLocation(data: Omit<Location, "id" | "createdAt" | "updatedAt" | "deletedAt" | "isActive">) {
  try {
    // Get current user for createdBy field
    const user = await getCurrentUser()

    const location = await createLocation({
      ...data,
      createdBy: user.id,
      isActive: true,
    })

    revalidatePath("/map")
    revalidatePath("/locations")
    revalidatePath(`/projects/${data.projectId}`)

    return { success: true, data: location }
  } catch (error) {
    console.error("Error adding location:", error)
    return {
      success: false,
      error: error instanceof Error ? error.message : "Failed to add location",
    }
  }
}

export async function editLocation(
  id: string,
  data: Partial<Omit<Location, "id" | "createdAt" | "updatedAt" | "deletedAt">>,
) {
  try {
    const location = await updateLocation(id, data)

    revalidatePath("/map")
    revalidatePath(`/locations/${id}`)
    revalidatePath("/locations")
    revalidatePath(`/projects/${location.projectId}`)

    return { success: true, data: location }
  } catch (error) {
    console.error("Error updating location:", error)
    return {
      success: false,
      error: error instanceof Error ? error.message : "Failed to update location",
    }
  }
}

export async function removeLocation(id: string) {
  try {
    const location = await deleteLocation(id)

    revalidatePath("/map")
    revalidatePath("/locations")
    revalidatePath(`/projects/${location.projectId}`)

    return { success: true, data: location }
  } catch (error) {
    console.error("Error deleting location:", error)
    return {
      success: false,
      error: error instanceof Error ? error.message : "Failed to delete location",
    }
  }
}

export async function approveLocationAction(id: string, reason?: string) {
  try {
    const user = await getCurrentUser()
    const location = await approveLocation(id, user.id)

    revalidatePath("/map")
    revalidatePath(`/locations/${id}`)
    revalidatePath("/locations")
    revalidatePath(`/projects/${location.projectId}`)

    return { success: true, data: location }
  } catch (error) {
    console.error("Error approving location:", error)
    return {
      success: false,
      error: error instanceof Error ? error.message : "Failed to approve location",
    }
  }
}

export async function rejectLocationAction(id: string, reason?: string) {
  try {
    const user = await getCurrentUser()
    const location = await rejectLocation(id, user.id, reason)

    revalidatePath("/map")
    revalidatePath(`/locations/${id}`)
    revalidatePath("/locations")
    revalidatePath(`/projects/${location.projectId}`)

    return { success: true, data: location }
  } catch (error) {
    console.error("Error rejecting location:", error)
    return {
      success: false,
      error: error instanceof Error ? error.message : "Failed to reject location",
    }
  }
}

export async function secureLocationAction(id: string) {
  try {
    const user = await getCurrentUser()
    const location = await secureLocation(id, user.id)

    revalidatePath("/map")
    revalidatePath(`/locations/${id}`)
    revalidatePath("/locations")
    revalidatePath(`/projects/${location.projectId}`)

    return { success: true, data: location }
  } catch (error) {
    console.error("Error securing location:", error)
    return {
      success: false,
      error: error instanceof Error ? error.message : "Failed to secure location",
    }
  }
}

export async function markLocationUnavailableAction(id: string) {
  try {
    const location = await markLocationUnavailable(id)

    revalidatePath("/map")
    revalidatePath(`/locations/${id}`)
    revalidatePath("/locations")
    revalidatePath(`/projects/${location.projectId}`)

    return { success: true, data: location }
  } catch (error) {
    console.error("Error marking location as unavailable:", error)
    return {
      success: false,
      error: error instanceof Error ? error.message : "Failed to mark location as unavailable",
    }
  }
}
