import { z } from "zod"
import { LOCATION_TYPES, LOCATION_STATUSES } from "../shared/constants"

// Location domain models
export type Location = {
  id: string
  name: string
  description?: string
  address: Address
  coordinates: Coordinates
  boundary?: any // Polygon data for location boundaries
  type: LocationType
  status: LocationStatus
  projectId?: string
  organizationId: string

  // Contact information
  contactName?: string
  contactEmail?: string
  contactPhone?: string

  // Location specifics
  locationSize?: string
  locationFeatures?: string[]
  locationTags?: string[]
  accessibility?: string
  parkingInfo?: string
  hourlyRate?: number
  dailyRate?: number

  // Permit information
  permitsRequired: boolean
  restrictions?: string
  approvedBy?: string
  approvedAt?: Date

  // Weather and lighting
  goldenMorningStart?: string
  goldenMorningEnd?: string
  goldenEveningStart?: string
  goldenEveningEnd?: string

  // Metadata and timestamps
  metadata?: Record<string, any>
  isActive: boolean
  createdBy?: string
  createdAt: Date
  updatedAt: Date
  deletedAt?: Date | null

  // Relations
  media?: LocationMedia[]

  // Computed properties for UI
  imageUrl?: string | null
}

// Location media model
export type LocationMedia = {
  id: string
  locationId: string
  url: string
  thumbnailUrl?: string
  type: string
  title?: string
  description?: string
  uploadedBy?: string
  uploadedAt: Date
  metadata?: Record<string, any>
  fileSize?: number
  width?: number
  height?: number
  isPublic: boolean
  ordering: number
}

export type Address = {
  street: string
  city: string
  state: string
  postalCode: string
  country: string
  formatted?: string
}

export type Coordinates = {
  latitude: number
  longitude: number
}

export type LocationType = (typeof LOCATION_TYPES)[number]
export type LocationStatus = (typeof LOCATION_STATUSES)[number]

// Validation schemas
export const addressSchema = z.object({
  street: z.string(),
  city: z.string(),
  state: z.string(),
  postalCode: z.string(),
  country: z.string(),
  formatted: z.string().optional(),
})

export const coordinatesSchema = z.object({
  latitude: z.number().min(-90).max(90),
  longitude: z.number().min(-180).max(180),
})

export const locationFeaturesSchema = z.array(z.string())
export const locationTagsSchema = z.array(z.string())

export const locationSchema = z.object({
  id: z.string().uuid(),
  name: z.string().min(1).max(255),
  description: z.string().optional(),
  address: addressSchema,
  coordinates: coordinatesSchema,
  type: z.enum(LOCATION_TYPES as unknown as [string, ...string[]]),
  status: z.enum(LOCATION_STATUSES as unknown as [string, ...string[]]),
  projectId: z.string().uuid().optional(),
  organizationId: z.string().uuid(),

  contactName: z.string().optional(),
  contactEmail: z.string().email().optional(),
  contactPhone: z.string().optional(),

  locationSize: z.string().optional(),
  locationFeatures: locationFeaturesSchema.optional(),
  locationTags: locationTagsSchema.optional(),
  accessibility: z.string().optional(),
  parkingInfo: z.string().optional(),
  hourlyRate: z.number().optional(),
  dailyRate: z.number().optional(),

  permitsRequired: z.boolean().default(false),
  restrictions: z.string().optional(),
  approvedBy: z.string().uuid().optional(),
  approvedAt: z.date().optional(),

  goldenMorningStart: z.string().optional(),
  goldenMorningEnd: z.string().optional(),
  goldenEveningStart: z.string().optional(),
  goldenEveningEnd: z.string().optional(),

  metadata: z.record(z.any()).optional(),
  isActive: z.boolean().default(true),
  // Allow any string for createdBy, not just UUIDs
  createdBy: z.string().optional(),
  createdAt: z.date(),
  updatedAt: z.date(),
  deletedAt: z.date().nullable().optional(),
})

export const createLocationSchema = locationSchema.omit({
  id: true,
  createdAt: true,
  updatedAt: true,
  deletedAt: true,
})

export const updateLocationSchema = createLocationSchema.partial()

export const locationIdSchema = locationSchema.pick({ id: true })

// Shared location link models
export type SharedLocationLink = {
  id: string
  token: string
  locationId: string
  viewMode: string
  passwordHash?: string
  expiresAt?: string
  createdBy: string
  createdAt: string
  updatedAt: string
  url: string
  isPasswordProtected?: boolean
  location?: Location
}

export const sharedLocationLinkSchema = z.object({
  id: z.string().uuid(),
  token: z.string(),
  locationId: z.string().uuid(),
  viewMode: z.enum(["client", "admin"]).default("client"),
  passwordHash: z.string().optional(),
  expiresAt: z.string().optional(),
  createdBy: z.string().uuid(),
  createdAt: z.string(),
  updatedAt: z.string(),
  url: z.string(),
  isPasswordProtected: z.boolean().optional(),
})

export const createSharedLocationLinkSchema = z.object({
  locationId: z.string().uuid(),
  viewMode: z.enum(["client", "admin"]).default("client"),
  password: z.string().optional(),
  expiresIn: z.number().optional(),
})
