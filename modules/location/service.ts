import { db } from "@/lib/db"
import { eq, and, isNull, like, or, sql, asc, count, type SQL } from "drizzle-orm"
import { NotFoundError } from "../shared/errors"
import { type Location, createLocationSchema, updateLocationSchema } from "./model"
import { createPoint, getLatitude, getLongitude } from "../shared/postgis"
import { z } from "zod"
import { locations, sharedLocationLinks, sharedLocationLinkAccessLogs, locationMedia } from "./schema"
import { randomBytes, createHash } from "node:crypto"
import { cache } from "@/lib/cache"

// Define a specific type for filters used in getLocations
interface GetLocationsFilters {
  status?: string;
  type?: string;
  projectId?: string;
  search?: string;
  tags?: string[];
  page?: number;
  pageSize?: number;
}

export async function getLocations(organizationId: string, filters: GetLocationsFilters = {}) {
  // Extract pagination parameters
  const page = filters.page || 1;
  const pageSize = filters.pageSize || 12;
  const offset = (page - 1) * pageSize;
  // Use the optimized index for organization_id + deleted_at
  const conditions = [eq(locations.organizationId, organizationId), isNull(locations.deletedAt)]

  // Apply status filter - uses the status index
  if (filters.status) {
    conditions.push(eq(locations.status, filters.status))
  }

  // Apply type filter - uses the type index
  if (filters.type) {
    conditions.push(eq(locations.type, filters.type))
  }

  // Apply project filter - uses the project_id index
  if (filters.projectId) {
    // If a specific project is requested, include only locations with that project ID
    conditions.push(eq(locations.projectId, filters.projectId))
  } else {
    // If no specific project is requested, explicitly include all locations
    // regardless of whether they have a project ID or not
    console.log("No projectId filter specified, explicitly including both null and non-null project IDs")
    
    // Explicitly include both null and non-null project IDs using OR condition
    // This ensures locations with null projectId are included
    conditions.push(or(isNull(locations.projectId), sql`${locations.projectId} IS NOT NULL`))
  }

  // Apply search filter
  if (filters.search) {
    const searchTerm = `%${filters.search}%`
    conditions.push(or(like(locations.name, searchTerm), like(locations.description || "", searchTerm)))
  }

  // Apply tag filter - optimize the JSONB query
  if (filters.tags && filters.tags.length > 0) {
    // More efficient JSONB containment operator for array values
    // This uses the GIN index on the JSONB column if available
    const tagsArray = filters.tags;
    
    // Use the @> operator for better performance with JSONB arrays
    // This checks if the metadata.tags JSONB array contains all the specified tags
    const jsonbCondition = sql`metadata->'tags' @> ${JSON.stringify(tagsArray)}::jsonb`;
    conditions.push(jsonbCondition as SQL<unknown>);
  }

  // Only select the columns we need to reduce data transfer
  const result = await db.query.locations.findMany({
    where: and(...conditions),
    orderBy: locations.createdAt,
    // Apply pagination
    limit: pageSize,
    offset: offset,
    // Only select necessary columns
    columns: {
      id: true,
      name: true,
      description: true,
      address: true,
      coordinates: true,
      type: true,
      status: true,
      projectId: true,
      organizationId: true,
      metadata: true,
      isActive: true,
      createdAt: true,
      updatedAt: true,
      createdBy: true,
      approvedBy: true,
    },
    // Include only necessary relations with limited columns
    with: {
      creator: {
        columns: {
          id: true,
          name: true,
        }
      },
      approver: {
        columns: {
          id: true,
          name: true,
        }
      },
      project: {
        columns: {
          id: true,
          name: true,
        }
      },
      media: {
        where: (locationMedia, { eq, and, isNull }) => 
          and(
            eq(locationMedia.isPublic, true),
            isNull(locationMedia.deletedAt)
          ),
        orderBy: [asc(locationMedia.ordering)],
        with: {
          uploader: {
            columns: {
              id: true,
              name: true,
            }
          }
        }
      },
    },
  })

  // Get total count for pagination
  const totalResult = await db
    .select({ count: count() })
    .from(locations)
    .where(and(...conditions))

  const total = totalResult[0]?.count || 0

  // Transform the locations with coordinates and imageUrl
  const transformedLocations = result.map((location) => {
    // Extract coordinates properly
    let coordinates = { latitude: 0, longitude: 0 };

    if (location.coordinates) {
      // Handle different possible formats of coordinates
      if (typeof location.coordinates === 'object') {
        // Format 1: { latitude: number, longitude: number }
        if ('latitude' in location.coordinates && 'longitude' in location.coordinates) {
          coordinates = {
            latitude: Number(location.coordinates.latitude),
            longitude: Number(location.coordinates.longitude)
          };
        }
        // Format 2: PostGIS point format from database
        else if ('x' in location.coordinates && 'y' in location.coordinates) {
          coordinates = {
            latitude: Number(location.coordinates.y),
            longitude: Number(location.coordinates.x)
          };
        }
      }
      // Format 3: String format like "POINT(longitude latitude)" from PostGIS
      else if (typeof location.coordinates === 'string') {
        const coordString = location.coordinates as string;
        if (coordString.startsWith('POINT')) {
          const match = coordString.match(/POINT\(([^ ]+) ([^)]+)\)/);
          if (match && match.length === 3) {
            coordinates = {
              longitude: Number(match[1]),
              latitude: Number(match[2])
            };
          }
        }
      }
    }

    // Validate coordinates
    if (Number.isNaN(coordinates.latitude) || Number.isNaN(coordinates.longitude)) {
      coordinates = { latitude: 0, longitude: 0 };
    }

    // Extract the first image URL from media for the card display
    const imageUrl = location.media && location.media.length > 0 ? location.media[0].url : null;

    return {
      ...location,
      coordinates,
      imageUrl
    };
  });

  return {
    locations: transformedLocations,
    total,
    page,
    pageSize,
    totalPages: Math.ceil(total / pageSize),
  };
}

export async function getLocationsByProject(projectId: string) {
  const result = await db.query.locations.findMany({
    where: and(eq(locations.projectId, projectId), isNull(locations.deletedAt)),
    orderBy: locations.createdAt,
    // Include the newly defined relations
    with: {
      creator: true, // Now defined in schema
      approver: true, // Now defined in schema
    },
  })

  // Return the coordinates as they are (they're already in the correct format)
  return result.map((location) => ({
    ...location,
    coordinates: location.coordinates || { latitude: 0, longitude: 0 },
  }))
}

export async function getLocation(id: string) {
  const location = await db.query.locations.findFirst({
    where: and(eq(locations.id, id), isNull(locations.deletedAt)),
    with: {
      creator: true,
      project: true,
      media: {
        where: (locationMedia, { eq, and, isNull }) => 
          and(
            eq(locationMedia.isPublic, true),
            isNull(locationMedia.deletedAt)
          ),
        orderBy: [asc(locationMedia.ordering)],
        with: {
          uploader: {
            columns: {
              id: true,
              name: true,
            }
          }
        }
      },
    },
  })

  if (!location) {
    throw new NotFoundError(`Location with ID ${id} not found`)
  }

  // Return the coordinates as they are (they're already in the correct format)
  return {
    ...location,
    coordinates: location.coordinates || { latitude: 0, longitude: 0 },
  }
}

// Update function signature to make createdBy optional, matching the database schema and model
export async function createLocation(data: Omit<Location, "id" | "createdAt" | "updatedAt" | "deletedAt" | "status"> & { createdBy?: string }) {
  // Use the original schema which has createdBy as optional
  const validatedData = createLocationSchema.parse(data);

  // Extract coordinates and createdBy separately
  const { coordinates, createdBy, ...rest } = validatedData;

  // Prepare the values for insertion using the correct type
  const insertValues = {
    ...rest,
    // Store coordinates as a JSON object, not as a PostGIS point
    coordinates: {
      latitude: coordinates.latitude,
      longitude: coordinates.longitude
    },
    // status defaults to 'Pending' in schema
    isActive: true,
  } as typeof locations.$inferInsert;

  // Only include createdBy if it's a valid UUID
  if (createdBy && /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(createdBy)) {
    insertValues.createdBy = createdBy;
  }

  // Only include projectId if it's a valid UUID and not the default zero UUID
  if (rest.projectId && 
      /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(rest.projectId) && 
      rest.projectId !== "00000000-0000-0000-0000-000000000000") {
    insertValues.projectId = rest.projectId;
  } else {
    // Remove projectId reference if it's not valid
    insertValues.projectId = undefined;
  }

  const [location] = await db
    .insert(locations)
    .values(insertValues)
    .returning();

  return {
    ...location,
    coordinates: {
      latitude: coordinates.latitude,
      longitude: coordinates.longitude,
    },
  }
}

export async function updateLocation(
  id: string,
  data: Partial<Omit<Location, "id" | "createdAt" | "updatedAt" | "deletedAt">>,
) {
  const validatedData = updateLocationSchema.parse(data)

  // Handle coordinates separately if they exist
  const { coordinates, ...rest } = validatedData

  // Use a more specific type for updateData based on the locations schema
  const updateData: Partial<typeof locations.$inferInsert> = {
    ...rest,
    updatedAt: new Date(),
  }

  // Only add coordinates if they were provided
  if (coordinates) {
    // Store coordinates as a JSON object, not as a PostGIS point
    updateData.coordinates = {
      latitude: coordinates.latitude,
      longitude: coordinates.longitude
    }
  }

  const [location] = await db
    .update(locations)
    .set(updateData)
    .where(and(eq(locations.id, id), isNull(locations.deletedAt)))
    .returning()

  if (!location) {
    throw new NotFoundError(`Location with ID ${id} not found`)
  }

  return {
    ...location,
    coordinates: location.coordinates || { latitude: 0, longitude: 0 },
  }
}

export async function deleteLocation(id: string) {
  const [location] = await db
    .update(locations)
    .set({
      isActive: false,
      deletedAt: new Date(),
      updatedAt: new Date(),
    })
    .where(and(eq(locations.id, id), isNull(locations.deletedAt)))
    .returning()

  if (!location) {
    throw new NotFoundError(`Location with ID ${id} not found`)
  }

  return location
}

export async function approveLocation(id: string, userId: string) {
  const [location] = await db
    .update(locations)
    .set({
      status: "approved",
      approvedBy: userId,
      approvedAt: new Date(),
      updatedAt: new Date(),
    })
    .where(and(eq(locations.id, id), isNull(locations.deletedAt)))
    .returning()

  if (!location) {
    throw new NotFoundError(`Location with ID ${id} not found`)
  }

  return location
}

export async function rejectLocation(id: string, userId: string, reason?: string) {
  // First, get the current location to access its metadata
  const existingLocation = await db.query.locations.findFirst({
    where: and(eq(locations.id, id), isNull(locations.deletedAt)),
  });

  if (!existingLocation) {
    throw new NotFoundError(`Location with ID ${id} not found`);
  }

  const [location] = await db
    .update(locations)
    .set({
      status: "rejected",
      approvedBy: userId,
      approvedAt: new Date(),
      metadata: {
        ...existingLocation.metadata,
        rejectionReason: reason,
      },
      updatedAt: new Date(),
    })
    .where(and(eq(locations.id, id), isNull(locations.deletedAt)))
    .returning();

  if (!location) {
    throw new NotFoundError(`Location with ID ${id} not found`);
  }

  return location;
}

export async function secureLocation(id: string, userId: string) {
  const [location] = await db
    .update(locations)
    .set({
      status: "secured",
      approvedBy: userId,
      approvedAt: new Date(),
      updatedAt: new Date(),
    })
    .where(and(eq(locations.id, id), isNull(locations.deletedAt)))
    .returning()

  if (!location) {
    throw new NotFoundError(`Location with ID ${id} not found`)
  }

  return location
}

export async function markLocationUnavailable(id: string) {
  const [location] = await db
    .update(locations)
    .set({
      status: "unavailable",
      updatedAt: new Date(),
    })
    .where(and(eq(locations.id, id), isNull(locations.deletedAt)))
    .returning()

  if (!location) {
    throw new NotFoundError(`Location with ID ${id} not found`)
  }

  return location
}

export async function getLocationsByCoordinates(lat: number, lng: number, radiusInMeters: number) {
  const point = createPoint(lat, lng)

  // Use the GiST spatial index for efficient spatial queries
  // Only select the columns we need to reduce data transfer
  const result = await db.query.locations.findMany({
    where: and(
      isNull(locations.deletedAt), 
      // Use ST_DWithin for efficient radius search using the spatial index
      sql`ST_DWithin(coordinates, ${point}, ${radiusInMeters})`
    ),
    // Only select necessary columns
    columns: {
      id: true,
      name: true,
      description: true,
      address: true,
      coordinates: true,
      type: true,
      status: true,
      projectId: true,
      organizationId: true,
      metadata: true,
      isActive: true,
    },
    // Order by distance for nearest-first results
    orderBy: sql`ST_Distance(coordinates, ${point})`,
    // Limit to a reasonable number to prevent excessive data transfer
    limit: 100,
  })

  // Pre-calculate the distance once for each location
  return result.map((location) => {
    // Calculate the distance outside the return statement to avoid redundant calculations
    const distance = Number.parseFloat(sql`ST_Distance(coordinates, ${point})`.toString());
    
    return {
      ...location,
      coordinates: location.coordinates || { latitude: 0, longitude: 0 },
      distance,
    };
  })
}

// Generate a secure random token for shared links
function generateShareToken(): string {
  return randomBytes(32).toString('hex')
}

// Hash a password for secure storage
function hashPassword(password: string): string {
  return createHash('sha256').update(password).digest('hex')
}

// Create a shared location link
export async function createSharedLocationLink(
  organizationId: string,
  userId: string,
  options: {
    locationId: string,
    viewMode?: string,
    password?: string,
    expiresIn?: number, // in seconds
  }
): Promise<any> {
  try {
    // Start a transaction
    const result = await db.transaction(async (tx) => {
      // Generate a secure token
      const token = generateShareToken();
      
      // Calculate expiration date if provided
      let expiresAt: Date | undefined = undefined;
      if (options.expiresIn) {
        expiresAt = new Date();
        expiresAt.setSeconds(expiresAt.getSeconds() + options.expiresIn);
      }
      
      // Hash password if provided
      const passwordHash = options.password ? hashPassword(options.password) : undefined;
      
      // Create the shared link
      const [sharedLink] = await tx
        .insert(sharedLocationLinks)
        .values({
          organizationId,
          token,
          locationId: options.locationId,
          viewMode: options.viewMode || "client",
          passwordHash,
          expiresAt,
          createdBy: userId,
        })
        .returning();
      
      // Generate the URL for the shared link
      const url = `${process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000'}/shared/location/${token}`;
      
      // Return the created shared link
      return {
        id: sharedLink.id,
        token: sharedLink.token,
        locationId: sharedLink.locationId,
        viewMode: sharedLink.viewMode,
        passwordHash: sharedLink.passwordHash || undefined,
        expiresAt: sharedLink.expiresAt?.toISOString(),
        createdBy: userId,
        createdAt: sharedLink.createdAt.toISOString(),
        updatedAt: sharedLink.updatedAt.toISOString(),
        url,
      };
    });
    
    // Invalidate the cache for shared location links for this organization
    cache.delete(`sharedLocationLinks:${organizationId}`);
    
    return result;
  } catch (error) {
    console.error("Error creating shared location link:", error);
    throw new Error("Failed to create shared location link");
  }
}

// Get a shared location link by token
export async function getSharedLocationLinkByToken(token: string, password?: string): Promise<any> {
  try {
    // First check if the link exists and hasn't expired without loading relations
    // This is more efficient as we can avoid loading relations if the link doesn't exist or has expired
    const linkCheck = await db.query.sharedLocationLinks.findFirst({
      where: eq(sharedLocationLinks.token, token),
      columns: {
        id: true,
        expiresAt: true,
        passwordHash: true,
      },
    });
    
    if (!linkCheck) {
      return null;
    }
    
    // Check if the link has expired
    if (linkCheck.expiresAt && new Date() > linkCheck.expiresAt) {
      return null;
    }
    
    // Check password if the link is password-protected
    if (linkCheck.passwordHash) {
      if (!password) {
        throw new Error("Password required");
      }
      
      const hashedPassword = hashPassword(password);
      if (hashedPassword !== linkCheck.passwordHash) {
        throw new Error("Invalid password");
      }
    }
    
    // Use cache for the full data with relations
    // Don't cache password-protected links with the password in the cache key
    const cacheKey = `sharedLocationLink:${linkCheck.id}`;
    
    return await cache.getOrSet(
      cacheKey,
      async () => {
        // Now that we've verified the link is valid, load the full data with relations
        const sharedLink = await db.query.sharedLocationLinks.findFirst({
          where: eq(sharedLocationLinks.id, linkCheck.id),
          columns: {
            id: true,
            token: true,
            locationId: true,
            viewMode: true,
            passwordHash: true,
            expiresAt: true,
            createdAt: true,
            updatedAt: true,
            organizationId: true,
          },
          with: {
            location: {
              columns: {
                id: true,
                name: true,
                description: true,
                address: true,
                coordinates: true,
                type: true,
                status: true,
                projectId: true,
                organizationId: true,
                contactName: true,
                contactEmail: true,
                contactPhone: true,
                locationSize: true,
                locationFeatures: true,
                locationTags: true,
                accessibility: true,
                parkingInfo: true,
                hourlyRate: true,
                dailyRate: true,
                permitsRequired: true,
                restrictions: true,
                metadata: true,
                isActive: true,
              },
            },
            creator: {
              columns: {
                id: true,
                name: true,
              },
            },
          },
        });
        
        if (!sharedLink) {
          return null;
        }
        
        // Generate the URL for the shared link
        const url = `${process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000'}/shared/location/${token}`;
        
        // Use the coordinates as they are
        const locationWithTransformedCoordinates = {
          ...sharedLink.location,
          coordinates: sharedLink.location.coordinates || { latitude: 0, longitude: 0 },
        };
        
        // Return the shared link
        return {
          id: sharedLink.id,
          token: sharedLink.token,
          locationId: sharedLink.locationId,
          viewMode: sharedLink.viewMode,
          passwordHash: sharedLink.passwordHash || undefined,
          expiresAt: sharedLink.expiresAt?.toISOString(),
          createdBy: sharedLink.creator.id,
          createdAt: sharedLink.createdAt.toISOString(),
          updatedAt: sharedLink.updatedAt.toISOString(),
          organizationId: sharedLink.organizationId,
          location: locationWithTransformedCoordinates,
          url,
          isPasswordProtected: !!sharedLink.passwordHash,
        };
      },
      { ttl: 5 * 60 * 1000 } // Cache for 5 minutes
    );
  } catch (error) {
    console.error("Error getting shared location link:", error);
    throw error;
  }
}

// Log access to a shared location link
export async function logSharedLocationLinkAccess(
  sharedLocationLinkId: string,
  ipAddress?: string,
  userAgent?: string
): Promise<void> {
  try {
    await db
      .insert(sharedLocationLinkAccessLogs)
      .values({
        sharedLocationLinkId,
        ipAddress,
        userAgent,
      })
  } catch (error) {
    console.error("Error logging shared location link access:", error)
    // Don't throw an error here, just log it
  }
}

// Get shared location links for an organization
export async function getSharedLocationLinksForOrganization(organizationId: string): Promise<any[]> {
  try {
    // Use cache to avoid redundant database queries
    const cacheKey = `sharedLocationLinks:${organizationId}`;
    
    return await cache.getOrSet(
      cacheKey,
      async () => {
        // Use the optimized index for organization_id
        const links = await db.query.sharedLocationLinks.findMany({
          where: eq(sharedLocationLinks.organizationId, organizationId),
          // Only select the fields we need to reduce data transfer
          columns: {
            id: true,
            token: true,
            locationId: true,
            viewMode: true,
            passwordHash: true,
            expiresAt: true,
            createdAt: true,
            updatedAt: true,
          },
          with: {
            // Only select the id from creator to reduce data transfer
            creator: {
              columns: {
                id: true,
                name: true,
              },
            },
            location: {
              columns: {
                id: true,
                name: true,
                type: true,
                status: true,
              },
            },
          },
          orderBy: (tables, { desc }) => [desc(tables.createdAt)],
        });
        
        // Generate app URL once outside the loop for better performance
        const appUrl = process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000';
        
        return links.map(link => {
          // Generate the URL for the shared link
          const url = `${appUrl}/shared/location/${link.token}`;
          
          return {
            id: link.id,
            token: link.token,
            locationId: link.locationId,
            viewMode: link.viewMode,
            passwordHash: link.passwordHash || undefined,
            expiresAt: link.expiresAt?.toISOString(),
            createdBy: link.creator.id,
            createdAt: link.createdAt.toISOString(),
            updatedAt: link.updatedAt.toISOString(),
            location: link.location,
            url,
            isPasswordProtected: !!link.passwordHash,
          };
        });
      },
      { ttl: 60 * 1000 } // Cache for 1 minute
    );
  } catch (error) {
    console.error("Error getting shared location links:", error);
    throw new Error("Failed to get shared location links");
  }
}

// Delete a shared location link
export async function deleteSharedLocationLink(id: string, organizationId: string): Promise<boolean> {
  try {
    // Start a transaction
    const result = await db.transaction(async (tx) => {
      // Check if the link exists and belongs to the organization
      const existingLink = await tx.query.sharedLocationLinks.findFirst({
        where: and(
          eq(sharedLocationLinks.id, id),
          eq(sharedLocationLinks.organizationId, organizationId)
        ),
        columns: {
          id: true,
          token: true,
        },
      });
      
      if (!existingLink) {
        return false;
      }
      
      // Delete access logs
      await tx
        .delete(sharedLocationLinkAccessLogs)
        .where(eq(sharedLocationLinkAccessLogs.sharedLocationLinkId, id));
      
      // Delete the link
      await tx
        .delete(sharedLocationLinks)
        .where(eq(sharedLocationLinks.id, id));
      
      return true;
    });
    
    if (result) {
      // Invalidate the cache for this specific link and the organization's links list
      cache.delete(`sharedLocationLink:${id}`);
      cache.delete(`sharedLocationLinks:${organizationId}`);
    }
    
    return result;
  } catch (error) {
    console.error("Error deleting shared location link:", error);
    throw new Error("Failed to delete shared location link");
  }
}
