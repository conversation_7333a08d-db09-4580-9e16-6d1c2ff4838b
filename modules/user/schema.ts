import { relations } from "drizzle-orm"
import { pgTable, uuid, varchar, timestamp, jsonb, boolean } from "drizzle-orm/pg-core"
import { createInsertSchema, createSelectSchema } from "drizzle-zod"

import { organizationUserRoles } from "../organization/schema"
import { projectUserRoles } from "../project/schema"

export const users = pgTable("users", {
  id: uuid("id").primaryKey().defaultRandom(),
  email: varchar("email", { length: 255 }).notNull().unique(),
  name: varchar("name", { length: 255 }),
  avatar: varchar("avatar", { length: 255 }),
  stytchUserId: varchar("stytch_user_id", { length: 255 }).unique(),
  metadata: jsonb("metadata").$type<Record<string, unknown>>(),
  isActive: boolean("is_active").default(true),
  createdAt: timestamp("created_at").defaultNow().notNull(),
  updatedAt: timestamp("updated_at").defaultNow().notNull(),
  deletedAt: timestamp("deleted_at"),
})

export const usersRelations = relations(users, ({ many }) => ({
  organizationRoles: many(organizationUserRoles),
  projectRoles: many(projectUserRoles),
}))

// Zod schemas for validation
export const insertUserSchema = createInsertSchema(users)
export const selectUserSchema = createSelectSchema(users)

export const createUserSchema = insertUserSchema.omit({
  id: true,
  createdAt: true,
  updatedAt: true,
  deletedAt: true,
})

export const updateUserSchema = createUserSchema.partial()

export const userIdSchema = selectUserSchema.pick({ id: true })
