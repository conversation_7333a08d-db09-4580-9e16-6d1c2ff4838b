import { db } from "@/lib/db"
import { users } from "./schema"
import { eq, and, isNull } from "drizzle-orm"
import { NotFoundError } from "../shared/errors"
import { type User, updateUserSchema } from "./model"

export async function getUsers() {
  return db.query.users.findMany({
    where: isNull(users.deletedAt),
    orderBy: users.createdAt,
  })
}

export async function getUser(id: string) {
  const user = await db.query.users.findFirst({
    where: and(eq(users.id, id), isNull(users.deletedAt)),
  })

  if (!user) {
    throw new NotFoundError(`User with ID ${id} not found`)
  }

  return user
}

export async function getUserByEmail(email: string) {
  return db.query.users.findFirst({
    where: and(eq(users.email, email), isNull(users.deletedAt)),
  })
}

// Stub implementation for development
export async function getUserByStytchId(stytchUserId: string) {
  // In a real implementation, this would query the database
  return {
    id: "user-1",
    email: "<EMAIL>",
    name: "Test User",
    stytchUserId,
  }
}

// Stub implementation for development
export async function createUser(data: any) {
  // In a real implementation, this would create a user in the database
  return {
    id: "user-1",
    email: data.email,
    name: data.name,
    stytchUserId: data.stytchUserId,
  }
}

export async function updateUser(
  id: string,
  data: Partial<Omit<User, "id" | "createdAt" | "updatedAt" | "deletedAt">>,
) {
  const validatedData = updateUserSchema.parse(data)

  const [user] = await db
    .update(users)
    .set({
      ...validatedData,
      updatedAt: new Date(),
    })
    .where(and(eq(users.id, id), isNull(users.deletedAt)))
    .returning()

  if (!user) {
    throw new NotFoundError(`User with ID ${id} not found`)
  }

  return user
}

export async function deleteUser(id: string) {
  const [user] = await db
    .update(users)
    .set({
      deletedAt: new Date(),
      updatedAt: new Date(),
    })
    .where(and(eq(users.id, id), isNull(users.deletedAt)))
    .returning()

  if (!user) {
    throw new NotFoundError(`User with ID ${id} not found`)
  }

  return user
}
