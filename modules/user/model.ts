import { z } from "zod"

// User domain models
export type User = {
  id: string
  email: string
  name?: string
  avatar?: string
  stytchUserId?: string
  metadata?: Record<string, any>
  isActive: boolean
  createdAt: Date
  updatedAt: Date
  deletedAt?: Date | null
}

// Validation schemas
export const userSchema = z.object({
  id: z.string().uuid(),
  email: z.string().email(),
  name: z.string().optional(),
  avatar: z.string().optional(),
  stytchUserId: z.string().optional(),
  metadata: z.record(z.any()).optional(),
  isActive: z.boolean(),
  createdAt: z.date(),
  updatedAt: z.date(),
  deletedAt: z.date().nullable().optional(),
})

export const createUserSchema = z.object({
  email: z.string().email(),
  name: z.string().optional(),
  avatar: z.string().optional(),
  stytchUserId: z.string().optional(),
  metadata: z.record(z.any()).optional(),
})

export const updateUserSchema = createUserSchema.partial()
