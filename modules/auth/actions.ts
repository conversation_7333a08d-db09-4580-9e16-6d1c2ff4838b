"use server"

import { redirect } from "next/navigation"
import { createMagicLink as createStytchMagicLink } from "./stytch"
import { createMagicLink as createStytchB2BMagicLink } from "./stytch-b2b"
import { authConfig } from "@/lib/config/auth"
import { validate } from "@/modules/shared/validation"
import { z } from "zod"

const loginSchema = z.object({
  email: z.string().email("Please enter a valid email address"),
  organizationId: z.string().nullable().optional(),
  redirectUrl: z.string().optional(),
})

export async function login(formData: FormData) {
  const email = formData.get("email") as string
  const organizationId = formData.get("organizationId") as string | null
  const customRedirectUrl = formData.get("redirectUrl") as string | null

  try {
    const { email: validatedEmail } = validate(loginSchema, { 
      email, 
      organizationId,
      redirectUrl: customRedirectUrl 
    })

    // Use custom redirectUrl if provided, otherwise use default
    const redirectUrl = customRedirectUrl || `${process.env.NEXT_PUBLIC_APP_URL}${authConfig.callbackUrl}`
    
    // Use B2B client if organizationId is provided, otherwise use regular client
    const result = organizationId
      ? await createStytchB2BMagicLink(validatedEmail, redirectUrl, organizationId)
      : await createStytchMagicLink(validatedEmail, redirectUrl)

    if (!result.success) {
      return {
        success: false,
        error: "Failed to send login link",
      }
    }

    return {
      success: true,
      message: "Check your email for a login link",
    }
  } catch (error) {
    console.error("Login error:", error)

    if (error instanceof z.ZodError) {
      return {
        success: false,
        error: error.errors[0]?.message || "Invalid email address",
      }
    }

    return {
      success: false,
      error: "An unexpected error occurred",
    }
  }
}

export async function logout() {
  // Import the service logout function
  const { logout: serviceLogout } = await import("./service")
  
  // Call the service logout function
  await serviceLogout()
  
  // Redirect to the home page
  redirect("/")
}
