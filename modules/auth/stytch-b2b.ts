import { getStytchB2<PERSON>lient as getClientStytchB2BClient } from "@/lib/stytch-b2b-client"
import { getStytchB2BClient as getServerStytchB2BClient } from "@/lib/stytch-b2b"

/**
 * Creates a magic link for authentication
 * @param email User's email address
 * @param redirectUrl URL to redirect to after authentication
 * @param organizationId Optional organization ID for organization-specific login
 * @returns The result of the magic link creation
 */
export async function createMagicLink(
  email: string,
  redirectUrl: string,
  organizationId?: string
) {
  try {
    console.log(`Creating magic link for email: ${email}, redirectUrl: ${redirectUrl}, organizationId: ${organizationId || 'none'}`);
    
    // Use client-side Stytch client for client-side operations
    const stytchClient = getClientStytchB2BClient()
    
    if (!stytchClient) {
      console.error("Stytch client not initialized");
      throw new Error("Stytch client not initialized")
    }
    
    // For B2B Discovery flow, we should use the discovery.send method
    if (!organizationId) {
      // Use discovery flow when no organization ID is provided
      console.log("Using discovery flow");
      const params = {
        email_address: email,
        discovery_redirect_url: redirectUrl,
      };
      console.log("Discovery params:", params);
      
      const response = await stytchClient.magicLinks.email.discovery.send(params);
      console.log("Discovery response:", response);
    } else {
      // Use organization-specific flow when organization ID is provided
      console.log("Using organization-specific flow");
      const params = {
        email_address: email,
        login_redirect_url: redirectUrl,
        organization_id: organizationId,
      };
      console.log("Organization-specific params:", params);
      
      const response = await stytchClient.magicLinks.email.loginOrSignup(params);
      console.log("Organization-specific response:", response);
    }
    
    return {
      success: true,
      emailId: "email_sent", // Use a generic success message
    }
  } catch (error) {
    console.error("Error creating magic link:", error)
    return {
      success: false,
      error: error instanceof Error ? error.message : String(error),
    }
  }
}

/**
 * Authenticates a magic link token
 * @param token Magic link token
 * @param tokenType Optional token type (discovery or magic_links)
 * @returns The authentication result including session and user information
 */
export async function authenticateMagicLink(token: string, tokenType?: string) {
  try {
    // Use server-side Stytch client for server-side operations
    const stytchClient = getServerStytchB2BClient()
    
    if (!stytchClient) {
      throw new Error("Stytch client not initialized")
    }
    
    // Handle discovery token type
    if (tokenType === "discovery") {
      const response = await stytchClient.magicLinks.discovery.authenticate({
        discovery_magic_links_token: token,
      })
      
      return {
        success: true,
        intermediateSessionToken: response.intermediate_session_token,
        discoveredOrganizations: response.discovered_organizations,
        // The member information is not directly available in the discovery flow
        member: null,
      }
    } 
    
    // Default to regular magic link token
    const response = await stytchClient.magicLinks.authenticate({
      magic_links_token: token,
      session_duration_minutes: 60 * 24 * 7 // 1 week
    })
    
    return {
      success: true,
      sessionToken: response.session_token,
      sessionJwt: response.session_jwt,
      user: response.member,
      organization: response.organization,
    }
  } catch (error) {
    console.error("Error authenticating magic link:", error)
    return {
      success: false,
      error: error instanceof Error ? error.message : String(error),
    }
  }
}

/**
 * Revokes a session
 * @param sessionToken Session token to revoke
 * @returns Success status
 */
export async function revokeSession(sessionToken: string) {
  try {
    // Use server-side Stytch client for server-side operations
    const stytchClient = getServerStytchB2BClient()
    
    if (!stytchClient) {
      throw new Error("Stytch client not initialized")
    }
    
    // Use the sessions property (note: it's plural)
    const sessionClient = stytchClient.sessions;
    // Then cast to the expected interface
    type SessionClient = {
      revoke: (options: { session_token: string }) => Promise<unknown>;
    };
    await (sessionClient as SessionClient).revoke({
      session_token: sessionToken
    });
    
    return {
      success: true,
    }
  } catch (error) {
    console.error("Error revoking session:", error)
    return {
      success: false,
      error: error instanceof Error ? error.message : String(error),
    }
  }
}

/**
 * Verifies a session
 * @param sessionToken Session token to verify
 * @returns The verification result including session and user information
 */
export async function verifySession(sessionToken: string) {
  try {
    // Use server-side Stytch client for server-side operations
    const stytchClient = getServerStytchB2BClient()
    
    if (!stytchClient) {
      throw new Error("Stytch client not initialized")
    }
    
    // Use the sessions property (note: it's plural)
    const sessionClient = stytchClient.sessions;
    // Then cast to the expected interface
    type SessionClient = {
      authenticate: (options: { session_token: string }) => Promise<{
        session_token: string;
        session_jwt: string;
        member: unknown;
        organization: unknown;
      }>;
    };
    const response = await (sessionClient as SessionClient).authenticate({
      session_token: sessionToken
    });
    
    return {
      success: true,
      sessionToken: response.session_token,
      sessionJwt: response.session_jwt,
      member: response.member,
      organization: response.organization,
    }
  } catch (error) {
    console.error("Error verifying session:", error)
    return {
      success: false,
      error: error instanceof Error ? error.message : String(error),
    }
  }
}
