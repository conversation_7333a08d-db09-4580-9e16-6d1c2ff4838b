import { z } from "zod"
import { DOCUMENT_TYPES, DOCUMENT_STATUSES } from "../shared/constants"

// Document domain models
export type Document = {
  id: string
  name: string
  description?: string
  fileUrl: string
  documentType: DocumentType
  status: DocumentStatus
  version: number
  organizationId: string
  projectId?: string
  locationId?: string
  createdBy?: string
  createdAt: Date
  updatedAt: Date
  expiresAt?: Date
  signingUrl?: string
  signedBy?: string
  signedAt?: Date
  metadata?: Record<string, any>
  fileSize?: number
  isPublic: boolean
  isArchived: boolean
  deletedAt?: Date | null
}

export type DocumentType = (typeof DOCUMENT_TYPES)[number]
export type DocumentStatus = (typeof DOCUMENT_STATUSES)[number]

// Validation schemas
export const documentSchema = z.object({
  id: z.string().uuid(),
  name: z.string().min(1).max(255),
  description: z.string().optional(),
  fileUrl: z.string().url(),
  documentType: z.enum(DOCUMENT_TYPES as [string, ...string[]]),
  status: z.enum(DOCUMENT_STATUSES as [string, ...string[]]).default("draft"),
  version: z.number().default(1),
  organizationId: z.string().uuid(),
  projectId: z.string().uuid().optional(),
  locationId: z.string().uuid().optional(),
  createdBy: z.string().uuid().optional(),
  createdAt: z.date(),
  updatedAt: z.date(),
  expiresAt: z.date().optional(),
  signingUrl: z.string().url().optional(),
  signedBy: z.string().uuid().optional(),
  signedAt: z.date().optional(),
  metadata: z.record(z.any()).optional(),
  fileSize: z.number().optional(),
  isPublic: z.boolean().default(false),
  isArchived: z.boolean().default(false),
  deletedAt: z.date().nullable().optional(),
})

export const createDocumentSchema = z.object({
  name: z.string().min(1).max(255),
  description: z.string().optional(),
  fileUrl: z.string().url(),
  documentType: z.enum(DOCUMENT_TYPES as [string, ...string[]]),
  status: z.enum(DOCUMENT_STATUSES as [string, ...string[]]).default("draft"),
  version: z.number().default(1),
  organizationId: z.string().uuid(),
  projectId: z.string().uuid().optional(),
  locationId: z.string().uuid().optional(),
  expiresAt: z.date().optional(),
  signingUrl: z.string().url().optional(),
  metadata: z.record(z.any()).optional(),
  fileSize: z.number().optional(),
  isPublic: z.boolean().default(false),
})

export const updateDocumentSchema = createDocumentSchema.partial()
