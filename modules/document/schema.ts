import { relations } from "drizzle-orm"
import { pgTable, uuid, varchar, text, timestamp, jsonb, boolean } from "drizzle-orm/pg-core"
import { createInsertSchema, createSelectSchema } from "drizzle-zod"

import { projects } from "../project/schema"
import { locations } from "../location/schema"
import { organizations } from "../organization/schema"
import { users } from "../user/schema"

export const documents = pgTable("documents", {
  id: uuid("id").primaryKey().defaultRandom(),
  name: varchar("name", { length: 255 }).notNull(),
  description: text("description"),
  fileUrl: varchar("file_url", { length: 255 }).notNull(),
  fileType: varchar("file_type", { length: 50 }).notNull(),
  fileSize: varchar("file_size", { length: 50 }),
  projectId: uuid("project_id").references(() => projects.id, { onDelete: "cascade" }),
  locationId: uuid("location_id").references(() => locations.id, { onDelete: "cascade" }),
  organizationId: uuid("organization_id")
    .references(() => organizations.id, { onDelete: "cascade" })
    .notNull(),
  uploadedById: uuid("uploaded_by_id").references(() => users.id),
  metadata: jsonb("metadata").$type<Record<string, any>>(),
  isArchived: boolean("is_archived").default(false),
  createdAt: timestamp("created_at").defaultNow().notNull(),
  updatedAt: timestamp("updated_at").defaultNow().notNull(),
  deletedAt: timestamp("deleted_at"),
})

export const documentsRelations = relations(documents, ({ one }) => ({
  project: one(projects, {
    fields: [documents.projectId],
    references: [projects.id],
    relationName: "project_documents",
  }),
  location: one(locations, {
    fields: [documents.locationId],
    references: [locations.id],
    relationName: "location_documents",
  }),
  organization: one(organizations, {
    fields: [documents.organizationId],
    references: [organizations.id],
    relationName: "organization_documents",
  }),
  uploadedBy: one(users, {
    fields: [documents.uploadedById],
    references: [users.id],
    relationName: "user_documents",
  }),
}))

// Zod schemas for validation
export const insertDocumentSchema = createInsertSchema(documents)
export const selectDocumentSchema = createSelectSchema(documents)

export const createDocumentSchema = insertDocumentSchema.omit({
  id: true,
  createdAt: true,
  updatedAt: true,
  deletedAt: true,
})

export const updateDocumentSchema = createDocumentSchema.partial()

export const documentIdSchema = selectDocumentSchema.pick({ id: true })
