import { db } from "@/lib/db"
import { eq, and, or, desc, isNull, count, sql } from "drizzle-orm"
import { 
  notifications, 
  notificationPreferences,
  notificationTypeEnum,
  notificationStatusEnum,
  notificationPriorityEnum
} from "./schema"
import { 
  type Notification,
  type CreateNotification,
  type UpdateNotification,
  type NotificationPreference,
  type CreateNotificationPreference,
  type UpdateNotificationPreference,
  type BulkUpdateNotificationPreferences,
  type GetNotificationsQuery,
  type NotificationCount,
  type NotificationWithRelations
} from "./model"
import { users } from "../user/schema"
import { projects } from "../project/schema"
import { locations } from "../location/schema"
import { documents } from "../document/schema"
import { tasks } from "../task/schema"

/**
 * Notification Service
 * 
 * Handles all notification-related operations including:
 * - Creating and sending notifications
 * - Retrieving notifications for a user
 * - Marking notifications as read
 * - Managing notification preferences
 */
export class NotificationService {
  /**
   * Create a new notification
   */
  static async createNotification(data: CreateNotification): Promise<Notification> {
    // Check if the user has notification preferences for this type
    const preferences = await db.query.notificationPreferences.findFirst({
      where: and(
        eq(notificationPreferences.userId, data.recipientId),
        eq(notificationPreferences.organizationId, data.organizationId),
        eq(notificationPreferences.notificationType, data.type)
      )
    })

    // If preferences exist and in-app notifications are disabled, don't create the notification
    if (preferences && !preferences.inApp) {
      throw new Error("In-app notifications are disabled for this notification type")
    }

    // Create the notification
    const [notification] = await db.insert(notifications)
      .values(data)
      .returning()

    // TODO: If email notifications are enabled, send an email
    if (preferences?.email) {
      // Send email notification (implementation will depend on email service)
      // await EmailService.sendNotificationEmail(notification)
    }

    return notification
  }

  /**
   * Get notifications for a user with pagination and filtering
   */
  static async getNotifications(query: GetNotificationsQuery): Promise<NotificationWithRelations[]> {
    const { recipientId, organizationId, status, type, priority, limit = 20, offset = 0 } = query

    // Build the where clause using the optimized composite index for recipient_id + organization_id + status
    const conditions = [
      eq(notifications.recipientId, recipientId),
      eq(notifications.organizationId, organizationId)
    ];
    
    // Add optional filters if provided
    if (status) {
      conditions.push(eq(notifications.status, status));
    }
    
    if (type) {
      conditions.push(eq(notifications.type, type));
    }
    
    if (priority) {
      conditions.push(eq(notifications.priority, priority));
    }
    
    const whereClause = and(...conditions);

    // Get notifications with only necessary columns and relations
    // This reduces the amount of data transferred from the database
    const notificationsWithPartialRelations = await db.query.notifications.findMany({
      where: whereClause,
      columns: {
        id: true,
        type: true,
        title: true,
        message: true,
        status: true,
        priority: true,
        recipientId: true,
        organizationId: true,
        projectId: true,
        locationId: true,
        documentId: true,
        taskId: true,
        actorId: true,
        metadata: true,
        isRead: true,
        readAt: true,
        createdAt: true,
        updatedAt: true,
      },
      with: {
        // Only select necessary fields from relations
        recipient: {
          columns: {
            id: true,
            name: true,
            email: true,
          }
        },
        actor: {
          columns: {
            id: true,
            name: true,
          }
        },
        project: {
          columns: {
            id: true,
            name: true,
          }
        },
        document: {
          columns: {
            id: true,
            name: true,
          }
        },
        task: {
          columns: {
            id: true,
            title: true,
          }
        },
      },
      orderBy: [desc(notifications.createdAt)],
      limit,
      offset,
    })

    // Add empty location property to match NotificationWithRelations type
    const notificationsWithRelations = notificationsWithPartialRelations.map(notification => ({
      ...notification,
      location: null
    }))

    return notificationsWithRelations as NotificationWithRelations[]
  }

  /**
   * Get notification count for a user
   */
  static async getNotificationCount(recipientId: string, organizationId: string): Promise<NotificationCount> {
    // Use a single query with conditional counting for better performance
    // This avoids making two separate database queries
    const result = await db
      .select({
        // Count all notifications for total
        total: count(),
        // Count only unread notifications using a CASE expression
        unread: count(
          eq(notifications.status, "unread")
        ),
      })
      .from(notifications)
      .where(
        and(
          eq(notifications.recipientId, recipientId),
          eq(notifications.organizationId, organizationId)
        )
      )
    
    return {
      total: result[0]?.total || 0,
      unread: result[0]?.unread || 0
    }
  }

  /**
   * Get a notification by ID
   */
  static async getNotificationById(id: string): Promise<NotificationWithRelations | null> {
    const notificationWithPartialRelations = await db.query.notifications.findFirst({
      where: eq(notifications.id, id),
      with: {
        recipient: true,
        actor: true,
        project: true,
        document: true,
        task: true,
      },
    })
    
    if (!notificationWithPartialRelations) {
      return null
    }
    
    // Add empty location property to match NotificationWithRelations type
    const notificationWithRelations = {
      ...notificationWithPartialRelations,
      location: null
    }

    return notificationWithRelations as NotificationWithRelations
  }

  /**
   * Mark a notification as read
   */
  static async markNotificationAsRead(id: string): Promise<Notification> {
    const [updatedNotification] = await db.update(notifications)
      .set({
        status: "read",
        isRead: true,
        readAt: new Date(),
        updatedAt: new Date(),
      })
      .where(eq(notifications.id, id))
      .returning()

    if (!updatedNotification) {
      throw new Error("Notification not found")
    }

    return updatedNotification
  }

  /**
   * Mark all notifications as read for a user
   */
  static async markAllNotificationsAsRead(recipientId: string, organizationId: string): Promise<number> {
    const result = await db.update(notifications)
      .set({
        status: "read",
        isRead: true,
        readAt: new Date(),
        updatedAt: new Date(),
      })
      .where(
        and(
          eq(notifications.recipientId, recipientId),
          eq(notifications.organizationId, organizationId),
          eq(notifications.status, "unread")
        )
      )

    return result.length || 0
  }

  /**
   * Delete a notification
   */
  static async deleteNotification(id: string): Promise<Notification> {
    const [deletedNotification] = await db.delete(notifications)
      .where(eq(notifications.id, id))
      .returning()

    if (!deletedNotification) {
      throw new Error("Notification not found")
    }

    return deletedNotification
  }

  /**
   * Archive a notification
   */
  static async archiveNotification(id: string): Promise<Notification> {
    const [archivedNotification] = await db.update(notifications)
      .set({
        status: "archived",
        updatedAt: new Date(),
      })
      .where(eq(notifications.id, id))
      .returning()

    if (!archivedNotification) {
      throw new Error("Notification not found")
    }

    return archivedNotification
  }

  /**
   * Get notification preferences for a user
   */
  static async getNotificationPreferences(userId: string, organizationId: string): Promise<NotificationPreference[]> {
    const preferences = await db.query.notificationPreferences.findMany({
      where: and(
        eq(notificationPreferences.userId, userId),
        eq(notificationPreferences.organizationId, organizationId)
      ),
      orderBy: [notificationPreferences.notificationType],
    })

    return preferences
  }

  /**
   * Create or update notification preferences for a user
   */
  static async createOrUpdateNotificationPreference(data: CreateNotificationPreference): Promise<NotificationPreference> {
    // Check if preference already exists
    const existingPreference = await db.query.notificationPreferences.findFirst({
      where: and(
        eq(notificationPreferences.userId, data.userId),
        eq(notificationPreferences.organizationId, data.organizationId),
        eq(notificationPreferences.notificationType, data.notificationType)
      ),
    })

    if (existingPreference) {
      // Update existing preference
      const [updatedPreference] = await db.update(notificationPreferences)
        .set({
          inApp: data.inApp,
          email: data.email,
          updatedAt: new Date(),
        })
        .where(eq(notificationPreferences.id, existingPreference.id))
        .returning()

      return updatedPreference
    } else {
      // Create new preference
      const [newPreference] = await db.insert(notificationPreferences)
        .values(data)
        .returning()

      return newPreference
    }
  }

  /**
   * Bulk update notification preferences for a user
   */
  static async bulkUpdateNotificationPreferences(data: BulkUpdateNotificationPreferences): Promise<NotificationPreference[]> {
    const { userId, organizationId, preferences } = data
    const updatedPreferences: NotificationPreference[] = []

    // Process each preference in the array
    for (const pref of preferences) {
      const preference = await this.createOrUpdateNotificationPreference({
        userId,
        organizationId,
        notificationType: pref.notificationType,
        inApp: pref.inApp,
        email: pref.email,
      })
      updatedPreferences.push(preference)
    }

    return updatedPreferences
  }

  /**
   * Initialize default notification preferences for a user
   */
  static async initializeDefaultPreferences(userId: string, organizationId: string): Promise<NotificationPreference[]> {
    const defaultPreferences: CreateNotificationPreference[] = Object.values(notificationTypeEnum.enumValues).map(type => ({
      userId,
      organizationId,
      notificationType: type,
      inApp: true,
      email: true,
    }))

    const createdPreferences: NotificationPreference[] = []

    for (const pref of defaultPreferences) {
      const preference = await this.createOrUpdateNotificationPreference(pref)
      createdPreferences.push(preference)
    }

    return createdPreferences
  }

  /**
   * Delete all notification preferences for a user
   */
  static async deleteNotificationPreferences(userId: string, organizationId: string): Promise<number> {
    const result = await db.delete(notificationPreferences)
      .where(
        and(
          eq(notificationPreferences.userId, userId),
          eq(notificationPreferences.organizationId, organizationId)
        )
      )

    return result.length || 0
  }
}
