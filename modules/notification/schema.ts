import { relations } from "drizzle-orm"
import { pgTable, uuid, varchar, text, timestamp, jsonb, boolean, pgEnum } from "drizzle-orm/pg-core"
import { createInsertSchema, createSelectSchema } from "drizzle-zod"

import { organizations } from "../organization/schema"
import { users } from "../user/schema"
import { projects } from "../project/schema"
import { locations } from "../location/schema"
import { documents } from "../document/schema"
import { tasks } from "../task/schema"

// Notification types enum
export const NOTIFICATION_TYPES = [
  "project_created",
  "project_updated",
  "project_deleted",
  "location_created",
  "location_updated",
  "location_deleted",
  "location_approved",
  "document_uploaded",
  "document_updated",
  "document_deleted",
  "task_created",
  "task_updated",
  "task_completed",
  "task_assigned",
  "member_added",
  "member_removed",
  "member_role_changed",
  "subscription_created",
  "subscription_updated",
  "subscription_canceled",
  "payment_succeeded",
  "payment_failed",
  "comment_added",
  "system_notification"
] as const;

// Notification status enum
export const NOTIFICATION_STATUS = ["unread", "read", "archived"] as const;

// Notification priority enum
export const NOTIFICATION_PRIORITY = ["low", "medium", "high", "urgent"] as const;

// Define pgEnums
export const notificationTypeEnum = pgEnum("notification_type", NOTIFICATION_TYPES);
export const notificationStatusEnum = pgEnum("notification_status", NOTIFICATION_STATUS);
export const notificationPriorityEnum = pgEnum("notification_priority", NOTIFICATION_PRIORITY);

// Notifications Table
export const notifications = pgTable("notifications", {
  id: uuid("id").primaryKey().defaultRandom(),
  type: notificationTypeEnum("type").notNull(),
  title: varchar("title", { length: 255 }).notNull(),
  message: text("message").notNull(),
  status: notificationStatusEnum("status").default("unread").notNull(),
  priority: notificationPriorityEnum("priority").default("medium").notNull(),
  recipientId: uuid("recipient_id")
    .references(() => users.id, { onDelete: "cascade" })
    .notNull(),
  organizationId: uuid("organization_id")
    .references(() => organizations.id, { onDelete: "cascade" })
    .notNull(),
  projectId: uuid("project_id")
    .references(() => projects.id, { onDelete: "set null" }),
  locationId: uuid("location_id")
    .references(() => locations.id, { onDelete: "set null" }),
  documentId: uuid("document_id")
    .references(() => documents.id, { onDelete: "set null" }),
  taskId: uuid("task_id")
    .references(() => tasks.id, { onDelete: "set null" }),
  actorId: uuid("actor_id")
    .references(() => users.id, { onDelete: "set null" }),
  metadata: jsonb("metadata").$type<Record<string, unknown>>().default({}),
  isRead: boolean("is_read").default(false).notNull(),
  readAt: timestamp("read_at"),
  expiresAt: timestamp("expires_at"),
  createdAt: timestamp("created_at").defaultNow().notNull(),
  updatedAt: timestamp("updated_at").defaultNow().notNull(),
})

// Notification Preferences Table
export const notificationPreferences = pgTable("notification_preferences", {
  id: uuid("id").primaryKey().defaultRandom(),
  userId: uuid("user_id")
    .references(() => users.id, { onDelete: "cascade" })
    .notNull(),
  organizationId: uuid("organization_id")
    .references(() => organizations.id, { onDelete: "cascade" })
    .notNull(),
  notificationType: notificationTypeEnum("notification_type").notNull(),
  inApp: boolean("in_app").default(true).notNull(),
  email: boolean("email").default(true).notNull(),
  createdAt: timestamp("created_at").defaultNow().notNull(),
  updatedAt: timestamp("updated_at").defaultNow().notNull(),
})

// Relations
export const notificationsRelations = relations(notifications, ({ one }) => ({
  recipient: one(users, {
    fields: [notifications.recipientId],
    references: [users.id],
    relationName: "user_notifications",
  }),
  organization: one(organizations, {
    fields: [notifications.organizationId],
    references: [organizations.id],
    relationName: "organization_notifications",
  }),
  project: one(projects, {
    fields: [notifications.projectId],
    references: [projects.id],
    relationName: "project_notifications",
  }),
  location: one(locations, {
    fields: [notifications.locationId],
    references: [locations.id],
    relationName: "location_notifications",
  }),
  document: one(documents, {
    fields: [notifications.documentId],
    references: [documents.id],
    relationName: "document_notifications",
  }),
  task: one(tasks, {
    fields: [notifications.taskId],
    references: [tasks.id],
    relationName: "task_notifications",
  }),
  actor: one(users, {
    fields: [notifications.actorId],
    references: [users.id],
    relationName: "actor_notifications",
  }),
}))

export const notificationPreferencesRelations = relations(notificationPreferences, ({ one }) => ({
  user: one(users, {
    fields: [notificationPreferences.userId],
    references: [users.id],
    relationName: "user_notification_preferences",
  }),
  organization: one(organizations, {
    fields: [notificationPreferences.organizationId],
    references: [organizations.id],
    relationName: "organization_notification_preferences",
  }),
}))

// Zod schemas for validation
export const insertNotificationSchema = createInsertSchema(notifications)
export const selectNotificationSchema = createSelectSchema(notifications)

export const createNotificationSchema = insertNotificationSchema.omit({
  id: true,
  createdAt: true,
  updatedAt: true,
  readAt: true,
})

export const updateNotificationSchema = createNotificationSchema.partial()

export const insertNotificationPreferenceSchema = createInsertSchema(notificationPreferences)
export const selectNotificationPreferenceSchema = createSelectSchema(notificationPreferences)

export const createNotificationPreferenceSchema = insertNotificationPreferenceSchema.omit({
  id: true,
  createdAt: true,
  updatedAt: true,
})

export const updateNotificationPreferenceSchema = createNotificationPreferenceSchema.partial()
