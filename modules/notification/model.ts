import { z } from "zod"
import { createInsertSchema, createSelectSchema } from "drizzle-zod"
import { notifications, notificationPreferences, NOTIFICATION_TYPES, NOTIFICATION_STATUS, NOTIFICATION_PRIORITY } from "./schema"

// Notification Types
export const NotificationTypeEnum = z.enum(NOTIFICATION_TYPES)
export type NotificationType = z.infer<typeof NotificationTypeEnum>

export const NotificationStatusEnum = z.enum(NOTIFICATION_STATUS)
export type NotificationStatus = z.infer<typeof NotificationStatusEnum>

export const NotificationPriorityEnum = z.enum(NOTIFICATION_PRIORITY)
export type NotificationPriority = z.infer<typeof NotificationPriorityEnum>

// Notification Models
export const insertNotificationSchema = createInsertSchema(notifications).extend({
  metadata: z.record(z.string(), z.unknown()).optional().default({})
})
export const selectNotificationSchema = createSelectSchema(notifications).extend({
  metadata: z.record(z.string(), z.unknown()).nullable().default({})
})

export const createNotificationSchema = insertNotificationSchema.omit({
  id: true,
  createdAt: true,
  updatedAt: true,
  readAt: true,
}).extend({
  metadata: z.record(z.string(), z.unknown()).optional().default({})
})

export const updateNotificationSchema = createNotificationSchema.partial()

export const markNotificationAsReadSchema = z.object({
  id: z.string().uuid(),
})

export const markAllNotificationsAsReadSchema = z.object({
  recipientId: z.string().uuid(),
  organizationId: z.string().uuid(),
})

export const deleteNotificationSchema = z.object({
  id: z.string().uuid(),
})

export type Notification = z.infer<typeof selectNotificationSchema>
export type CreateNotification = z.infer<typeof createNotificationSchema>
export type UpdateNotification = z.infer<typeof updateNotificationSchema>

// Notification with related entities
export const notificationWithRelationsSchema = selectNotificationSchema.extend({
  metadata: z.record(z.string(), z.unknown()).nullable().default({}),
  recipient: z.object({
    id: z.string().uuid(),
    name: z.string().nullable(),
    email: z.string().email(),
    avatar: z.string().nullable(),
  }).nullable(),
  actor: z.object({
    id: z.string().uuid(),
    name: z.string().nullable(),
    email: z.string().email(),
    avatar: z.string().nullable(),
  }).nullable(),
  project: z.object({
    id: z.string().uuid(),
    name: z.string(),
  }).nullable(),
  location: z.object({
    id: z.string().uuid(),
    name: z.string(),
  }).nullable(),
  document: z.object({
    id: z.string().uuid(),
    name: z.string(),
  }).nullable(),
  task: z.object({
    id: z.string().uuid(),
    title: z.string(),
  }).nullable(),
})

export type NotificationWithRelations = z.infer<typeof notificationWithRelationsSchema>

// Notification Preference Models
export const insertNotificationPreferenceSchema = createInsertSchema(notificationPreferences)
export const selectNotificationPreferenceSchema = createSelectSchema(notificationPreferences)

export const createNotificationPreferenceSchema = insertNotificationPreferenceSchema.omit({
  id: true,
  createdAt: true,
  updatedAt: true,
})

export const updateNotificationPreferenceSchema = createNotificationPreferenceSchema.partial()

export const bulkUpdateNotificationPreferencesSchema = z.object({
  userId: z.string().uuid(),
  organizationId: z.string().uuid(),
  preferences: z.array(
    z.object({
      notificationType: NotificationTypeEnum,
      inApp: z.boolean(),
      email: z.boolean(),
    })
  ),
})

export type NotificationPreference = z.infer<typeof selectNotificationPreferenceSchema>
export type CreateNotificationPreference = z.infer<typeof createNotificationPreferenceSchema>
export type UpdateNotificationPreference = z.infer<typeof updateNotificationPreferenceSchema>
export type BulkUpdateNotificationPreferences = z.infer<typeof bulkUpdateNotificationPreferencesSchema>

// Notification Count
export const notificationCountSchema = z.object({
  total: z.number(),
  unread: z.number(),
})

export type NotificationCount = z.infer<typeof notificationCountSchema>

// Notification Query Params
export const getNotificationsQuerySchema = z.object({
  recipientId: z.string().uuid(),
  organizationId: z.string().uuid(),
  status: NotificationStatusEnum.optional(),
  type: NotificationTypeEnum.optional(),
  priority: NotificationPriorityEnum.optional(),
  limit: z.number().min(1).max(100).default(20),
  offset: z.number().min(0).default(0),
})

export type GetNotificationsQuery = z.infer<typeof getNotificationsQuerySchema>
