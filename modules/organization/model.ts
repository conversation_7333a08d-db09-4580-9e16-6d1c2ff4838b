import { z } from "zod"
import { USER_ROLES } from "../shared/constants"

// Organization domain models
export type Organization = {
  id: string
  name: string
  slug: string
  description?: string
  logo?: string
  stytchOrganizationId?: string
  polarCustomerId?: string
  subscriptionTier?: string
  subscriptionStatus?: string
  subscriptionValidUntil?: Date
  maxProjects?: number
  maxLocationsPerProject?: number
  maxTeamMembers?: number
  maxStorage?: number
  metadata?: Record<string, any>
  isActive: boolean
  createdAt: Date
  updatedAt: Date
  deletedAt?: Date | null
}

export type OrganizationUserRole = {
  id: string
  organizationId: string
  userId: string
  role: UserRole
  createdAt: Date
  updatedAt: Date
}

export type OrganizationInvitation = {
  id: string
  email: string
  organizationId: string
  role: UserRole
  token: string
  expiresAt: Date
  createdAt: Date
  updatedAt: Date
}

export type OrganizationWithMembers = Organization & {
  members: Array<{
    userId: string
    role: UserRole
    user: {
      id: string
      name?: string
      email: string
      avatar?: string
    }
  }>
}

export type UserRole = (typeof USER_ROLES)[number]

// Validation schemas
export const organizationSchema = z.object({
  id: z.string().uuid(),
  name: z.string().min(1).max(255),
  slug: z
    .string()
    .min(1)
    .max(255)
    .regex(/^[a-z0-9]+(?:-[a-z0-9]+)*$/),
  description: z.string().optional(),
  logo: z.string().optional(),
  stytchOrganizationId: z.string().optional(),
  polarCustomerId: z.string().optional(),
  subscriptionTier: z.string().optional(),
  subscriptionStatus: z.string().optional(),
  subscriptionValidUntil: z.date().optional(),
  maxProjects: z.number().optional(),
  maxLocationsPerProject: z.number().optional(),
  maxTeamMembers: z.number().optional(),
  maxStorage: z.number().optional(),
  metadata: z.record(z.unknown()).optional(),
  isActive: z.boolean(),
  createdAt: z.date(),
  updatedAt: z.date(),
  deletedAt: z.date().nullable().optional(),
})

export const organizationUserRoleSchema = z.object({
  id: z.string().uuid(),
  organizationId: z.string().uuid(),
  userId: z.string().uuid(),
  role: z.enum(USER_ROLES as unknown as [string, ...string[]]),
  createdAt: z.date(),
  updatedAt: z.date(),
})

export const organizationInvitationSchema = z.object({
  id: z.string().uuid(),
  email: z.string().email(),
  organizationId: z.string().uuid(),
  role: z.enum(USER_ROLES as unknown as [string, ...string[]]),
  token: z.string(),
  expiresAt: z.date(),
  createdAt: z.date(),
  updatedAt: z.date(),
})

export const createOrganizationSchema = z.object({
  name: z.string().min(1).max(255),
  slug: z
    .string()
    .min(1)
    .max(255)
    .regex(/^[a-z0-9]+(?:-[a-z0-9]+)*$/)
    .optional(),
  description: z.string().optional(),
  logo: z.string().optional(),
  metadata: z.record(z.any()).optional(),
})

export const updateOrganizationSchema = createOrganizationSchema.partial()

export const inviteToOrganizationSchema = z.object({
  email: z.string().email(),
  role: z.enum(USER_ROLES as unknown as [string, ...string[]]),
})
