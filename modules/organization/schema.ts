import { relations } from "drizzle-orm"
import { pgTable, uuid, varchar, text, timestamp, jsonb, boolean, integer } from "drizzle-orm/pg-core"
import { createInsertSchema, createSelectSchema } from "drizzle-zod"

import { users } from "../user/schema"
import { projects } from "../project/schema"
import { locations } from "../location/schema"

export const organizations = pgTable("organizations", {
  id: uuid("id").primaryKey().defaultRandom(),
  name: varchar("name", { length: 255 }).notNull(),
  slug: varchar("slug", { length: 255 }).notNull().unique(),
  description: text("description"),
  logo: varchar("logo", { length: 255 }),
  stytchOrganizationId: varchar("stytch_organization_id", { length: 255 }).unique(),
  polarCustomerId: varchar("polar_customer_id", { length: 255 }).unique(),
  subscriptionTier: varchar("subscription_tier", { length: 50 }).default("basic"),
  subscriptionStatus: varchar("subscription_status", { length: 50 }),
  subscriptionValidUntil: timestamp("subscription_valid_until"),
  maxProjects: integer("max_projects").default(5),
  maxLocationsPerProject: integer("max_locations_per_project").default(50),
  maxTeamMembers: integer("max_team_members").default(5),
  maxStorage: integer("max_storage").default(10), // in GB
  metadata: jsonb("metadata").$type<Record<string, unknown>>(),
  isActive: boolean("is_active").default(true),
  createdAt: timestamp("created_at").defaultNow().notNull(),
  updatedAt: timestamp("updated_at").defaultNow().notNull(),
  deletedAt: timestamp("deleted_at"),
})

export const organizationsRelations = relations(organizations, ({ many }) => ({
  members: many(organizationUserRoles),
  projects: many(projects),
  locations: many(locations),
}))

export const organizationUserRoles = pgTable("organization_user_roles", {
  id: uuid("id").primaryKey().defaultRandom(),
  organizationId: uuid("organization_id")
    .references(() => organizations.id, { onDelete: "cascade" })
    .notNull(),
  userId: uuid("user_id")
    .references(() => users.id, { onDelete: "cascade" })
    .notNull(),
  role: varchar("role", { length: 50 }).notNull(),
  createdAt: timestamp("created_at").defaultNow().notNull(),
  updatedAt: timestamp("updated_at").defaultNow().notNull(),
})

export const organizationUserRolesRelations = relations(organizationUserRoles, ({ one }) => ({
  organization: one(organizations, {
    fields: [organizationUserRoles.organizationId],
    references: [organizations.id],
  }),
  user: one(users, {
    fields: [organizationUserRoles.userId],
    references: [users.id],
  }),
}))

// Zod schemas for validation
export const insertOrganizationSchema = createInsertSchema(organizations)
export const selectOrganizationSchema = createSelectSchema(organizations)

export const createOrganizationSchema = insertOrganizationSchema.omit({
  id: true,
  createdAt: true,
  updatedAt: true,
  deletedAt: true,
})

export const updateOrganizationSchema = createOrganizationSchema.partial()

export const organizationIdSchema = selectOrganizationSchema.pick({ id: true })

export const insertOrganizationUserRoleSchema = createInsertSchema(organizationUserRoles)
export const selectOrganizationUserRoleSchema = createSelectSchema(organizationUserRoles)

export const createOrganizationUserRoleSchema = insertOrganizationUserRoleSchema.omit({
  id: true,
  createdAt: true,
  updatedAt: true,
})

export const organizationInvitations = pgTable("organization_invitations", {
  id: uuid("id").primaryKey().defaultRandom(),
  organizationId: uuid("organization_id")
    .references(() => organizations.id, { onDelete: "cascade" })
    .notNull(),
  email: varchar("email", { length: 255 }).notNull(),
  role: varchar("role", { length: 50 }).notNull(),
  token: varchar("token", { length: 255 }).notNull().unique(),
  expiresAt: timestamp("expires_at").notNull(),
  createdAt: timestamp("created_at").defaultNow().notNull(),
  updatedAt: timestamp("updated_at").defaultNow().notNull(),
})

export const organizationInvitationsRelations = relations(organizationInvitations, ({ one }) => ({
  organization: one(organizations, {
    fields: [organizationInvitations.organizationId],
    references: [organizations.id],
  }),
}))

export const insertOrganizationInvitationSchema = createInsertSchema(organizationInvitations)
export const selectOrganizationInvitationSchema = createSelectSchema(organizationInvitations)

export const createOrganizationInvitationSchema = insertOrganizationInvitationSchema.omit({
  id: true,
  createdAt: true,
  updatedAt: true,
})
