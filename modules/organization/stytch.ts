import { getStytchB2BClient } from "@/lib/stytch-b2b" // This is the server-side client
import { db } from "@/lib/db"
import { organizations } from "./schema"
import { eq } from "drizzle-orm"

/**
 * Creates a new organization in Stytch
 * @param name Organization name
 * @param slug Organization slug
 * @param allowedDomains Optional array of allowed email domains
 * @returns The created organization data from Stytch
 */
export async function createStytchOrganization(
  name: string,
  slug: string,
  allowedDomains?: string[]
) {
  try {
    const stytchClient = getStytchB2BClient()
    
    const organizationParams: {
      organization_name: string;
      organization_slug: string;
      email_allowed_domains?: string[];
    } = {
      organization_name: name,
      organization_slug: slug,
    }

    if (allowedDomains && allowedDomains.length > 0) {
      organizationParams.email_allowed_domains = allowedDomains
    }

    const response = await stytchClient.organizations.create(organizationParams)
    
    return response.organization
  } catch (error) {
    console.error("Error creating Stytch organization:", error)
    throw new Error(`Failed to create organization in Stytch: ${error instanceof Error ? error.message : String(error)}`)
  }
}

/**
 * Gets an organization from Stytch by ID
 * @param organizationId Stytch organization ID
 * @returns The organization data from Stytch
 */
export async function getStytchOrganization(organizationId: string) {
  try {
    const stytchClient = getStytchB2BClient()
    
    const response = await stytchClient.organizations.get({
      organization_id: organizationId
    })
    
    return response.organization
  } catch (error) {
    console.error("Error getting Stytch organization:", error)
    throw new Error(`Failed to get organization from Stytch: ${error instanceof Error ? error.message : String(error)}`)
  }
}

/**
 * Updates an organization in Stytch
 * @param organizationId Stytch organization ID
 * @param data Object containing fields to update
 * @returns The updated organization data from Stytch
 */
export async function updateStytchOrganization(
  organizationId: string,
  data: {
    name?: string;
    slug?: string;
    allowedDomains?: string[];
  }
) {
  try {
    const stytchClient = getStytchB2BClient()
    
    const updateParams: {
      organization_id: string;
      organization_name?: string;
      organization_slug?: string;
      email_allowed_domains?: string[];
    } = {
      organization_id: organizationId
    }

    if (data.name) {
      updateParams.organization_name = data.name
    }

    if (data.slug) {
      updateParams.organization_slug = data.slug
    }

    if (data.allowedDomains) {
      updateParams.email_allowed_domains = data.allowedDomains
    }

    const response = await stytchClient.organizations.update(updateParams)
    
    return response.organization
  } catch (error) {
    console.error("Error updating Stytch organization:", error)
    throw new Error(`Failed to update organization in Stytch: ${error instanceof Error ? error.message : String(error)}`)
  }
}

/**
 * Deletes an organization in Stytch
 * @param organizationId Stytch organization ID
 * @returns Success status
 */
export async function deleteStytchOrganization(organizationId: string) {
  try {
    const stytchClient = getStytchB2BClient()
    
    await stytchClient.organizations.delete({
      organization_id: organizationId
    })
    
    return { success: true }
  } catch (error) {
    console.error("Error deleting Stytch organization:", error)
    throw new Error(`Failed to delete organization in Stytch: ${error instanceof Error ? error.message : String(error)}`)
  }
}

/**
 * Gets all organizations from Stytch
 * @returns Array of organizations from Stytch
 */
export async function getStytchOrganizations() {
  try {
    const stytchClient = getStytchB2BClient()
    
    const response = await stytchClient.organizations.search({})
    
    return response.organizations
  } catch (error) {
    console.error("Error getting Stytch organizations:", error)
    throw new Error(`Failed to get organizations from Stytch: ${error instanceof Error ? error.message : String(error)}`)
  }
}

/**
 * Invites a user to an organization in Stytch
 * @param organizationId Stytch organization ID
 * @param email User email to invite
 * @param role Role to assign to the user
 * @returns The invitation data from Stytch
 */
export async function inviteUserToOrganization(
  organizationId: string,
  email: string,
  role: string
) {
  try {
    const stytchClient = getStytchB2BClient()
    
    const response = await stytchClient.organizations.members.create({
      organization_id: organizationId,
      email_address: email,
      roles: [role]
    })
    
    return response.member
  } catch (error) {
    console.error("Error inviting user to Stytch organization:", error)
    throw new Error(`Failed to invite user to organization in Stytch: ${error instanceof Error ? error.message : String(error)}`)
  }
}

/**
 * Gets all members of an organization from Stytch
 * @param organizationId Stytch organization ID
 * @returns Array of organization members from Stytch
 */
export async function getStytchOrganizationMembers(organizationId: string) {
  try {
    const stytchClient = getStytchB2BClient()
    
    const response = await stytchClient.organizations.members.search({
      organization_ids: [organizationId]
    })
    
    return response.members
  } catch (error) {
    console.error("Error getting Stytch organization members:", error)
    throw new Error(`Failed to get organization members from Stytch: ${error instanceof Error ? error.message : String(error)}`)
  }
}

/**
 * Removes a user from an organization in Stytch
 * @param organizationId Stytch organization ID
 * @param userId Stytch user ID
 * @returns Success status
 */
export async function removeUserFromOrganization(
  organizationId: string,
  userId: string
) {
  try {
    const stytchClient = getStytchB2BClient()
    
    await stytchClient.organizations.members.delete({
      organization_id: organizationId,
      member_id: userId
    })
    
    return { success: true }
  } catch (error) {
    console.error("Error removing user from Stytch organization:", error)
    throw new Error(`Failed to remove user from organization in Stytch: ${error instanceof Error ? error.message : String(error)}`)
  }
}

/**
 * Gets an organization from the database by Stytch ID
 * @param stytchOrganizationId Stytch organization ID
 * @returns The organization from the database
 */
export async function getOrganizationByStytchId(stytchOrganizationId: string) {
  try {
    const result = await db.query.organizations.findFirst({
      where: eq(organizations.stytchOrganizationId, stytchOrganizationId)
    })
    
    return result
  } catch (error) {
    console.error("Error getting organization by Stytch ID:", error)
    throw new Error(`Failed to get organization by Stytch ID: ${error instanceof Error ? error.message : String(error)}`)
  }
}
