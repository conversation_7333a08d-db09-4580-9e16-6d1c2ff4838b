import { db } from "@/lib/db"
import { organizations, organizationUserRoles, organizationInvitations } from "./schema"
import { users } from "../user/schema"
import { eq, and, isNull } from "drizzle-orm"
import { NotFoundError, ConflictError } from "../shared/errors"
import { type Organization, createOrganizationSchema, updateOrganizationSchema } from "./model"
import { generateSlug } from "@/lib/utils/string"
import { generateToken } from "@/lib/utils/auth"
import {
  createStytchOrganization,
  updateStytchOrganization,
  deleteStytchOrganization,
  inviteUserToOrganization as inviteUserToStytchOrganization,
  // getOrganizationByStytchId, // Commented out as it's not used
} from "./stytch"

export async function getOrganizations(userId: string) {
  return db.query.organizationUserRoles.findMany({
    where: eq(organizationUserRoles.userId, userId),
    with: {
      organization: true,
    },
  })
}

export async function getOrganization(id: string) {
  const organization = await db.query.organizations.findFirst({
    where: and(eq(organizations.id, id), isNull(organizations.deletedAt)),
    with: {
      members: {
        with: {
          user: true,
        },
      },
    },
  })

  if (!organization) {
    throw new NotFoundError(`Organization with ID ${id} not found`)
  }

  return organization
}

export async function getOrganizationBySlug(slug: string) {
  const organization = await db.query.organizations.findFirst({
    where: and(eq(organizations.slug, slug), isNull(organizations.deletedAt)),
    with: {
      members: {
        with: {
          user: true,
        },
      },
    },
  })

  if (!organization) {
    throw new NotFoundError(`Organization with slug ${slug} not found`)
  }

  return organization
}

export async function createOrganization(
  data: Omit<Organization, "id" | "createdAt" | "updatedAt" | "deletedAt" | "isActive">,
  userId: string,
) {
  const validatedData = createOrganizationSchema.parse(data)

  // Generate slug if not provided
  if (!validatedData.slug) {
    validatedData.slug = generateSlug(validatedData.name)
  }

  // Check if slug is already taken
  const existingOrg = await db.query.organizations.findFirst({
    where: eq(organizations.slug, validatedData.slug),
  })

  if (existingOrg) {
    throw new ConflictError(`Organization with slug ${validatedData.slug} already exists`)
  }

  try {
    // Ensure slug is defined
    const slug = validatedData.slug || generateSlug(validatedData.name)
    
    // Create organization in Stytch
    const stytchOrganization = await createStytchOrganization(
      validatedData.name,
      slug
    )

    // Create organization in database
    const [organization] = await db
      .insert(organizations)
      .values({
        name: validatedData.name,
        slug: slug,
        description: validatedData.description,
        logo: validatedData.logo,
        metadata: validatedData.metadata,
        stytchOrganizationId: stytchOrganization.organization_id,
        isActive: true,
      })
      .returning()

    // Add creator as owner
    await db.insert(organizationUserRoles).values({
      organizationId: organization.id,
      userId,
      role: "owner",
    })

    return organization
  } catch (error) {
    console.error("Error creating organization:", error)
    throw new Error(`Failed to create organization: ${error instanceof Error ? error.message : String(error)}`)
  }
}

export async function updateOrganization(
  id: string,
  data: Partial<Omit<Organization, "id" | "createdAt" | "updatedAt" | "deletedAt">>,
) {
  const validatedData = updateOrganizationSchema.parse(data)

  // If slug is being updated, check if it's already taken
    if (validatedData.slug) {
    const existingOrg = await db.query.organizations.findFirst({
      where: and(eq(organizations.slug, validatedData.slug), eq(organizations.id, id)),
    })

    if (existingOrg) {
      throw new ConflictError(`Organization with slug ${validatedData.slug} already exists`)
    }
  }

  try {
    // Get the organization to update
    const existingOrg = await getOrganization(id)
    
    if (existingOrg.stytchOrganizationId && (validatedData.name || validatedData.slug)) {
      // Update organization in Stytch
      await updateStytchOrganization(existingOrg.stytchOrganizationId, {
        name: validatedData.name,
        slug: validatedData.slug,
      })
    }

    // Update organization in database
    const [organization] = await db
      .update(organizations)
      .set({
        ...validatedData,
        updatedAt: new Date(),
      })
      .where(and(eq(organizations.id, id), isNull(organizations.deletedAt)))
      .returning()

    if (!organization) {
      throw new NotFoundError(`Organization with ID ${id} not found`)
    }

    return organization
  } catch (error) {
    console.error("Error updating organization:", error)
    throw new Error(`Failed to update organization: ${error instanceof Error ? error.message : String(error)}`)
  }
}

export async function deleteOrganization(id: string) {
  try {
    // Get the organization to delete
    const existingOrg = await getOrganization(id)
    
    if (existingOrg.stytchOrganizationId) {
      // Delete organization in Stytch
      await deleteStytchOrganization(existingOrg.stytchOrganizationId)
    }

    // Soft delete organization in database
    const [organization] = await db
      .update(organizations)
      .set({
        deletedAt: new Date(),
        updatedAt: new Date(),
      })
      .where(and(eq(organizations.id, id), isNull(organizations.deletedAt)))
      .returning()

    if (!organization) {
      throw new NotFoundError(`Organization with ID ${id} not found`)
    }

    return organization
  } catch (error) {
    console.error("Error deleting organization:", error)
    throw new Error(`Failed to delete organization: ${error instanceof Error ? error.message : String(error)}`)
  }
}

export async function inviteToOrganization(organizationId: string, email: string, role: string) {
  try {
    // Get the organization
    const organization = await getOrganization(organizationId)
    
    if (organization.stytchOrganizationId) {
      // Invite user to organization in Stytch
      await inviteUserToStytchOrganization(organization.stytchOrganizationId, email, role)
    }

    // Generate invitation token for our system
    const token = generateToken()
    const expiresAt = new Date()
    expiresAt.setDate(expiresAt.getDate() + 7) // Expires in 7 days

    const [invitation] = await db
      .insert(organizationInvitations)
      .values({
        organizationId,
        email,
        role,
        token,
        expiresAt,
      })
      .returning()

    return invitation
  } catch (error) {
    console.error("Error inviting user to organization:", error)
    throw new Error(`Failed to invite user to organization: ${error instanceof Error ? error.message : String(error)}`)
  }
}

/**
 * Gets an organization from the database by Stytch organization ID
 * @param stytchOrganizationId Stytch organization ID
 * @returns The organization from the database
 */
export async function findOrganizationByStytchId(stytchOrganizationId: string) {
  const organization = await db.query.organizations.findFirst({
    where: eq(organizations.stytchOrganizationId, stytchOrganizationId),
    with: {
      members: {
        with: {
          user: true,
        },
      },
    },
  })

  if (!organization) {
    throw new NotFoundError(`Organization with Stytch ID ${stytchOrganizationId} not found`)
  }

  return organization
}

/**
 * Gets an organization from the database by Polar customer ID
 * @param polarCustomerId Polar customer ID
 * @returns The organization from the database or null if not found
 */
export async function getOrganizationByPolarCustomerId(polarCustomerId: string) {
  const organization = await db.query.organizations.findFirst({
    where: eq(organizations.polarCustomerId, polarCustomerId),
  })

  return organization
}

export async function acceptInvitation(token: string, userId: string) {
  try {
    const invitation = await db.query.organizationInvitations.findFirst({
      where: eq(organizationInvitations.token, token),
    })

    if (!invitation) {
      throw new NotFoundError("Invitation not found or has expired")
    }

    if (invitation.expiresAt < new Date()) {
      throw new NotFoundError("Invitation has expired")
    }

    // Get the user
    const user = await db.query.users.findFirst({
      where: eq(users.id, userId),
    })

    if (!user) {
      throw new NotFoundError(`User with ID ${userId} not found`)
    }

    // Add user to organization in database
    await db.insert(organizationUserRoles).values({
      organizationId: invitation.organizationId,
      userId,
      role: invitation.role,
    })

    // Delete the invitation
    await db.delete(organizationInvitations).where(eq(organizationInvitations.id, invitation.id))

    return invitation
  } catch (error) {
    console.error("Error accepting invitation:", error)
    throw new Error(`Failed to accept invitation: ${error instanceof Error ? error.message : String(error)}`)
  }
}
