import type { z } from "zod"
import type { InferSelectModel } from "drizzle-orm"
import type { 
  favoriteLists, 
  favoriteListLocations 
} from "./schema"

// Types based on schema
export type FavoriteList = InferSelectModel<typeof favoriteLists>
export type FavoriteListLocation = InferSelectModel<typeof favoriteListLocations>

// Extended types with relations
export type FavoriteListWithLocations = FavoriteList & {
  locations: {
    id: string
    name: string
    type: string
    status: string
    addedAt: string
  }[]
}

// API response types
export type FavoriteListResponse = {
  id: string
  name: string
  type: "project" | "scene" | "custom"
  projectId: string | null
  sceneId: string | null
  createdBy: string
  createdAt: string
  updatedAt: string
  locations: {
    id: string
    name: string
    type: string
    status: string
    addedAt: string
  }[]
}

// API request types
export type CreateFavoriteListRequest = z.infer<typeof createFavoriteListSchema>
export type AddLocationToFavoriteListRequest = z.infer<typeof addLocationToFavoriteListSchema>
export type RemoveFavoriteLocationRequest = z.infer<typeof removeFavoriteLocationSchema>
export type UpdateFavoriteListRequest = z.infer<typeof updateFavoriteListSchema>
export type DeleteFavoriteListRequest = z.infer<typeof deleteFavoriteListSchema>

// Import schemas after type definitions to avoid circular dependencies
import type {
  createFavoriteListSchema,
  addLocationToFavoriteListSchema,
  removeFavoriteLocationSchema,
  updateFavoriteListSchema,
  deleteFavoriteListSchema
} from "./schema"
