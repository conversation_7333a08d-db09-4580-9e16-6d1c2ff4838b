import { relations } from "drizzle-orm"
import { 
  pgTable, 
  uuid, 
  varchar, 
  timestamp, 
  uniqueIndex
} from "drizzle-orm/pg-core"
import { createInsertSchema, createSelectSchema } from "drizzle-zod"
import { z } from "zod"

import { organizations } from "@/modules/organization/schema"
import { projects } from "@/modules/project/schema"
import { scenes } from "@/modules/scene/schema"
import { users } from "@/modules/user/schema"
import { locations } from "@/modules/location/schema"

// Favorite Lists Table
export const favoriteLists = pgTable("favorite_lists", {
  id: uuid("id").defaultRandom().primaryKey(),
  organizationId: uuid("organization_id").notNull().references(() => organizations.id),
  name: varchar("name", { length: 255 }).notNull(),
  type: varchar("type", { length: 50 }).notNull().$type<"project" | "scene" | "custom">(),
  projectId: uuid("project_id").references(() => projects.id),
  sceneId: uuid("scene_id").references(() => scenes.id),
  createdById: uuid("created_by").notNull().references(() => users.id),
  createdAt: timestamp("created_at").defaultNow().notNull(),
  updatedAt: timestamp("updated_at").defaultNow().notNull(),
  deletedAt: timestamp("deleted_at"),
})

// Favorite List Locations Junction Table
export const favoriteListLocations = pgTable("favorite_list_locations", {
  id: uuid("id").defaultRandom().primaryKey(),
  favoriteListId: uuid("favorite_list_id").notNull().references(() => favoriteLists.id),
  locationId: uuid("location_id").notNull().references(() => locations.id),
  addedById: uuid("added_by").notNull().references(() => users.id),
  addedAt: timestamp("added_at").defaultNow().notNull(),
}, (table) => {
  return {
    favoriteListLocationIdx: uniqueIndex("favorite_list_location_idx").on(table.favoriteListId, table.locationId),
  }
})

// Relations
export const favoriteListsRelations = relations(favoriteLists, ({ one, many }) => ({
  organization: one(organizations, {
    fields: [favoriteLists.organizationId],
    references: [organizations.id],
  }),
  project: one(projects, {
    fields: [favoriteLists.projectId],
    references: [projects.id],
  }),
  scene: one(scenes, {
    fields: [favoriteLists.sceneId],
    references: [scenes.id],
  }),
  createdBy: one(users, {
    fields: [favoriteLists.createdById],
    references: [users.id],
  }),
  locations: many(favoriteListLocations),
}))

export const favoriteListLocationsRelations = relations(favoriteListLocations, ({ one }) => ({
  favoriteList: one(favoriteLists, {
    fields: [favoriteListLocations.favoriteListId],
    references: [favoriteLists.id],
  }),
  location: one(locations, {
    fields: [favoriteListLocations.locationId],
    references: [locations.id],
  }),
  addedBy: one(users, {
    fields: [favoriteListLocations.addedById],
    references: [users.id],
  }),
}))

// Zod Schemas
export const insertFavoriteListSchema = createInsertSchema(favoriteLists, {
  type: z.enum(["project", "scene", "custom"]),
})

export const selectFavoriteListSchema = createSelectSchema(favoriteLists, {
  type: z.enum(["project", "scene", "custom"]),
})

export const insertFavoriteListLocationSchema = createInsertSchema(favoriteListLocations)
export const selectFavoriteListLocationSchema = createSelectSchema(favoriteListLocations)

// Custom Zod Schemas for API
export const createFavoriteListSchema = z.object({
  organizationId: z.string().uuid(),
  name: z.string().min(1).max(255),
  type: z.enum(["project", "scene", "custom"]),
  projectId: z.string().uuid().optional(),
  sceneId: z.string().uuid().optional(),
})

export const addLocationToFavoriteListSchema = z.object({
  organizationId: z.string().uuid(),
  listId: z.string().uuid(),
  locationId: z.string().uuid(),
})

export const removeFavoriteLocationSchema = z.object({
  organizationId: z.string().uuid(),
  listId: z.string().uuid(),
  locationId: z.string().uuid(),
})

export const updateFavoriteListSchema = z.object({
  organizationId: z.string().uuid(),
  listId: z.string().uuid(),
  name: z.string().min(1).max(255).optional(),
})

export const deleteFavoriteListSchema = z.object({
  organizationId: z.string().uuid(),
  listId: z.string().uuid(),
})
