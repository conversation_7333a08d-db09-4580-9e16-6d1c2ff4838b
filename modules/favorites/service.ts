import { db } from "@/lib/db"
import { and, eq, isNull } from "drizzle-orm"
import { 
  favoriteLists, 
  favoriteListLocations
} from "./schema"
import type { 
  FavoriteListResponse
} from "./model"
import { locations } from "@/modules/location/schema"

// Define types for the query results
type FavoriteListWithRelations = {
  id: string
  name: string
  type: "project" | "scene" | "custom"
  projectId: string | null
  sceneId: string | null
  createdAt: Date
  updatedAt: Date
  createdBy: {
    name: string | null
    email: string | null
  }
  locations: {
    location: {
      id: string
      name: string
      type: string
      status: string
    }
    addedAt: Date
  }[]
}

/**
 * Get all favorite lists for an organization
 */
export async function getFavoriteLists(organizationId: string): Promise<FavoriteListResponse[]> {
  // Get all favorite lists for the organization
  const lists = await db.query.favoriteLists.findMany({
    where: and(
      eq(favoriteLists.organizationId, organizationId),
      isNull(favoriteLists.deletedAt)
    ),
    with: {
      locations: {
        with: {
          location: true
        }
      },
      createdBy: true
    }
  })

  // Format the response
  return lists.map((list: FavoriteListWithRelations) => ({
    id: list.id,
    name: list.name,
    type: list.type,
    projectId: list.projectId || null,
    sceneId: list.sceneId || null,
    createdBy: list.createdBy.name || list.createdBy.email || "Unknown",
    createdAt: list.createdAt.toISOString(),
    updatedAt: list.updatedAt.toISOString(),
    locations: list.locations.map((loc) => ({
      id: loc.location.id,
      name: loc.location.name,
      type: loc.location.type,
      status: loc.location.status,
      addedAt: loc.addedAt && loc.addedAt instanceof Date && !Number.isNaN(loc.addedAt.getTime()) 
        ? loc.addedAt.toISOString() 
        : new Date().toISOString()
    }))
  }))
}

/**
 * Get a favorite list by ID
 */
export async function getFavoriteList(listId: string, organizationId: string): Promise<FavoriteListResponse | null> {
  // Get the favorite list
  const list = await db.query.favoriteLists.findFirst({
    where: and(
      eq(favoriteLists.id, listId),
      eq(favoriteLists.organizationId, organizationId),
      isNull(favoriteLists.deletedAt)
    ),
    with: {
      locations: {
        with: {
          location: true
        }
      },
      createdBy: true
    }
  })

  if (!list) return null

  // Format the response
  return {
    id: list.id,
    name: list.name,
    type: list.type,
    projectId: list.projectId || null,
    sceneId: list.sceneId || null,
    createdBy: list.createdBy.name || list.createdBy.email || "Unknown",
    createdAt: list.createdAt.toISOString(),
    updatedAt: list.updatedAt.toISOString(),
    locations: list.locations.map((loc) => ({
      id: loc.location.id,
      name: loc.location.name,
      type: loc.location.type,
      status: loc.location.status,
      addedAt: loc.addedAt && loc.addedAt instanceof Date && !Number.isNaN(loc.addedAt.getTime()) 
        ? loc.addedAt.toISOString() 
        : new Date().toISOString()
    }))
  }
}

/**
 * Create a new favorite list
 */
export async function createFavoriteList(
  data: {
    organizationId: string
    name: string
    type: "project" | "scene" | "custom"
    projectId?: string
    sceneId?: string
    createdById: string
  }
): Promise<FavoriteListResponse> {
  // Insert the favorite list
  const [list] = await db.insert(favoriteLists)
    .values({
      organizationId: data.organizationId,
      name: data.name,
      type: data.type,
      projectId: data.projectId,
      sceneId: data.sceneId,
      createdById: data.createdById,
    })
    .returning()

  // Return the created list
  return {
    id: list.id,
    name: list.name,
    type: list.type,
    projectId: list.projectId || null,
    sceneId: list.sceneId || null,
    createdBy: data.createdById,
    createdAt: list.createdAt.toISOString(),
    updatedAt: list.updatedAt.toISOString(),
    locations: []
  }
}

/**
 * Add a location to a favorite list
 */
export async function addLocationToFavoriteList(
  data: {
    favoriteListId: string
    locationId: string
    addedById: string
    organizationId: string
  }
): Promise<boolean> {
  // Verify the favorite list belongs to the organization
  const list = await db.query.favoriteLists.findFirst({
    where: and(
      eq(favoriteLists.id, data.favoriteListId),
      eq(favoriteLists.organizationId, data.organizationId),
      isNull(favoriteLists.deletedAt)
    )
  })

  if (!list) {
    throw new Error("Favorite list not found")
  }

  // Verify the location belongs to the organization
  const location = await db.query.locations.findFirst({
    where: and(
      eq(locations.id, data.locationId),
      eq(locations.organizationId, data.organizationId)
    )
  })

  if (!location) {
    throw new Error("Location not found")
  }

  // Check if the location is already in the list
  const existingEntry = await db.query.favoriteListLocations.findFirst({
    where: and(
      eq(favoriteListLocations.favoriteListId, data.favoriteListId),
      eq(favoriteListLocations.locationId, data.locationId)
    )
  })

  if (existingEntry) {
    // Location is already in the list, no need to add it again
    return true
  }

  // Add the location to the list
  await db.insert(favoriteListLocations)
    .values({
      favoriteListId: data.favoriteListId,
      locationId: data.locationId,
      addedById: data.addedById
    })

  return true
}

/**
 * Remove a location from a favorite list
 */
export async function removeLocationFromFavoriteList(
  data: {
    favoriteListId: string
    locationId: string
    organizationId: string
  }
): Promise<boolean> {
  // Verify the favorite list belongs to the organization
  const list = await db.query.favoriteLists.findFirst({
    where: and(
      eq(favoriteLists.id, data.favoriteListId),
      eq(favoriteLists.organizationId, data.organizationId),
      isNull(favoriteLists.deletedAt)
    )
  })

  if (!list) {
    throw new Error("Favorite list not found")
  }

  // Remove the location from the list
  await db.delete(favoriteListLocations)
    .where(and(
      eq(favoriteListLocations.favoriteListId, data.favoriteListId),
      eq(favoriteListLocations.locationId, data.locationId)
    ))

  return true
}

/**
 * Update a favorite list
 */
export async function updateFavoriteList(
  data: {
    listId: string
    name?: string
    organizationId: string
  }
): Promise<FavoriteListResponse | null> {
  // Verify the favorite list belongs to the organization
  const list = await db.query.favoriteLists.findFirst({
    where: and(
      eq(favoriteLists.id, data.listId),
      eq(favoriteLists.organizationId, data.organizationId),
      isNull(favoriteLists.deletedAt)
    )
  })

  if (!list) {
    throw new Error("Favorite list not found")
  }

  // Update the list
  const [updatedList] = await db.update(favoriteLists)
    .set({
      name: data.name || list.name,
      updatedAt: new Date()
    })
    .where(eq(favoriteLists.id, data.listId))
    .returning()

  // Get the updated list with locations
  return getFavoriteList(updatedList.id, data.organizationId)
}

/**
 * Delete a favorite list (soft delete)
 */
export async function deleteFavoriteList(
  data: {
    listId: string
    organizationId: string
  }
): Promise<boolean> {
  // Verify the favorite list belongs to the organization
  const list = await db.query.favoriteLists.findFirst({
    where: and(
      eq(favoriteLists.id, data.listId),
      eq(favoriteLists.organizationId, data.organizationId),
      isNull(favoriteLists.deletedAt)
    )
  })

  if (!list) {
    throw new Error("Favorite list not found")
  }

  // Soft delete the list
  await db.update(favoriteLists)
    .set({
      deletedAt: new Date()
    })
    .where(eq(favoriteLists.id, data.listId))

  return true
}
