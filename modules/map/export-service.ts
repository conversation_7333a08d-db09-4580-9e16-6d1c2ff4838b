import type { ExportOptions } from "@/components/maps/export-map-modal";

/**
 * Interface for location-like objects that can be exported
 */
interface LocationLike {
  id: string;
  name: string;
  type: string;
  status: string;
  description?: string | null;
  address: any;
  coordinates: any;
  contactName?: string | null;
  contactPhone?: string | null;
  contactEmail?: string | null;
  media?: any[];
  [key: string]: any; // Allow additional properties
}

/**
 * Service for exporting map data to various formats
 */
export const MapExportService = {
  /**
   * Export map data to PDF or CSV
   * 
   * @param locations - The locations to include in the export
   * @param options - Export options
   * @returns A Promise that resolves to a URL for downloading the exported file
   */
  async exportMap(
    locations: LocationLike[],
    options: ExportOptions
  ): Promise<string> {
    if (options.format === "pdf") {
      return this.exportToPdf(locations, options);
    } 
    
    return this.exportToCsv(locations, options);
  },

  /**
   * Export map data to PDF
   * 
   * @param locations - The locations to include in the export
   * @param options - Export options
   * @returns A Promise that resolves to a URL for downloading the PDF
   */
  async exportToPdf(
    locations: LocationLike[],
    options: ExportOptions
  ): Promise<string> {
    // In a real implementation, we would use a PDF generation library
    // For now, we'll just create a simple HTML representation and convert it to a data URL
    
    const html = `
      <!DOCTYPE html>
      <html>
        <head>
          <title>Scene-o-matic Map Export</title>
          <style>
            body { font-family: Arial, sans-serif; margin: 20px; }
            h1 { text-align: center; }
            .date { text-align: center; margin-bottom: 20px; }
            table { width: 100%; border-collapse: collapse; margin-bottom: 20px; }
            th { background-color: #f2f2f2; text-align: left; padding: 8px; }
            td { border: 1px solid #ddd; padding: 8px; }
            .section { margin-top: 20px; }
            .location-name { font-weight: bold; }
          </style>
        </head>
        <body>
          <h1>Scene-o-matic Map Export</h1>
          <p class="date">Generated on ${new Date().toLocaleDateString()}</p>
          
          <h2>Locations (${locations.length})</h2>
          <table>
            <thead>
              <tr>
                <th>Name</th>
                <th>Type</th>
                <th>Address</th>
                <th>Status</th>
              </tr>
            </thead>
            <tbody>
              ${locations.map(location => `
                <tr>
                  <td>${location.name}</td>
                  <td>${location.type}</td>
                  <td>${location.address.formatted || 
                    `${location.address.street}, ${location.address.city}, ${location.address.state} ${location.address.postalCode}`}</td>
                  <td>${location.status}</td>
                </tr>
              `).join('')}
            </tbody>
          </table>
          
          ${options.includeContactInfo ? `
            <div class="section">
              <h2>Contact Information</h2>
              ${locations.map(location => `
                <div>
                  <p class="location-name">${location.name}</p>
                  <p>Contact: ${location.contactName || "Not specified"}</p>
                  <p>Phone: ${location.contactPhone || "Not specified"}</p>
                  <p>Email: ${location.contactEmail || "Not specified"}</p>
                </div>
              `).join('')}
            </div>
          ` : ''}
          
          ${options.includeDocuments && locations.some(loc => loc.media && loc.media.length > 0) ? `
            <div class="section">
              <h2>Documents</h2>
              ${locations.map(location => `
                <div>
                  <p class="location-name">${location.name}</p>
                  ${location.media && location.media.length > 0 
                    ? location.media
                        .filter((media: { type: string }) => media.type !== "image" && media.type !== "video")
                        .map((doc: { title?: string; type: string }) => `<p>${doc.title || "Untitled"} (${doc.type})</p>`)
                        .join('') || '<p>No documents available</p>'
                    : '<p>No documents available</p>'
                  }
                </div>
              `).join('')}
            </div>
          ` : ''}
        </body>
      </html>
    `;
    
    // In a real implementation, we would convert this HTML to PDF
    // For now, we'll just create a data URL
    const blob = new Blob([html], { type: "text/html" });
    return URL.createObjectURL(blob);
  },

  /**
   * Export map data to CSV
   * 
   * @param locations - The locations to include in the export
   * @param options - Export options
   * @returns A Promise that resolves to a URL for downloading the CSV
   */
  async exportToCsv(
    locations: LocationLike[],
    options: ExportOptions
  ): Promise<string> {
    // Define CSV headers
    const headers = [
      "Name",
      "Type",
      "Status",
      "Address",
      "Coordinates",
      "Description"
    ];
    
    // Add optional headers based on options
    if (options.includeContactInfo) {
      headers.push("Contact Name", "Contact Phone", "Contact Email");
    }
    
    // Create CSV content
    let csv = `${headers.join(",")}\n`;
    
    // Add rows
    for (const location of locations) {
      const row = [
        this.escapeCsvValue(location.name),
        this.escapeCsvValue(location.type),
        this.escapeCsvValue(location.status),
        this.escapeCsvValue(location.address.formatted || 
          `${location.address.street}, ${location.address.city}, ${location.address.state} ${location.address.postalCode}`),
        this.escapeCsvValue(`${location.coordinates.latitude}, ${location.coordinates.longitude}`),
        this.escapeCsvValue(location.description || "")
      ];
      
      if (options.includeContactInfo) {
        row.push(
          this.escapeCsvValue(location.contactName || ""),
          this.escapeCsvValue(location.contactPhone || ""),
          this.escapeCsvValue(location.contactEmail || "")
        );
      }
      
      csv = `${csv}${row.join(",")}\n`;
    }
    
    // Create a blob and URL for download
    const blob = new Blob([csv], { type: "text/csv;charset=utf-8;" });
    return URL.createObjectURL(blob);
  },
  
  /**
   * Escape a value for CSV
   * 
   * @param value - The value to escape
   * @returns The escaped value
   */
  escapeCsvValue(value: string): string {
    // If the value contains a comma, quote, or newline, wrap it in quotes
    if (value.includes(",") || value.includes("\"") || value.includes("\n")) {
      // Double up any quotes and wrap in quotes
      return `"${value.replace(/"/g, "\"\"")}"`;
    }
    return value;
  }
};
