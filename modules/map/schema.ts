import { relations } from "drizzle-orm"
import { pgTable, uuid, varchar, text, jsonb, timestamp } from "drizzle-orm/pg-core"
import { organizations } from "../organization/schema"
import { users } from "../user/schema"
import { locations } from "../location/schema"

export const mapViews = pgTable("map_views", {
  id: uuid("id").primaryKey().defaultRandom(),
  organizationId: uuid("organization_id")
    .notNull()
    .references(() => organizations.id),
  name: varchar("name", { length: 255 }).notNull(),
  description: text("description"),
  filters: jsonb("filters"),
  viewSettings: jsonb("view_settings"),
  createdBy: uuid("created_by")
    .notNull()
    .references(() => users.id),
  createdAt: timestamp("created_at", { withTimezone: true }).notNull().defaultNow(),
  updatedAt: timestamp("updated_at", { withTimezone: true }).notNull().defaultNow(),
  deletedAt: timestamp("deleted_at", { withTimezone: true }),
})

export const mapViewLocations = pgTable("map_view_locations", {
  id: uuid("id").primaryKey().defaultRandom(),
  mapViewId: uuid("map_view_id")
    .notNull()
    .references(() => mapViews.id),
  locationId: uuid("location_id")
    .notNull()
    .references(() => locations.id),
})

export const mapViewsRelations = relations(mapViews, ({ one, many }) => ({
  organization: one(organizations, {
    fields: [mapViews.organizationId],
    references: [organizations.id],
  }),
  creator: one(users, {
    fields: [mapViews.createdBy],
    references: [users.id],
  }),
  locations: many(mapViewLocations),
}))

export const mapViewLocationsRelations = relations(mapViewLocations, ({ one }) => ({
  mapView: one(mapViews, {
    fields: [mapViewLocations.mapViewId],
    references: [mapViews.id],
  }),
  location: one(locations, {
    fields: [mapViewLocations.locationId],
    references: [locations.id],
  }),
}))

// Shared map links schema
export const sharedMapLinks = pgTable("shared_map_links", {
  id: uuid("id").primaryKey().defaultRandom(),
  organizationId: uuid("organization_id")
    .notNull()
    .references(() => organizations.id),
  token: varchar("token", { length: 255 }).notNull().unique(),
  mapViewId: uuid("map_view_id")
    .references(() => mapViews.id),
  filters: jsonb("filters"),
  viewSettings: jsonb("view_settings"),
  passwordHash: varchar("password_hash", { length: 255 }),
  expiresAt: timestamp("expires_at", { withTimezone: true }),
  createdBy: uuid("created_by")
    .notNull()
    .references(() => users.id),
  createdAt: timestamp("created_at", { withTimezone: true }).notNull().defaultNow(),
  updatedAt: timestamp("updated_at", { withTimezone: true }).notNull().defaultNow(),
})

export const sharedMapLinkLocations = pgTable("shared_map_link_locations", {
  id: uuid("id").primaryKey().defaultRandom(),
  sharedMapLinkId: uuid("shared_map_link_id")
    .notNull()
    .references(() => sharedMapLinks.id),
  locationId: uuid("location_id")
    .notNull()
    .references(() => locations.id),
})

export const sharedMapLinkAccessLogs = pgTable("shared_map_link_access_logs", {
  id: uuid("id").primaryKey().defaultRandom(),
  sharedMapLinkId: uuid("shared_map_link_id")
    .notNull()
    .references(() => sharedMapLinks.id),
  ipAddress: varchar("ip_address", { length: 45 }),
  userAgent: text("user_agent"),
  accessedAt: timestamp("accessed_at", { withTimezone: true }).notNull().defaultNow(),
})

export const sharedMapLinksRelations = relations(sharedMapLinks, ({ one, many }) => ({
  organization: one(organizations, {
    fields: [sharedMapLinks.organizationId],
    references: [organizations.id],
  }),
  creator: one(users, {
    fields: [sharedMapLinks.createdBy],
    references: [users.id],
  }),
  mapView: one(mapViews, {
    fields: [sharedMapLinks.mapViewId],
    references: [mapViews.id],
  }),
  locations: many(sharedMapLinkLocations),
  accessLogs: many(sharedMapLinkAccessLogs),
}))

export const sharedMapLinkLocationsRelations = relations(sharedMapLinkLocations, ({ one }) => ({
  sharedMapLink: one(sharedMapLinks, {
    fields: [sharedMapLinkLocations.sharedMapLinkId],
    references: [sharedMapLinks.id],
  }),
  location: one(locations, {
    fields: [sharedMapLinkLocations.locationId],
    references: [locations.id],
  }),
}))

export const sharedMapLinkAccessLogsRelations = relations(sharedMapLinkAccessLogs, ({ one }) => ({
  sharedMapLink: one(sharedMapLinks, {
    fields: [sharedMapLinkAccessLogs.sharedMapLinkId],
    references: [sharedMapLinks.id],
  }),
}))
