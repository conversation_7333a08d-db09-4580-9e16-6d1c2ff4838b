import { z } from "zod"

// Custom Map domain models
export type CustomMap = {
  id: string
  name: string
  description?: string
  organizationId: string
  projectId?: string
  createdBy?: string
  createdAt: Date
  updatedAt: Date
  center: {
    latitude: number
    longitude: number
  }
  zoom: number
  style: string
  isPublic: boolean
  settings?: Record<string, any>
  mapLayers?: Record<string, any>
}

export type MapMarker = {
  id: string
  mapId: string
  locationId?: string
  coordinates: {
    latitude: number
    longitude: number
  }
  title?: string
  description?: string
  color: string
  icon: string
  createdBy?: string
  createdAt: Date
  isVisible: boolean
  customData?: Record<string, any>
}

// Validation schemas
export const customMapSchema = z.object({
  id: z.string().uuid(),
  name: z.string().min(1).max(255),
  description: z.string().optional(),
  organizationId: z.string().uuid(),
  projectId: z.string().uuid().optional(),
  createdBy: z.string().uuid().optional(),
  createdAt: z.date(),
  updatedAt: z.date(),
  center: z.object({
    latitude: z.number().min(-90).max(90),
    longitude: z.number().min(-180).max(180),
  }),
  zoom: z.number().default(10),
  style: z.string().default("streets"),
  isPublic: z.boolean().default(false),
  settings: z.record(z.any()).optional(),
  mapLayers: z.record(z.any()).optional(),
})

export const mapMarkerSchema = z.object({
  id: z.string().uuid(),
  mapId: z.string().uuid(),
  locationId: z.string().uuid().optional(),
  coordinates: z.object({
    latitude: z.number().min(-90).max(90),
    longitude: z.number().min(-180).max(180),
  }),
  title: z.string().optional(),
  description: z.string().optional(),
  color: z.string().default("#FF0000"),
  icon: z.string().default("pin"),
  createdBy: z.string().uuid().optional(),
  createdAt: z.date(),
  isVisible: z.boolean().default(true),
  customData: z.record(z.any()).optional(),
})

export const createCustomMapSchema = customMapSchema.omit({
  id: true,
  createdAt: true,
  updatedAt: true,
})

export const createMapMarkerSchema = mapMarkerSchema.omit({
  id: true,
  createdAt: true,
})

export const updateCustomMapSchema = createCustomMapSchema.partial()
export const updateMapMarkerSchema = createMapMarkerSchema.partial()
