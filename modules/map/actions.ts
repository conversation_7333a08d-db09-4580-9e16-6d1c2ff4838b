"use server"
import { getFilteredLocations, getProjectsForMap, saveUserMapSettings } from "./service"
import { mapViewSettingsSchema } from "./model"
import { getCurrentUser } from "../auth/service"
import { validate } from "../shared/validation"

export async function getMapLocations(organizationId: string, filters = {}) {
  try {
    // Ensure user has access to this organization
    await getCurrentUser()

    const locations = await getFilteredLocations(organizationId, filters)
    return { success: true, data: locations }
  } catch (error) {
    console.error("Error fetching map locations:", error)
    return {
      success: false,
      error: error instanceof Error ? error.message : "Failed to fetch locations",
    }
  }
}

export async function getMapProjects(organizationId: string) {
  try {
    // Ensure user has access to this organization
    await getCurrentUser()

    const projects = await getProjectsForMap(organizationId)
    return { success: true, data: projects }
  } catch (error) {
    console.error("Error fetching map projects:", error)
    return {
      success: false,
      error: error instanceof Error ? error.message : "Failed to fetch projects",
    }
  }
}

export async function saveMapViewSettings(settings: any) {
  try {
    const user = await getCurrentUser()
    const validatedSettings = validate(mapViewSettingsSchema, settings)

    await saveUserMapSettings(user.id, validatedSettings)

    return { success: true }
  } catch (error) {
    console.error("Error updating map settings:", error)
    return {
      success: false,
      error: error instanceof Error ? error.message : "Failed to update map settings",
    }
  }
}
