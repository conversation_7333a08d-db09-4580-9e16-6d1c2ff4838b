import { db } from "@/lib/db"
import { eq, and, isNull } from "drizzle-orm"
import { mapViews, mapViewLocations, sharedMapLinks, sharedMapLinkLocations, sharedMapLinkAccessLogs } from "./schema"
import { users } from "../user/schema"
import type { SavedView, MapFilter, MapViewSettings, SharedMapLink } from "./model"
import type { Location } from "../location/model"
import { randomBytes, createHash } from "node:crypto"
import { cache } from "@/lib/cache"

export async function getSavedViewsForOrganization(organizationId: string): Promise<SavedView[]> {
  try {
    // Use cache to avoid redundant database queries
    const cacheKey = `savedViews:${organizationId}`;
    
    return await cache.getOrSet(
      cacheKey,
      async () => {
        // Use the optimized index for organization_id + deleted_at
        const views = await db.query.mapViews.findMany({
          where: and(
            eq(mapViews.organizationId, organizationId),
            isNull(mapViews.deletedAt)
          ),
          // Only select the fields we need to reduce data transfer
          columns: {
            id: true,
            name: true,
            description: true,
            filters: true,
            viewSettings: true,
            createdBy: true,
            createdAt: true,
            updatedAt: true,
          },
          with: {
            // Only select the id from creator to reduce data transfer
            creator: {
              columns: {
                id: true,
              },
            },
          },
          orderBy: (tables, { desc }) => [desc(tables.createdAt)],
        });

        return views.map((view) => ({
          id: view.id,
          name: view.name,
          description: view.description || undefined,
          filters: view.filters as MapFilter,
          viewSettings: view.viewSettings as MapViewSettings,
          createdBy: view.creator.id,
          createdAt: view.createdAt.toISOString(),
          updatedAt: view.updatedAt.toISOString(),
        }));
      },
      { ttl: 60 * 1000 } // Cache for 1 minute
    );
  } catch (error) {
    console.error("Error getting saved views:", error);
    throw new Error("Failed to get saved views");
  }
}

export async function getSavedViewById(id: string, organizationId: string): Promise<SavedView | null> {
  try {
    // Use cache to avoid redundant database queries
    const cacheKey = `savedView:${id}:${organizationId}`;
    
    return await cache.getOrSet(
      cacheKey,
      async () => {
        const view = await db.query.mapViews.findFirst({
          where: and(
            eq(mapViews.id, id),
            eq(mapViews.organizationId, organizationId),
            isNull(mapViews.deletedAt)
          ),
          with: {
            creator: {
              columns: {
                id: true,
              },
            },
            locations: {
              with: {
                location: {
                  columns: {
                    id: true,
                    name: true,
                    description: true,
                    address: true,
                    coordinates: true,
                    type: true,
                    status: true,
                    metadata: true,
                    isActive: true,
                  },
                },
              },
            },
          },
        });

        if (!view) {
          return null;
        }

        return {
          id: view.id,
          name: view.name,
          description: view.description || undefined,
          filters: view.filters as MapFilter,
          viewSettings: view.viewSettings as MapViewSettings,
          locations: view.locations.map((l) => l.location as unknown as Location),
          createdBy: view.creator.id,
          createdAt: view.createdAt.toISOString(),
          updatedAt: view.updatedAt.toISOString(),
        };
      },
      { ttl: 60 * 1000 } // Cache for 1 minute
    );
  } catch (error) {
    console.error("Error getting saved view:", error);
    throw new Error("Failed to get saved view");
  }
}

export async function createSavedView(
  organizationId: string,
  name: string,
  userId: string,
  viewSettings: MapViewSettings,
  filters: MapFilter,
  description?: string,
  selectedLocationIds?: string[]
): Promise<SavedView> {
  try {
    // Start a transaction
    const result = await db.transaction(async (tx) => {
      // Create the saved view
      const [view] = await tx
        .insert(mapViews)
        .values({
          organizationId,
          name,
          description,
          filters,
          viewSettings,
          createdBy: userId,
        })
        .returning();

      // If there are selected locations, add them to the view
      if (selectedLocationIds && selectedLocationIds.length > 0) {
        await tx
          .insert(mapViewLocations)
          .values(
            selectedLocationIds.map(locationId => ({
              mapViewId: view.id,
              locationId,
            }))
          );
      }

      // Return the created view
      return {
        id: view.id,
        name: view.name,
        description: view.description || undefined,
        filters: view.filters as MapFilter,
        viewSettings: view.viewSettings as MapViewSettings,
        createdBy: userId,
        createdAt: view.createdAt.toISOString(),
        updatedAt: view.updatedAt.toISOString(),
      };
    });
    
    // Invalidate the cache for saved views for this organization
    cache.delete(`savedViews:${organizationId}`);
    
    return result;
  } catch (error) {
    console.error("Error creating saved view:", error);
    throw new Error("Failed to create saved view");
  }
}

export async function updateSavedView(
  id: string,
  organizationId: string,
  userId: string,
  data: {
    name?: string
    description?: string
    viewSettings?: MapViewSettings
    filters?: MapFilter
    selectedLocationIds?: string[]
  }
): Promise<SavedView | null> {
  try {
    // Start a transaction
    const result = await db.transaction(async (tx) => {
      // Check if the view exists and belongs to the organization
      const existingView = await tx.query.mapViews.findFirst({
        where: and(
          eq(mapViews.id, id),
          eq(mapViews.organizationId, organizationId),
          isNull(mapViews.deletedAt)
        ),
      });

      if (!existingView) {
        return null;
      }

      // Update the view
      const [updatedView] = await tx
        .update(mapViews)
        .set({
          name: data.name !== undefined ? data.name : existingView.name,
          description: data.description !== undefined ? data.description : existingView.description,
          viewSettings: data.viewSettings !== undefined ? data.viewSettings : existingView.viewSettings,
          filters: data.filters !== undefined ? data.filters : existingView.filters,
          updatedAt: new Date(),
        })
        .where(eq(mapViews.id, id))
        .returning();

      // If selectedLocationIds is provided, update the locations
      if (data.selectedLocationIds !== undefined) {
        // Delete existing locations
        await tx
          .delete(mapViewLocations)
          .where(eq(mapViewLocations.mapViewId, id));

        // Add new locations if any
        if (data.selectedLocationIds.length > 0) {
          await tx
            .insert(mapViewLocations)
            .values(
              data.selectedLocationIds.map(locationId => ({
                mapViewId: id,
                locationId,
              }))
            );
        }
      }

      // Get the user
      const user = await tx.query.users.findFirst({
        where: eq(users.id, userId),
      });

      if (!user) {
        throw new Error("User not found");
      }

      // Return the updated view
      return {
        id: updatedView.id,
        name: updatedView.name,
        description: updatedView.description || undefined,
        filters: updatedView.filters as MapFilter,
        viewSettings: updatedView.viewSettings as MapViewSettings,
        createdBy: existingView.createdBy,
        createdAt: existingView.createdAt.toISOString(),
        updatedAt: updatedView.updatedAt.toISOString(),
      };
    });
    
    if (result) {
      // Invalidate the cache for this specific view and the organization's views list
      cache.delete(`savedView:${id}:${organizationId}`);
      cache.delete(`savedViews:${organizationId}`);
    }
    
    return result;
  } catch (error) {
    console.error("Error updating saved view:", error);
    throw new Error("Failed to update saved view");
  }
}

export async function deleteSavedView(id: string, organizationId: string): Promise<boolean> {
  try {
    // Start a transaction
    const result = await db.transaction(async (tx) => {
      // Check if the view exists and belongs to the organization
      const existingView = await tx.query.mapViews.findFirst({
        where: and(
          eq(mapViews.id, id),
          eq(mapViews.organizationId, organizationId),
          isNull(mapViews.deletedAt)
        ),
      });

      if (!existingView) {
        return false;
      }

      // Soft delete the view
      await tx
        .update(mapViews)
        .set({
          deletedAt: new Date(),
        })
        .where(eq(mapViews.id, id));

      return true;
    });
    
    if (result) {
      // Invalidate the cache for this specific view and the organization's views list
      cache.delete(`savedView:${id}:${organizationId}`);
      cache.delete(`savedViews:${organizationId}`);
    }
    
    return result;
  } catch (error) {
    console.error("Error deleting saved view:", error);
    throw new Error("Failed to delete saved view");
  }
}

// Generate a secure random token for shared links
function generateShareToken(): string {
  return randomBytes(32).toString('hex')
}

// Hash a password for secure storage
function hashPassword(password: string): string {
  return createHash('sha256').update(password).digest('hex')
}

// Create a shared map link
export async function createSharedMapLink(
  organizationId: string,
  userId: string,
  options: {
    mapViewId?: string,
    filters?: MapFilter,
    viewSettings?: MapViewSettings,
    password?: string,
    expiresIn?: number, // in seconds
    locationIds?: string[]
  }
): Promise<SharedMapLink> {
  try {
    // Start a transaction
    const result = await db.transaction(async (tx) => {
      // Generate a secure token
      const token = generateShareToken();
      
      // Calculate expiration date if provided
      let expiresAt: Date | undefined = undefined;
      if (options.expiresIn) {
        expiresAt = new Date();
        expiresAt.setSeconds(expiresAt.getSeconds() + options.expiresIn);
      }
      
      // Hash password if provided
      const passwordHash = options.password ? hashPassword(options.password) : undefined;
      
      // If mapViewId is provided, get the view settings and filters from it
      let viewSettings = options.viewSettings;
      let filters = options.filters;
      
      if (options.mapViewId) {
        const savedView = await tx.query.mapViews.findFirst({
          where: and(
            eq(mapViews.id, options.mapViewId),
            eq(mapViews.organizationId, organizationId),
            isNull(mapViews.deletedAt)
          ),
        });
        
        if (!savedView) {
          throw new Error("Saved view not found");
        }
        
        viewSettings = savedView.viewSettings as MapViewSettings;
        filters = savedView.filters as MapFilter;
      }
      
      if (!viewSettings) {
        throw new Error("View settings are required");
      }
      
      // Create the shared link
      const [sharedLink] = await tx
        .insert(sharedMapLinks)
        .values({
          organizationId,
          token,
          mapViewId: options.mapViewId,
          filters,
          viewSettings,
          passwordHash,
          expiresAt,
          createdBy: userId,
        })
        .returning();
      
      // If there are selected locations, add them to the shared link
      if (options.locationIds && options.locationIds.length > 0) {
        await tx
          .insert(sharedMapLinkLocations)
          .values(
            options.locationIds.map(locationId => ({
              sharedMapLinkId: sharedLink.id,
              locationId,
            }))
          );
      }
      
      // Generate the URL for the shared link
      const url = `${process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000'}/shared/map/${token}`;
      
      // Return the created shared link
      return {
        id: sharedLink.id,
        token: sharedLink.token,
        mapViewId: sharedLink.mapViewId || undefined,
        filters: sharedLink.filters as MapFilter | undefined,
        viewSettings: sharedLink.viewSettings as MapViewSettings,
        passwordHash: sharedLink.passwordHash || undefined,
        expiresAt: sharedLink.expiresAt?.toISOString(),
        createdBy: userId,
        createdAt: sharedLink.createdAt.toISOString(),
        updatedAt: sharedLink.updatedAt.toISOString(),
        url,
      };
    });
    
    // Invalidate the cache for shared map links for this organization
    cache.delete(`sharedMapLinks:${organizationId}`);
    
    return result;
  } catch (error) {
    console.error("Error creating shared map link:", error);
    throw new Error("Failed to create shared map link");
  }
}

// Get a shared map link by token
export async function getSharedMapLinkByToken(token: string, password?: string): Promise<SharedMapLink | null> {
  try {
    // First check if the link exists and hasn't expired without loading relations
    // This is more efficient as we can avoid loading relations if the link doesn't exist or has expired
    const linkCheck = await db.query.sharedMapLinks.findFirst({
      where: eq(sharedMapLinks.token, token),
      columns: {
        id: true,
        expiresAt: true,
        passwordHash: true,
      },
    });
    
    if (!linkCheck) {
      return null;
    }
    
    // Check if the link has expired
    if (linkCheck.expiresAt && new Date() > linkCheck.expiresAt) {
      return null;
    }
    
    // Check password if the link is password-protected
    if (linkCheck.passwordHash) {
      if (!password) {
        throw new Error("Password required");
      }
      
      const hashedPassword = hashPassword(password);
      if (hashedPassword !== linkCheck.passwordHash) {
        throw new Error("Invalid password");
      }
    }
    
    // Use cache for the full data with relations
    // Don't cache password-protected links with the password in the cache key
    const cacheKey = `sharedMapLink:${linkCheck.id}`;
    
    return await cache.getOrSet(
      cacheKey,
      async () => {
        // Now that we've verified the link is valid, load the full data with relations
        const sharedLink = await db.query.sharedMapLinks.findFirst({
          where: eq(sharedMapLinks.id, linkCheck.id),
          columns: {
            id: true,
            token: true,
            mapViewId: true,
            filters: true,
            viewSettings: true,
            passwordHash: true,
            expiresAt: true,
            createdAt: true,
            updatedAt: true,
          },
          with: {
            locations: {
              columns: {
                sharedMapLinkId: true,
                locationId: true,
              },
              with: {
                location: {
                  columns: {
                    id: true,
                    name: true,
                    description: true,
                    address: true,
                    coordinates: true,
                    type: true,
                    status: true,
                    metadata: true,
                    isActive: true,
                  },
                },
              },
            },
            creator: {
              columns: {
                id: true,
              },
            },
          },
        });
        
        if (!sharedLink) {
          return null;
        }
        
        // Generate the URL for the shared link
        const url = `${process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000'}/shared/map/${token}`;
        
        // Return the shared link
        return {
          id: sharedLink.id,
          token: sharedLink.token,
          mapViewId: sharedLink.mapViewId || undefined,
          filters: sharedLink.filters as MapFilter | undefined,
          viewSettings: sharedLink.viewSettings as MapViewSettings,
          passwordHash: sharedLink.passwordHash || undefined,
          expiresAt: sharedLink.expiresAt?.toISOString(),
          createdBy: sharedLink.creator.id,
          createdAt: sharedLink.createdAt.toISOString(),
          updatedAt: sharedLink.updatedAt.toISOString(),
          locations: sharedLink.locations.map(l => l.location as unknown as Location),
          url,
        };
      },
      { ttl: 5 * 60 * 1000 } // Cache for 5 minutes
    );
  } catch (error) {
    console.error("Error getting shared map link:", error);
    throw new Error("Failed to get shared map link");
  }
}

// Log access to a shared map link
export async function logSharedMapLinkAccess(
  sharedMapLinkId: string,
  ipAddress?: string,
  userAgent?: string
): Promise<void> {
  try {
    await db
      .insert(sharedMapLinkAccessLogs)
      .values({
        sharedMapLinkId,
        ipAddress,
        userAgent,
      })
  } catch (error) {
    console.error("Error logging shared map link access:", error)
    // Don't throw an error here, just log it
  }
}

// Get shared map links for an organization
export async function getSharedMapLinksForOrganization(organizationId: string): Promise<SharedMapLink[]> {
  try {
    // Use cache to avoid redundant database queries
    const cacheKey = `sharedMapLinks:${organizationId}`;
    
    return await cache.getOrSet(
      cacheKey,
      async () => {
        // Use the optimized index for organization_id
        const links = await db.query.sharedMapLinks.findMany({
          where: eq(sharedMapLinks.organizationId, organizationId),
          // Only select the fields we need to reduce data transfer
          columns: {
            id: true,
            token: true,
            mapViewId: true,
            filters: true,
            viewSettings: true,
            passwordHash: true,
            expiresAt: true,
            createdAt: true,
            updatedAt: true,
          },
          with: {
            // Only select the id from creator to reduce data transfer
            creator: {
              columns: {
                id: true,
              },
            },
          },
          orderBy: (tables, { desc }) => [desc(tables.createdAt)],
        });
        
        // Generate app URL once outside the loop for better performance
        const appUrl = process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000';
        
        return links.map(link => {
          // Generate the URL for the shared link
          const url = `${appUrl}/shared/map/${link.token}`;
          
          return {
            id: link.id,
            token: link.token,
            mapViewId: link.mapViewId || undefined,
            filters: link.filters as MapFilter | undefined,
            viewSettings: link.viewSettings as MapViewSettings,
            passwordHash: link.passwordHash || undefined,
            expiresAt: link.expiresAt?.toISOString(),
            createdBy: link.creator.id,
            createdAt: link.createdAt.toISOString(),
            updatedAt: link.updatedAt.toISOString(),
            url,
          };
        });
      },
      { ttl: 60 * 1000 } // Cache for 1 minute
    );
  } catch (error) {
    console.error("Error getting shared map links:", error);
    throw new Error("Failed to get shared map links");
  }
}

// Delete a shared map link
export async function deleteSharedMapLink(id: string, organizationId: string): Promise<boolean> {
  try {
    // Start a transaction
    const result = await db.transaction(async (tx) => {
      // Check if the link exists and belongs to the organization
      const existingLink = await tx.query.sharedMapLinks.findFirst({
        where: and(
          eq(sharedMapLinks.id, id),
          eq(sharedMapLinks.organizationId, organizationId)
        ),
        columns: {
          id: true,
          token: true,
        },
      });
      
      if (!existingLink) {
        return false;
      }
      
      // Delete access logs
      await tx
        .delete(sharedMapLinkAccessLogs)
        .where(eq(sharedMapLinkAccessLogs.sharedMapLinkId, id));
      
      // Delete locations
      await tx
        .delete(sharedMapLinkLocations)
        .where(eq(sharedMapLinkLocations.sharedMapLinkId, id));
      
      // Delete the link
      await tx
        .delete(sharedMapLinks)
        .where(eq(sharedMapLinks.id, id));
      
      return true;
    });
    
    if (result) {
      // Invalidate the cache for this specific link and the organization's links list
      cache.delete(`sharedMapLink:${id}`);
      cache.delete(`sharedMapLinks:${organizationId}`);
    }
    
    return result;
  } catch (error) {
    console.error("Error deleting shared map link:", error);
    throw new Error("Failed to delete shared map link");
  }
}
