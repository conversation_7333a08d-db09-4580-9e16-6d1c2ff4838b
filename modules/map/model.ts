import { z } from "zod"
import { LOCATION_TYPES, LOCATION_STATUSES } from "../shared/constants"
import type { Location } from "../location/model"

// Map view settings model
export type MapViewSettings = {
  center: {
    latitude: number
    longitude: number
  }
  zoom: number
  pitch: number
  bearing: number
  styleId?: string
}

export type MapFilter = {
  locationTypes?: string[]
  projectIds?: string[]
  searchQuery?: string
  status?: string[]
  dateFrom?: string
  dateTo?: string
  favoriteListId?: string
  locationTags?: string[]
  locationFeatures?: string[]
  permitsRequired?: boolean
  hourlyRateMin?: number
  hourlyRateMax?: number
  dailyRateMin?: number
  dailyRateMax?: number
}

// Map state model
export type MapState = {
  viewSettings: MapViewSettings
  selectedLocationId?: string
  filters: MapFilter
}

// Validation schemas
export const mapViewSettingsSchema = z.object({
  center: z.object({
    latitude: z.number(),
    longitude: z.number(),
  }),
  zoom: z.number(),
  pitch: z.number(),
  bearing: z.number(),
  styleId: z.string().optional(),
})

export const mapFilterSchema = z.object({
  locationTypes: z.array(z.enum(LOCATION_TYPES as unknown as [string, ...string[]])).optional(),
  projectIds: z.array(z.string().uuid()).optional(),
  searchQuery: z.string().optional(),
  status: z.array(z.enum(LOCATION_STATUSES as unknown as [string, ...string[]])).optional(),
  dateFrom: z.string().optional(),
  dateTo: z.string().optional(),
  favoriteListId: z.string().uuid().optional(),
  locationTags: z.array(z.string()).optional(),
  locationFeatures: z.array(z.string()).optional(),
  permitsRequired: z.boolean().optional(),
  hourlyRateMin: z.number().optional(),
  hourlyRateMax: z.number().optional(),
  dailyRateMin: z.number().optional(),
  dailyRateMax: z.number().optional(),
})

export const mapStateSchema = z.object({
  viewSettings: mapViewSettingsSchema,
  selectedLocationId: z.string().uuid().optional(),
  filters: mapFilterSchema,
})

// Map clustering settings
export type ClusterSettings = {
  enabled: boolean
  radius: number
  maxZoom: number
}

// Saved view model
export type SavedView = {
  id: string
  name: string
  description?: string
  filters: MapFilter
  viewSettings: MapViewSettings
  locations?: Location[]
  createdBy: string
  createdAt: string
  updatedAt: string
}

export const savedViewSchema = z.object({
  id: z.string().uuid(),
  name: z.string().min(1).max(255),
  description: z.string().optional(),
  filters: mapFilterSchema,
  viewSettings: mapViewSettingsSchema,
  locations: z.array(z.any()).optional(),
  createdBy: z.string().uuid(),
  createdAt: z.string(),
  updatedAt: z.string(),
})

export const createSavedViewSchema = z.object({
  organizationId: z.string().uuid(),
  name: z.string().min(1).max(255),
  description: z.string().optional(),
  filters: mapFilterSchema,
  viewSettings: mapViewSettingsSchema,
  selectedLocationIds: z.array(z.string().uuid()).optional(),
})

export const defaultMapViewSettings: MapViewSettings = {
  center: {
    latitude: 39.8283,
    longitude: -98.5795,
  },
  zoom: 3,
  pitch: 0,
  bearing: 0,
}

export const defaultClusterSettings: ClusterSettings = {
  enabled: true,
  radius: 50,
  maxZoom: 14,
}

// Shared map link models
export type SharedMapLink = {
  id: string
  token: string
  mapViewId?: string
  filters?: MapFilter
  viewSettings: MapViewSettings
  passwordHash?: string
  expiresAt?: string
  createdBy: string
  createdAt: string
  updatedAt: string
  locations?: Location[]
  url: string
}

export const sharedMapLinkSchema = z.object({
  id: z.string().uuid(),
  token: z.string().min(1),
  mapViewId: z.string().uuid().optional(),
  filters: mapFilterSchema.optional(),
  viewSettings: mapViewSettingsSchema,
  passwordHash: z.string().optional(),
  expiresAt: z.string().optional(),
  createdBy: z.string().uuid(),
  createdAt: z.string(),
  updatedAt: z.string(),
  locations: z.array(z.any()).optional(),
  url: z.string().url(),
})

export const createSharedMapLinkSchema = z.object({
  organizationId: z.string().uuid(),
  mapViewId: z.string().uuid().optional(),
  filters: mapFilterSchema.optional(),
  viewSettings: mapViewSettingsSchema.optional(),
  password: z.string().optional(),
  expiresIn: z.number().optional(), // Expiration time in seconds
  locationIds: z.array(z.string().uuid()).optional(),
})

export const accessSharedMapLinkSchema = z.object({
  token: z.string().min(1),
  password: z.string().optional(),
})

export type SharedMapLinkAccess = {
  id: string
  sharedMapLinkId: string
  ipAddress?: string
  userAgent?: string
  accessedAt: string
}
