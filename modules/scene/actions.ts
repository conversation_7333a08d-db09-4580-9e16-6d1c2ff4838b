"use server"

import { revalidatePath } from "next/cache"
import {
  createScene,
  updateScene,
  deleteScene,
  addLocationToScene,
  updateSceneLocation,
  removeLocationFromScene,
  selectLocationForScene,
  addNoteToScene,
  deleteSceneNote,
  voteForLocation,
} from "./service"
import type { Scene, SceneLocation, SceneNote, LocationVote } from "./model"
import { getCurrentUser } from "../auth/service"

// Scene actions
export async function addScene(data: Omit<Scene, "id" | "createdAt" | "updatedAt" | "deletedAt">) {
  try {
    // Get current user for createdById field
    const user = await getCurrentUser()

    const scene = await createScene({
      ...data,
      createdById: user.id,
    })

    revalidatePath(`/projects/${data.projectId}`)
    revalidatePath(`/scenes/${scene.id}`)

    return { success: true, data: scene }
  } catch (error) {
    console.error("Error adding scene:", error)
    return {
      success: false,
      error: error instanceof Error ? error.message : "Failed to add scene",
    }
  }
}

export async function editScene(
  id: string,
  data: Partial<Omit<Scene, "id" | "createdAt" | "updatedAt" | "deletedAt">>,
) {
  try {
    const scene = await updateScene(id, data)

    revalidatePath(`/projects/${scene.projectId}`)
    revalidatePath(`/scenes/${id}`)

    return { success: true, data: scene }
  } catch (error) {
    console.error("Error updating scene:", error)
    return {
      success: false,
      error: error instanceof Error ? error.message : "Failed to update scene",
    }
  }
}

export async function removeScene(id: string, projectId: string) {
  try {
    const scene = await deleteScene(id)

    revalidatePath(`/projects/${projectId}`)
    revalidatePath(`/scenes/${id}`)

    return { success: true, data: scene }
  } catch (error) {
    console.error("Error deleting scene:", error)
    return {
      success: false,
      error: error instanceof Error ? error.message : "Failed to delete scene",
    }
  }
}

// Scene Location actions
export async function addLocationToSceneAction(data: Omit<SceneLocation, "id" | "createdAt" | "updatedAt">) {
  try {
    // Get current user for addedById field
    const user = await getCurrentUser()

    const sceneLocation = await addLocationToScene({
      ...data,
      addedById: user.id,
    })

    revalidatePath(`/scenes/${data.sceneId}`)

    return { success: true, data: sceneLocation }
  } catch (error) {
    console.error("Error adding location to scene:", error)
    return {
      success: false,
      error: error instanceof Error ? error.message : "Failed to add location to scene",
    }
  }
}

export async function updateSceneLocationAction(
  id: string,
  data: Partial<Omit<SceneLocation, "id" | "createdAt" | "updatedAt">>,
  sceneId: string,
) {
  try {
    const sceneLocation = await updateSceneLocation(id, data)

    revalidatePath(`/scenes/${sceneId}`)

    return { success: true, data: sceneLocation }
  } catch (error) {
    console.error("Error updating scene location:", error)
    return {
      success: false,
      error: error instanceof Error ? error.message : "Failed to update scene location",
    }
  }
}

export async function removeLocationFromSceneAction(id: string, sceneId: string) {
  try {
    const sceneLocation = await removeLocationFromScene(id)

    revalidatePath(`/scenes/${sceneId}`)

    return { success: true, data: sceneLocation }
  } catch (error) {
    console.error("Error removing location from scene:", error)
    return {
      success: false,
      error: error instanceof Error ? error.message : "Failed to remove location from scene",
    }
  }
}

export async function selectLocationForSceneAction(sceneLocationId: string, sceneId: string) {
  try {
    const result = await selectLocationForScene(sceneLocationId, sceneId)

    revalidatePath(`/scenes/${sceneId}`)
    revalidatePath(`/projects/${result.scene.projectId}`)

    return { success: true, data: result }
  } catch (error) {
    console.error("Error selecting location for scene:", error)
    return {
      success: false,
      error: error instanceof Error ? error.message : "Failed to select location for scene",
    }
  }
}

// Scene Notes actions
export async function addNoteToSceneAction(data: Omit<SceneNote, "id" | "createdAt" | "updatedAt" | "authorId">) {
  try {
    // Get current user for authorId field
    const user = await getCurrentUser()

    const note = await addNoteToScene({
      ...data,
      authorId: user.id,
    })

    revalidatePath(`/scenes/${data.sceneId}`)

    return { success: true, data: note }
  } catch (error) {
    console.error("Error adding note to scene:", error)
    return {
      success: false,
      error: error instanceof Error ? error.message : "Failed to add note to scene",
    }
  }
}

export async function deleteSceneNoteAction(id: string, sceneId: string) {
  try {
    const note = await deleteSceneNote(id)

    revalidatePath(`/scenes/${sceneId}`)

    return { success: true, data: note }
  } catch (error) {
    console.error("Error deleting scene note:", error)
    return {
      success: false,
      error: error instanceof Error ? error.message : "Failed to delete scene note",
    }
  }
}

// Location Votes actions
export async function voteForLocationAction(
  data: Omit<LocationVote, "id" | "createdAt" | "updatedAt" | "userId">,
  sceneId: string,
) {
  try {
    // Get current user for userId field
    const user = await getCurrentUser()

    const vote = await voteForLocation({
      ...data,
      userId: user.id,
    })

    revalidatePath(`/scenes/${sceneId}`)

    return { success: true, data: vote }
  } catch (error) {
    console.error("Error voting for location:", error)
    return {
      success: false,
      error: error instanceof Error ? error.message : "Failed to vote for location",
    }
  }
}
