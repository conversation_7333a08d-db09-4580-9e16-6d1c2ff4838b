import { relations } from "drizzle-orm"
import { pgTable, uuid, varchar, text, timestamp, jsonb, boolean } from "drizzle-orm/pg-core"
import { createInsertSchema, createSelectSchema } from "drizzle-zod"

import { projects } from "../project/schema"
import { locations } from "../location/schema"
import { users } from "../user/schema"

export const scenes = pgTable("scenes", {
  id: uuid("id").primaryKey().defaultRandom(),
  name: varchar("name", { length: 255 }).notNull(),
  description: text("description"),
  sceneNumber: varchar("scene_number", { length: 50 }),
  timeOfDay: varchar("time_of_day", { length: 50 }),
  interior: boolean("interior").default(false),
  exterior: boolean("exterior").default(false),
  status: varchar("status", { length: 50 }).notNull().default("planning"),
  projectId: uuid("project_id")
    .references(() => projects.id, { onDelete: "cascade" })
    .notNull(),
  finalLocationId: uuid("final_location_id").references(() => locations.id),
  createdById: uuid("created_by_id").references(() => users.id),
  metadata: jsonb("metadata").$type<Record<string, any>>(),
  createdAt: timestamp("created_at").defaultNow().notNull(),
  updatedAt: timestamp("updated_at").defaultNow().notNull(),
  deletedAt: timestamp("deleted_at"),
})

export const scenesRelations = relations(scenes, ({ one, many }) => ({
  project: one(projects, {
    fields: [scenes.projectId],
    references: [projects.id],
  }),
  finalLocation: one(locations, {
    fields: [scenes.finalLocationId],
    references: [locations.id],
  }),
  creator: one(users, {
    fields: [scenes.createdById],
    references: [users.id],
  }),
  sceneLocations: many(sceneLocations),
  sceneNotes: many(sceneNotes),
}))

export const sceneLocations = pgTable("scene_locations", {
  id: uuid("id").primaryKey().defaultRandom(),
  sceneId: uuid("scene_id")
    .references(() => scenes.id, { onDelete: "cascade" })
    .notNull(),
  locationId: uuid("location_id")
    .references(() => locations.id, { onDelete: "cascade" })
    .notNull(),
  notes: text("notes"),
  addedById: uuid("added_by_id").references(() => users.id),
  isSelected: boolean("is_selected").default(false),
  createdAt: timestamp("created_at").defaultNow().notNull(),
  updatedAt: timestamp("updated_at").defaultNow().notNull(),
})

export const sceneLocationsRelations = relations(sceneLocations, ({ one, many }) => ({
  scene: one(scenes, {
    fields: [sceneLocations.sceneId],
    references: [scenes.id],
  }),
  location: one(locations, {
    fields: [sceneLocations.locationId],
    references: [locations.id],
  }),
  addedBy: one(users, {
    fields: [sceneLocations.addedById],
    references: [users.id],
  }),
  votes: many(locationVotes),
}))

export const sceneNotes = pgTable("scene_notes", {
  id: uuid("id").primaryKey().defaultRandom(),
  sceneId: uuid("scene_id")
    .references(() => scenes.id, { onDelete: "cascade" })
    .notNull(),
  content: text("content").notNull(),
  authorId: uuid("author_id").references(() => users.id),
  createdAt: timestamp("created_at").defaultNow().notNull(),
  updatedAt: timestamp("updated_at").defaultNow().notNull(),
})

export const sceneNotesRelations = relations(sceneNotes, ({ one }) => ({
  scene: one(scenes, {
    fields: [sceneNotes.sceneId],
    references: [scenes.id],
  }),
  author: one(users, {
    fields: [sceneNotes.authorId],
    references: [users.id],
  }),
}))

export const locationVotes = pgTable("location_votes", {
  id: uuid("id").primaryKey().defaultRandom(),
  sceneLocationId: uuid("scene_location_id")
    .references(() => sceneLocations.id, { onDelete: "cascade" })
    .notNull(),
  userId: uuid("user_id")
    .references(() => users.id, { onDelete: "cascade" })
    .notNull(),
  vote: varchar("vote", { length: 10 }).notNull(), // "up", "down", or "neutral"
  createdAt: timestamp("created_at").defaultNow().notNull(),
  updatedAt: timestamp("updated_at").defaultNow().notNull(),
})

export const locationVotesRelations = relations(locationVotes, ({ one }) => ({
  sceneLocation: one(sceneLocations, {
    fields: [locationVotes.sceneLocationId],
    references: [sceneLocations.id],
  }),
  user: one(users, {
    fields: [locationVotes.userId],
    references: [users.id],
  }),
}))

// Zod schemas for validation
export const insertSceneSchema = createInsertSchema(scenes)
export const selectSceneSchema = createSelectSchema(scenes)

export const createSceneSchema = insertSceneSchema.omit({
  id: true,
  createdAt: true,
  updatedAt: true,
  deletedAt: true,
})

export const updateSceneSchema = createSceneSchema.partial()

export const sceneIdSchema = selectSceneSchema.pick({ id: true })

export const insertSceneLocationSchema = createInsertSchema(sceneLocations)
export const selectSceneLocationSchema = createSelectSchema(sceneLocations)

export const createSceneLocationSchema = insertSceneLocationSchema.omit({
  id: true,
  createdAt: true,
  updatedAt: true,
})

export const updateSceneLocationSchema = createSceneLocationSchema.partial()

export const insertSceneNoteSchema = createInsertSchema(sceneNotes)
export const selectSceneNoteSchema = createSelectSchema(sceneNotes)

export const createSceneNoteSchema = insertSceneNoteSchema.omit({
  id: true,
  createdAt: true,
  updatedAt: true,
})

export const insertLocationVoteSchema = createInsertSchema(locationVotes)
export const selectLocationVoteSchema = createSelectSchema(locationVotes)

export const createLocationVoteSchema = insertLocationVoteSchema.omit({
  id: true,
  createdAt: true,
  updatedAt: true,
})
