import { db } from "@/lib/db"
import { scenes, sceneLocations, sceneNotes, locationVotes } from "./schema"
import { eq, and, isNull } from "drizzle-orm"
import { NotFoundError } from "../shared/errors"
import {
  type Scene,
  type SceneLocation,
  type SceneNote,
  type LocationVote,
  createSceneSchema,
  updateSceneSchema,
  createSceneLocationSchema,
  updateSceneLocationSchema,
  createSceneNoteSchema,
  createLocationVoteSchema,
} from "./model"

// Scene CRUD operations
export async function getScenes(projectId: string) {
  return db.query.scenes.findMany({
    where: and(eq(scenes.projectId, projectId), isNull(scenes.deletedAt)),
    orderBy: scenes.sceneNumber,
    with: {
      finalLocation: true,
    },
  })
}

export async function getScene(id: string) {
  const scene = await db.query.scenes.findFirst({
    where: and(eq(scenes.id, id), isNull(scenes.deletedAt)),
    with: {
      finalLocation: true,
      project: true,
      creator: true,
      sceneLocations: {
        with: {
          location: true,
          addedBy: true,
          votes: {
            with: {
              user: true,
            },
          },
        },
      },
      sceneNotes: {
        with: {
          author: true,
        },
        orderBy: (sceneNotes, { desc }) => [desc(sceneNotes.createdAt)],
      },
    },
  })

  if (!scene) {
    throw new NotFoundError(`Scene with ID ${id} not found`)
  }

  return scene
}

export async function createScene(data: Omit<Scene, "id" | "createdAt" | "updatedAt" | "deletedAt">) {
  const validatedData = createSceneSchema.parse(data)

  const [scene] = await db
    .insert(scenes)
    .values({
      ...validatedData,
    })
    .returning()

  return scene
}

export async function updateScene(
  id: string,
  data: Partial<Omit<Scene, "id" | "createdAt" | "updatedAt" | "deletedAt">>,
) {
  const validatedData = updateSceneSchema.parse(data)

  const [scene] = await db
    .update(scenes)
    .set({
      ...validatedData,
      updatedAt: new Date(),
    })
    .where(and(eq(scenes.id, id), isNull(scenes.deletedAt)))
    .returning()

  if (!scene) {
    throw new NotFoundError(`Scene with ID ${id} not found`)
  }

  return scene
}

export async function deleteScene(id: string) {
  const [scene] = await db
    .update(scenes)
    .set({
      deletedAt: new Date(),
      updatedAt: new Date(),
    })
    .where(and(eq(scenes.id, id), isNull(scenes.deletedAt)))
    .returning()

  if (!scene) {
    throw new NotFoundError(`Scene with ID ${id} not found`)
  }

  return scene
}

// Scene Location operations
export async function getSceneLocations(sceneId: string) {
  return db.query.sceneLocations.findMany({
    where: eq(sceneLocations.sceneId, sceneId),
    with: {
      location: true,
      addedBy: true,
      votes: {
        with: {
          user: true,
        },
      },
    },
  })
}

export async function addLocationToScene(data: Omit<SceneLocation, "id" | "createdAt" | "updatedAt">) {
  const validatedData = createSceneLocationSchema.parse(data)

  // Check if the location is already added to the scene
  const existingSceneLocation = await db.query.sceneLocations.findFirst({
    where: and(
      eq(sceneLocations.sceneId, validatedData.sceneId),
      eq(sceneLocations.locationId, validatedData.locationId),
    ),
  })

  if (existingSceneLocation) {
    // Update the existing scene location
    const [updatedSceneLocation] = await db
      .update(sceneLocations)
      .set({
        notes: validatedData.notes,
        isSelected: validatedData.isSelected,
        updatedAt: new Date(),
      })
      .where(eq(sceneLocations.id, existingSceneLocation.id))
      .returning()

    return updatedSceneLocation
  }

  // Create a new scene location
  const [sceneLocation] = await db
    .insert(sceneLocations)
    .values({
      ...validatedData,
    })
    .returning()

  return sceneLocation
}

export async function updateSceneLocation(
  id: string,
  data: Partial<Omit<SceneLocation, "id" | "createdAt" | "updatedAt">>,
) {
  const validatedData = updateSceneLocationSchema.parse(data)

  const [sceneLocation] = await db
    .update(sceneLocations)
    .set({
      ...validatedData,
      updatedAt: new Date(),
    })
    .where(eq(sceneLocations.id, id))
    .returning()

  if (!sceneLocation) {
    throw new NotFoundError(`Scene location with ID ${id} not found`)
  }

  return sceneLocation
}

export async function removeLocationFromScene(id: string) {
  const [sceneLocation] = await db.delete(sceneLocations).where(eq(sceneLocations.id, id)).returning()

  if (!sceneLocation) {
    throw new NotFoundError(`Scene location with ID ${id} not found`)
  }

  return sceneLocation
}

export async function selectLocationForScene(sceneLocationId: string, sceneId: string) {
  // Start a transaction
  return db.transaction(async (tx) => {
    // First, unselect all locations for this scene
    await tx.update(sceneLocations).set({ isSelected: false }).where(eq(sceneLocations.sceneId, sceneId))

    // Then, select the specified location
    const [selectedLocation] = await tx
      .update(sceneLocations)
      .set({ isSelected: true })
      .where(eq(sceneLocations.id, sceneLocationId))
      .returning()

    if (!selectedLocation) {
      throw new NotFoundError(`Scene location with ID ${sceneLocationId} not found`)
    }

    // Update the scene's finalLocationId
    const [updatedScene] = await tx
      .update(scenes)
      .set({
        finalLocationId: selectedLocation.locationId,
        updatedAt: new Date(),
      })
      .where(eq(scenes.id, sceneId))
      .returning()

    return {
      sceneLocation: selectedLocation,
      scene: updatedScene,
    }
  })
}

// Scene Notes operations
export async function getSceneNotes(sceneId: string) {
  return db.query.sceneNotes.findMany({
    where: eq(sceneNotes.sceneId, sceneId),
    with: {
      author: true,
    },
    orderBy: (sceneNotes, { desc }) => [desc(sceneNotes.createdAt)],
  })
}

export async function addNoteToScene(data: Omit<SceneNote, "id" | "createdAt" | "updatedAt">) {
  const validatedData = createSceneNoteSchema.parse(data)

  const [note] = await db
    .insert(sceneNotes)
    .values({
      ...validatedData,
    })
    .returning()

  return note
}

export async function deleteSceneNote(id: string) {
  const [note] = await db.delete(sceneNotes).where(eq(sceneNotes.id, id)).returning()

  if (!note) {
    throw new NotFoundError(`Scene note with ID ${id} not found`)
  }

  return note
}

// Location Votes operations
export async function voteForLocation(data: Omit<LocationVote, "id" | "createdAt" | "updatedAt">) {
  const validatedData = createLocationVoteSchema.parse(data)

  // Check if the user has already voted for this location
  const existingVote = await db.query.locationVotes.findFirst({
    where: and(
      eq(locationVotes.sceneLocationId, validatedData.sceneLocationId),
      eq(locationVotes.userId, validatedData.userId),
    ),
  })

  if (existingVote) {
    // Update the existing vote
    const [updatedVote] = await db
      .update(locationVotes)
      .set({
        vote: validatedData.vote,
        updatedAt: new Date(),
      })
      .where(eq(locationVotes.id, existingVote.id))
      .returning()

    return updatedVote
  }

  // Create a new vote
  const [vote] = await db
    .insert(locationVotes)
    .values({
      ...validatedData,
    })
    .returning()

  return vote
}

export async function getVotesForSceneLocation(sceneLocationId: string) {
  return db.query.locationVotes.findMany({
    where: eq(locationVotes.sceneLocationId, sceneLocationId),
    with: {
      user: true,
    },
  })
}

export async function getVoteStats(sceneLocationId: string) {
  const votes = await getVotesForSceneLocation(sceneLocationId)

  const upVotes = votes.filter((vote) => vote.vote === "up").length
  const downVotes = votes.filter((vote) => vote.vote === "down").length
  const neutralVotes = votes.filter((vote) => vote.vote === "neutral").length

  return {
    upVotes,
    downVotes,
    neutralVotes,
    total: votes.length,
  }
}
