import { z } from "zod"
import { SCENE_STATUSES } from "../shared/constants"

// Scene domain models
export type Scene = {
  id: string
  name: string
  description?: string
  sceneNumber?: string
  timeOfDay?: string
  interior: boolean
  exterior: boolean
  status: SceneStatus
  projectId: string
  finalLocationId?: string
  createdById?: string
  metadata?: Record<string, any>
  createdAt: Date
  updatedAt: Date
  deletedAt?: Date | null
}

export type SceneLocation = {
  id: string
  sceneId: string
  locationId: string
  notes?: string
  addedById?: string
  isSelected: boolean
  createdAt: Date
  updatedAt: Date
}

export type SceneNote = {
  id: string
  sceneId: string
  content: string
  authorId?: string
  createdAt: Date
  updatedAt: Date
}

export type LocationVote = {
  id: string
  sceneLocationId: string
  userId: string
  vote: "up" | "down" | "neutral"
  createdAt: Date
  updatedAt: Date
}

export type SceneStatus = (typeof SCENE_STATUSES)[number]

// Validation schemas
export const sceneSchema = z.object({
  id: z.string().uuid(),
  name: z.string().min(1).max(255),
  description: z.string().optional(),
  sceneNumber: z.string().optional(),
  timeOfDay: z.string().optional(),
  interior: z.boolean().default(false),
  exterior: z.boolean().default(false),
  status: z.enum(SCENE_STATUSES as [string, ...string[]]),
  projectId: z.string().uuid(),
  finalLocationId: z.string().uuid().optional(),
  createdById: z.string().uuid().optional(),
  metadata: z.record(z.any()).optional(),
  createdAt: z.date(),
  updatedAt: z.date(),
  deletedAt: z.date().nullable().optional(),
})

export const sceneLocationSchema = z.object({
  id: z.string().uuid(),
  sceneId: z.string().uuid(),
  locationId: z.string().uuid(),
  notes: z.string().optional(),
  addedById: z.string().uuid().optional(),
  isSelected: z.boolean().default(false),
  createdAt: z.date(),
  updatedAt: z.date(),
})

export const sceneNoteSchema = z.object({
  id: z.string().uuid(),
  sceneId: z.string().uuid(),
  content: z.string(),
  authorId: z.string().uuid().optional(),
  createdAt: z.date(),
  updatedAt: z.date(),
})

export const locationVoteSchema = z.object({
  id: z.string().uuid(),
  sceneLocationId: z.string().uuid(),
  userId: z.string().uuid(),
  vote: z.enum(["up", "down", "neutral"]),
  createdAt: z.date(),
  updatedAt: z.date(),
})

export const createSceneSchema = z.object({
  name: z.string().min(1).max(255),
  description: z.string().optional(),
  sceneNumber: z.string().optional(),
  timeOfDay: z.string().optional(),
  interior: z.boolean().default(false),
  exterior: z.boolean().default(false),
  status: z.enum(SCENE_STATUSES as [string, ...string[]]).default("planning"),
  projectId: z.string().uuid(),
  finalLocationId: z.string().uuid().optional(),
  metadata: z.record(z.any()).optional(),
})

export const updateSceneSchema = createSceneSchema.partial()

export const createSceneLocationSchema = z.object({
  sceneId: z.string().uuid(),
  locationId: z.string().uuid(),
  notes: z.string().optional(),
  isSelected: z.boolean().default(false),
})

export const updateSceneLocationSchema = createSceneLocationSchema.partial()

export const createSceneNoteSchema = z.object({
  sceneId: z.string().uuid(),
  content: z.string(),
})

export const createLocationVoteSchema = z.object({
  sceneLocationId: z.string().uuid(),
  vote: z.enum(["up", "down", "neutral"]),
})
