"use client"

import { useState, useEffect } from "react"
import { Card } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Compass, ZoomIn, ZoomOut, Layers, MapPin, Info } from "lucide-react"
import { 
  <PERSON><PERSON>, 
  <PERSON><PERSON><PERSON>ontent, 
  <PERSON><PERSON>Header, 
  DialogTitle, 
  DialogDescription 
} from "@/components/ui/dialog"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { ScrollArea } from "@/components/ui/scroll-area"

interface ProjectMapProps {
  projectId: string
}

interface Location {
  id: string
  name: string
  address: string
  coordinates: { latitude: number; longitude: number }
  type: string
  status: string
  scenes: Array<{
    id: string
    name: string
    status: string
  }>
}

export function ProjectMap({ projectId }: ProjectMapProps) {
  const [locations, setLocations] = useState<Location[]>([])
  const [selectedLocation, setSelectedLocation] = useState<Location | null>(null)
  const [isDialogOpen, setIsDialogOpen] = useState(false)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    // In a real implementation, this would fetch locations from an API
    const fetchLocations = async () => {
      try {
        // Simulate API call
        const mockLocations: Location[] = [
          {
            id: "loc1",
            name: "Downtown Financial District",
            address: "123 Main St, New York, NY",
            coordinates: { latitude: 40.7128, longitude: -74.006 },
            type: "exterior",
            status: "approved",
            scenes: [
              { id: "scene1", name: "Opening Chase", status: "scheduled" },
              { id: "scene7", name: "Final Confrontation", status: "planning" }
            ]
          },
          {
            id: "loc2",
            name: "Downtown Loft",
            address: "456 Broadway, New York, NY",
            coordinates: { latitude: 40.7218, longitude: -73.9982 },
            type: "interior",
            status: "approved",
            scenes: [
              { id: "scene2", name: "Apartment Confrontation", status: "scheduled" }
            ]
          },
          {
            id: "loc3",
            name: "City Rooftop",
            address: "789 Skyline Ave, New York, NY",
            coordinates: { latitude: 40.7589, longitude: -73.9851 },
            type: "exterior",
            status: "pending",
            scenes: [
              { id: "scene3", name: "Rooftop Meeting", status: "not_scheduled" }
            ]
          },
          {
            id: "loc4",
            name: "Police Station Set",
            address: "101 Precinct Blvd, Queens, NY",
            coordinates: { latitude: 40.7282, longitude: -73.7949 },
            type: "interior",
            status: "approved",
            scenes: [
              { id: "scene4", name: "Police Station Briefing", status: "scheduled" }
            ]
          },
          {
            id: "loc5",
            name: "Harbor View",
            address: "202 Waterfront Dr, Brooklyn, NY",
            coordinates: { latitude: 40.7039, longitude: -73.9957 },
            type: "exterior",
            status: "pending",
            scenes: [
              { id: "scene5", name: "Harbor Chase", status: "planning" }
            ]
          }
        ]
        
        setLocations(mockLocations)
      } catch (error) {
        console.error("Error fetching locations:", error)
      } finally {
        setLoading(false)
      }
    }

    fetchLocations()
  }, [projectId])

  const handleMarkerClick = (location: Location) => {
    setSelectedLocation(location)
    setIsDialogOpen(true)
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case "approved":
        return "bg-green-500"
      case "pending":
        return "bg-yellow-500"
      case "rejected":
        return "bg-red-500"
      default:
        return "bg-gray-500"
    }
  }

  const getSceneStatusColor = (status: string) => {
    switch (status) {
      case "scheduled":
        return "default"
      case "planning":
        return "secondary"
      case "not_scheduled":
        return "outline"
      default:
        return "outline"
    }
  }

  return (
    <div className="relative h-full w-full">
      {loading ? (
        <div className="flex h-full items-center justify-center">
          <div className="text-center">
            <div className="animate-spin h-8 w-8 border-4 border-primary border-t-transparent rounded-full mx-auto" />
            <p className="mt-4 text-muted-foreground">Loading map...</p>
          </div>
        </div>
      ) : (
        <>
          <div 
            className="h-full w-full bg-muted"
            style={{
              background: "url('/map-placeholder.jpg') no-repeat center center",
              backgroundSize: "cover",
              position: "relative"
            }}
          >
            {/* Map markers */}
            {locations.map((location, index) => {
              // Calculate position based on index for demo purposes
              // In a real implementation, this would use actual coordinates
              const top = 20 + (index * 15) + "%"
              const left = 20 + (index * 12) + "%"
              
              return (
                <div
                  key={location.id}
                  className="absolute cursor-pointer"
                  style={{ top, left }}
                  onClick={() => handleMarkerClick(location)}
                >
                  <div className={`flex h-8 w-8 items-center justify-center rounded-full ${getStatusColor(location.status)} text-white shadow-lg`}>
                    <MapPin className="h-4 w-4" />
                  </div>
                  <div className="absolute top-full left-1/2 transform -translate-x-1/2 mt-1 bg-background px-2 py-1 rounded text-xs font-medium shadow-md whitespace-nowrap">
                    {location.name}
                  </div>
                </div>
              )
            })}
          </div>
          
          {/* Map controls */}
          <div className="absolute right-4 top-4 flex flex-col space-y-2">
            <Button variant="secondary" size="icon" className="h-8 w-8 rounded-full shadow-md">
              <ZoomIn className="h-4 w-4" />
            </Button>
            <Button variant="secondary" size="icon" className="h-8 w-8 rounded-full shadow-md">
              <ZoomOut className="h-4 w-4" />
            </Button>
            <Button variant="secondary" size="icon" className="h-8 w-8 rounded-full shadow-md">
              <Compass className="h-4 w-4" />
            </Button>
            <Button variant="secondary" size="icon" className="h-8 w-8 rounded-full shadow-md">
              <Layers className="h-4 w-4" />
            </Button>
          </div>
          
          {/* Location button */}
          <div className="absolute bottom-4 right-4">
            <Button variant="default" className="shadow-md">
              <MapPin className="mr-2 h-4 w-4" />
              Add Location
            </Button>
          </div>
          
          {/* Location info dialog */}
          <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
            <DialogContent className="sm:max-w-[600px]">
              <DialogHeader>
                <DialogTitle>{selectedLocation?.name}</DialogTitle>
                <DialogDescription>{selectedLocation?.address}</DialogDescription>
              </DialogHeader>
              
              <Tabs defaultValue="details" className="mt-4">
                <TabsList className="grid w-full grid-cols-3">
                  <TabsTrigger value="details">Details</TabsTrigger>
                  <TabsTrigger value="scenes">Scenes</TabsTrigger>
                  <TabsTrigger value="notes">Notes</TabsTrigger>
                </TabsList>
                
                <TabsContent value="details" className="space-y-4 mt-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <p className="text-sm text-muted-foreground">Type</p>
                      <p className="font-medium capitalize">{selectedLocation?.type}</p>
                    </div>
                    <div>
                      <p className="text-sm text-muted-foreground">Status</p>
                      <div className="flex items-center">
                        <div className={`h-2 w-2 rounded-full ${selectedLocation ? getStatusColor(selectedLocation.status) : 'bg-gray-500'} mr-2`} />
                        <p className="font-medium capitalize">{selectedLocation?.status}</p>
                      </div>
                    </div>
                    <div>
                      <p className="text-sm text-muted-foreground">Coordinates</p>
                      <p className="font-medium">
                        {selectedLocation?.coordinates.latitude.toFixed(4)}, {selectedLocation?.coordinates.longitude.toFixed(4)}
                      </p>
                    </div>
                    <div>
                      <p className="text-sm text-muted-foreground">Scenes</p>
                      <p className="font-medium">{selectedLocation?.scenes.length} assigned</p>
                    </div>
                  </div>
                  
                  <div className="pt-4 border-t">
                    <h4 className="text-sm font-medium mb-2">Location Details</h4>
                    <div className="space-y-2 text-sm">
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">Size:</span>
                        <span>2,500 sq ft</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">Permit Required:</span>
                        <span>Yes</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">Permit Status:</span>
                        <span>Pending</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">Contact:</span>
                        <span>John Doe (555-123-4567)</span>
                      </div>
                    </div>
                  </div>
                  
                  <div className="flex justify-end gap-2">
                    <Button variant="outline">
                      <Info className="mr-2 h-4 w-4" />
                      View Full Details
                    </Button>
                  </div>
                </TabsContent>
                
                <TabsContent value="scenes" className="mt-4">
                  <ScrollArea className="h-[300px]">
                    <div className="space-y-3">
                      {selectedLocation?.scenes.map((scene) => (
                        <Card key={scene.id} className="p-3">
                          <div className="flex justify-between items-start">
                            <div>
                              <p className="font-medium">{scene.name}</p>
                              <p className="text-xs text-muted-foreground mt-1">Scene ID: {scene.id}</p>
                            </div>
                            <Badge variant={getSceneStatusColor(scene.status)}>
                              {scene.status.replace('_', ' ')}
                            </Badge>
                          </div>
                          <div className="flex justify-end mt-3">
                            <Button variant="outline" size="sm">View Scene</Button>
                          </div>
                        </Card>
                      ))}
                    </div>
                  </ScrollArea>
                </TabsContent>
                
                <TabsContent value="notes" className="mt-4">
                  <div className="text-center py-8">
                    <Info className="h-8 w-8 mx-auto text-muted-foreground mb-2" />
                    <h3 className="font-medium mb-1">No Notes Yet</h3>
                    <p className="text-sm text-muted-foreground">
                      Add notes about this location to share with your team.
                    </p>
                    <Button className="mt-4">Add Note</Button>
                  </div>
                </TabsContent>
              </Tabs>
            </DialogContent>
          </Dialog>
        </>
      )}
    </div>
  )
}
