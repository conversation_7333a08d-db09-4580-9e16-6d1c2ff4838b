"use client";

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Avatar } from "@/components/ui/avatar";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { useState } from "react";

interface Note {
  id: string;
  content: string;
  createdAt: string;
  createdBy: {
    id: string;
    name: string;
    avatar?: string;
  };
}

interface ProjectNotesProps {
  notes: Note[];
  onAddNote?: (content: string) => void;
}

export function ProjectNotes({
  notes,
  onAddNote,
}: ProjectNotesProps) {
  const [newNote, setNewNote] = useState("");

  const handleAddNote = () => {
    if (newNote.trim() && onAddNote) {
      onAddNote(newNote);
      setNewNote("");
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>Project Notes</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {onAddNote && (
            <div className="space-y-2">
              <Textarea
                placeholder="Add a note..."
                value={newNote}
                onChange={(e) => setNewNote(e.target.value)}
                className="min-h-[100px]"
              />
              <Button onClick={handleAddNote} disabled={!newNote.trim()}>
                Add Note
              </Button>
            </div>
          )}

          <div className="space-y-4">
            {notes.length === 0 ? (
              <div className="text-center text-sm text-muted-foreground">
                No notes added to this project yet.
              </div>
            ) : (
              notes.map((note) => (
                <div
                  key={note.id}
                  className="rounded-md border p-4 space-y-2"
                >
                  <div className="flex items-center gap-2">
                    <Avatar className="h-6 w-6">
                      <span className="text-xs">
                        {note.createdBy.name.substring(0, 2).toUpperCase()}
                      </span>
                    </Avatar>
                    <div className="flex items-center gap-1 text-sm">
                      <span className="font-medium">{note.createdBy.name}</span>
                      <span className="text-muted-foreground">
                        {formatDate(note.createdAt)}
                      </span>
                    </div>
                  </div>
                  <div className="whitespace-pre-wrap text-sm">
                    {note.content}
                  </div>
                </div>
              ))
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  );
}

function formatDate(dateString: string): string {
  const date = new Date(dateString);
  return new Intl.DateTimeFormat("en-US", {
    year: "numeric",
    month: "short",
    day: "numeric",
    hour: "numeric",
    minute: "numeric",
  }).format(date);
}
