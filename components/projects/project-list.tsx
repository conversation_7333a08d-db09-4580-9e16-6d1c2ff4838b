"use client";

import { <PERSON><PERSON> } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import Link from "next/link";
import { Pencil, Archive, Image as ImageIcon } from "lucide-react";
import type { Project as ProjectModel } from "@/modules/project/model"; // Import the model type

// Removed unused local Project type alias

interface ProjectListProps {
  projects: ProjectModel[]; // Use ProjectModel[]
  organizationSlug: string;
  handleArchiveClick: (project: ProjectModel) => void; // Use ProjectModel
}

export function ProjectList({ projects, organizationSlug, handleArchiveClick }: ProjectListProps) {
  return (
    <div className="rounded-lg border"> {/* Use rounded-lg for consistency */}
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead className="w-[350px]">Project Name</TableHead>
            <TableHead>Status</TableHead>
            <TableHead>Dates</TableHead>
            <TableHead className="text-right w-[100px]">Actions</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {projects.map((project) => (
            <TableRow key={project.id} className="transition-all hover:shadow-[0_0_10px_rgba(35,35,255,0.7)]">
              <TableCell className="font-medium">
                <div className="flex items-center gap-3">
                  <div className="flex h-10 w-10 items-center justify-center rounded-md bg-muted flex-shrink-0">
                    <ImageIcon className="h-5 w-5 text-muted-foreground" />
                    {/* Placeholder Image */}
                    <ImageIcon className="h-5 w-5 text-muted-foreground" />
                  </div>
                  <Link href={`/organizations/${organizationSlug}/projects/${project.id}`} className="hover:underline line-clamp-1">
                    {project.name}
                  </Link>
                </div>
              </TableCell>
              <TableCell>
                <Badge variant={getStatusVariant(project.status)} className="capitalize">
                  {project.status}
                </Badge>
              </TableCell>
              <TableCell className="text-xs text-muted-foreground">
                {project.startDate && (
                  <span>
                    {new Date(project.startDate).toLocaleDateString()}
                    {project.endDate && ` - ${new Date(project.endDate).toLocaleDateString()}`}
                  </span>
                 )}
               </TableCell>
               <TableCell className="text-right">
                 <div className="flex items-center justify-end space-x-1">
                  <Link href={`/organizations/${organizationSlug}/projects/${project.id}/edit`}>
                    <Button
                      variant="ghost"
                      size="icon"
                      className="h-8 w-8 text-muted-foreground transition-all hover:bg-transparent group"
                    >
                      <Pencil className="h-4 w-4 transition-all group-hover:[filter:drop-shadow(0_0_6px_rgba(22,255,0,0.8))]" />
                      <span className="sr-only">Edit Project</span>
                    </Button>
                  </Link>
                  <Button
                    variant="ghost"
                    size="icon"
                    className="h-8 w-8 text-muted-foreground transition-all hover:bg-transparent group"
                    onClick={() => handleArchiveClick(project)}
                  >
                    <Archive className="h-4 w-4 transition-all group-hover:[filter:drop-shadow(0_0_6px_rgba(246,25,129,0.8))]" />
                    <span className="sr-only">Archive Project</span>
                  </Button>
                </div>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </div>
  );
}

function getStatusVariant(status: string): "default" | "secondary" | "destructive" | "outline" {
  switch (status.toLowerCase()) {
    case "active":
      return "default";
    case "planning":
      return "secondary";
    case "completed":
      return "outline";
    case "cancelled":
      return "destructive";
    default:
      return "default";
  }
}
