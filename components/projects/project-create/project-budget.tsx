import { Label } from "@/components/ui/label"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Textarea } from "@/components/ui/textarea"
import { Separator } from "@/components/ui/separator"
import { Switch } from "@/components/ui/switch"

export function ProjectBudget() {
  return (
    <div className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div className="space-y-2">
          <Label htmlFor="totalBudget">Total Budget</Label>
          <div className="relative">
            <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
              <span className="text-muted-foreground">$</span>
            </div>
            <Input id="totalBudget" type="number" min="0" className="pl-7" placeholder="0.00" />
          </div>
        </div>

        <div className="space-y-2">
          <Label htmlFor="currency">Currency</Label>
          <Select defaultValue="usd">
            <SelectTrigger id="currency">
              <SelectValue placeholder="Select currency" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="usd">USD ($)</SelectItem>
              <SelectItem value="eur">EUR (€)</SelectItem>
              <SelectItem value="gbp">GBP (£)</SelectItem>
              <SelectItem value="cad">CAD ($)</SelectItem>
              <SelectItem value="aud">AUD ($)</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      <Separator />

      <div className="space-y-4">
        <Label>Budget Allocation</Label>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="space-y-2">
            <Label htmlFor="locationBudget">Location Budget</Label>
            <div className="relative">
              <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                <span className="text-muted-foreground">$</span>
              </div>
              <Input id="locationBudget" type="number" min="0" className="pl-7" placeholder="0.00" />
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="permitBudget">Permit Budget</Label>
            <div className="relative">
              <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                <span className="text-muted-foreground">$</span>
              </div>
              <Input id="permitBudget" type="number" min="0" className="pl-7" placeholder="0.00" />
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="travelBudget">Travel Budget</Label>
            <div className="relative">
              <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                <span className="text-muted-foreground">$</span>
              </div>
              <Input id="travelBudget" type="number" min="0" className="pl-7" placeholder="0.00" />
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="equipmentBudget">Equipment Budget</Label>
            <div className="relative">
              <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                <span className="text-muted-foreground">$</span>
              </div>
              <Input id="equipmentBudget" type="number" min="0" className="pl-7" placeholder="0.00" />
            </div>
          </div>
        </div>
      </div>

      <Separator />

      <div className="space-y-2">
        <Label htmlFor="budgetNotes">Budget Notes</Label>
        <Textarea id="budgetNotes" placeholder="Add any additional notes about the budget" className="min-h-[100px]" />
      </div>

      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <div className="space-y-0.5">
            <Label htmlFor="trackExpenses">Track Expenses</Label>
            <p className="text-sm text-muted-foreground">Enable expense tracking for this project</p>
          </div>
          <Switch id="trackExpenses" defaultChecked />
        </div>

        <div className="flex items-center justify-between">
          <div className="space-y-0.5">
            <Label htmlFor="budgetAlerts">Budget Alerts</Label>
            <p className="text-sm text-muted-foreground">Receive notifications when approaching budget limits</p>
          </div>
          <Switch id="budgetAlerts" defaultChecked />
        </div>
      </div>
    </div>
  )
}

