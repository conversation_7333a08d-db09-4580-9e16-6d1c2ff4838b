"use client"

import { useState } from "react"
import { Label } from "@/components/ui/label"
import { Input } from "@/components/ui/input"
import { But<PERSON> } from "@/components/ui/button"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Badge } from "@/components/ui/badge"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Search, Plus, X } from "lucide-react"

interface ProjectTeamProps {
  organizationSlug: string
}

export function ProjectTeam({ organizationSlug }: ProjectTeamProps) {
  // Sample team members from the organization
  const organizationMembers = [
    {
      id: 1,
      name: "<PERSON>",
      role: "Director",
      department: "Direction",
      email: "<EMAIL>",
      avatar: "/placeholder.svg?height=32&width=32",
      initials: "J<PERSON>",
    },
    {
      id: 2,
      name: "<PERSON>",
      role: "Location Manager",
      department: "Locations",
      email: "<EMAIL>",
      avatar: "/placeholder.svg?height=32&width=32",
      initials: "<PERSON>",
    },
    {
      id: 3,
      name: "<PERSON>",
      role: "Production Designer",
      department: "Art",
      email: "<EMAIL>",
      avatar: "/placeholder.svg?height=32&width=32",
      initials: "SJ",
    },
    {
      id: 4,
      name: "David Rodriguez",
      role: "Director of Photography",
      department: "Camera",
      email: "<EMAIL>",
      avatar: "/placeholder.svg?height=32&width=32",
      initials: "DR",
    },
  ]

  // State for selected team members
  const [selectedMembers, setSelectedMembers] = useState([
    {
      id: 1,
      name: "Jane Smith",
      role: "Director",
      projectRole: "Director",
      avatar: "/placeholder.svg?height=32&width=32",
      initials: "JS",
    },
  ])

  // Function to add a team member
  const addTeamMember = (member: any) => {
    setSelectedMembers([
      ...selectedMembers,
      {
        ...member,
        projectRole: member.role,
      },
    ])
  }

  // Function to remove a team member
  const removeTeamMember = (id: number) => {
    setSelectedMembers(selectedMembers.filter((member) => member.id !== id))
  }

  return (
    <div className="space-y-6">
      <div className="space-y-2">
        <Label>Project Team Members</Label>
        <p className="text-sm text-muted-foreground">Add team members to this project and assign their roles</p>
      </div>

      <div className="space-y-4">
        <div className="flex flex-col md:flex-row gap-4">
          <div className="relative flex-1">
            <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input placeholder="Search team members..." className="pl-8" />
          </div>
          <Button variant="outline">
            <Plus className="mr-2 h-4 w-4" />
            Invite New Member
          </Button>
        </div>

        <div className="border rounded-md">
          <div className="p-4 border-b">
            <h3 className="font-medium">Organization Members</h3>
          </div>
          <div className="p-4 space-y-4">
            {organizationMembers.map((member) => {
              const isSelected = selectedMembers.some((m) => m.id === member.id)
              return (
                <div key={member.id} className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <Avatar className="h-8 w-8">
                      <AvatarImage src={member.avatar} alt={member.name} />
                      <AvatarFallback>{member.initials}</AvatarFallback>
                    </Avatar>
                    <div>
                      <p className="font-medium">{member.name}</p>
                      <p className="text-xs text-muted-foreground">{member.role}</p>
                    </div>
                  </div>
                  <Button variant="outline" size="sm" onClick={() => addTeamMember(member)} disabled={isSelected}>
                    {isSelected ? "Added" : "Add"}
                  </Button>
                </div>
              )
            })}
          </div>
        </div>
      </div>

      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <h3 className="font-medium">Selected Team Members</h3>
          <Badge variant="outline">{selectedMembers.length}</Badge>
        </div>

        {selectedMembers.length > 0 ? (
          <div className="space-y-4">
            {selectedMembers.map((member) => (
              <div key={member.id} className="flex items-center justify-between border rounded-md p-3">
                <div className="flex items-center gap-3">
                  <Avatar className="h-8 w-8">
                    <AvatarImage src={member.avatar} alt={member.name} />
                    <AvatarFallback>{member.initials}</AvatarFallback>
                  </Avatar>
                  <div>
                    <p className="font-medium">{member.name}</p>
                  </div>
                </div>

                <div className="flex items-center gap-2">
                  <Select defaultValue={member.projectRole}>
                    <SelectTrigger className="w-[180px]">
                      <SelectValue placeholder="Select role" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="Director">Director</SelectItem>
                      <SelectItem value="Producer">Producer</SelectItem>
                      <SelectItem value="Location Manager">Location Manager</SelectItem>
                      <SelectItem value="Location Scout">Location Scout</SelectItem>
                      <SelectItem value="Production Designer">Production Designer</SelectItem>
                      <SelectItem value="Director of Photography">Director of Photography</SelectItem>
                      <SelectItem value="Production Assistant">Production Assistant</SelectItem>
                    </SelectContent>
                  </Select>

                  <Button variant="ghost" size="icon" onClick={() => removeTeamMember(member.id)}>
                    <X className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div className="border rounded-md p-8 text-center">
            <p className="text-muted-foreground">No team members selected yet</p>
          </div>
        )}
      </div>
    </div>
  )
}

