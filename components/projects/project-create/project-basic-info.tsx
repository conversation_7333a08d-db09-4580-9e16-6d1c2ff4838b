import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"

export function ProjectBasicInfo() {
  return (
    <div className="space-y-6">
      <div className="space-y-2">
        <Label htmlFor="projectTitle">
          Project Title <span className="text-destructive">*</span>
        </Label>
        <Input id="projectTitle" placeholder="Enter project title" />
      </div>

      <div className="space-y-2">
        <Label htmlFor="projectDescription">Description</Label>
        <Textarea
          id="projectDescription"
          placeholder="Provide a brief description of the project"
          className="min-h-[120px]"
        />
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div className="space-y-2">
          <Label htmlFor="projectType">
            Project Type <span className="text-destructive">*</span>
          </Label>
          <Select>
            <SelectTrigger id="projectType">
              <SelectValue placeholder="Select project type" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="feature">Feature Film</SelectItem>
              <SelectItem value="short">Short Film</SelectItem>
              <SelectItem value="tv">TV Series</SelectItem>
              <SelectItem value="commercial">Commercial</SelectItem>
              <SelectItem value="documentary">Documentary</SelectItem>
              <SelectItem value="music">Music Video</SelectItem>
              <SelectItem value="other">Other</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <div className="space-y-2">
          <Label htmlFor="projectGenre">Genre</Label>
          <Select>
            <SelectTrigger id="projectGenre">
              <SelectValue placeholder="Select genre" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="action">Action</SelectItem>
              <SelectItem value="comedy">Comedy</SelectItem>
              <SelectItem value="drama">Drama</SelectItem>
              <SelectItem value="horror">Horror</SelectItem>
              <SelectItem value="scifi">Science Fiction</SelectItem>
              <SelectItem value="thriller">Thriller</SelectItem>
              <SelectItem value="documentary">Documentary</SelectItem>
              <SelectItem value="other">Other</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      <div className="space-y-2">
        <Label>Project Status</Label>
        <RadioGroup defaultValue="planning" className="flex flex-col space-y-1">
          <div className="flex items-center space-x-2">
            <RadioGroupItem value="planning" id="planning" />
            <Label htmlFor="planning" className="font-normal">
              Planning
            </Label>
          </div>
          <div className="flex items-center space-x-2">
            <RadioGroupItem value="preproduction" id="preproduction" />
            <Label htmlFor="preproduction" className="font-normal">
              Pre-production
            </Label>
          </div>
          <div className="flex items-center space-x-2">
            <RadioGroupItem value="production" id="production" />
            <Label htmlFor="production" className="font-normal">
              Production
            </Label>
          </div>
          <div className="flex items-center space-x-2">
            <RadioGroupItem value="postproduction" id="postproduction" />
            <Label htmlFor="postproduction" className="font-normal">
              Post-production
            </Label>
          </div>
        </RadioGroup>
      </div>

      <div className="space-y-2">
        <Label htmlFor="projectDirector">Director</Label>
        <Input id="projectDirector" placeholder="Enter director's name" />
      </div>

      <div className="space-y-2">
        <Label htmlFor="projectProducer">Producer</Label>
        <Input id="projectProducer" placeholder="Enter producer's name" />
      </div>

      <div className="space-y-2">
        <Label htmlFor="projectCompany">Production Company</Label>
        <Input id="projectCompany" placeholder="Enter production company" />
      </div>
    </div>
  )
}
