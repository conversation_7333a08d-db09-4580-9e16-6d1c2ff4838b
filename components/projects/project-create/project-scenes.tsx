"use client"

import { useState } from "react"
import { Label } from "@/components/ui/label"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Button } from "@/components/ui/button"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Plus, Trash2 } from "lucide-react"
import { SceneLocationSelection } from "@/components/scenes/scene-location-selection"

export function ProjectScenes() {
  const [scenes, setScenes] = useState([{ id: 1, name: "", description: "", locationId: null }])

  const addScene = () => {
    const newId = scenes.length > 0 ? Math.max(...scenes.map((scene) => scene.id)) + 1 : 1
    setScenes([...scenes, { id: newId, name: "", description: "", locationId: null }])
  }

  const removeScene = (id: number) => {
    if (scenes.length > 1) {
      setScenes(scenes.filter((scene) => scene.id !== id))
    }
  }

  const updateScene = (id: number, field: string, value: any) => {
    setScenes(scenes.map((scene) => (scene.id === id ? { ...scene, [field]: value } : scene)))
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-lg font-medium">Project Scenes</h3>
          <p className="text-sm text-muted-foreground">Add scenes for your project and associate them with locations</p>
        </div>
        <Button onClick={addScene} variant="outline" size="sm">
          <Plus className="mr-2 h-4 w-4" />
          Add Scene
        </Button>
      </div>

      <div className="space-y-4">
        {scenes.map((scene) => (
          <Card key={scene.id}>
            <CardHeader className="pb-3">
              <div className="flex items-center justify-between">
                <CardTitle className="text-base">{scene.name ? scene.name : `Scene ${scene.id}`}</CardTitle>
                <Button variant="ghost" size="sm" onClick={() => removeScene(scene.id)} disabled={scenes.length <= 1}>
                  <Trash2 className="h-4 w-4 text-muted-foreground" />
                </Button>
              </div>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor={`scene-name-${scene.id}`}>Scene Name</Label>
                <Input
                  id={`scene-name-${scene.id}`}
                  placeholder="Enter scene name"
                  value={scene.name}
                  onChange={(e) => updateScene(scene.id, "name", e.target.value)}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor={`scene-description-${scene.id}`}>Description</Label>
                <Textarea
                  id={`scene-description-${scene.id}`}
                  placeholder="Describe the scene"
                  value={scene.description}
                  onChange={(e) => updateScene(scene.id, "description", e.target.value)}
                />
              </div>
              <div className="space-y-2">
                <Label>Location</Label>
                <SceneLocationSelection
                  selectedLocationId={scene.locationId}
                  onLocationSelect={(locationId) => updateScene(scene.id, "locationId", locationId)}
                />
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  )
}

