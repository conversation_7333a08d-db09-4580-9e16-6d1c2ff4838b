"use client";

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";

interface TimelineEvent {
  id: string;
  title: string;
  description?: string;
  date: string;
  type: "milestone" | "task" | "location" | "document" | "team";
  status?: string;
}

interface ProjectTimelineProps {
  events: TimelineEvent[];
}

export function ProjectTimeline({ events }: ProjectTimelineProps) {
  // Sort events by date
  const sortedEvents = [...events].sort(
    (a, b) => new Date(a.date).getTime() - new Date(b.date).getTime()
  );

  return (
    <Card>
      <CardHeader>
        <CardTitle>Project Timeline</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="relative space-y-4 pl-6 before:absolute before:left-2 before:top-2 before:h-[calc(100%-16px)] before:w-[2px] before:bg-muted">
          {sortedEvents.length === 0 ? (
            <div className="text-center text-sm text-muted-foreground">
              No timeline events yet.
            </div>
          ) : (
            sortedEvents.map((event) => (
              <div key={event.id} className="relative pb-4">
                <div className="absolute -left-[19px] top-1 h-4 w-4 rounded-full bg-primary" />
                <div className="space-y-1">
                  <div className="flex items-center gap-2">
                    <h4 className="font-medium">{event.title}</h4>
                    <Badge variant={getEventTypeBadge(event.type)}>
                      {formatEventType(event.type)}
                    </Badge>
                    {event.status && (
                      <Badge variant="outline">{event.status}</Badge>
                    )}
                  </div>
                  {event.description && (
                    <p className="text-sm text-muted-foreground">
                      {event.description}
                    </p>
                  )}
                  <p className="text-xs text-muted-foreground">
                    {formatDate(event.date)}
                  </p>
                </div>
              </div>
            ))
          )}
        </div>
      </CardContent>
    </Card>
  );
}

function formatEventType(type: string): string {
  return type.charAt(0).toUpperCase() + type.slice(1);
}

function getEventTypeBadge(
  type: string
): "default" | "secondary" | "outline" | "destructive" {
  switch (type) {
    case "milestone":
      return "default";
    case "task":
      return "secondary";
    case "location":
      return "outline";
    case "document":
      return "secondary";
    case "team":
      return "outline";
    default:
      return "outline";
  }
}

function formatDate(dateString: string): string {
  const date = new Date(dateString);
  return new Intl.DateTimeFormat("en-US", {
    year: "numeric",
    month: "short",
    day: "numeric",
    hour: "numeric",
    minute: "numeric",
  }).format(date);
}
