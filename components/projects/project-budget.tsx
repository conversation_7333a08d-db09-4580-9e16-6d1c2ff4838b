"use client";

import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";

interface BudgetCategory {
  id: string;
  name: string;
  allocated: number;
  spent: number;
  currency: string;
}

interface ProjectBudgetProps {
  totalBudget: number;
  totalSpent: number;
  currency: string;
  categories: BudgetCategory[];
}

export function ProjectBudget({
  totalBudget,
  totalSpent,
  currency,
  categories,
}: ProjectBudgetProps) {
  const percentSpent = (totalSpent / totalBudget) * 100;
  const remaining = totalBudget - totalSpent;

  return (
    <Card>
      <CardHeader>
        <CardTitle>Project Budget</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-6">
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <div className="grid gap-0.5">
                <h3 className="text-sm font-medium">Total Budget</h3>
                <p className="text-xs text-muted-foreground">
                  {formatCurrency(totalSpent, currency)} of{" "}
                  {formatCurrency(totalBudget, currency)} spent
                </p>
              </div>
              <p className="text-sm font-medium">
                {percentSpent.toFixed(0)}%
              </p>
            </div>
            <Progress value={percentSpent} className="h-2" />
            <div className="flex justify-between text-xs text-muted-foreground">
              <p>
                Remaining: {formatCurrency(remaining, currency)}
              </p>
              <p>
                {formatCurrency(totalBudget, currency)}
              </p>
            </div>
          </div>

          <div className="space-y-4">
            <h3 className="text-sm font-medium">Budget Categories</h3>
            {categories.map((category) => {
              const categoryPercent = (category.spent / category.allocated) * 100;
              return (
                <div key={category.id} className="space-y-2">
                  <div className="flex items-center justify-between">
                    <div className="grid gap-0.5">
                      <h4 className="text-sm font-medium">{category.name}</h4>
                      <p className="text-xs text-muted-foreground">
                        {formatCurrency(category.spent, category.currency)} of{" "}
                        {formatCurrency(category.allocated, category.currency)}
                      </p>
                    </div>
                    <p className="text-sm font-medium">
                      {categoryPercent.toFixed(0)}%
                    </p>
                  </div>
                  <Progress
                    value={categoryPercent}
                    className="h-1.5"
                    variant={categoryPercent > 90 ? "destructive" : "default"}
                  />
                </div>
              );
            })}
          </div>
        </div>
      </CardContent>
    </Card>
  );
}

function formatCurrency(amount: number, currency: string): string {
  return new Intl.NumberFormat("en-US", {
    style: "currency",
    currency,
    maximumFractionDigits: 0,
  }).format(amount);
}
