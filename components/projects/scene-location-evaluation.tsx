"use client"

import { useState } from "react"
import { <PERSON>, <PERSON><PERSON><PERSON>nt, Card<PERSON><PERSON>er, Card<PERSON>itle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Textarea } from "@/components/ui/textarea"
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog"
import {
  MapPin,
  ArrowLeft,
  ThumbsUp,
  ThumbsDown,
  MessageSquare,
  Check,
  X,
  AlertTriangle,
  Info,
  Camera,
  Calendar,
  Clock,
  DollarSign,
  Truck,
  Users,
} from "lucide-react"

interface SceneLocationEvaluationProps {
  scene: any
  onBack: () => void
}

export function SceneLocationEvaluation({ scene, onBack }: SceneLocationEvaluationProps) {
  const [selectedLocation, setSelectedLocation] = useState<string | null>(null)
  const [showApprovalDialog, setShowApprovalDialog] = useState(false)
  const [approvalNotes, setApprovalNotes] = useState("")

  // Find the location that is currently approved (if any)
  const approvedLocation = scene.potentialLocations?.find((loc: any) => loc.status === "approved")
  
  // If there's an approved location and no location is selected, select the approved one
  if (approvedLocation && !selectedLocation) {
    setSelectedLocation(approvedLocation.id)
  }

  const handleLocationSelect = (locationId: string) => {
    setSelectedLocation(locationId)
  }

  const handleApproveLocation = () => {
    // In a real implementation, this would call an API to update the location status
    setShowApprovalDialog(false)
    // We would update the location status here
  }

  const getLocationById = (locationId: string) => {
    return scene.potentialLocations?.find((loc: any) => loc.id === locationId) || null
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case "approved":
        return "bg-green-500"
      case "pending":
        return "bg-yellow-500"
      case "rejected":
        return "bg-red-500"
      case "not_selected":
        return "bg-gray-500"
      default:
        return "bg-gray-500"
    }
  }

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "approved":
        return "default"
      case "pending":
        return "secondary"
      case "rejected":
        return "destructive"
      case "not_selected":
        return "outline"
      default:
        return "outline"
    }
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <Button variant="ghost" onClick={onBack} className="gap-1">
          <ArrowLeft className="h-4 w-4" />
          Back to Scene
        </Button>
        <h2 className="text-xl font-bold">Location Evaluation for {scene.name}</h2>
        <div className="w-[100px]"></div> {/* Spacer for centering */}
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="md:col-span-1">
          <Card className="h-full">
            <CardHeader className="pb-2">
              <CardTitle className="text-base">Potential Locations</CardTitle>
            </CardHeader>
            <CardContent className="p-0">
              <ScrollArea className="h-[600px]">
                <div className="px-4 py-2 space-y-2">
                  {scene.potentialLocations?.map((location: any) => (
                    <div
                      key={location.id}
                      className={`p-3 rounded-md border cursor-pointer transition-colors ${
                        selectedLocation === location.id ? "bg-muted border-primary" : ""
                      }`}
                      onClick={() => handleLocationSelect(location.id)}
                      onKeyDown={(e) => {
                        if (e.key === 'Enter' || e.key === ' ') {
                          handleLocationSelect(location.id)
                        }
                      }}
                      tabIndex={0}
                      role="button"
                      aria-pressed={selectedLocation === location.id}
                    >
                      <div className="flex justify-between items-start">
                        <div>
                          <div className="font-medium">{location.name}</div>
                          <div className="text-xs text-muted-foreground mt-1">{location.address}</div>
                        </div>
                        <div className="flex flex-col items-end gap-2">
                          <Badge variant={getStatusBadge(location.status)}>
                            {location.status.replace("_", " ")}
                          </Badge>
                          <div className="flex items-center gap-2 text-xs">
                            <div className="flex items-center">
                              <ThumbsUp className="h-3 w-3 text-green-500 mr-1" />
                              <span>{location.votes.up}</span>
                            </div>
                            <div className="flex items-center">
                              <ThumbsDown className="h-3 w-3 text-red-500 mr-1" />
                              <span>{location.votes.down}</span>
                            </div>
                          </div>
                        </div>
                      </div>
                      <div className="mt-2 text-xs text-muted-foreground line-clamp-2">
                        {location.notes}
                      </div>
                    </div>
                  ))}
                </div>
              </ScrollArea>
            </CardContent>
          </Card>
        </div>

        <div className="md:col-span-2">
          {selectedLocation ? (
            <Card>
              <CardHeader className="pb-2 flex flex-row items-start justify-between">
                <div>
                  <CardTitle>{getLocationById(selectedLocation)?.name}</CardTitle>
                  <p className="text-sm text-muted-foreground mt-1">{getLocationById(selectedLocation)?.address}</p>
                </div>
                <div className="flex gap-2">
                  {getLocationById(selectedLocation)?.status !== "approved" ? (
                    <Dialog open={showApprovalDialog} onOpenChange={setShowApprovalDialog}>
                      <DialogTrigger asChild>
                        <Button>
                          <Check className="mr-2 h-4 w-4" />
                          Approve Location
                        </Button>
                      </DialogTrigger>
                      <DialogContent>
                        <DialogHeader>
                          <DialogTitle>Approve Location</DialogTitle>
                        </DialogHeader>
                        <div className="py-4">
                          <p className="mb-4">
                            Are you sure you want to approve this location for {scene.name}? This will mark other locations as "not selected".
                          </p>
                          <div className="space-y-2">
                            <label htmlFor="approval-notes" className="text-sm font-medium">
                              Approval Notes (optional)
                            </label>
                            <Textarea
                              id="approval-notes"
                              placeholder="Add any notes about this approval..."
                              value={approvalNotes}
                              onChange={(e) => setApprovalNotes(e.target.value)}
                            />
                          </div>
                        </div>
                        <div className="flex justify-end gap-2">
                          <Button variant="outline" onClick={() => setShowApprovalDialog(false)}>
                            Cancel
                          </Button>
                          <Button onClick={handleApproveLocation}>
                            Approve Location
                          </Button>
                        </div>
                      </DialogContent>
                    </Dialog>
                  ) : (
                    <Button variant="outline" disabled>
                      <Check className="mr-2 h-4 w-4" />
                      Approved
                    </Button>
                  )}
                  {getLocationById(selectedLocation)?.status !== "rejected" && 
                   getLocationById(selectedLocation)?.status !== "not_selected" && (
                    <Button variant="outline" className="text-red-500 hover:text-red-500">
                      <X className="mr-2 h-4 w-4" />
                      Reject
                    </Button>
                  )}
                </div>
              </CardHeader>
              <CardContent>
                <Tabs defaultValue="details">
                  <TabsList className="w-full">
                    <TabsTrigger value="details" className="flex-1">
                      Details
                    </TabsTrigger>
                    <TabsTrigger value="comparison" className="flex-1">
                      Comparison
                    </TabsTrigger>
                    <TabsTrigger value="discussion" className="flex-1">
                      Discussion
                    </TabsTrigger>
                    <TabsTrigger value="media" className="flex-1">
                      Media
                    </TabsTrigger>
                  </TabsList>

                  <TabsContent value="details" className="space-y-4 mt-4">
                    <div className="aspect-video relative rounded-md overflow-hidden border">
                      <div 
                        className="h-full w-full bg-muted flex items-center justify-center"
                        style={{
                          background: "url('/placeholder.svg?height=400&width=800&text=Location+Map') no-repeat center center",
                          backgroundSize: "cover"
                        }}
                      />
                    </div>

                    <div className="grid grid-cols-2 gap-4">
                      <Card>
                        <CardHeader className="pb-2">
                          <CardTitle className="text-sm flex items-center">
                            <Info className="h-4 w-4 mr-2" />
                            Basic Information
                          </CardTitle>
                        </CardHeader>
                        <CardContent className="space-y-2 text-sm">
                          <div className="flex justify-between">
                            <span className="text-muted-foreground">Type:</span>
                            <span>Exterior</span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-muted-foreground">Size:</span>
                            <span>2,500 sq ft</span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-muted-foreground">Accessibility:</span>
                            <span>Good</span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-muted-foreground">Parking:</span>
                            <span>Available</span>
                          </div>
                        </CardContent>
                      </Card>

                      <Card>
                        <CardHeader className="pb-2">
                          <CardTitle className="text-sm flex items-center">
                            <DollarSign className="h-4 w-4 mr-2" />
                            Cost & Permits
                          </CardTitle>
                        </CardHeader>
                        <CardContent className="space-y-2 text-sm">
                          <div className="flex justify-between">
                            <span className="text-muted-foreground">Daily Rate:</span>
                            <span>$1,500</span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-muted-foreground">Permit Required:</span>
                            <span>Yes</span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-muted-foreground">Permit Cost:</span>
                            <span>$500</span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-muted-foreground">Permit Status:</span>
                            <span>Pending</span>
                          </div>
                        </CardContent>
                      </Card>

                      <Card>
                        <CardHeader className="pb-2">
                          <CardTitle className="text-sm flex items-center">
                            <Calendar className="h-4 w-4 mr-2" />
                            Availability
                          </CardTitle>
                        </CardHeader>
                        <CardContent className="space-y-2 text-sm">
                          <div className="flex justify-between">
                            <span className="text-muted-foreground">Available From:</span>
                            <span>Jun 10, 2025</span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-muted-foreground">Available To:</span>
                            <span>Jun 30, 2025</span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-muted-foreground">Restrictions:</span>
                            <span>None</span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-muted-foreground">Notice Required:</span>
                            <span>7 days</span>
                          </div>
                        </CardContent>
                      </Card>

                      <Card>
                        <CardHeader className="pb-2">
                          <CardTitle className="text-sm flex items-center">
                            <Camera className="h-4 w-4 mr-2" />
                            Filming Conditions
                          </CardTitle>
                        </CardHeader>
                        <CardContent className="space-y-2 text-sm">
                          <div className="flex justify-between">
                            <span className="text-muted-foreground">Lighting:</span>
                            <span>Excellent</span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-muted-foreground">Sound:</span>
                            <span>Moderate noise</span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-muted-foreground">Power Access:</span>
                            <span>Available</span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-muted-foreground">Weather Concerns:</span>
                            <span>Minimal</span>
                          </div>
                        </CardContent>
                      </Card>
                    </div>

                    <div className="border-t pt-4">
                      <h4 className="text-sm font-medium mb-2">Location Notes</h4>
                      <p className="text-sm">{getLocationById(selectedLocation)?.notes}</p>
                    </div>

                    <div className="border-t pt-4">
                      <h4 className="text-sm font-medium mb-2">Contact Information</h4>
                      <div className="grid grid-cols-2 gap-4 text-sm">
                        <div className="flex justify-between">
                          <span className="text-muted-foreground">Contact Name:</span>
                          <span>John Doe</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-muted-foreground">Phone:</span>
                          <span>(*************</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-muted-foreground">Email:</span>
                          <span><EMAIL></span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-muted-foreground">Response Time:</span>
                          <span>24 hours</span>
                        </div>
                      </div>
                    </div>
                  </TabsContent>

                  <TabsContent value="comparison" className="mt-4">
                    <div className="space-y-4">
                      <div className="overflow-x-auto">
                        <table className="w-full border-collapse">
                          <thead>
                            <tr className="border-b">
                              <th className="text-left p-2">Criteria</th>
                              {scene.potentialLocations?.map((location: any) => (
                                <th key={location.id} className="text-left p-2">
                                  {location.name}
                                  <div className="flex items-center mt-1">
                                    <div className={`h-2 w-2 rounded-full ${getStatusColor(location.status)} mr-2`} />
                                    <span className="text-xs capitalize">{location.status.replace("_", " ")}</span>
                                  </div>
                                </th>
                              ))}
                            </tr>
                          </thead>
                          <tbody>
                            <tr className="border-b">
                              <td className="p-2 font-medium">Cost</td>
                              <td className="p-2">$1,500/day</td>
                              <td className="p-2">$2,000/day</td>
                              <td className="p-2">$1,200/day</td>
                            </tr>
                            <tr className="border-b">
                              <td className="p-2 font-medium">Permits</td>
                              <td className="p-2">Required ($500)</td>
                              <td className="p-2">Not Required</td>
                              <td className="p-2">Required ($800)</td>
                            </tr>
                            <tr className="border-b">
                              <td className="p-2 font-medium">Availability</td>
                              <td className="p-2">Jun 10-30</td>
                              <td className="p-2">Jun 15-25</td>
                              <td className="p-2">Jun 5-20</td>
                            </tr>
                            <tr className="border-b">
                              <td className="p-2 font-medium">Lighting</td>
                              <td className="p-2">Excellent</td>
                              <td className="p-2">Good</td>
                              <td className="p-2">Fair</td>
                            </tr>
                            <tr className="border-b">
                              <td className="p-2 font-medium">Sound</td>
                              <td className="p-2">Moderate noise</td>
                              <td className="p-2">Quiet</td>
                              <td className="p-2">Noisy</td>
                            </tr>
                            <tr className="border-b">
                              <td className="p-2 font-medium">Accessibility</td>
                              <td className="p-2">Good</td>
                              <td className="p-2">Excellent</td>
                              <td className="p-2">Limited</td>
                            </tr>
                            <tr className="border-b">
                              <td className="p-2 font-medium">Parking</td>
                              <td className="p-2">Available</td>
                              <td className="p-2">Limited</td>
                              <td className="p-2">Available</td>
                            </tr>
                            <tr className="border-b">
                              <td className="p-2 font-medium">Team Vote</td>
                              <td className="p-2">
                                <div className="flex items-center gap-2">
                                  <div className="flex items-center">
                                    <ThumbsUp className="h-4 w-4 text-green-500 mr-1" />
                                    <span>{getLocationById(scene.potentialLocations[0].id)?.votes.up}</span>
                                  </div>
                                  <div className="flex items-center">
                                    <ThumbsDown className="h-4 w-4 text-red-500 mr-1" />
                                    <span>{getLocationById(scene.potentialLocations[0].id)?.votes.down}</span>
                                  </div>
                                </div>
                              </td>
                              <td className="p-2">
                                <div className="flex items-center gap-2">
                                  <div className="flex items-center">
                                    <ThumbsUp className="h-4 w-4 text-green-500 mr-1" />
                                    <span>{getLocationById(scene.potentialLocations[1].id)?.votes.up}</span>
                                  </div>
                                  <div className="flex items-center">
                                    <ThumbsDown className="h-4 w-4 text-red-500 mr-1" />
                                    <span>{getLocationById(scene.potentialLocations[1].id)?.votes.down}</span>
                                  </div>
                                </div>
                              </td>
                              <td className="p-2">
                                <div className="flex items-center gap-2">
                                  <div className="flex items-center">
                                    <ThumbsUp className="h-4 w-4 text-green-500 mr-1" />
                                    <span>{getLocationById(scene.potentialLocations[2]?.id)?.votes.up || 0}</span>
                                  </div>
                                  <div className="flex items-center">
                                    <ThumbsDown className="h-4 w-4 text-red-500 mr-1" />
                                    <span>{getLocationById(scene.potentialLocations[2]?.id)?.votes.down || 0}</span>
                                  </div>
                                </div>
                              </td>
                            </tr>
                          </tbody>
                        </table>
                      </div>

                      <div className="border-t pt-4">
                        <h4 className="text-sm font-medium mb-2">Recommendation</h4>
                        <div className="flex items-start gap-2 p-3 bg-muted rounded-md">
                          <div className="mt-0.5">
                            <Check className="h-5 w-5 text-green-500" />
                          </div>
                          <div>
                            <p className="font-medium">Downtown Financial District is recommended</p>
                            <p className="text-sm text-muted-foreground mt-1">
                              Based on team votes, cost considerations, and scene requirements, this location offers the best balance of features needed for the scene.
                            </p>
                          </div>
                        </div>
                      </div>
                    </div>
                  </TabsContent>

                  <TabsContent value="discussion" className="mt-4">
                    <div className="border rounded-md">
                      <div className="p-4 border-b">
                        <h3 className="font-medium">Location Discussion</h3>
                      </div>

                      <ScrollArea className="h-[300px] p-4">
                        <div className="space-y-4">
                          <div className="flex gap-3">
                            <Avatar className="h-8 w-8">
                              <AvatarImage src="/placeholder.svg?height=40&width=40" alt="David Rodriguez" />
                              <AvatarFallback>DR</AvatarFallback>
                            </Avatar>
                            <div className="flex-1">
                              <div className="flex justify-between">
                                <div className="font-medium text-sm">David Rodriguez</div>
                                <div className="text-xs text-muted-foreground">2 days ago</div>
                              </div>
                              <div className="mt-1 text-sm">
                                I think this location works perfectly for the scene. The wide streets will give us plenty of room for the chase sequence.
                              </div>
                              <div className="mt-2 flex items-center gap-2">
                                <Button variant="outline" size="sm" className="h-7 px-2 text-xs">
                                  <ThumbsUp className="h-3 w-3 mr-1" />
                                  I agree
                                </Button>
                                <Button variant="outline" size="sm" className="h-7 px-2 text-xs">
                                  <ThumbsDown className="h-3 w-3 mr-1" />
                                  I disagree
                                </Button>
                              </div>
                            </div>
                          </div>

                          <div className="flex gap-3">
                            <Avatar className="h-8 w-8">
                              <AvatarImage src="/placeholder.svg?height=40&width=40" alt="Sarah Johnson" />
                              <AvatarFallback>SJ</AvatarFallback>
                            </Avatar>
                            <div className="flex-1">
                              <div className="flex justify-between">
                                <div className="font-medium text-sm">Sarah Johnson</div>
                                <div className="text-xs text-muted-foreground">1 day ago</div>
                              </div>
                              <div className="mt-1 text-sm">
                                I've checked with the city office about permits. They said we should be able to get approval for the dates we need.
                              </div>
                              <div className="mt-2 flex items-center gap-2">
                                <Button variant="outline" size="sm" className="h-7 px-2 text-xs">
                                  <ThumbsUp className="h-3 w-3 mr-1" />
                                  I agree
                                </Button>
                                <Button variant="outline" size="sm" className="h-7 px-2 text-xs">
                                  <ThumbsDown className="h-3 w-3 mr-1" />
                                  I disagree
                                </Button>
                              </div>
                            </div>
                          </div>

                          <div className="flex gap-3">
                            <Avatar className="h-8 w-8">
                              <AvatarImage src="/placeholder.svg?height=40&width=40" alt="Mike Rodriguez" />
                              <AvatarFallback>MR</AvatarFallback>
                            </Avatar>
                            <div className="flex-1">
                              <div className="flex justify-between">
                                <div className="font-medium text-sm">Mike Rodriguez</div>
                                <div className="text-xs text-muted-foreground">12 hours ago</div>
                              </div>
                              <div className="mt-1 text-sm">
                                The lighting at this location is perfect for the morning scene. We'll get great natural light without needing too much additional equipment.
                              </div>
                              <div className="mt-2 flex items-center gap-2">
                                <Button variant="outline" size="sm" className="h-7 px-2 text-xs">
                                  <ThumbsUp className="h-3 w-3 mr-1" />
                                  I agree
                                </Button>
                                <Button variant="outline" size="sm" className="h-7 px-2 text-xs">
                                  <ThumbsDown className="h-3 w-3 mr-1" />
                                  I disagree
                                </Button>
                              </div>
                            </div>
                          </div>
                        </div>
                      </ScrollArea>

                      <div className="p-4 border-t">
                        <div className="flex gap-3">
                          <Avatar className="h-8 w-8">
                            <AvatarFallback>YO</AvatarFallback>
                          </Avatar>
                          <div className="flex-1 space-y-2">
                            <Textarea placeholder="Add a comment about this location..." className="min-h-[80px]" />
                            <div className="flex justify-between">
                              <div className="flex gap-2">
                                <Button variant="outline" size="sm">
                                  <ThumbsUp className="mr-2 h-4 w-4" />
                                  Vote Up
                                </Button>
                                <Button variant="outline" size="sm">
                                  <ThumbsDown className="mr-2 h-4 w-4" />
                                  Vote Down
                                </Button>
                              </div>
                              <Button size="sm">Add Comment</Button>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </TabsContent>

                  <TabsContent value="media" className="mt-4">
                    <div className="grid grid-cols-2 gap-4">
                      <div className="aspect-video relative rounded-md overflow-hidden border">
                        <img
                          src="/placeholder.svg?height=200&width=300&text=Location+Photo+1"
                          alt="Location 1"
                          className="object-cover w-full h-full"
                        />
                      </div>
                      <div className="aspect-video relative rounded-md overflow-hidden border">
                        <img
                          src="/placeholder.svg?height=200&width=300&text=Location+Photo+2"
                          alt="Location 2"
                          className="object-cover w-full h-full"
                        />
                      </div>
                      <div className="aspect-video relative rounded-md overflow-hidden border">
                        <img
                          src="/placeholder.svg?height=200&width=300&text=Location+Photo+3"
                          alt="Location 3"
                          className="object-cover w-full h-full"
                        />
                      </div>
                      <div className="aspect-video relative rounded-md overflow-hidden border">
                        <img
                          src="/placeholder.svg?height=200&width=300&text=Location+Photo+4"
                          alt="Location 4"
                          className="object-cover w-full h-full"
                        />
                      </div>
                    </div>

                    <div className="mt-4 border-t pt-4">
                      <div className="flex justify-between items-center mb-4">
                        <h4 className="font-medium">360° Views</h4>
                        <Button variant="outline" size="sm">View in VR</Button>
                      </div>
                      <div className="aspect-video relative rounded-md overflow-hidden border">
                        <img
                          src="/placeholder.svg?height=400&width=800&text=360+Panorama"
                          alt="360 View"
                          className="object-cover w-full h-full"
                        />
                        <div className="absolute inset-0 flex items-center justify-center">
                          <Button variant="secondary" size="sm">
                            View 360° Tour
                          </Button>
                        </div>
                      </div>
                    </div>
                  </TabsContent>
                </Tabs>
              </CardContent>
            </Card>
          ) : (
            <div className="border rounded-md p-8 text-center h-full flex items-center justify-center">
              <div>
                <MapPin className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
                <h3 className="font-medium text-lg mb-1">No Location Selected</h3>
                <p className="text-muted-foreground mb-4">
                  Select a location from the list to view details and evaluate it for this scene.
                </p>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}
