"use client"

import { useState } from "react"
import { <PERSON>, <PERSON><PERSON><PERSON>nt, Card<PERSON><PERSON>er, Card<PERSON>itle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Ta<PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs"
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import {
  Plus,
  Search,
  MapPin,
  Calendar,
  Film,
  Sun,
  Moon,
  Cloud,
  Filter,
  Check,
  MessageSquare,
  ImageIcon,
  X,
  Edit,
  Share2,
  Eye,
  Download,
} from "lucide-react"

interface ProjectScenesProps {
  projectId: string
}

export function ProjectScenes({ projectId }: ProjectScenesProps) {
  const [activeScene, setActiveScene] = useState<string | null>("scene1")
  const [searchQuery, setSearchQuery] = useState("")
  const [filterStatus, setFilterStatus] = useState<string | null>(null)

  // Sample scenes data
  const scenes = [
    {
      id: "scene1",
      name: "Opening Chase",
      description: "Character runs through busy downtown streets, dodging pedestrians",
      timeOfDay: "Morning",
      interiorExterior: "Exterior",
      status: "Scheduled",
      scheduledDate: "Jun 15, 2025",
      duration: "4 hours",
      location: "Downtown Financial District",
      locationStatus: "Approved",
      notes: [
        {
          id: "note1",
          user: {
            name: "David Rodriguez",
            avatar: "/placeholder.svg?height=40&width=40",
            initials: "DR",
          },
          timestamp: "2 days ago",
          content: "We need to secure permits for closing the street during filming.",
        },
        {
          id: "note2",
          user: {
            name: "Sarah Johnson",
            avatar: "/placeholder.svg?height=40&width=40",
            initials: "SJ",
          },
          timestamp: "1 day ago",
          content: "I've contacted the city office about permits. Should hear back by Friday.",
        },
      ],
      images: [
        "/placeholder.svg?height=200&width=300&text=Location+1",
        "/placeholder.svg?height=200&width=300&text=Location+2",
      ],
    },
    {
      id: "scene2",
      name: "Apartment Confrontation",
      description: "Main character confronts antagonist in a small apartment",
      timeOfDay: "Evening",
      interiorExterior: "Interior",
      status: "Pending",
      scheduledDate: "Jun 18, 2025",
      duration: "6 hours",
      location: "Downtown Loft",
      locationStatus: "Approved",
      notes: [
        {
          id: "note3",
          user: {
            name: "Jane Smith",
            avatar: "/placeholder.svg?height=40&width=40",
            initials: "JS",
          },
          timestamp: "3 days ago",
          content: "We need to make sure the lighting setup works for the evening scene.",
        },
      ],
      images: [
        "/placeholder.svg?height=200&width=300&text=Apartment+1",
        "/placeholder.svg?height=200&width=300&text=Apartment+2",
      ],
    },
    {
      id: "scene3",
      name: "Rooftop Meeting",
      description: "Secret meeting between informant and detective",
      timeOfDay: "Night",
      interiorExterior: "Exterior",
      status: "Not Scheduled",
      scheduledDate: null,
      duration: null,
      location: "City Rooftop",
      locationStatus: "Pending",
      notes: [],
      images: ["/placeholder.svg?height=200&width=300&text=Rooftop"],
    },
    {
      id: "scene4",
      name: "Police Station Briefing",
      description: "Team discusses the case and reviews evidence",
      timeOfDay: "Day",
      interiorExterior: "Interior",
      status: "Scheduled",
      scheduledDate: "Jun 20, 2025",
      duration: "3 hours",
      location: "Police Station Set",
      locationStatus: "Approved",
      notes: [],
      images: ["/placeholder.svg?height=200&width=300&text=Police+Station"],
    },
  ]

  // Filter scenes based on search query and status filter
  const filteredScenes = scenes.filter((scene) => {
    const matchesSearch =
      searchQuery === "" ||
      scene.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      scene.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
      scene.location.toLowerCase().includes(searchQuery.toLowerCase())

    const matchesFilter = filterStatus === null || scene.status === filterStatus

    return matchesSearch && matchesFilter
  })

  const activeSceneData = activeScene ? scenes.find((scene) => scene.id === activeScene) : null

  const getStatusColor = (status: string) => {
    switch (status) {
      case "Scheduled":
        return "default"
      case "Pending":
        return "secondary"
      case "Not Scheduled":
        return "outline"
      default:
        return "outline"
    }
  }

  const getTimeOfDayIcon = (timeOfDay: string) => {
    switch (timeOfDay) {
      case "Morning":
        return <Sun className="h-4 w-4" />
      case "Day":
        return <Sun className="h-4 w-4" />
      case "Evening":
        return <Cloud className="h-4 w-4" />
      case "Night":
        return <Moon className="h-4 w-4" />
      default:
        return <Sun className="h-4 w-4" />
    }
  }

  return (
    <div className="space-y-6">
      <div className="flex flex-col sm:flex-row justify-between gap-4">
        <div className="relative flex-1">
          <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search scenes..."
            className="pl-8"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
        </div>
        <div className="flex gap-2">
          <Dialog>
            <DialogTrigger asChild>
              <Button>
                <Plus className="mr-2 h-4 w-4" />
                Add Scene
              </Button>
            </DialogTrigger>
            <DialogContent className="sm:max-w-[600px]">
              <DialogHeader>
                <DialogTitle>Add New Scene</DialogTitle>
              </DialogHeader>
              <div className="grid gap-4 py-4">
                <div className="space-y-2">
                  <label htmlFor="scene-name" className="text-sm font-medium">
                    Scene Name
                  </label>
                  <Input id="scene-name" placeholder="Enter scene name" />
                </div>
                <div className="space-y-2">
                  <label htmlFor="scene-description" className="text-sm font-medium">
                    Description
                  </label>
                  <Textarea id="scene-description" placeholder="Describe the scene" />
                </div>
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <label htmlFor="time-of-day" className="text-sm font-medium">
                      Time of Day
                    </label>
                    <select
                      id="time-of-day"
                      className="w-full rounded-md border border-input bg-background px-3 py-2 text-sm"
                    >
                      <option value="Morning">Morning</option>
                      <option value="Day">Day</option>
                      <option value="Evening">Evening</option>
                      <option value="Night">Night</option>
                    </select>
                  </div>
                  <div className="space-y-2">
                    <label htmlFor="interior-exterior" className="text-sm font-medium">
                      Interior/Exterior
                    </label>
                    <select
                      id="interior-exterior"
                      className="w-full rounded-md border border-input bg-background px-3 py-2 text-sm"
                    >
                      <option value="Interior">Interior</option>
                      <option value="Exterior">Exterior</option>
                      <option value="Both">Both</option>
                    </select>
                  </div>
                </div>
                <div className="space-y-2">
                  <label htmlFor="location" className="text-sm font-medium">
                    Location
                  </label>
                  <select
                    id="location"
                    className="w-full rounded-md border border-input bg-background px-3 py-2 text-sm"
                  >
                    <option value="">Select a location</option>
                    <option value="Downtown Financial District">Downtown Financial District</option>
                    <option value="Downtown Loft">Downtown Loft</option>
                    <option value="City Rooftop">City Rooftop</option>
                    <option value="Police Station Set">Police Station Set</option>
                  </select>
                </div>
              </div>
              <div className="flex justify-end">
                <Button>Add Scene</Button>
              </div>
            </DialogContent>
          </Dialog>

          <Dialog>
            <DialogTrigger asChild>
              <Button variant="outline">
                <Filter className="mr-2 h-4 w-4" />
                Filter
              </Button>
            </DialogTrigger>
            <DialogContent className="sm:max-w-[400px]">
              <DialogHeader>
                <DialogTitle>Filter Scenes</DialogTitle>
              </DialogHeader>
              <div className="space-y-4 py-4">
                <div className="space-y-2">
                  <label className="text-sm font-medium">Status</label>
                  <div className="flex flex-wrap gap-2">
                    <Badge
                      variant={filterStatus === null ? "default" : "outline"}
                      className="cursor-pointer"
                      onClick={() => setFilterStatus(null)}
                    >
                      All
                    </Badge>
                    <Badge
                      variant={filterStatus === "Scheduled" ? "default" : "outline"}
                      className="cursor-pointer"
                      onClick={() => setFilterStatus("Scheduled")}
                    >
                      Scheduled
                    </Badge>
                    <Badge
                      variant={filterStatus === "Pending" ? "default" : "outline"}
                      className="cursor-pointer"
                      onClick={() => setFilterStatus("Pending")}
                    >
                      Pending
                    </Badge>
                    <Badge
                      variant={filterStatus === "Not Scheduled" ? "default" : "outline"}
                      className="cursor-pointer"
                      onClick={() => setFilterStatus("Not Scheduled")}
                    >
                      Not Scheduled
                    </Badge>
                  </div>
                </div>
                <div className="space-y-2">
                  <label className="text-sm font-medium">Time of Day</label>
                  <div className="flex flex-wrap gap-2">
                    <Badge variant="outline" className="cursor-pointer">
                      Morning
                    </Badge>
                    <Badge variant="outline" className="cursor-pointer">
                      Day
                    </Badge>
                    <Badge variant="outline" className="cursor-pointer">
                      Evening
                    </Badge>
                    <Badge variant="outline" className="cursor-pointer">
                      Night
                    </Badge>
                  </div>
                </div>
                <div className="space-y-2">
                  <label className="text-sm font-medium">Interior/Exterior</label>
                  <div className="flex flex-wrap gap-2">
                    <Badge variant="outline" className="cursor-pointer">
                      Interior
                    </Badge>
                    <Badge variant="outline" className="cursor-pointer">
                      Exterior
                    </Badge>
                  </div>
                </div>
              </div>
              <div className="flex justify-between">
                <Button variant="outline" onClick={() => setFilterStatus(null)}>
                  Reset Filters
                </Button>
                <Button>Apply Filters</Button>
              </div>
            </DialogContent>
          </Dialog>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="md:col-span-1">
          <Card className="h-full">
            <CardHeader className="pb-2">
              <CardTitle className="text-base">Scene List</CardTitle>
            </CardHeader>
            <CardContent className="p-0">
              <ScrollArea className="h-[600px]">
                <div className="px-4 py-2 space-y-2">
                  {filteredScenes.length > 0 ? (
                    filteredScenes.map((scene) => (
                      <div
                        key={scene.id}
                        className={`p-3 rounded-md border cursor-pointer transition-colors ${
                          activeScene === scene.id ? "bg-muted border-primary" : ""
                        }`}
                        onClick={() => setActiveScene(scene.id)}
                      >
                        <div className="flex justify-between items-start">
                          <div>
                            <div className="font-medium">{scene.name}</div>
                            <div className="text-xs text-muted-foreground mt-1 line-clamp-2">{scene.description}</div>
                          </div>
                          <Badge variant={getStatusColor(scene.status)}>{scene.status}</Badge>
                        </div>
                        <div className="flex gap-2 mt-2">
                          <div className="flex items-center text-xs text-muted-foreground">
                            {getTimeOfDayIcon(scene.timeOfDay)}
                            <span className="ml-1">{scene.timeOfDay}</span>
                          </div>
                          <div className="flex items-center text-xs text-muted-foreground">
                            <MapPin className="h-4 w-4" />
                            <span className="ml-1">{scene.location}</span>
                          </div>
                        </div>
                      </div>
                    ))
                  ) : (
                    <div className="text-center py-8 text-muted-foreground">No scenes match your search criteria</div>
                  )}
                </div>
              </ScrollArea>
            </CardContent>
          </Card>
        </div>

        <div className="md:col-span-2">
          {activeSceneData ? (
            <Card>
              <CardHeader className="pb-2 flex flex-row items-start justify-between">
                <div>
                  <CardTitle>{activeSceneData.name}</CardTitle>
                  <p className="text-sm text-muted-foreground mt-1">{activeSceneData.description}</p>
                </div>
                <div className="flex gap-2">
                  <Button variant="outline" size="sm">
                    <Edit className="h-4 w-4 mr-1" />
                    Edit
                  </Button>
                  <Button variant="outline" size="sm">
                    <Share2 className="h-4 w-4 mr-1" />
                    Share
                  </Button>
                </div>
              </CardHeader>
              <CardContent>
                <Tabs defaultValue="details">
                  <TabsList className="w-full">
                    <TabsTrigger value="details" className="flex-1">
                      Details
                    </TabsTrigger>
                    <TabsTrigger value="location" className="flex-1">
                      Location
                    </TabsTrigger>
                    <TabsTrigger value="notes" className="flex-1">
                      Notes
                    </TabsTrigger>
                    <TabsTrigger value="media" className="flex-1">
                      Media
                    </TabsTrigger>
                  </TabsList>

                  <TabsContent value="details" className="space-y-4 mt-4">
                    <div className="grid grid-cols-2 gap-4">
                      <div className="space-y-1">
                        <p className="text-sm text-muted-foreground">Status</p>
                        <Badge variant={getStatusColor(activeSceneData.status)}>{activeSceneData.status}</Badge>
                      </div>
                      <div className="space-y-1">
                        <p className="text-sm text-muted-foreground">Time of Day</p>
                        <div className="flex items-center">
                          {getTimeOfDayIcon(activeSceneData.timeOfDay)}
                          <span className="ml-1">{activeSceneData.timeOfDay}</span>
                        </div>
                      </div>
                      <div className="space-y-1">
                        <p className="text-sm text-muted-foreground">Interior/Exterior</p>
                        <p>{activeSceneData.interiorExterior}</p>
                      </div>
                      <div className="space-y-1">
                        <p className="text-sm text-muted-foreground">Duration</p>
                        <p>{activeSceneData.duration || "Not set"}</p>
                      </div>
                      <div className="space-y-1">
                        <p className="text-sm text-muted-foreground">Scheduled Date</p>
                        <div className="flex items-center">
                          <Calendar className="h-4 w-4 mr-1 text-muted-foreground" />
                          <p>{activeSceneData.scheduledDate || "Not scheduled"}</p>
                        </div>
                      </div>
                      <div className="space-y-1">
                        <p className="text-sm text-muted-foreground">Location</p>
                        <div className="flex items-center">
                          <MapPin className="h-4 w-4 mr-1 text-muted-foreground" />
                          <p>{activeSceneData.location}</p>
                        </div>
                      </div>
                    </div>

                    <div className="border-t pt-4 mt-4">
                      <h4 className="text-sm font-medium mb-2">Scene Requirements</h4>
                      <div className="grid grid-cols-2 gap-4">
                        <div className="border rounded-md p-3">
                          <h5 className="text-sm font-medium mb-1">Equipment</h5>
                          <ul className="text-sm space-y-1">
                            <li className="flex items-center">
                              <Check className="h-3 w-3 text-green-500 mr-2" />
                              Camera Package
                            </li>
                            <li className="flex items-center">
                              <Check className="h-3 w-3 text-green-500 mr-2" />
                              Lighting Kit
                            </li>
                            <li className="flex items-center">
                              <Check className="h-3 w-3 text-green-500 mr-2" />
                              Sound Equipment
                            </li>
                          </ul>
                        </div>
                        <div className="border rounded-md p-3">
                          <h5 className="text-sm font-medium mb-1">Cast & Crew</h5>
                          <ul className="text-sm space-y-1">
                            <li className="flex items-center">
                              <Check className="h-3 w-3 text-green-500 mr-2" />
                              Main Cast (3)
                            </li>
                            <li className="flex items-center">
                              <Check className="h-3 w-3 text-green-500 mr-2" />
                              Extras (10)
                            </li>
                            <li className="flex items-center">
                              <Check className="h-3 w-3 text-green-500 mr-2" />
                              Core Crew
                            </li>
                          </ul>
                        </div>
                      </div>
                    </div>

                    <div className="flex justify-end gap-2 mt-4">
                      <Button variant="outline">
                        <Calendar className="mr-2 h-4 w-4" />
                        Schedule Scene
                      </Button>
                      <Button>
                        <Check className="mr-2 h-4 w-4" />
                        Mark as Ready
                      </Button>
                    </div>
                  </TabsContent>

                  <TabsContent value="location" className="mt-4">
                    <div className="space-y-4">
                      <div className="flex justify-between items-start">
                        <div>
                          <h3 className="font-medium">{activeSceneData.location}</h3>
                          <p className="text-sm text-muted-foreground">123 Main St, New York, NY</p>
                        </div>
                        <Badge variant={activeSceneData.locationStatus === "Approved" ? "default" : "outline"}>
                          {activeSceneData.locationStatus}
                        </Badge>
                      </div>

                      <div className="aspect-video relative rounded-md overflow-hidden border">
                        <img
                          src="/placeholder.svg?height=400&width=800&text=Location+Map"
                          alt="Location Map"
                          className="object-cover w-full h-full"
                        />
                      </div>

                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div className="border rounded-md p-3">
                          <h4 className="text-sm font-medium mb-2">Location Details</h4>
                          <div className="space-y-2 text-sm">
                            <div className="flex justify-between">
                              <span className="text-muted-foreground">Type:</span>
                              <span>{activeSceneData.interiorExterior}</span>
                            </div>
                            <div className="flex justify-between">
                              <span className="text-muted-foreground">Size:</span>
                              <span>2,500 sq ft</span>
                            </div>
                            <div className="flex justify-between">
                              <span className="text-muted-foreground">Permit Required:</span>
                              <span>Yes</span>
                            </div>
                            <div className="flex justify-between">
                              <span className="text-muted-foreground">Permit Status:</span>
                              <span>Pending</span>
                            </div>
                          </div>
                        </div>

                        <div className="border rounded-md p-3">
                          <h4 className="text-sm font-medium mb-2">Contact Information</h4>
                          <div className="space-y-2 text-sm">
                            <div className="flex justify-between">
                              <span className="text-muted-foreground">Contact Name:</span>
                              <span>John Doe</span>
                            </div>
                            <div className="flex justify-between">
                              <span className="text-muted-foreground">Phone:</span>
                              <span>(*************</span>
                            </div>
                            <div className="flex justify-between">
                              <span className="text-muted-foreground">Email:</span>
                              <span><EMAIL></span>
                            </div>
                          </div>
                        </div>
                      </div>

                      <div className="flex justify-end gap-2">
                        <Button variant="outline">
                          <MapPin className="mr-2 h-4 w-4" />
                          View Full Details
                        </Button>
                      </div>
                    </div>
                  </TabsContent>

                  <TabsContent value="notes" className="mt-4">
                    <div className="border rounded-md">
                      <div className="p-4 border-b">
                        <h3 className="font-medium">Discussion</h3>
                      </div>

                      <ScrollArea className="h-[300px] p-4">
                        {activeSceneData.notes.length > 0 ? (
                          <div className="space-y-4">
                            {activeSceneData.notes.map((note) => (
                              <div key={note.id} className="flex gap-3">
                                <Avatar className="h-8 w-8">
                                  <AvatarImage src={note.user.avatar} alt={note.user.name} />
                                  <AvatarFallback>{note.user.initials}</AvatarFallback>
                                </Avatar>
                                <div className="flex-1">
                                  <div className="flex justify-between">
                                    <div className="font-medium text-sm">{note.user.name}</div>
                                    <div className="text-xs text-muted-foreground">{note.timestamp}</div>
                                  </div>
                                  <div className="mt-1 text-sm">{note.content}</div>
                                </div>
                              </div>
                            ))}
                          </div>
                        ) : (
                          <div className="text-center py-8">
                            <MessageSquare className="h-8 w-8 mx-auto text-muted-foreground mb-2" />
                            <h3 className="font-medium mb-1">No Comments Yet</h3>
                            <p className="text-sm text-muted-foreground">
                              Be the first to add a comment to this scene.
                            </p>
                          </div>
                        )}
                      </ScrollArea>

                      <div className="p-4 border-t">
                        <div className="flex gap-3">
                          <Avatar className="h-8 w-8">
                            <AvatarFallback>YO</AvatarFallback>
                          </Avatar>
                          <div className="flex-1 space-y-2">
                            <Textarea placeholder="Add a comment..." className="min-h-[80px]" />
                            <div className="flex justify-end">
                              <Button size="sm">Add Comment</Button>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </TabsContent>

                  <TabsContent value="media" className="mt-4">
                    <div className="space-y-4">
                      <div className="flex justify-between items-center">
                        <h3 className="font-medium">Scene Media</h3>
                        <Button variant="outline" size="sm">
                          <ImageIcon className="mr-2 h-4 w-4" />
                          Add Media
                        </Button>
                      </div>

                      <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
                        {activeSceneData.images.map((image, index) => (
                          <div key={index} className="group relative aspect-[4/3] rounded-md overflow-hidden border">
                            <img
                              src={image || "/placeholder.svg"}
                              alt={`Scene ${index + 1}`}
                              className="object-cover w-full h-full"
                            />
                            <div className="absolute inset-0 bg-black/60 opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center gap-2">
                              <Button variant="secondary" size="sm" className="h-8 w-8 p-0">
                                <Eye className="h-4 w-4" />
                              </Button>
                              <Button variant="secondary" size="sm" className="h-8 w-8 p-0">
                                <Download className="h-4 w-4" />
                              </Button>
                              <Button variant="secondary" size="sm" className="h-8 w-8 p-0">
                                <X className="h-4 w-4" />
                              </Button>
                            </div>
                          </div>
                        ))}
                      </div>

                      <div className="border-t pt-4">
                        <h4 className="font-medium mb-2">Storyboards</h4>
                        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                          <div className="aspect-square relative rounded-md overflow-hidden border bg-muted flex items-center justify-center">
                            <Plus className="h-8 w-8 text-muted-foreground" />
                          </div>
                        </div>
                      </div>
                    </div>
                  </TabsContent>
                </Tabs>
              </CardContent>
            </Card>
          ) : (
            <div className="border rounded-md p-8 text-center h-full flex items-center justify-center">
              <div>
                <Film className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
                <h3 className="font-medium text-lg mb-1">No Scene Selected</h3>
                <p className="text-muted-foreground mb-4">
                  Select a scene from the list or add a new scene to get started.
                </p>
                <Button>
                  <Plus className="mr-2 h-4 w-4" />
                  Add New Scene
                </Button>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}

