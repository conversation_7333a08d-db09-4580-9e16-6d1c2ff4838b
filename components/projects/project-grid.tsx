"use client";

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { <PERSON>ge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import Link from "next/link";
import { Pencil, Archive, Image as ImageIcon } from "lucide-react";
import React from "react";
import type { Project as ProjectModel } from "@/modules/project/model"; // Use type-only import
import { usePermissions, hasRequiredRole } from "@/hooks/usePermissions";

// Removed unused local Project type alias

interface ProjectGridProps {
  projects: ProjectModel[]; // Use ProjectModel[]
  organizationSlug: string;
  handleArchiveClick: (project: ProjectModel) => void; // Use ProjectModel
}

export function ProjectGrid({ projects, organizationSlug, handleArchiveClick }: ProjectGridProps) {
  const userRoles = usePermissions();
  const canEditProject = hasRequiredRole(userRoles, ['admin', 'manager']);
  const canArchiveProject = hasRequired<PERSON><PERSON>(user<PERSON><PERSON><PERSON>, ['admin']);

  return (
    <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3"> {/* Increased gap */}
      {projects.map((project) => (
        <Card 
          key={project.id} 
          className="h-full flex flex-col transition-all hover:shadow-[0_0_10px_rgba(35,35,255,0.7)]" // Added flex, glow effect
        >
          <CardHeader className="flex flex-row items-start gap-4 space-y-0 pb-4"> {/* Adjusted layout */}
            {/* Placeholder Image */}
            <div className="flex h-16 w-16 items-center justify-center rounded-lg bg-muted flex-shrink-0">
              <ImageIcon className="h-8 w-8 text-muted-foreground" /> 
              {/* TODO: Replace with actual project image/thumbnail */}
            </div>
            <div className="flex-1 space-y-1">
              {/* Clickable Project Name */}
              <Link href={`/organizations/${organizationSlug}/projects/${project.id}`} className="hover:underline">
                {/* TODO: Ensure link works with dynamic data */}
                <CardTitle className="line-clamp-2">{project.name}</CardTitle>
              </Link>
              <p className="line-clamp-2 text-sm text-muted-foreground pt-1">
                {project.description}
              </p>
            </div>
            <Badge variant={getStatusVariant(project.status)} className="capitalize flex-shrink-0"> {/* Capitalize status */}
              {project.status}
            </Badge>
          </CardHeader>
          <CardContent className="flex-grow">
            {/* Content can be added here if needed */}
          </CardContent>
          <CardFooter className="flex justify-between items-center text-sm text-muted-foreground pt-4">
            <div className="flex flex-col">
              {project.startDate && (
                <span className="text-xs">
                  {new Date(project.startDate).toLocaleDateString()}
                  {project.endDate &&
                    ` - ${new Date(project.endDate).toLocaleDateString()}`}
                </span>
              )}
               {/* <span className="text-xs">{project.locationCount} locations</span> */} {/* Removed location count display */}
            </div>
            {/* Action Buttons */}
            <div className="flex items-center space-x-1">
              {canEditProject && (
                <Link href={`/organizations/${organizationSlug}/projects/${project.id}/edit`} onClick={(e) => e.stopPropagation()}>
                  {/* TODO: Add edit page */}
                  <Button
                    variant="ghost"
                    size="icon"
                    className="h-8 w-8 text-muted-foreground transition-all hover:bg-transparent group" // Added group for group-hover
                  >
                    <Pencil className="h-4 w-4 transition-all group-hover:[filter:drop-shadow(0_0_6px_rgba(22,255,0,0.8))]" /> {/* Increased green drop-shadow */}
                    <span className="sr-only">Edit Project</span>
                  </Button>
                </Link>
              )}
              {canArchiveProject && (
                <Button
                  variant="ghost"
                  size="icon"
                  className="h-8 w-8 text-muted-foreground transition-all hover:bg-transparent group" // Added group for group-hover
                  onClick={(e) => {
                    e.preventDefault(); // Prevent link navigation
                    e.stopPropagation();
                    handleArchiveClick(project);
                  }}
                >
                  <Archive className="h-4 w-4 transition-all group-hover:[filter:drop-shadow(0_0_6px_rgba(246,25,129,0.8))]" /> {/* Increased pink drop-shadow */}
                  <span className="sr-only">Archive Project</span>
                </Button>
              )}
            </div>
          </CardFooter>
        </Card>
      ))}
    </div>
  );
}

function getStatusVariant(status: string): "default" | "secondary" | "destructive" | "outline" {
  switch (status.toLowerCase()) {
    case "active":
      return "default";
    case "planning":
      return "secondary";
    case "completed":
      return "outline";
    case "cancelled":
      return "destructive";
    default:
      return "default";
  }
}
