"use client";

import { Calendar } from "@/components/ui/calendar";
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card";
import { useState } from "react";
import type { Project as ProjectModel } from "@/modules/project/model"; // Import ProjectModel

// Removed local Project interface

interface CalendarEvent {
  id: string;
  name: string;
  date: Date;
  projectId: string;
  type: "start" | "end" | "milestone";
}

interface ProjectCalendarProps {
  projects: ProjectModel[]; // Use ProjectModel[]
  events?: CalendarEvent[];
}

export function ProjectCalendar({ projects, events = [] }: ProjectCalendarProps) {
  const [date, setDate] = useState<Date | undefined>(new Date());
  
  // Generate events from project start and end dates
  const projectEvents = projects.flatMap((project) => {
    const projectEvents: CalendarEvent[] = [];
    
    // Use Date object directly if it exists
    // Ensure startDate is converted to a Date object
    if (project.startDate) {
      projectEvents.push({
        id: `${project.id}-start`,
        name: `${project.name} Start`,
        date: new Date(project.startDate), // Explicitly create Date object
        projectId: project.id,
        type: "start",
      });
    }
    
    // Ensure endDate is converted to a Date object
    if (project.endDate) {
      projectEvents.push({
        id: `${project.id}-end`,
        name: `${project.name} End`,
        date: new Date(project.endDate), // Explicitly create Date object
        projectId: project.id,
        type: "end",
      });
    }
    
    return projectEvents;
  });
  
  const allEvents = [...projectEvents, ...events];
  
  // Function to get events for a specific day
  const getDayEvents = (day: Date) => {
    return allEvents.filter(
      (event) =>
        event.date.getDate() === day.getDate() &&
        event.date.getMonth() === day.getMonth() &&
        event.date.getFullYear() === day.getFullYear()
    );
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>Project Calendar</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="grid gap-4 md:grid-cols-2">
          <Calendar
            mode="single"
            selected={date}
            onSelect={setDate}
            className="rounded-md border"
            modifiers={{
              event: (date: Date) => getDayEvents(date).length > 0,
            }}
            modifiersClassNames={{
              event: "bg-primary/10 font-bold",
            }}
          />
          <div className="space-y-4">
            <h3 className="font-medium">
              Events for {date?.toLocaleDateString()}
            </h3>
            <div className="space-y-2">
              {date &&
                getDayEvents(date).map((event) => (
                  <div
                    key={event.id}
                    className="rounded-md border p-2 text-sm"
                  >
                    <div className="font-medium">{event.name}</div>
                    <div className="text-xs text-muted-foreground">
                      {event.type.charAt(0).toUpperCase() + event.type.slice(1)}
                    </div>
                  </div>
                ))}
              {date && getDayEvents(date).length === 0 && (
                <div className="text-sm text-muted-foreground">
                  No events for this day
                </div>
              )}
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
