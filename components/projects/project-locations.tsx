"use client";

import { Badge } from "@/components/ui/badge";
import { <PERSON>, <PERSON>Content, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import Link from "next/link";

interface Location {
  id: string;
  name: string;
  description: string;
  status: string;
  address: {
    street?: string;
    city?: string;
    state?: string;
    country?: string;
  };
  type: string;
  imageUrl?: string;
}

interface ProjectLocationsProps {
  projectId: string;
  locations: Location[];
}

export function ProjectLocations({ projectId, locations }: ProjectLocationsProps) {
  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between">
        <CardTitle>Project Locations</CardTitle>
        <Link
          href={`/projects/${projectId}/locations`}
          className="text-sm text-muted-foreground hover:underline"
        >
          View all
        </Link>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {locations.length === 0 ? (
            <div className="text-center text-sm text-muted-foreground">
              No locations added to this project yet.
            </div>
          ) : (
            locations.map((location) => (
              <Link
                key={location.id}
                href={`/locations/${location.id}`}
                className="block"
              >
                <div className="flex items-center gap-4 rounded-md border p-3 transition-colors hover:bg-muted/50">
                  {location.imageUrl ? (
                    <div
                      className="h-12 w-12 rounded-md bg-cover bg-center bg-no-repeat"
                      style={{ backgroundImage: `url(${location.imageUrl})` }}
                    />
                  ) : (
                    <div className="flex h-12 w-12 items-center justify-center rounded-md bg-muted">
                      <span className="text-xs font-medium">No img</span>
                    </div>
                  )}
                  <div className="flex-1 space-y-1">
                    <div className="flex items-center justify-between">
                      <p className="font-medium">{location.name}</p>
                      <Badge variant={getStatusVariant(location.status)}>
                        {location.status}
                      </Badge>
                    </div>
                    <p className="text-xs text-muted-foreground">
                      {formatAddress(location.address)}
                    </p>
                    <p className="text-xs text-muted-foreground">
                      Type: {location.type}
                    </p>
                  </div>
                </div>
              </Link>
            ))
          )}
        </div>
      </CardContent>
    </Card>
  );
}

function formatAddress(address: {
  street?: string;
  city?: string;
  state?: string;
  country?: string;
}): string {
  const parts = [];
  if (address.street) parts.push(address.street);
  if (address.city) parts.push(address.city);
  if (address.state) parts.push(address.state);
  if (address.country) parts.push(address.country);
  
  return parts.join(", ") || "No address provided";
}

function getStatusVariant(status: string): "default" | "secondary" | "destructive" | "outline" {
  switch (status.toLowerCase()) {
    case "approved":
      return "default";
    case "pending":
      return "secondary";
    case "rejected":
      return "destructive";
    default:
      return "outline";
  }
}
