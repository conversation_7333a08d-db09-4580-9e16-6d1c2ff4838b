"use client";

import { Badge } from "@/components/ui/badge";
import { <PERSON>, <PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Checkbox } from "@/components/ui/checkbox";
import { <PERSON><PERSON> } from "@/components/ui/button";
import Link from "next/link";

interface Task {
  id: string;
  title: string;
  description?: string;
  status: "todo" | "in-progress" | "completed" | "blocked";
  priority: "low" | "medium" | "high" | "urgent";
  dueDate?: string;
  assignedTo?: {
    id: string;
    name: string;
    avatar?: string;
  };
}

interface ProjectTasksProps {
  projectId: string;
  tasks: Task[];
  onTaskStatusChange?: (taskId: string, completed: boolean) => void;
}

export function ProjectTasks({
  projectId,
  tasks,
  onTaskStatusChange,
}: ProjectTasksProps) {
  const sortedTasks = [...tasks].sort((a, b) => {
    // Sort by status (todo first, then in-progress, then blocked, then completed)
    const statusOrder = {
      todo: 0,
      "in-progress": 1,
      blocked: 2,
      completed: 3,
    };
    
    const statusDiff = statusOrder[a.status] - statusOrder[b.status];
    if (statusDiff !== 0) return statusDiff;
    
    // Then sort by priority (urgent first, then high, then medium, then low)
    const priorityOrder = {
      urgent: 0,
      high: 1,
      medium: 2,
      low: 3,
    };
    
    return priorityOrder[a.priority] - priorityOrder[b.priority];
  });

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between">
        <CardTitle>Project Tasks</CardTitle>
        <Link
          href={`/projects/${projectId}/tasks`}
          className="text-sm text-muted-foreground hover:underline"
        >
          View all
        </Link>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {sortedTasks.length === 0 ? (
            <div className="text-center text-sm text-muted-foreground">
              No tasks created for this project yet.
            </div>
          ) : (
            sortedTasks.map((task) => (
              <div
                key={task.id}
                className="flex items-start justify-between rounded-md border p-3"
              >
                <div className="flex items-start gap-3">
                  <Checkbox
                    id={`task-${task.id}`}
                    checked={task.status === "completed"}
                    onCheckedChange={(checked: boolean | "indeterminate") => {
                      onTaskStatusChange?.(task.id, checked === true);
                    }}
                    disabled={task.status === "blocked"}
                  />
                  <div>
                    <label
                      htmlFor={`task-${task.id}`}
                      className={`font-medium ${
                        task.status === "completed" ? "line-through text-muted-foreground" : ""
                      }`}
                    >
                      {task.title}
                    </label>
                    {task.description && (
                      <p className="text-xs text-muted-foreground">
                        {task.description}
                      </p>
                    )}
                    <div className="mt-1 flex flex-wrap items-center gap-2 text-xs">
                      <Badge variant={getStatusVariant(task.status)}>
                        {formatStatus(task.status)}
                      </Badge>
                      <Badge variant={getPriorityVariant(task.priority)}>
                        {formatPriority(task.priority)}
                      </Badge>
                      {task.dueDate && (
                        <span className="text-muted-foreground">
                          Due: {formatDate(task.dueDate)}
                        </span>
                      )}
                      {task.assignedTo && (
                        <span className="text-muted-foreground">
                          Assigned to: {task.assignedTo.name}
                        </span>
                      )}
                    </div>
                  </div>
                </div>
                <Button variant="ghost" size="sm" asChild>
                  <Link href={`/projects/${projectId}/tasks/${task.id}`}>
                    Details
                  </Link>
                </Button>
              </div>
            ))
          )}
        </div>
      </CardContent>
    </Card>
  );
}

function formatStatus(status: string): string {
  return status
    .split("-")
    .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
    .join(" ");
}

function formatPriority(priority: string): string {
  return priority.charAt(0).toUpperCase() + priority.slice(1);
}

function getStatusVariant(
  status: string
): "default" | "secondary" | "outline" | "destructive" {
  switch (status) {
    case "completed":
      return "default";
    case "in-progress":
      return "secondary";
    case "blocked":
      return "destructive";
    default:
      return "outline";
  }
}

function getPriorityVariant(
  priority: string
): "default" | "secondary" | "outline" | "destructive" {
  switch (priority) {
    case "urgent":
      return "destructive";
    case "high":
      return "default";
    case "medium":
      return "secondary";
    default:
      return "outline";
  }
}

function formatDate(dateString: string): string {
  return new Date(dateString).toLocaleDateString();
}
