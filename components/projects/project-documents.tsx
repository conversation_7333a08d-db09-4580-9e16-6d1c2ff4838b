"use client";

import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Title } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import Link from "next/link";

interface Document {
  id: string;
  name: string;
  type: string;
  size: number;
  uploadedBy: string;
  uploadedAt: string;
  url: string;
}

interface ProjectDocumentsProps {
  projectId: string;
  documents: Document[];
}

export function ProjectDocuments({ projectId, documents }: ProjectDocumentsProps) {
  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between">
        <CardTitle>Project Documents</CardTitle>
        <Link
          href={`/projects/${projectId}/documents`}
          className="text-sm text-muted-foreground hover:underline"
        >
          View all
        </Link>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {documents.length === 0 ? (
            <div className="text-center text-sm text-muted-foreground">
              No documents uploaded to this project yet.
            </div>
          ) : (
            documents.map((document) => (
              <div
                key={document.id}
                className="flex items-center justify-between rounded-md border p-3"
              >
                <div className="flex items-center gap-3">
                  <div className="flex h-10 w-10 items-center justify-center rounded-md bg-muted">
                    <DocumentIcon type={document.type} />
                  </div>
                  <div>
                    <p className="font-medium">{document.name}</p>
                    <p className="text-xs text-muted-foreground">
                      {formatFileSize(document.size)} • Uploaded by{" "}
                      {document.uploadedBy} on{" "}
                      {new Date(document.uploadedAt).toLocaleDateString()}
                    </p>
                  </div>
                </div>
                <div className="flex gap-2">
                  <a href={document.url} target="_blank" rel="noopener noreferrer">
                    <Button variant="ghost" size="sm">
                      View
                    </Button>
                  </a>
                  <a href={document.url} download>
                    <Button variant="outline" size="sm">
                      Download
                    </Button>
                  </a>
                </div>
              </div>
            ))
          )}
        </div>
      </CardContent>
    </Card>
  );
}

function DocumentIcon({ type }: { type: string }) {
  // This is a placeholder for document type icons
  // In a real implementation, you would use proper icons based on file type
  return <span className="text-xs">{type.toUpperCase()}</span>;
}

function formatFileSize(bytes: number): string {
  if (bytes === 0) return "0 Bytes";
  
  const k = 1024;
  const sizes = ["Bytes", "KB", "MB", "GB"];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return `${Number.parseFloat((bytes / (k ** i)).toFixed(2))} ${sizes[i]}`;
}
