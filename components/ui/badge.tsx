import type * as React from "react"
import { cva, type VariantProps } from "class-variance-authority"

import { cn } from "@/lib/utils"

const badgeVariants = cva(
  "inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-medium transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",
  {
    variants: {
      variant: {
        default:
          "border-transparent bg-primary text-primary-foreground hover:bg-primary/80",
        secondary:
          "border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",
        destructive:
          "border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",
        outline: "text-foreground",
        // Status variants using our CSS variables
        available: "border-transparent bg-status-green-bg text-status-green-text",
        pending: "border-transparent bg-status-yellow-bg text-status-yellow-text",
        cancelled: "border-transparent bg-status-red-bg text-status-red-text",
        sold: "border-transparent bg-status-red-bg text-status-red-text",
        draft: "border-transparent bg-status-blue-bg text-status-blue-text",
        // Futuristic neon variants
        neonBlue: "border-transparent bg-neon-blue/10 text-neon-blue shadow-[0_0_5px_rgba(35,35,255,0.5)] hover:shadow-[0_0_8px_rgba(35,35,255,0.7)] hover:bg-neon-blue/15 transition-all duration-300",
        neonGreen: "border-transparent bg-neon-green/10 text-neon-green shadow-[0_0_5px_rgba(22,255,0,0.5)] hover:shadow-[0_0_8px_rgba(22,255,0,0.7)] hover:bg-neon-green/15 transition-all duration-300",
        neonPink: "border-transparent bg-neon-pink/10 text-neon-pink shadow-[0_0_5px_rgba(246,25,129,0.5)] hover:shadow-[0_0_8px_rgba(246,25,129,0.7)] hover:bg-neon-pink/15 transition-all duration-300",
        neonYellow: "border-transparent bg-neon-yellow/10 text-deep-black shadow-[0_0_5px_rgba(255,237,0,0.5)] hover:shadow-[0_0_8px_rgba(255,237,0,0.7)] hover:bg-neon-yellow/15 transition-all duration-300",
      },
      size: {
        default: "px-2.5 py-0.5 text-xs",
        sm: "px-2 py-0.5 text-xs",
        lg: "px-3 py-1 text-sm",
      }
    },
    defaultVariants: {
      variant: "default",
      size: "default",
    },
  }
)

export interface BadgeProps
  extends React.HTMLAttributes<HTMLDivElement>,
    VariantProps<typeof badgeVariants> {}

function Badge({ className, variant, size, ...props }: BadgeProps) {
  return (
    <div className={cn(badgeVariants({ variant, size }), className)} {...props} />
  )
}

export { Badge, badgeVariants }
