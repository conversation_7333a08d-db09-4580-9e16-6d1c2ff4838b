"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { ChevronLeft, ChevronRight, MoreHorizontal } from "lucide-react"

interface PaginationProps {
  currentPage: number
  totalPages: number
  onPageChange: (page: number) => void
  maxVisiblePages?: number
}

export function Pagination({
  currentPage,
  totalPages,
  onPageChange,
  maxVisiblePages = 5,
}: PaginationProps) {
  // Don't render pagination if there's only one page
  if (totalPages <= 1) {
    return null
  }

  // Calculate the range of page numbers to display
  const getPageNumbers = () => {
    const pageNumbers = []
    
    // Always show first page
    pageNumbers.push(1)
    
    // Calculate start and end of the middle section
    const startPage = Math.max(2, currentPage - Math.floor(maxVisiblePages / 2))
    const endPage = Math.min(totalPages - 1, startPage + maxVisiblePages - 3)
    
    // Adjust if we're near the start
    if (startPage > 2) {
      pageNumbers.push(-1) // Indicator for ellipsis
    }
    
    // Add middle pages
    for (let i = startPage; i <= endPage; i++) {
      pageNumbers.push(i)
    }
    
    // Adjust if we're near the end
    if (endPage < totalPages - 1) {
      pageNumbers.push(-2) // Indicator for ellipsis
    }
    
    // Always show last page if there is more than one page
    if (totalPages > 1) {
      pageNumbers.push(totalPages)
    }
    
    return pageNumbers
  }

  const pageNumbers = getPageNumbers()

  return (
    <div className="flex items-center justify-center space-x-2">
      <Button
        variant="outline"
        size="icon"
        onClick={() => {
          // Reset scroll position when changing pages
          window.scrollTo(0, 0);
          onPageChange(currentPage - 1);
        }}
        disabled={currentPage === 1}
      >
        <ChevronLeft className="h-4 w-4" />
        <span className="sr-only">Previous page</span>
      </Button>
      
      {pageNumbers.map((pageNumber) => {
        // Render ellipsis
        if (pageNumber < 0) {
          return (
            <Button
              key={`ellipsis-${pageNumber === -1 ? 'start' : 'end'}`}
              variant="outline"
              size="icon"
              disabled
            >
              <MoreHorizontal className="h-4 w-4" />
              <span className="sr-only">More pages</span>
            </Button>
          )
        }
        
        // Render page number
        return (
          <Button
            key={pageNumber}
            variant={currentPage === pageNumber ? "default" : "outline"}
            size="icon"
            onClick={() => {
              // Reset scroll position when changing pages
              window.scrollTo(0, 0);
              onPageChange(pageNumber);
            }}
          >
            {pageNumber}
            <span className="sr-only">Page {pageNumber}</span>
          </Button>
        )
      })}
      
      <Button
        variant="outline"
        size="icon"
        onClick={() => {
          // Reset scroll position when changing pages
          window.scrollTo(0, 0);
          onPageChange(currentPage + 1);
        }}
        disabled={currentPage === totalPages}
      >
        <ChevronRight className="h-4 w-4" />
        <span className="sr-only">Next page</span>
      </Button>
    </div>
  )
}
