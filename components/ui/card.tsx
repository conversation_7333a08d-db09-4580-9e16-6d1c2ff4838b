import * as React from "react"

import { cn } from "@/lib/utils"

interface CardProps extends React.HTMLAttributes<HTMLDivElement> {
  variant?: "default" | "bordered" | "flat" | "neon" | "neonBlue" | "neonGreen" | "neonPink" | "neonYellow"
}

const Card = React.forwardRef<HTMLDivElement, CardProps>(
  ({ className, variant = "default", ...props }, ref) => {
    const variantStyles = {
      default: "border border-border bg-card text-card-foreground shadow-md transition-all duration-300 hover:shadow-lg hover:translate-y-[-2px]",
      bordered: "border-2 border-border bg-card text-card-foreground transition-all duration-300 hover:shadow-md hover:translate-y-[-2px]",
      flat: "bg-card text-card-foreground transition-all duration-300 hover:shadow-md hover:translate-y-[-2px]",
      neon: "border border-neon-blue/30 bg-card text-card-foreground shadow-[0_0_15px_rgba(35,35,255,0.3)] transition-all duration-300 hover:shadow-[0_0_20px_rgba(35,35,255,0.5)] hover:translate-y-[-2px]",
      neonBlue: "border border-neon-blue/30 bg-card text-card-foreground shadow-[0_0_15px_rgba(35,35,255,0.3)] transition-all duration-300 hover:shadow-[0_0_20px_rgba(35,35,255,0.5)] hover:translate-y-[-2px]",
      neonGreen: "border border-neon-green/30 bg-card text-card-foreground shadow-[0_0_15px_rgba(22,255,0,0.3)] transition-all duration-300 hover:shadow-[0_0_20px_rgba(22,255,0,0.5)] hover:translate-y-[-2px]",
      neonPink: "border border-neon-pink/30 bg-card text-card-foreground shadow-[0_0_15px_rgba(246,25,129,0.3)] transition-all duration-300 hover:shadow-[0_0_20px_rgba(246,25,129,0.5)] hover:translate-y-[-2px]",
      neonYellow: "border border-neon-yellow/30 bg-card text-card-foreground shadow-[0_0_15px_rgba(255,237,0,0.3)] transition-all duration-300 hover:shadow-[0_0_20px_rgba(255,237,0,0.5)] hover:translate-y-[-2px]",
    }

    return (
      <div
        ref={ref}
        className={cn("rounded-lg", variantStyles[variant], className)}
        {...props}
      />
    )
  }
)
Card.displayName = "Card"

const CardHeader = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement>
>(({ className, ...props }, ref) => (
  <div
    ref={ref}
    className={cn("flex flex-col space-y-1.5 p-6", className)}
    {...props}
  />
))
CardHeader.displayName = "CardHeader"

const CardTitle = React.forwardRef<
  HTMLParagraphElement,
  React.HTMLAttributes<HTMLHeadingElement>
>(({ className, ...props }, ref) => (
  <h3
    ref={ref}
    className={cn(
      "text-lg font-semibold leading-none tracking-tight text-foreground",
      className
    )}
    {...props}
  />
))
CardTitle.displayName = "CardTitle"

const CardDescription = React.forwardRef<
  HTMLParagraphElement,
  React.HTMLAttributes<HTMLParagraphElement>
>(({ className, ...props }, ref) => (
  <p
    ref={ref}
    className={cn("text-sm text-muted-foreground", className)}
    {...props}
  />
))
CardDescription.displayName = "CardDescription"

const CardContent = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement>
>(({ className, ...props }, ref) => (
  <div ref={ref} className={cn("p-6 pt-0", className)} {...props} />
))
CardContent.displayName = "CardContent"

const CardFooter = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement>
>(({ className, ...props }, ref) => (
  <div
    ref={ref}
    className={cn("flex items-center p-6 pt-0", className)}
    {...props}
  />
))
CardFooter.displayName = "CardFooter"

export { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent }
