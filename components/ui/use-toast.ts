// Simplified toast implementation without Radix UI dependency
import * as React from "react"

type ToastProps = {
  id: string
  title?: React.ReactNode
  description?: React.ReactNode
  action?: React.ReactNode
  variant?: "default" | "destructive"
}

type Toast = ToastProps & {
  open: boolean
}

type ToastActionElement = React.ReactElement

const TOAST_LIMIT = 5
const TOAST_REMOVE_DELAY = 5000

const toastTimeouts = new Map<string, ReturnType<typeof setTimeout>>()

const useToast = () => {
  const [toasts, setToasts] = React.useState<Toast[]>([])

  const toast = React.useCallback(
    ({ title, description, variant, action }: Omit<ToastProps, "id">) => {
      const id = Math.random().toString(36).substring(2, 9)
      
      setToasts((prevToasts) => {
        const newToast: Toast = {
          id,
          title,
          description,
          variant,
          action,
          open: true,
        }
        
        const updatedToasts = [...prevToasts, newToast].slice(-TOAST_LIMIT)
        
        // Auto dismiss toast after delay
        toastTimeouts.set(
          id,
          setTimeout(() => {
            setToasts((prevToasts) =>
              prevToasts.map((t) => (t.id === id ? { ...t, open: false } : t))
            )
            
            setTimeout(() => {
              setToasts((prevToasts) => prevToasts.filter((t) => t.id !== id))
            }, 300) // Animation duration
          }, TOAST_REMOVE_DELAY)
        )
        
        return updatedToasts
      })
      
      return id
    },
    []
  )

  const dismiss = React.useCallback((id: string) => {
    setToasts((prevToasts) =>
      prevToasts.map((t) => (t.id === id ? { ...t, open: false } : t))
    )
    
    setTimeout(() => {
      setToasts((prevToasts) => prevToasts.filter((t) => t.id !== id))
    }, 300) // Animation duration
  }, [])

  const dismissAll = React.useCallback(() => {
    setToasts((prevToasts) =>
      prevToasts.map((t) => ({ ...t, open: false }))
    )
    
    setTimeout(() => {
      setToasts([])
    }, 300) // Animation duration
  }, [])

  return {
    toast,
    dismiss,
    dismissAll,
    toasts,
  }
}

export { useToast, type ToastProps, type ToastActionElement }
