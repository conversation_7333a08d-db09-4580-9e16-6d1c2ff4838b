import { Badge } from "@/components/ui/badge"
import { cn } from "@/lib/utils"

// Include all variants that are defined in the Badge component
type BadgeVariant = 
  | "available" 
  | "pending" 
  | "cancelled" 
  | "sold" 
  | "draft"
  | "neonBlue"
  | "neonGreen"
  | "neonPink"
  | "neonYellow"

interface StatusBadgeProps {
  status: string
  className?: string
  futuristic?: boolean
}

export function StatusBadge({ status, className, futuristic = true }: StatusBadgeProps) {
  // Map status to variant
  const getVariant = (status: string, futuristic: boolean): BadgeVariant => {
    const statusLower = status.toLowerCase()
    
    if (futuristic) {
      if (statusLower === "available" || statusLower === "active") return "neonGreen"
      if (statusLower === "pending" || statusLower === "in progress" || statusLower === "in-progress") return "neonYellow"
      if (statusLower === "cancelled" || statusLower === "rejected") return "neonPink"
      if (statusLower === "sold") return "neonPink"
      if (statusLower === "draft" || statusLower === "completed") return "neonBlue"
      
      // Default to neonBlue if status is not recognized
      return "neonBlue"
    }
    
    // Non-futuristic variants
    if (statusLower === "available") return "available"
    if (statusLower === "pending" || statusLower === "in progress" || statusLower === "in-progress") return "pending"
    if (statusLower === "cancelled" || statusLower === "rejected") return "cancelled"
    if (statusLower === "sold") return "sold"
    if (statusLower === "draft" || statusLower === "completed") return "draft"
    
    // Default to available if status is not recognized
    return "available"
  }

  // Map status to display text (capitalize first letter)
  const getDisplayText = (status: string): string => {
    return status.charAt(0).toUpperCase() + status.slice(1).toLowerCase()
  }

  return (
    <Badge 
      variant={getVariant(status, futuristic)} 
      className={cn("font-medium", className)}
    >
      {getDisplayText(status)}
    </Badge>
  )
}
