import * as React from "react"
import { Slot } from "@radix-ui/react-slot"
import { cva, type VariantProps } from "class-variance-authority"
import { Loader2 } from "lucide-react"

import { cn } from "@/lib/utils"

const buttonVariants = cva(
  "inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",
  {
    variants: {
      variant: {
        default: "bg-sidebar-bg text-sidebar-text hover:bg-sidebar-bg/90 transition-all duration-300 hover:translate-y-[-1px]", // Deep black background with white text
        primary: "bg-primary text-primary-foreground hover:bg-primary-dark transition-all duration-300 hover:translate-y-[-1px]", // Neon blue background with white text
        destructive:
          "bg-destructive text-destructive-foreground hover:bg-destructive/90 transition-all duration-300 hover:translate-y-[-1px]",
        outline:
          "border border-border-medium bg-background text-foreground hover:bg-accent hover:text-accent-foreground transition-all duration-300 hover:translate-y-[-1px]",
        secondary:
          "bg-white text-foreground border border-border-medium hover:bg-secondary/80 transition-all duration-300 hover:translate-y-[-1px] hover:shadow-md", // White background with gray text and border
        ghost: "hover:bg-accent hover:text-accent-foreground transition-all duration-300",
        link: "text-primary underline-offset-4 hover:underline transition-all duration-300",
        icon: "text-muted-foreground hover:text-foreground hover:bg-accent p-2 transition-all duration-300", // Icon-only button
        neonBlue: "bg-neon-blue text-white hover:bg-neon-blue/90 shadow-[0_0_10px_rgba(35,35,255,0.7)] hover:shadow-[0_0_15px_rgba(35,35,255,0.9)] hover:translate-y-[-1px] transition-all duration-300",
        neonGreen: "bg-neon-green text-deep-black hover:bg-neon-green/90 shadow-[0_0_10px_rgba(22,255,0,0.7)] hover:shadow-[0_0_15px_rgba(22,255,0,0.9)] hover:translate-y-[-1px] transition-all duration-300",
        neonPink: "bg-neon-pink text-white hover:bg-neon-pink/90 shadow-[0_0_10px_rgba(246,25,129,0.7)] hover:shadow-[0_0_15px_rgba(246,25,129,0.9)] hover:translate-y-[-1px] transition-all duration-300",
        neonYellow: "bg-neon-yellow text-deep-black hover:bg-neon-yellow/90 shadow-[0_0_10px_rgba(255,237,0,0.7)] hover:shadow-[0_0_15px_rgba(255,237,0,0.9)] hover:translate-y-[-1px] transition-all duration-300",
      },
      size: {
        default: "h-10 px-4 py-2",
        sm: "h-9 rounded-md px-3 py-1.5",
        lg: "h-11 rounded-md px-8 py-2.5",
        icon: "h-10 w-10",
        xs: "h-8 rounded-md px-2 py-1 text-xs",
      },
      isLoading: {
        true: "cursor-not-allowed opacity-70",
        false: "",
      },
    },
    defaultVariants: {
      variant: "default",
      size: "default",
      isLoading: false,
    },
  }
)

export interface ButtonProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement>,
    VariantProps<typeof buttonVariants> {
  asChild?: boolean
  isLoading?: boolean
  loadingText?: string
}

const Button = React.forwardRef<HTMLButtonElement, ButtonProps>(
  ({ className, variant, size, isLoading, loadingText, asChild = false, children, ...props }, ref) => {
    const Comp = asChild ? Slot : "button"
    return (
      <Comp
        className={cn(buttonVariants({ variant, size, isLoading, className }))}
        ref={ref}
        disabled={isLoading || props.disabled}
        {...props}
      >
        {isLoading ? (
          <>
            <Loader2 className="mr-2 h-4 w-4 animate-spin" />
            {loadingText || children}
          </>
        ) : (
          children
        )}
      </Comp>
    )
  }
)
Button.displayName = "Button"

export { Button, buttonVariants }
