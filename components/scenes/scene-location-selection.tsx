"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Command, CommandEmpty, CommandGroup, CommandInput, CommandItem, CommandList } from "@/components/ui/command"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { Check, ChevronsUpDown, MapPin } from "lucide-react"
import { cn } from "@/lib/utils"

// Sample locations data
const locations = [
  { id: "loc1", name: "Downtown Loft", address: "123 Main St, New York, NY" },
  { id: "loc2", name: "Beach House", address: "456 Ocean Dr, Miami, FL" },
  { id: "loc3", name: "Mountain Cabin", address: "789 Pine Rd, Aspen, CO" },
  { id: "loc4", name: "Desert Motel", address: "101 Sand Ave, Phoenix, AZ" },
  { id: "loc5", name: "City Park", address: "202 Green St, Chicago, IL" },
]

interface SceneLocationSelectionProps {
  selectedLocationId: string | null
  onLocationSelect: (locationId: string | null) => void
}

export function SceneLocationSelection({ selectedLocationId, onLocationSelect }: SceneLocationSelectionProps) {
  const [open, setOpen] = useState(false)

  const selectedLocation = selectedLocationId ? locations.find((location) => location.id === selectedLocationId) : null

  return (
    <div className="space-y-4">
      <Popover open={open} onOpenChange={setOpen}>
        <PopoverTrigger asChild>
          <Button variant="outline" role="combobox" aria-expanded={open} className="w-full justify-between">
            {selectedLocation ? (
              <div className="flex items-center">
                <MapPin className="mr-2 h-4 w-4 shrink-0 opacity-50" />
                <span>{selectedLocation.name}</span>
              </div>
            ) : (
              <div className="flex items-center text-muted-foreground">
                <MapPin className="mr-2 h-4 w-4 shrink-0 opacity-50" />
                <span>Select location</span>
              </div>
            )}
            <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-[300px] p-0">
          <Command>
            <CommandInput placeholder="Search locations..." />
            <CommandList>
              <CommandEmpty>No locations found.</CommandEmpty>
              <CommandGroup>
                {locations.map((location) => (
                  <CommandItem
                    key={location.id}
                    value={location.id}
                    onSelect={() => {
                      onLocationSelect(location.id === selectedLocationId ? null : location.id)
                      setOpen(false)
                    }}
                  >
                    <Check
                      className={cn("mr-2 h-4 w-4", selectedLocationId === location.id ? "opacity-100" : "opacity-0")}
                    />
                    <div className="flex flex-col">
                      <span>{location.name}</span>
                      <span className="text-xs text-muted-foreground">{location.address}</span>
                    </div>
                  </CommandItem>
                ))}
              </CommandGroup>
            </CommandList>
          </Command>
        </PopoverContent>
      </Popover>

      {selectedLocation && (
        <div className="text-sm text-muted-foreground">
          <p>Selected: {selectedLocation.name}</p>
          <p>{selectedLocation.address}</p>
        </div>
      )}
    </div>
  )
}

