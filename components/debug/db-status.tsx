"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"

export function DbStatus() {
  const [status, setStatus] = useState<"loading" | "connected" | "error">("loading")
  const [message, setMessage] = useState("")
  const [details, setDetails] = useState<Record<string, any> | null>(null)

  const checkConnection = async () => {
    setStatus("loading")
    setMessage("Checking database connection...")

    try {
      const response = await fetch("/api/debug/db-status")
      const data = await response.json()

      if (data.success) {
        setStatus("connected")
        setMessage("Database connected successfully!")
        setDetails(data)
      } else {
        setStatus("error")
        setMessage(data.error || "Failed to connect to database")
        setDetails(data)
      }
    } catch (error) {
      setStatus("error")
      setMessage("Failed to check database connection")
      console.error(error)
    }
  }

  useEffect(() => {
    checkConnection()
  }, [])

  return (
    <Card>
      <CardHeader>
        <CardTitle>Database Status</CardTitle>
        <CardDescription>Check the connection to your Neon database</CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-2">
          <div className="flex items-center gap-2">
            <div
              className={`h-3 w-3 rounded-full ${
                status === "connected" ? "bg-green-500" : status === "error" ? "bg-red-500" : "bg-yellow-500"
              }`}
            />
            <span className="font-medium">
              {status === "connected" ? "Connected" : status === "error" ? "Error" : "Checking..."}
            </span>
          </div>
          <p className="text-sm text-muted-foreground">{message}</p>

          {details && (
            <div className="mt-4 rounded-md bg-muted p-4">
              <pre className="text-xs overflow-auto">{JSON.stringify(details, null, 2)}</pre>
            </div>
          )}
        </div>
      </CardContent>
      <CardFooter>
        <Button onClick={checkConnection} variant="outline" size="sm">
          Refresh Connection
        </Button>
      </CardFooter>
    </Card>
  )
}
