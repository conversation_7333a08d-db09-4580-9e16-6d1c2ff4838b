"use client";

import { useState } from "react";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";

export function ApiDebug() {
  const [results, setResults] = useState<any[]>([]);
  const [isLoading, setIsLoading] = useState(false);

  const testEndpoint = async (url: string, headers: Record<string, string> = {}) => {
    setIsLoading(true);
    const startTime = Date.now();
    
    try {
      console.log(`Testing: ${url}`);
      console.log(`Headers:`, headers);
      
      const response = await fetch(url, { headers });
      const endTime = Date.now();
      
      console.log(`Response status: ${response.status}`);
      console.log(`Response time: ${endTime - startTime}ms`);
      
      const responseText = await response.text();
      console.log(`Response text (first 200 chars):`, responseText.substring(0, 200));
      
      let parsedData;
      try {
        parsedData = JSON.parse(responseText);
      } catch (e) {
        parsedData = { error: "Not valid JSON", responseText: responseText.substring(0, 500) };
      }
      
      setResults(prev => [...prev, {
        url,
        status: response.status,
        statusText: response.statusText,
        responseTime: endTime - startTime,
        headers: Object.fromEntries(response.headers.entries()),
        data: parsedData,
        timestamp: new Date().toISOString()
      }]);
      
    } catch (error) {
      const endTime = Date.now();
      console.error(`Error testing ${url}:`, error);
      
      setResults(prev => [...prev, {
        url,
        status: 'ERROR',
        statusText: error instanceof Error ? error.message : 'Unknown error',
        responseTime: endTime - startTime,
        error: error instanceof Error ? error.message : 'Unknown error',
        timestamp: new Date().toISOString()
      }]);
    } finally {
      setIsLoading(false);
    }
  };

  const runTests = async () => {
    setResults([]);

    // Test basic API endpoints (middleware will add auth headers automatically)
    await testEndpoint('/api/organizations');
    await testEndpoint('/api/locations');
    await testEndpoint('/api/locations/metadata');

    // Test organization slug endpoint
    const orgSlug = 'wearefireflymedia-organization'; // Replace with your actual org slug
    await testEndpoint(`/api/organizations/slug/${orgSlug}`);
  };

  return (
    <Card className="w-full max-w-4xl mx-auto">
      <CardHeader>
        <CardTitle>API Debug Tool</CardTitle>
        <Button onClick={runTests} disabled={isLoading}>
          {isLoading ? 'Testing...' : 'Run API Tests'}
        </Button>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {results.map((result, index) => (
            <div key={index} className="border p-4 rounded">
              <div className="font-mono text-sm">
                <div className="flex justify-between items-center mb-2">
                  <span className="font-bold">{result.url}</span>
                  <span className={`px-2 py-1 rounded text-xs ${
                    result.status === 200 ? 'bg-green-100 text-green-800' :
                    result.status === 'ERROR' ? 'bg-red-100 text-red-800' :
                    'bg-yellow-100 text-yellow-800'
                  }`}>
                    {result.status} {result.statusText}
                  </span>
                </div>
                <div className="text-xs text-gray-600 mb-2">
                  Response time: {result.responseTime}ms | {result.timestamp}
                </div>
                {result.error && (
                  <div className="bg-red-50 p-2 rounded mb-2">
                    <strong>Error:</strong> {result.error}
                  </div>
                )}
                <details className="mt-2">
                  <summary className="cursor-pointer text-blue-600">View Response</summary>
                  <pre className="mt-2 p-2 bg-gray-100 rounded text-xs overflow-auto max-h-40">
                    {JSON.stringify(result.data, null, 2)}
                  </pre>
                </details>
              </div>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
}
