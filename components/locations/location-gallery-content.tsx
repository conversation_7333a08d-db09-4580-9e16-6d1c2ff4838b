"use client";

import { useState } from "react";
import Image from "next/image";
import { Expand, ChevronLeft, ChevronRight, X, ZoomIn, ZoomOut } from "lucide-react";
import {
  Dialog,
  DialogContent,
  DialogClose,
  DialogTitle,
} from "@/components/ui/dialog";

interface MediaItem {
  id: string;
  url: string;
  filename: string;
  type: "image" | "video";
  thumbnailUrl?: string;
  uploadedBy: {
    id: string;
    name: string;
  };
  uploadedAt: string;
}

interface LocationGalleryContentProps {
  media: MediaItem[];
}

export function LocationGalleryContent({ media }: LocationGalleryContentProps) {
  const [activeItem, setActiveItem] = useState<MediaItem | null>(
    media.length > 0 ? media[0] : null
  );
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [scale, setScale] = useState(1);

  const images = media.filter((item) => item.type === "image");
  const videos = media.filter((item) => item.type === "video");

  const handlePrevious = () => {
    if (!activeItem) return;
    const currentIndex = media.findIndex((item) => item.id === activeItem.id);
    const prevIndex = (currentIndex - 1 + media.length) % media.length;
    setActiveItem(media[prevIndex]);
  };

  const handleNext = () => {
    if (!activeItem) return;
    const currentIndex = media.findIndex((item) => item.id === activeItem.id);
    const nextIndex = (currentIndex + 1) % media.length;
    setActiveItem(media[nextIndex]);
  };

  const handleZoomIn = () => {
    setScale((prev) => Math.min(prev + 0.25, 3));
  };

  const handleZoomOut = () => {
    setScale((prev) => Math.max(prev - 0.25, 0.5));
  };

  const handleFullscreenToggle = () => {
    setIsFullscreen(true);
    // Reset scale when entering fullscreen
    setScale(1);
  };

  const handleCloseFullscreen = () => {
    setIsFullscreen(false);
    // Reset scale when exiting fullscreen
    setScale(1);
  };

  return (
    <div className="h-full w-full p-0">
      {media.length === 0 ? (
        <div className="flex h-full w-full items-center justify-center">
          <div className="text-center text-sm text-muted-foreground">
            No media available for this location.
          </div>
        </div>
      ) : (
        <div className="h-full w-full flex flex-col space-y-4 p-0">
            {/* Main display area */}
          {activeItem && (
            <div className="overflow-hidden flex-1 bg-muted/20 relative">
              {activeItem.type === "image" ? (
                <div className="w-full flex items-center justify-center" style={{ height: "auto", minHeight: "650px", position: "relative" }}>
                  <div className="absolute inset-0 flex items-center justify-center">
                    <Image
                      src={activeItem.url}
                      alt={activeItem.filename}
                      width={1600}
                      height={1200}
                      className="object-contain w-full h-full"
                      sizes="100vw"
                      priority
                    />
                  </div>
                  <button
                    onClick={handleFullscreenToggle}
                    className="absolute top-2 right-2 p-2 bg-black/50 rounded-full text-white hover:bg-black/70 transition-colors"
                    aria-label="View fullscreen"
                  >
                    <Expand className="h-5 w-5" />
                  </button>
                </div>
              ) : (
                <div className="w-full flex items-center justify-center" style={{ height: "auto", minHeight: "650px", position: "relative" }}>
                  <div className="absolute inset-0 flex items-center justify-center">
                    <video
                      src={activeItem.url}
                      controls
                      className="w-full h-full object-contain"
                    >
                      <track kind="captions" src="" label="English" />
                    </video>
                  </div>
                  <button
                    onClick={handleFullscreenToggle}
                    className="absolute top-2 right-2 p-2 bg-black/50 rounded-full text-white hover:bg-black/70 transition-colors"
                    aria-label="View fullscreen"
                  >
                    <Expand className="h-5 w-5" />
                  </button>
                </div>
              )}
              <div className="p-4 text-sm border-t">
                <p className="font-medium">{activeItem.filename}</p>
                <p className="text-xs text-muted-foreground mt-1">
                  Uploaded by {activeItem.uploadedBy.name} on{" "}
                  {new Date(activeItem.uploadedAt).toLocaleDateString()}
                </p>
              </div>
            </div>
          )}

          {/* Navigation controls removed as requested */}

          {/* Thumbnails */}
          <div className="flex justify-center">
            <div className="grid grid-cols-5 md:grid-cols-8 gap-2 max-w-[800px]">
              {media.map((item) => (
                <button
                  type="button"
                  key={item.id}
                  className={`relative aspect-square w-[60px] h-[60px] overflow-hidden rounded-md transition-all hover:opacity-90 ${
                    activeItem?.id === item.id
                      ? "ring-2 ring-primary ring-offset-1"
                      : "border border-muted-foreground/20"
                  }`}
                  onClick={() => setActiveItem(item)}
                >
                  {item.type === "image" ? (
                    <Image
                      src={item.thumbnailUrl || item.url}
                      alt={item.filename}
                      fill
                      className="object-cover"
                      sizes="60px"
                    />
                  ) : (
                    <div className="flex h-full w-full items-center justify-center bg-muted">
                      <span className="text-xs">Video</span>
                    </div>
                  )}
                </button>
              ))}
            </div>
          </div>

          {/* Stats */}
          <div className="flex justify-between text-sm text-muted-foreground">
            <span>{images.length} images</span>
            <span>{videos.length} videos</span>
          </div>
        </div>
      )}

      {/* Fullscreen Dialog */}
      <Dialog open={isFullscreen} onOpenChange={handleCloseFullscreen}>
        <DialogContent className="max-w-[95vw] w-[95vw] h-[95vh] p-0 bg-black/95 border-none">
          <DialogTitle className="sr-only">Media Gallery Fullscreen View</DialogTitle>
          <div className="relative w-full h-full flex flex-col">
            {/* Fullscreen controls */}
            <div className="absolute top-4 right-4 z-10 flex space-x-2">
              <button
                onClick={handleZoomIn}
                className="p-2 bg-black/50 rounded-full text-white hover:bg-black/70 transition-colors"
                aria-label="Zoom in"
              >
                <ZoomIn className="h-5 w-5" />
              </button>
              <button
                onClick={handleZoomOut}
                className="p-2 bg-black/50 rounded-full text-white hover:bg-black/70 transition-colors"
                aria-label="Zoom out"
              >
                <ZoomOut className="h-5 w-5" />
              </button>
              <DialogClose className="p-2 bg-black/50 rounded-full text-white hover:bg-black/70 transition-colors">
                <X className="h-5 w-5" />
              </DialogClose>
            </div>

            {/* Main image area */}
            <div className="flex-1 flex items-center justify-center overflow-hidden">
              {activeItem && activeItem.type === "image" ? (
                <div className="relative w-full h-full flex items-center justify-center">
                  <div 
                    className="relative transition-transform"
                    style={{ transform: `scale(${scale})` }}
                  >
                    <Image
                      src={activeItem.url}
                      alt={activeItem.filename}
                      width={1200}
                      height={800}
                      className="object-contain max-h-[80vh]"
                      sizes="95vw"
                      priority
                    />
                  </div>
                </div>
              ) : activeItem ? (
                <div className="relative w-full h-full flex items-center justify-center">
                  <video
                    src={activeItem.url}
                    controls
                    className="max-w-full max-h-[80vh]"
                  >
                    <track kind="captions" src="" label="English" />
                  </video>
                </div>
              ) : null}
            </div>

            {/* Navigation controls */}
            <div className="absolute left-4 top-1/2 transform -translate-y-1/2">
              <button
                onClick={handlePrevious}
                className="p-3 rounded-full bg-black/50 text-white hover:bg-black/70 transition-colors"
                aria-label="Previous image"
                disabled={media.length <= 1}
              >
                <ChevronLeft className="h-6 w-6" />
              </button>
            </div>
            <div className="absolute right-4 top-1/2 transform -translate-y-1/2">
              <button
                onClick={handleNext}
                className="p-3 rounded-full bg-black/50 text-white hover:bg-black/70 transition-colors"
                aria-label="Next image"
                disabled={media.length <= 1}
              >
                <ChevronRight className="h-6 w-6" />
              </button>
            </div>

            {/* Fullscreen thumbnails */}
            <div className="p-4 bg-black/80">
              <div className="flex justify-center">
                <div className="flex space-x-2 overflow-x-auto py-2 max-w-[90vw]">
                  {media.map((item) => (
                    <button
                      type="button"
                      key={item.id}
                      className={`relative flex-shrink-0 w-[80px] h-[60px] overflow-hidden rounded-md transition-all hover:opacity-90 ${
                        activeItem?.id === item.id
                          ? "ring-2 ring-white ring-offset-1 ring-offset-black"
                          : "border border-gray-700"
                      }`}
                      onClick={() => setActiveItem(item)}
                    >
                      {item.type === "image" ? (
                        <Image
                          src={item.thumbnailUrl || item.url}
                          alt={item.filename}
                          fill
                          className="object-cover"
                          sizes="80px"
                        />
                      ) : (
                        <div className="flex h-full w-full items-center justify-center bg-gray-800">
                          <span className="text-xs text-white">Video</span>
                        </div>
                      )}
                    </button>
                  ))}
                </div>
              </div>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
}
