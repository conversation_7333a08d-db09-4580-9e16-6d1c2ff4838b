"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON>, <PERSON>alog<PERSON>ontent, DialogDescription, <PERSON><PERSON><PERSON><PERSON>er, <PERSON>alog<PERSON>eader, DialogTitle } from "@/components/ui/dialog"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Loader2, Edit, MapPin, Check } from "lucide-react"
import { useLocationMutations } from "@/hooks/use-location-mutations"
import { useLocationDetails } from "@/hooks/use-location-details"
import { toast } from "sonner"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { LocationMapPreview } from "@/components/locations/location-create/location-map-preview"
import { LocationMedia } from "@/components/locations/location-create/location-media"
import { geocodeAddress } from "@/lib/utils/geocoding"
import type { LocationType } from "@/modules/location/model"

interface EditLocationModalProps {
  isOpen: boolean
  onClose: () => void
  locationId: string
  organizationSlug: string
}

export function EditLocationModal({ isOpen, onClose, locationId }: EditLocationModalProps) {
  const [activeTab, setActiveTab] = useState("basic-info")
  const [isGeocoding, setIsGeocoding] = useState(false)
  const [geocodingSuccess, setGeocodingSuccess] = useState(false)
  const [addressSuggestion, setAddressSuggestion] = useState<string | null>(null)
  
  // Fetch location data
  const { 
    data: location, 
    isLoading: isLoadingLocation, 
    isError, 
    error,
    refetch
  } = useLocationDetails(locationId)
  
  // Use the location mutations hook
  const { updateLocation } = useLocationMutations()
  
  // Form state
  const [formData, setFormData] = useState({
    name: "",
    description: "",
    type: "",
    address: {
      street: "",
      city: "",
      state: "",
      postalCode: "",
      country: "",
      formatted: ""
    },
    coordinates: {
      latitude: 0,
      longitude: 0
    },
    dailyRate: 0,
    locationSize: "",
    parkingInfo: "",
    restrictions: "",
    contactName: "",
    contactEmail: "",
    contactPhone: ""
  })
  
  // Update form data when location data is loaded
  useEffect(() => {
    if (location) {
      setFormData({
        name: location.name || "",
        description: location.description || "",
        type: location.type || "",
        address: {
          street: location.address?.street || "",
          city: location.address?.city || "",
          state: location.address?.state || "",
          postalCode: location.address?.postalCode || "",
          country: location.address?.country || "",
          formatted: location.address?.formatted || ""
        },
        coordinates: {
          latitude: location.coordinates?.latitude || 0,
          longitude: location.coordinates?.longitude || 0
        },
        dailyRate: location.dailyRate || 0,
        locationSize: location.locationSize || "",
        parkingInfo: location.parkingInfo || "",
        restrictions: location.restrictions || "",
        contactName: location.contactName || "",
        contactEmail: location.contactEmail || "",
        contactPhone: location.contactPhone || ""
      })
      
      // Set geocoding success if coordinates are set
      if (location.coordinates?.latitude && location.coordinates?.longitude) {
        setGeocodingSuccess(true)
      }
    }
  }, [location])
  
  // Function to build full address string
  const getFullAddress = () => {
    const { street, city, state, country, postalCode } = formData.address
    return [street, city, state, postalCode, country]
      .filter(part => part && part.trim() !== '')
      .join(', ')
  }
  
  // Function to handle geocoding
  const handleGeocode = async () => {
    const fullAddress = getFullAddress()
    
    if (!fullAddress) {
      toast.error("Please enter address details to find coordinates")
      return
    }
    
    setIsGeocoding(true)
    setGeocodingSuccess(false)
    
    try {
      const result = await geocodeAddress(fullAddress)
      
      // Update coordinates in form data
      setFormData(prev => ({
        ...prev,
        coordinates: {
          latitude: result.latitude,
          longitude: result.longitude
        }
      }))
      
      setGeocodingSuccess(true)
      toast.success("Address geocoded successfully")
    } catch (error) {
      console.error("Geocoding error:", error)
      toast.error(error instanceof Error ? error.message : "Failed to geocode address")
    } finally {
      setIsGeocoding(false)
    }
  }
  
  // Handle form input changes
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target
    
    if (name.includes('.')) {
      const [parent, child] = name.split('.')
      
      if (parent === 'address') {
        setFormData(prev => ({
          ...prev,
          address: {
            ...prev.address,
            [child]: value
          }
        }))
      } else if (parent === 'coordinates') {
        setFormData(prev => ({
          ...prev,
          coordinates: {
            ...prev.coordinates,
            [child]: value
          }
        }))
      }
    } else {
      setFormData(prev => ({
        ...prev,
        [name]: value
      }))
    }
  }
  
  // Handle select changes
  const handleSelectChange = (name: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      [name]: value
    }))
  }
  
  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    try {
      // Validate required fields
      if (!formData.name) {
        toast.error("Location name is required");
        return;
      }
      
      // Ensure type is not empty and is a valid LocationType
      if (!formData.type) {
        toast.error("Location type is required");
        return;
      }
      
      // Create a new object without spreading formData to avoid type errors
      const updatedData = {
        name: formData.name,
        description: formData.description,
        // Cast the type to LocationType and ensure it's not empty
        type: formData.type as LocationType,
        address: formData.address,
        // Convert coordinates to numbers
        coordinates: {
          latitude: Number(formData.coordinates.latitude),
          longitude: Number(formData.coordinates.longitude)
        },
        // Convert dailyRate to number
        dailyRate: Number(formData.dailyRate),
        locationSize: formData.locationSize,
        parkingInfo: formData.parkingInfo,
        restrictions: formData.restrictions,
        contactName: formData.contactName,
        // Only include contactEmail if it's a valid email
        contactEmail: formData.contactEmail?.includes('@') 
          ? formData.contactEmail 
          : undefined,
        contactPhone: formData.contactPhone,
      }
      
      await updateLocation.mutateAsync({
        id: locationId,
        data: updatedData
      })
      
      toast.success("Location updated successfully")
      
      // Refetch location data to update the UI
      refetch()
      
      // Close the modal
      onClose()
    } catch (error) {
      console.error("Error updating location:", error)
      if (error instanceof Error) {
        toast.error(error.message)
      } else {
        toast.error("Failed to update location")
      }
    }
  }
  
  // If loading, show skeleton loader in the modal
  if (isLoadingLocation) {
    return (
      <Dialog open={isOpen} onOpenChange={onClose}>
        <DialogContent className="sm:max-w-[800px] max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle className="flex items-center">
              <Edit className="mr-2 h-5 w-5" />
              Edit Location
            </DialogTitle>
            <DialogDescription>
              Loading location details...
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-6">
            <div className="h-10 w-64 bg-gray-200 animate-pulse rounded" />
            <div className="h-[500px] bg-gray-200 animate-pulse rounded" />
          </div>
        </DialogContent>
      </Dialog>
    )
  }
  
  // If error, show error message in the modal
  if (isError) {
    return (
      <Dialog open={isOpen} onOpenChange={onClose}>
        <DialogContent className="sm:max-w-[800px]">
          <DialogHeader>
            <DialogTitle className="flex items-center">
              <Edit className="mr-2 h-5 w-5" />
              Edit Location
            </DialogTitle>
            <DialogDescription>
              There was an error loading the location details.
            </DialogDescription>
          </DialogHeader>
          <div className="flex flex-col items-center justify-center py-12">
            <h2 className="text-2xl font-bold mb-2">Error Loading Location</h2>
            <p className="text-muted-foreground mb-6">
              {error instanceof Error ? error.message : "Failed to load location details"}
            </p>
            <Button onClick={onClose}>
              Close
            </Button>
          </div>
        </DialogContent>
      </Dialog>
    )
  }
  
  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[800px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center">
            <Edit className="mr-2 h-5 w-5" />
            Edit Location
          </DialogTitle>
          <DialogDescription>
            Make changes to the location details below.
          </DialogDescription>
        </DialogHeader>
        
        <form onSubmit={handleSubmit} className="space-y-6">
          <Tabs value={activeTab} onValueChange={setActiveTab}>
            <TabsList className="grid grid-cols-4 w-full">
              <TabsTrigger value="basic-info">Basic Info</TabsTrigger>
              <TabsTrigger value="details">Details</TabsTrigger>
              <TabsTrigger value="media">Media</TabsTrigger>
              <TabsTrigger value="contact">Contact</TabsTrigger>
            </TabsList>
            
            {/* Basic Info Tab */}
            <TabsContent value="basic-info">
              <Card>
                <CardHeader>
                  <CardTitle>Basic Information</CardTitle>
                  <CardDescription>
                    Edit the basic information about this location.
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="name">Location Name</Label>
                    <Input
                      id="name"
                      name="name"
                      value={formData.name}
                      onChange={handleInputChange}
                      placeholder="Enter location name"
                      required
                    />
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="description">Description</Label>
                    <Textarea
                      id="description"
                      name="description"
                      value={formData.description}
                      onChange={handleInputChange}
                      placeholder="Enter location description"
                      rows={4}
                    />
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="type">Location Type</Label>
                    <Select
                      value={formData.type}
                      onValueChange={(value) => handleSelectChange("type", value)}
                    >
                      <SelectTrigger id="type">
                        <SelectValue placeholder="Select location type" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="interior">Interior</SelectItem>
                        <SelectItem value="exterior">Exterior</SelectItem>
                        <SelectItem value="both">Both</SelectItem>
                        <SelectItem value="studio">Studio</SelectItem>
                        <SelectItem value="house">House</SelectItem>
                        <SelectItem value="apartment">Apartment</SelectItem>
                        <SelectItem value="office">Office</SelectItem>
                        <SelectItem value="warehouse">Warehouse</SelectItem>
                        <SelectItem value="retail">Retail</SelectItem>
                        <SelectItem value="restaurant">Restaurant</SelectItem>
                        <SelectItem value="bar">Bar</SelectItem>
                        <SelectItem value="hotel">Hotel</SelectItem>
                        <SelectItem value="park">Park</SelectItem>
                        <SelectItem value="beach">Beach</SelectItem>
                        <SelectItem value="forest">Forest</SelectItem>
                        <SelectItem value="urban">Urban</SelectItem>
                        <SelectItem value="rural">Rural</SelectItem>
                        <SelectItem value="industrial">Industrial</SelectItem>
                        <SelectItem value="historic">Historic</SelectItem>
                        <SelectItem value="modern">Modern</SelectItem>
                        <SelectItem value="other">Other</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  
                  {/* Address Section */}
                  <div className="space-y-4 p-4 border rounded-md bg-muted/20">
                    <div className="flex items-center justify-between">
                      <h3 className="text-lg font-medium">Address Information</h3>
                      <Button 
                        type="button" 
                        variant="outline" 
                        size="sm"
                        onClick={handleGeocode}
                        disabled={isGeocoding}
                      >
                        {isGeocoding ? (
                          <>
                            <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                            Finding Coordinates...
                          </>
                        ) : (
                          <>
                            <MapPin className="mr-2 h-4 w-4" />
                            Get Coordinates
                          </>
                        )}
                      </Button>
                    </div>

                    {addressSuggestion && (
                      <div className="bg-blue-50 p-3 rounded-md mb-3">
                        <div className="flex justify-between items-center">
                          <p className="text-sm text-blue-700">Suggested address:</p>
                          <Button 
                            type="button" 
                            variant="ghost" 
                            size="sm" 
                            onClick={() => {
                              // Apply address suggestion
                              if (!addressSuggestion) return;
                              
                              // Parse the formatted address into components
                              const parts = addressSuggestion.split(', ');
                              
                              if (parts.length >= 3) {
                                // Attempt to extract components from the formatted address
                                const street = parts[0];
                                const city = parts[1];
                                const statePostal = parts[2].split(' ');
                                const state = statePostal[0];
                                const postalCode = statePostal.length > 1 ? statePostal[1] : '';
                                const country = parts.length > 3 ? parts[3] : '';
                                
                                setFormData(prev => ({
                                  ...prev,
                                  address: {
                                    ...prev.address,
                                    street,
                                    city,
                                    state,
                                    postalCode,
                                    country,
                                    formatted: addressSuggestion
                                  }
                                }));
                                
                                setAddressSuggestion(null);
                                toast.success("Address updated with suggestion");
                              }
                            }}
                            className="h-8 text-blue-700"
                          >
                            <Check className="mr-1 h-4 w-4" />
                            Use This
                          </Button>
                        </div>
                        <p className="text-sm font-medium">{addressSuggestion}</p>
                      </div>
                    )}

                    <div className="space-y-2">
                      <Label htmlFor="address.street">Street Address</Label>
                      <Input
                        id="address.street"
                        name="address.street"
                        value={formData.address.street}
                        onChange={handleInputChange}
                        placeholder="Enter street address"
                      />
                    </div>

                    <div className="grid grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="address.city">City</Label>
                        <Input
                          id="address.city"
                          name="address.city"
                          value={formData.address.city}
                          onChange={handleInputChange}
                          placeholder="Enter city"
                        />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="address.state">State/Province</Label>
                        <Input
                          id="address.state"
                          name="address.state"
                          value={formData.address.state}
                          onChange={handleInputChange}
                          placeholder="Enter state/province"
                        />
                      </div>
                    </div>

                    <div className="grid grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="address.postalCode">Postal Code</Label>
                        <Input
                          id="address.postalCode"
                          name="address.postalCode"
                          value={formData.address.postalCode}
                          onChange={handleInputChange}
                          placeholder="Enter postal code"
                        />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="address.country">Country</Label>
                        <Input
                          id="address.country"
                          name="address.country"
                          value={formData.address.country}
                          onChange={handleInputChange}
                          placeholder="Enter country"
                        />
                      </div>
                    </div>

                    {/* Coordinates Display */}
                    <div className="mt-4 p-3 bg-muted rounded-md">
                      <div className="flex justify-between items-center">
                        <span className="text-sm font-medium">Coordinates:</span>
                        {geocodingSuccess && (
                          <span className="text-xs bg-green-100 text-green-800 px-2 py-1 rounded-full">
                            Verified
                          </span>
                        )}
                      </div>
                      <div className="mt-1 grid grid-cols-2 gap-2">
                        <div>
                          <Label htmlFor="coordinates.latitude" className="text-xs">Latitude</Label>
                          <Input 
                            id="coordinates.latitude"
                            name="coordinates.latitude"
                            type="number"
                            step="0.000001"
                            value={formData.coordinates.latitude}
                            onChange={handleInputChange}
                            className="bg-muted/50 text-sm"
                          />
                        </div>
                        <div>
                          <Label htmlFor="coordinates.longitude" className="text-xs">Longitude</Label>
                          <Input 
                            id="coordinates.longitude"
                            name="coordinates.longitude"
                            type="number"
                            step="0.000001"
                            value={formData.coordinates.longitude}
                            onChange={handleInputChange}
                            className="bg-muted/50 text-sm"
                          />
                        </div>
                      </div>
                    </div>
                  </div>
                  
                  {/* Map Preview */}
                  {formData.coordinates.latitude !== 0 && formData.coordinates.longitude !== 0 && (
                    <div className="mt-6">
                      <h3 className="text-lg font-medium mb-2">Location Preview</h3>
                      <LocationMapPreview 
                        latitude={Number(formData.coordinates.latitude)} 
                        longitude={Number(formData.coordinates.longitude)} 
                      />
                    </div>
                  )}
                </CardContent>
              </Card>
            </TabsContent>
            
            {/* Details Tab */}
            <TabsContent value="details">
              <Card>
                <CardHeader>
                  <CardTitle>Location Details</CardTitle>
                  <CardDescription>
                    Edit the detailed specifications of this location.
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="dailyRate">Daily Rate ($)</Label>
                    <Input
                      id="dailyRate"
                      name="dailyRate"
                      type="number"
                      value={formData.dailyRate}
                      onChange={handleInputChange}
                      placeholder="Enter daily rate"
                    />
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="locationSize">Size</Label>
                    <Input
                      id="locationSize"
                      name="locationSize"
                      value={formData.locationSize}
                      onChange={handleInputChange}
                      placeholder="Enter size (e.g., 2000 sq ft)"
                    />
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="parkingInfo">Parking Information</Label>
                    <Input
                      id="parkingInfo"
                      name="parkingInfo"
                      value={formData.parkingInfo}
                      onChange={handleInputChange}
                      placeholder="Enter parking details"
                    />
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="restrictions">Restrictions</Label>
                    <Textarea
                      id="restrictions"
                      name="restrictions"
                      value={formData.restrictions}
                      onChange={handleInputChange}
                      placeholder="Enter any restrictions"
                      rows={3}
                    />
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
            
            {/* Media Tab */}
            <TabsContent value="media">
              <Card>
                <CardHeader>
                  <CardTitle>Media & Documents</CardTitle>
                  <CardDescription>
                    Upload photos, floor plans, and documents for this location.
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <LocationMedia locationId={locationId} />
                </CardContent>
              </Card>
            </TabsContent>
            
            {/* Contact Tab */}
            <TabsContent value="contact">
              <Card>
                <CardHeader>
                  <CardTitle>Contact Information</CardTitle>
                  <CardDescription>
                    Edit the contact information for this location.
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="contactName">Contact Name</Label>
                    <Input
                      id="contactName"
                      name="contactName"
                      value={formData.contactName}
                      onChange={handleInputChange}
                      placeholder="Enter contact name"
                    />
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="contactEmail">Contact Email</Label>
                    <Input
                      id="contactEmail"
                      name="contactEmail"
                      type="email"
                      value={formData.contactEmail}
                      onChange={handleInputChange}
                      placeholder="Enter contact email"
                    />
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="contactPhone">Contact Phone</Label>
                    <Input
                      id="contactPhone"
                      name="contactPhone"
                      value={formData.contactPhone}
                      onChange={handleInputChange}
                      placeholder="Enter contact phone"
                    />
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
          
          <DialogFooter className="flex justify-end gap-4">
            <Button 
              type="button" 
              variant="outline" 
              onClick={onClose}
            >
              Cancel
            </Button>
            <Button 
              type="submit" 
              disabled={updateLocation.isPending}
            >
              {updateLocation.isPending ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  <span>Saving...</span>
                </>
              ) : (
                "Save Changes"
              )}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  )
}
