"use client";

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Loader2 } from "lucide-react";
import type { Location } from "@/modules/location/model";
import type { ExtendedLocation } from "../locations/location-details-client";
import { LocationMapPreview } from "./location-create/location-map-preview";

interface LocationMapProps {
  location: Location | ExtendedLocation;
  zoom?: number;
}

export function LocationMap({ location, zoom = 14 }: LocationMapProps) {
  // Extract coordinates from location object
  let latitude = 0;
  let longitude = 0;
  let hasValidCoordinates = false;
  
  // Check if coordinates exist and extract them
  if (location.coordinates) {
    console.log("Raw coordinates data:", location.coordinates);
    
    // Handle different possible formats of coordinates
    if (typeof location.coordinates === 'object') {
      // Format 1: { latitude: number, longitude: number }
      if (location.coordinates && 'latitude' in location.coordinates && 'longitude' in location.coordinates) {
        const coords = location.coordinates as { latitude: number | string, longitude: number | string };
        latitude = Number(coords.latitude);
        longitude = Number(coords.longitude);
      }
      // Format 2: PostGIS point format from database
      else if (location.coordinates && 'x' in location.coordinates && 'y' in location.coordinates) {
        const coords = location.coordinates as { x: number | string, y: number | string };
        longitude = Number(coords.x);
        latitude = Number(coords.y);
      }
      // Format 3: Some other object format with lat/lng or similar
      else if (location.coordinates && 'lat' in location.coordinates && 'lng' in location.coordinates) {
        const coords = location.coordinates as { lat: number | string, lng: number | string };
        latitude = Number(coords.lat);
        longitude = Number(coords.lng);
      }
    }
    // Format 4: String format like "POINT(longitude latitude)" from PostGIS
    else if (typeof location.coordinates === 'string') {
      const coordString = location.coordinates as string;
      if (coordString.startsWith('POINT')) {
        const match = coordString.match(/POINT\(([^ ]+) ([^)]+)\)/);
        if (match && match.length === 3) {
          longitude = Number(match[1]);
          latitude = Number(match[2]);
        }
      }
    }
  }
  
  // Check if we have valid coordinates (both must be non-zero and valid numbers)
  hasValidCoordinates = 
    latitude !== 0 && 
    longitude !== 0 && 
    !Number.isNaN(latitude) && 
    !Number.isNaN(longitude) &&
    Math.abs(latitude) <= 90 && 
    Math.abs(longitude) <= 180;
  
  // Debug log to help diagnose coordinate issues
  console.log("Location coordinates:", {
    locationId: location.id,
    rawCoordinates: location.coordinates,
    extractedLatitude: latitude,
    extractedLongitude: longitude,
    isValid: hasValidCoordinates
  });

  return (
    <Card className="overflow-hidden">
      <CardHeader>
        <CardTitle>Location Map</CardTitle>
      </CardHeader>
      <CardContent className="p-0">
        {hasValidCoordinates ? (
          <LocationMapPreview latitude={latitude} longitude={longitude} zoom={zoom} />
        ) : (
          <div className="h-[400px] w-full relative flex items-center justify-center bg-muted/20">
            <div className="text-center">
              <Loader2 className="h-8 w-8 animate-spin text-muted-foreground mx-auto mb-4" />
              <p className="text-destructive text-sm">Invalid location coordinates</p>
              <p className="text-muted-foreground text-xs mt-2">
                Coordinates: {latitude}, {longitude}
              </p>
            </div>
          </div>
        )}
        
        <div className="border-t p-4">
          <h3 className="font-medium">{location.name}</h3>
          <p className="text-sm text-muted-foreground">
            {formatAddress(location.address)}
          </p>
          <p className="mt-2 text-sm">
            Coordinates: {hasValidCoordinates 
              ? `${latitude}, ${longitude}` 
              : "Not available"}
          </p>
        </div>
      </CardContent>
    </Card>
  );
}

function formatAddress(address: {
  street?: string;
  city?: string;
  state?: string;
  postalCode?: string;
  country?: string;
  formatted?: string;
} | string | undefined): string {
  if (!address) return "No address";
  
  if (typeof address === "string") return address;
  
  if (address.formatted) return address.formatted;
  
  const parts = [];
  if (address.street) parts.push(address.street);
  if (address.city) parts.push(address.city);
  if (address.state) parts.push(address.state);
  if (address.postalCode) parts.push(address.postalCode);
  if (address.country) parts.push(address.country);
  
  return parts.length > 0 ? parts.join(", ") : "No address";
}
