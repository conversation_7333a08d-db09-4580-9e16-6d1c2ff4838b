"use client"

import { useState } from "react"
import { useRouter } from "next/navigation"
import { useLocationDetails } from "@/hooks/use-location-details"
import { useLocationMutations } from "@/hooks/use-location-mutations"
import { usePermissions } from "@/hooks/use-permissions"
import { DashboardShell } from "@/components/dashboard/dashboard-shell"
import { But<PERSON> } from "@/components/ui/button"
import { <PERSON><PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import type { Location } from "@/modules/location/model"
import {
  Calendar,
  Edit,
  FileText,
  MoreHorizontal,
  Clock,
  Home,
  DollarSign,
  Ruler,
  Lightbulb,
  Wifi,
  ParkingMeterIcon as Parking,
  Truck,
  ShieldAlert,
  Share2,
  <PERSON>rash2,
  <PERSON><PERSON><PERSON><PERSON>,
  XCircle,
  <PERSON><PERSON><PERSON><PERSON>gle,
  <PERSON><PERSON>2
} from "lucide-react"
import { LocationGalleryContent } from "@/components/locations/location-gallery-content"
import { LocationMapContent } from "@/components/locations/location-map-content"
import { LocationDocumentsContent } from "@/components/locations/location-documents-content"
import { LocationNotesContent } from "@/components/locations/location-notes-content"
import { ShareLocationModal } from "@/components/locations/share-location-modal"
import { EditLocationModal } from "@/components/locations/edit-location-modal"
import { toast } from "sonner"
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog"

// Define the types expected by the components
export interface MediaItem {
  id: string;
  url: string;
  filename: string;
  type: "image" | "video" | "pdf" | "doc" | "document";
  thumbnailUrl?: string;
  uploadedBy: {
    id: string;
    name: string;
  };
  uploadedAt: string;
  fileSize?: number;
  metadata?: Record<string, unknown>;
}

interface Document {
  id: string;
  name: string;
  type: string;
  size: number;
  url: string;
  uploadedBy: {
    id: string;
    name: string;
  };
  uploadedAt: string;
}

interface Note {
  id: string;
  content: string;
  createdAt: string;
  createdBy: {
    id: string;
    name: string;
    avatar?: string;
  };
}

// Adapter functions to transform our data to match component expectations
// Define a type that matches what LocationGalleryContent expects
type GalleryMediaItem = {
  id: string;
  url: string;
  filename: string;
  type: "image" | "video";
  thumbnailUrl?: string;
  uploadedBy: {
    id: string;
    name: string;
  };
  uploadedAt: string;
};


/**
 * Adapts media items specifically for the gallery component
 * @param mediaItems The media items from the API response
 * @returns Media items formatted for the gallery component
 */
function adaptMediaItemsForGallery(mediaItems: LocationMedia[] = []): GalleryMediaItem[] {
  if (!mediaItems || !Array.isArray(mediaItems)) {
    return [];
  }

  // Filter to only include images and videos for the gallery
  const galleryCompatibleItems = mediaItems.filter(item => {
    const type = item.type || '';
    return type.includes('image') || type.includes('video') || 
           item.url?.match(/\.(jpg|jpeg|png|gif|webp|mp4|webm|ogg)$/i);
  });

  return galleryCompatibleItems.map(item => {
    // Determine the media type - for gallery, we only support image and video
    let type: "image" | "video" = "image"; // Default to image
    if (item.type?.includes('video')) {
      type = "video";
    } else if (item.url?.match(/\.(mp4|webm|ogg)$/i)) {
      type = "video";
    }

    // Create the adapted item
    return {
      id: item.id || `temp-${Math.random().toString(36).substring(2, 11)}`,
      url: item.url || '',
      filename: item.title || 'Untitled',
      type,
      thumbnailUrl: item.thumbnailUrl || undefined,
      uploadedBy: {
        id: item.uploader?.id || item.uploadedBy || 'unknown',
        name: item.uploader?.name || (item.uploadedBy ? `User ${item.uploadedBy?.substring(0, 8)}` : 'Unknown User')
      },
      uploadedAt: item.uploadedAt ? new Date(item.uploadedAt).toISOString() : new Date().toISOString()
    };
  }).filter(Boolean) as GalleryMediaItem[]; // Filter out null items
}

/**
 * Adapts the media items to document items for the LocationDocumentsContent component
 * @param mediaItems The media items from the API response
 * @returns The adapted document items
 */
function adaptDocuments(mediaItems: LocationMedia[] = []): Document[] {
  if (!mediaItems || !Array.isArray(mediaItems)) {
    console.warn('adaptDocuments received invalid input:', mediaItems);
    return [];
  }

  // Filter for document types (pdf, doc, etc.)
  return mediaItems
    .filter(item => {
      const type = item.type || '';
      return type === 'pdf' || type === 'doc' || type === 'document' || 
             item.url?.match(/\.(pdf|doc|docx|xls|xlsx|ppt|pptx|txt|csv|tsv)$/i);
    })
    .map(item => {
      // Determine file type and preview availability
      const url = item.url || '';
      let fileType = 'unknown';
      let previewAvailable = false;
      let previewUrl = url;

      if (url.match(/\.pdf$/i)) {
        fileType = 'pdf';
        previewAvailable = true;
        // PDF can be previewed directly
      } else if (url.match(/\.(doc|docx)$/i)) {
        fileType = url.match(/\.docx$/i) ? 'docx' : 'doc';
        // Use Google Docs Viewer for Office documents
        previewAvailable = true;
        previewUrl = `https://docs.google.com/viewer?url=${encodeURIComponent(url)}&embedded=true`;
      } else if (url.match(/\.(xls|xlsx|csv|tsv)$/i)) {
        fileType = url.match(/\.xlsx$/i) ? 'xlsx' : 
                  url.match(/\.csv$/i) ? 'csv' : 
                  url.match(/\.tsv$/i) ? 'tsv' : 'xls';
        previewAvailable = true;
        previewUrl = `https://docs.google.com/viewer?url=${encodeURIComponent(url)}&embedded=true`;
      } else if (url.match(/\.(ppt|pptx)$/i)) {
        fileType = url.match(/\.pptx$/i) ? 'pptx' : 'ppt';
        previewAvailable = true;
        previewUrl = `https://docs.google.com/viewer?url=${encodeURIComponent(url)}&embedded=true`;
      } else if (url.match(/\.txt$/i)) {
        fileType = 'txt';
        previewAvailable = true;
        // Text files can be previewed directly
      } else if (url.match(/\.(jpg|jpeg|png|gif|webp|svg)$/i)) {
        // Handle image files that might be categorized as documents
        fileType = 'image';
        previewAvailable = true;
        // Images can be previewed directly
      }

      // Create the adapted document item
      return {
        id: item.id || `temp-${Math.random().toString(36).substring(2, 11)}`,
        name: item.title || 'Untitled',
        url: url,
        previewUrl: previewAvailable ? previewUrl : undefined,
        type: fileType || item.type || 'document',
        size: item.fileSize || 0,
        uploadedBy: {
          id: item.uploader?.id || item.uploadedBy || 'unknown',
          name: item.uploader?.name || (item.uploadedBy ? `User ${item.uploadedBy?.substring(0, 8)}` : 'Unknown User')
        },
        uploadedAt: item.uploadedAt ? new Date(item.uploadedAt).toISOString() : new Date().toISOString(),
        fileType,
        previewAvailable
      };
    });
}

function adaptNotes(notes: LocationNote[] = []): Note[] {
  return notes.map(note => ({
    id: note.id,
    content: note.content,
    createdAt: note.createdAt || new Date().toISOString(),
    createdBy: {
      id: "unknown",
      name: note.createdBy || "Unknown User"
    }
  }));
}

// Extended Location type with additional UI-specific properties
export interface ExtendedLocation extends Location {
  // UI display properties
  image?: string;
  size?: string; // Maps to locationSize in the base model
  ceilingHeight?: string;
  powerAvailable?: string;
  wifiAvailable?: string;
  parking?: string; // Maps to parkingInfo in the base model
  loadingAccess?: string;
  
  // Related data
  media?: LocationMedia[];
  documents?: LocationDocument[];
  notes?: LocationNote[];
  schedule?: LocationScheduleEvent[];
  project?: {
    id: string;
    name: string;
  };
  sceneInfo?: {
    scenes: string;
    description: string;
  };
}

// Type guard to check if a location is an ExtendedLocation with required properties
function isExtendedLocation(location: Record<string, unknown> | Location | undefined): location is ExtendedLocation {
  return location !== null && typeof location === 'object' && 'id' in location;
}

export interface LocationMedia {
  id: string;
  locationId: string;
  url: string;
  thumbnailUrl?: string;
  title?: string;
  type: string;
  uploadedBy?: string;
  uploadedAt: Date;
  metadata?: Record<string, unknown>;
  fileSize?: number;
  width?: number;
  height?: number;
  isPublic: boolean;
  ordering: number;
  uploader?: {
    id: string;
    name: string;
  };
}

export interface LocationDocument {
  id: string;
  name: string;
  url: string;
  type: string;
  size?: number;
  uploadedAt?: string;
}

export interface LocationNote {
  id: string;
  content: string;
  createdBy?: string;
  createdAt?: string;
}

export interface LocationScheduleEvent {
  title: string;
  date: string;
  time: string;
  status: string;
}

interface LocationDetailsClientProps {
  locationId: string;
  organizationSlug: string;
  isSharedView?: boolean;
  viewMode?: string;
  location?: ExtendedLocation; // Used for shared view
}

// Helper function to format address object
function formatAddress(address: {
  street?: string;
  city?: string;
  state?: string;
  country?: string;
  formatted?: string;
} | string | undefined): string {
  if (!address) return "No address";
  
  if (typeof address === "string") return address;
  
  if (address.formatted) return address.formatted;
  
  if (address.city && address.state) {
    return `${address.city}, ${address.state}`;
  }
  if (address.city) {
    return address.city;
  }
  if (address.state) {
    return address.state;
  }
  if (address.country) {
    return address.country;
  }
  return "No address";
}

export function LocationDetailsClient({ 
  locationId, 
  organizationSlug,
  isSharedView = false,
  viewMode: initialViewMode,
  location: sharedLocation
}: LocationDetailsClientProps) {
  const router = useRouter()
  const { userRoles } = usePermissions()
  
  // State for view mode (admin or client)
  const [viewMode, setViewMode] = useState(initialViewMode || "client")
  
  // State for modals
  const [isShareModalOpen, setIsShareModalOpen] = useState(false)
  const [isEditModalOpen, setIsEditModalOpen] = useState(false)
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false)
  const [isApproveDialogOpen, setIsApproveDialogOpen] = useState(false)
  const [isRejectDialogOpen, setIsRejectDialogOpen] = useState(false)
  
  // Fetch location data if not in shared view
  const { 
    data: location, 
    isLoading, 
    isError, 
    error,
    refetch 
  } = useLocationDetails(isSharedView ? null : locationId)
  
  // Use the location mutations hook
  const { 
    deleteLocation, 
    approveLocation, 
    rejectLocation 
  } = useLocationMutations()
  
  // Determine if user has admin permissions - temporarily set all to true for testing
  const canEdit = true // hasRole("admin") || hasRole("location_manager")
  const canApprove = true // hasRole("admin") || hasRole("location_approver")
  const canDelete = true // hasRole("admin") || hasRole("location_manager")
  const canShare = true // hasAnyRole(["admin", "location_manager", "location_viewer"])
  
  // Log user roles for debugging
  console.log("User roles:", userRoles)
  
  // Use shared location data if in shared view
  const locationData: ExtendedLocation | undefined = isSharedView 
    ? (sharedLocation && isExtendedLocation(sharedLocation) ? sharedLocation : undefined)
    : (location && isExtendedLocation(location) ? location : undefined)
  
  // Handle view mode toggle
  const toggleViewMode = () => {
    setViewMode(viewMode === "admin" ? "client" : "admin")
  }
  
  // Handle edit location - opens the modal
  const handleEditLocation = (e: React.MouseEvent) => {
    // Prevent any default navigation behavior
    if (e) {
      e.preventDefault()
      e.stopPropagation()
    }
    
    // Open the edit modal
    setIsEditModalOpen(true)
  }
  
  // Handle delete location
  const handleDeleteLocation = async () => {
    try {
      await deleteLocation.mutateAsync(locationId)
      router.push(`/organizations/${organizationSlug}/locations`)
    } catch (error) {
      console.error("Error deleting location:", error)
    }
  }
  
  // Handle approve location
  const handleApproveLocation = async () => {
    try {
      await approveLocation.mutateAsync(locationId)
      setIsApproveDialogOpen(false)
      toast.success("Location approved successfully")
    } catch (error) {
      console.error("Error approving location:", error)
    }
  }
  
  // Handle reject location
  const handleRejectLocation = async () => {
    try {
      await rejectLocation.mutateAsync({ id: locationId })
      setIsRejectDialogOpen(false)
      toast.success("Location rejected successfully")
    } catch (error) {
      console.error("Error rejecting location:", error)
    }
  }
  
  // If loading, show skeleton loader
  if (isLoading && !isSharedView) {
    return (
      <DashboardShell>
        <div className="flex justify-between items-center mb-6">
          <div className="space-y-2">
            <div className="h-8 w-64 bg-gray-200 animate-pulse rounded" />
            <div className="h-4 w-48 bg-gray-200 animate-pulse rounded" />
          </div>
          <div className="flex items-center gap-2">
            <div className="h-10 w-32 bg-gray-200 animate-pulse rounded" />
            <div className="h-10 w-32 bg-gray-200 animate-pulse rounded" />
          </div>
        </div>
        
        <div className="grid gap-6 md:grid-cols-7">
          <div className="md:col-span-5 space-y-6">
            <div className="h-[400px] bg-gray-200 animate-pulse rounded" />
            <div className="h-[300px] bg-gray-200 animate-pulse rounded" />
          </div>
          <div className="md:col-span-2 space-y-6">
            <div className="h-[200px] bg-gray-200 animate-pulse rounded" />
            <div className="h-[200px] bg-gray-200 animate-pulse rounded" />
            <div className="h-[200px] bg-gray-200 animate-pulse rounded" />
          </div>
        </div>
      </DashboardShell>
    )
  }
  
  // If error, show error message
  if (isError && !isSharedView) {
    return (
      <DashboardShell>
        <div className="flex flex-col items-center justify-center py-12">
          <AlertTriangle className="h-12 w-12 text-destructive mb-4" />
          <h2 className="text-2xl font-bold mb-2">Error Loading Location</h2>
          <p className="text-muted-foreground mb-6">
            {error instanceof Error ? error.message : "Failed to load location details"}
          </p>
          <Button onClick={() => router.push(`/organizations/${organizationSlug}/locations`)}>
            Back to Locations
          </Button>
        </div>
      </DashboardShell>
    )
  }
  
  // If no location data, show empty state
  if (!locationData && !isLoading && !isSharedView) {
    return (
      <DashboardShell>
        <div className="flex flex-col items-center justify-center py-12">
          <h2 className="text-2xl font-bold mb-2">Location Not Found</h2>
          <p className="text-muted-foreground mb-6">
            The location you&apos;re looking for doesn&apos;t exist or you don&apos;t have permission to view it.
          </p>
          <Button onClick={() => router.push(`/organizations/${organizationSlug}/locations`)}>
            Back to Locations
          </Button>
        </div>
      </DashboardShell>
    )
  }
  
  // If we have location data, render the location details
  return (
    <>
      <div className="flex justify-between items-center mb-6">
        <div>
          <h1 className="text-2xl font-bold tracking-tight">{locationData?.name}</h1>
          <p className="text-muted-foreground">
            {locationData?.address?.formatted || 
             (typeof locationData?.address === 'object' ? 
              formatAddress(locationData?.address) : 
              locationData?.address || 'No address')}
          </p>
        </div>
        <div className="flex items-center gap-2">
          {!isSharedView && canEdit && (
            <Button variant="outline" onClick={toggleViewMode}>
              <ShieldAlert className="mr-2 h-4 w-4" />
              {viewMode === "admin" ? "Switch to Client View" : "Switch to Admin View"}
            </Button>
          )}
          
          {!isSharedView && canShare && (
            <Button variant="outline" onClick={() => setIsShareModalOpen(true)}>
              <Share2 className="mr-2 h-4 w-4" />
              Share
            </Button>
          )}
          
          {!isSharedView && canEdit && (
            <Button variant="outline" onClick={handleEditLocation}>
              <Edit className="mr-2 h-4 w-4" />
              Edit
            </Button>
          )}
          
          {!isSharedView && canDelete && (
            <Button variant="outline" onClick={() => setIsDeleteDialogOpen(true)}>
              <Trash2 className="mr-2 h-4 w-4" />
              Delete
            </Button>
          )}
          
          {!isSharedView && canApprove && locationData?.status === "pending" && (
            <>
              <Button variant="outline" onClick={() => setIsApproveDialogOpen(true)}>
                <CheckCircle className="mr-2 h-4 w-4" />
                Approve
              </Button>
              <Button variant="outline" onClick={() => setIsRejectDialogOpen(true)}>
                <XCircle className="mr-2 h-4 w-4" />
                Reject
              </Button>
            </>
          )}
          
          {!isSharedView && (
            <Button variant="outline" size="icon">
              <MoreHorizontal className="h-4 w-4" />
            </Button>
          )}
        </div>
      </div>
      
      {viewMode === "client" && !isSharedView && (
        <div className="bg-blue-50 border border-blue-200 text-blue-700 px-4 py-2 rounded-md mb-6 flex items-center">
          <ShieldAlert className="h-4 w-4 mr-2" />
          <p className="text-sm">You are viewing the client version of this location. Some sensitive information is hidden.</p>
        </div>
      )}

      <div className="grid gap-6 md:grid-cols-7">
        <div className="md:col-span-5 space-y-6">
          <Card>
            <div className="flex flex-col">
              <Tabs defaultValue="gallery" className="w-full">
                <div className="flex justify-between items-center px-4 pt-4">
                  <TabsList className="h-9 bg-background shadow-sm">
                    <TabsTrigger value="gallery" className="data-[state=active]:bg-primary/10 data-[state=active]:text-primary data-[state=active]:shadow-sm">Gallery</TabsTrigger>
                    <TabsTrigger value="map" className="data-[state=active]:bg-primary/10 data-[state=active]:text-primary data-[state=active]:shadow-sm">Map</TabsTrigger>
                    <TabsTrigger value="documents" className="data-[state=active]:bg-primary/10 data-[state=active]:text-primary data-[state=active]:shadow-sm">Documents</TabsTrigger>
                    <TabsTrigger value="notes" className="data-[state=active]:bg-primary/10 data-[state=active]:text-primary data-[state=active]:shadow-sm">Notes</TabsTrigger>
                  </TabsList>
                  <div className="flex items-center gap-2">
                    <Badge variant="outline">{locationData?.type}</Badge>
                    <Badge
                      variant={
                        locationData?.status === "approved" ? "default" : 
                        locationData?.status === "pending" ? "outline" : "secondary"
                      }
                      className="text-sm px-3 py-1"
                    >
                      {locationData?.status === "approved" ? "Approved" : 
                       locationData?.status === "pending" ? "Pending" : 
                       locationData?.status === "rejected" ? "Rejected" : 
                       locationData?.status || "Unknown"}
                    </Badge>
                  </div>
                </div>
                
                <TabsContent value="gallery" className="m-0 border-0 p-0 h-[600px]">
                  <LocationGalleryContent 
                    media={adaptMediaItemsForGallery(locationData?.media)} 
                  />
                </TabsContent>
                
                <TabsContent value="map" className="m-0 border-0 p-0 h-[600px]">
                  {locationData && <LocationMapContent location={locationData} />}
                </TabsContent>
                
                <TabsContent value="documents" className="m-0 border-0 p-0 h-[600px]">
                  <LocationDocumentsContent documents={adaptDocuments(locationData?.media)} />
                </TabsContent>
                
                <TabsContent value="notes" className="m-0 border-0 p-0 h-[600px]">
                  <LocationNotesContent notes={adaptNotes(locationData?.notes)} />
                </TabsContent>
              </Tabs>
              
              <CardHeader>
                <div className="flex justify-between items-start">
                  <div>
                    <CardTitle>{locationData?.name}</CardTitle>
                    <CardDescription>
                      {locationData?.address?.formatted || 
                       (typeof locationData?.address === 'object' ? 
                        formatAddress(locationData?.address) : 
                        locationData?.address || 'No address')}
                    </CardDescription>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <p>{locationData?.description}</p>

                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4 pt-2">
                    <div className="space-y-1">
                      <div className="flex items-center text-muted-foreground">
                        <DollarSign className="h-4 w-4 mr-1" />
                        <span className="text-sm">Daily Rate</span>
                      </div>
                      {(viewMode === "admin" || isSharedView && initialViewMode === "admin") ? (
                        <p className="font-medium">{locationData?.dailyRate}</p>
                      ) : (
                        <p className="font-medium text-muted-foreground">Contact for pricing</p>
                      )}
                    </div>
                    <div className="space-y-1">
                      <div className="flex items-center text-muted-foreground">
                        <Ruler className="h-4 w-4 mr-1" />
                        <span className="text-sm">Size</span>
                      </div>
                      <p className="font-medium">{locationData?.size}</p>
                    </div>
                    <div className="space-y-1">
                      <div className="flex items-center text-muted-foreground">
                        <Home className="h-4 w-4 mr-1" />
                        <span className="text-sm">Ceiling Height</span>
                      </div>
                      <p className="font-medium">{locationData?.ceilingHeight}</p>
                    </div>
                    <div className="space-y-1">
                      <div className="flex items-center text-muted-foreground">
                        <Lightbulb className="h-4 w-4 mr-1" />
                        <span className="text-sm">Power</span>
                      </div>
                      <p className="font-medium">{locationData?.powerAvailable}</p>
                    </div>
                    <div className="space-y-1">
                      <div className="flex items-center text-muted-foreground">
                        <Wifi className="h-4 w-4 mr-1" />
                        <span className="text-sm">WiFi</span>
                      </div>
                      <p className="font-medium">{locationData?.wifiAvailable}</p>
                    </div>
                    <div className="space-y-1">
                      <div className="flex items-center text-muted-foreground">
                        <Parking className="h-4 w-4 mr-1" />
                        <span className="text-sm">Parking</span>
                      </div>
                      <p className="font-medium">{locationData?.parking}</p>
                    </div>
                    <div className="space-y-1">
                      <div className="flex items-center text-muted-foreground">
                        <Truck className="h-4 w-4 mr-1" />
                        <span className="text-sm">Loading</span>
                      </div>
                      <p className="font-medium">{locationData?.loadingAccess}</p>
                    </div>
                    <div className="space-y-1">
                      <div className="flex items-center text-muted-foreground">
                        <Clock className="h-4 w-4 mr-1" />
                        <span className="text-sm">Restrictions</span>
                      </div>
                      <p className="font-medium">{locationData?.restrictions}</p>
                    </div>
                  </div>
                </div>
              </CardContent>
            </div>
          </Card>
        </div>

        <div className="md:col-span-2 space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Location Contact</CardTitle>
            </CardHeader>
            <CardContent>
              {(viewMode === "admin" || isSharedView && initialViewMode === "admin") ? (
                <div className="space-y-4">
                  <div className="flex items-center gap-4">
                    <Avatar className="h-10 w-10">
                      <AvatarImage src="/placeholder.svg?height=40&width=40" alt="Contact" />
                      <AvatarFallback>{locationData?.contactName?.split(' ').map((n: string) => n[0]).join('') || 'JD'}</AvatarFallback>
                    </Avatar>
                    <div>
                      <p className="font-medium">{locationData?.contactName}</p>
                      <p className="text-sm text-muted-foreground">Property Manager</p>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <p className="text-sm">{locationData?.contactPhone}</p>
                    <p className="text-sm">{locationData?.contactEmail}</p>
                  </div>

                  <Button variant="outline" className="w-full">
                    Contact
                  </Button>
                </div>
              ) : (
                <div className="space-y-4">
                  <div className="flex items-center justify-center p-4">
                    <div className="text-center">
                      <p className="text-muted-foreground mb-4">Contact information is only visible to administrators</p>
                      <Button variant="outline" className="w-full">
                        Request Contact Info
                      </Button>
                    </div>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Shooting Schedule</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {locationData?.schedule && locationData.schedule.length > 0 ? (
                locationData.schedule.map((event: LocationScheduleEvent, i: number) => (
                  <div key={`event-${i}-${event.title}`} className="border rounded-md p-3">
                    <div className="flex items-center gap-2 mb-1">
                      <Calendar className="h-4 w-4 text-primary" />
                      <p className="font-medium">{event.title}</p>
                    </div>
                    <p className="text-sm text-muted-foreground mb-1">{event.date} • {event.time}</p>
                    <Badge variant="outline">{event.status}</Badge>
                  </div>
                ))
              ) : (
                <div className="border rounded-md p-3">
                  <div className="flex items-center gap-2 mb-1">
                    <Calendar className="h-4 w-4 text-primary" />
                    <p className="font-medium">No scheduled events</p>
                  </div>
                  <p className="text-sm text-muted-foreground">This location has no scheduled shooting events.</p>
                </div>
              )}
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Project</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {locationData?.project ? (
                  <>
                    <div className="flex items-center gap-4">
                      <div className="h-10 w-10 rounded-md bg-primary/10 flex items-center justify-center">
                        <FileText className="h-5 w-5 text-primary" />
                      </div>
                      <div>
                        <p className="font-medium">{locationData.project?.name || "Unnamed Project"}</p>
                        <p className="text-sm text-muted-foreground">Active Project</p>
                      </div>
                    </div>

                    {locationData?.sceneInfo && (
                      <div className="border-t pt-4">
                        <p className="text-sm text-muted-foreground mb-1">Scene Information</p>
                        <p className="text-sm">{locationData.sceneInfo.scenes}</p>
                        <p className="text-sm">{locationData.sceneInfo.description}</p>
                      </div>
                    )}
                  </>
                ) : (
                  <div className="flex items-center justify-center p-4">
                    <div className="text-center">
                      <p className="text-muted-foreground mb-4">This location is not assigned to any project</p>
                      {(viewMode === "admin" || isSharedView && initialViewMode === "admin") && (
                        <Button variant="outline" className="w-full">
                          Assign to Project
                        </Button>
                      )}
                    </div>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
      
      {/* Share Location Modal */}
      {isShareModalOpen && (
        <ShareLocationModal
          isOpen={isShareModalOpen}
          onClose={() => setIsShareModalOpen(false)}
          locationId={locationId}
          locationName={locationData?.name || ""}
        />
      )}
      
      {/* Edit Location Modal */}
      {isEditModalOpen && (
        <EditLocationModal
          isOpen={isEditModalOpen}
          onClose={() => {
            setIsEditModalOpen(false)
            // Refetch location data to update the UI with the latest changes
            if (!isSharedView) {
              setTimeout(() => {
                // Use a small timeout to ensure the modal is fully closed before refetching
                // This prevents UI jank and ensures the latest data is fetched
                // Refetch the location data to update the UI with the latest changes
                refetch()
              }, 300)
            }
          }}
          locationId={locationId}
          organizationSlug={organizationSlug}
        />
      )}
      
      {/* Delete Confirmation Dialog */}
      <AlertDialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Are you sure you want to delete this location?</AlertDialogTitle>
            <AlertDialogDescription>
              This action cannot be undone. This will permanently delete the location
              and all associated data.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction 
              onClick={handleDeleteLocation}
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
            >
              {deleteLocation.isPending ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Deleting...
                </>
              ) : (
                "Delete"
              )}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
      
      {/* Approve Confirmation Dialog */}
      <AlertDialog open={isApproveDialogOpen} onOpenChange={setIsApproveDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Approve this location?</AlertDialogTitle>
            <AlertDialogDescription>
              This will mark the location as approved and make it available for use in projects.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction onClick={handleApproveLocation}>
              {approveLocation.isPending ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Approving...
                </>
              ) : (
                "Approve"
              )}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
      
      {/* Reject Confirmation Dialog */}
      <AlertDialog open={isRejectDialogOpen} onOpenChange={setIsRejectDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Reject this location?</AlertDialogTitle>
            <AlertDialogDescription>
              This will mark the location as rejected. You can provide a reason for the rejection.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction 
              onClick={handleRejectLocation}
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
            >
              {rejectLocation.isPending ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Rejecting...
                </>
              ) : (
                "Reject"
              )}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  )
}
