"use client"

import { useState } from "react"
import { useRouter } from "next/navigation"
import { useLocationDetails } from "@/hooks/use-location-details"
import { useLocationMutations } from "@/hooks/use-location-mutations"
import { usePermissions } from "@/hooks/use-permissions"
import { DashboardShell } from "@/components/dashboard/dashboard-shell"
import { Button } from "@/components/ui/button"
import { <PERSON><PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import type { Location } from "@/modules/location/model"
import {
  Calendar,
  Edit,
  FileText,
  MoreHorizontal,
  Clock,
  Home,
  DollarSign,
  Ruler,
  Lightbulb,
  Wifi,
  ParkingMeterIcon as Parking,
  Truck,
  ShieldAlert,
  Share2,
  <PERSON>rash2,
  Check<PERSON><PERSON><PERSON>,
  XCircle,
  Alert<PERSON><PERSON>gle,
  <PERSON>ader2
} from "lucide-react"
import { LocationGallery } from "@/components/locations/location-gallery"
import { LocationMap } from "@/components/locations/location-map"
import { LocationDocuments } from "@/components/locations/location-documents"
import { LocationNotes } from "@/components/locations/location-notes"
import { ShareLocationModal } from "@/components/locations/share-location-modal"
import { toast } from "sonner"
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog"
import Image from "next/image"

// Define the types expected by the components
interface MediaItem {
  id: string;
  url: string;
  filename: string;
  type: "image" | "video";
  thumbnailUrl?: string;
  uploadedBy: {
    id: string;
    name: string;
  };
  uploadedAt: string;
}

interface Document {
  id: string;
  name: string;
  type: string;
  size: number;
  url: string;
  uploadedBy: {
    id: string;
    name: string;
  };
  uploadedAt: string;
}

interface Note {
  id: string;
  content: string;
  createdAt: string;
  createdBy: {
    id: string;
    name: string;
    avatar?: string;
  };
}

// Adapter functions to transform our data to match component expectations
function adaptMediaItems(mediaItems: LocationMedia[] = []): MediaItem[] {
  return mediaItems.map(item => ({
    id: item.id,
    url: item.url,
    filename: item.title || "Untitled",
    type: item.type.includes("image") ? "image" : "video",
    thumbnailUrl: item.thumbnailUrl,
    uploadedBy: {
      id: "unknown",
      name: "Unknown User"
    },
    uploadedAt: new Date().toISOString() // Fallback if not available
  }));
}

function adaptDocuments(documents: LocationDocument[] = []): Document[] {
  return documents.map(doc => ({
    id: doc.id,
    name: doc.name,
    url: doc.url,
    type: doc.type,
    size: doc.size || 0, // Provide default size if not available
    uploadedBy: {
      id: "unknown",
      name: "Unknown User"
    },
    uploadedAt: doc.uploadedAt || new Date().toISOString()
  }));
}

function adaptNotes(notes: LocationNote[] = []): Note[] {
  return notes.map(note => ({
    id: note.id,
    content: note.content,
    createdAt: note.createdAt || new Date().toISOString(),
    createdBy: {
      id: "unknown",
      name: note.createdBy || "Unknown User"
    }
  }));
}

// Extended Location type with additional UI-specific properties
export interface ExtendedLocation extends Location {
  // UI display properties
  image?: string;
  size?: string; // Maps to locationSize in the base model
  ceilingHeight?: string;
  powerAvailable?: string;
  wifiAvailable?: string;
  parking?: string; // Maps to parkingInfo in the base model
  loadingAccess?: string;
  
  // Related data
  media?: LocationMedia[];
  documents?: LocationDocument[];
  notes?: LocationNote[];
  schedule?: LocationScheduleEvent[];
  project?: {
    id: string;
    name: string;
  };
  sceneInfo?: {
    scenes: string;
    description: string;
  };
}

// Type guard to check if a location is an ExtendedLocation with required properties
function isExtendedLocation(location: Record<string, unknown> | Location | undefined): location is ExtendedLocation {
  return location !== null && typeof location === 'object' && 'id' in location;
}

export interface LocationMedia {
  id: string;
  url: string;
  thumbnailUrl?: string;
  title?: string;
  type: string;
}

export interface LocationDocument {
  id: string;
  name: string;
  url: string;
  type: string;
  size?: number;
  uploadedAt?: string;
}

export interface LocationNote {
  id: string;
  content: string;
  createdBy?: string;
  createdAt?: string;
}

export interface LocationScheduleEvent {
  title: string;
  date: string;
  time: string;
  status: string;
}

interface LocationDetailsClientProps {
  locationId: string;
  organizationSlug: string;
  isSharedView?: boolean;
  viewMode?: string;
  location?: ExtendedLocation; // Used for shared view
}

// Helper function to format address object
function formatAddress(address: {
  street?: string;
  city?: string;
  state?: string;
  country?: string;
  formatted?: string;
} | string | undefined): string {
  if (!address) return "No address";
  
  if (typeof address === "string") return address;
  
  if (address.formatted) return address.formatted;
  
  if (address.city && address.state) {
    return `${address.city}, ${address.state}`;
  }
  if (address.city) {
    return address.city;
  }
  if (address.state) {
    return address.state;
  }
  if (address.country) {
    return address.country;
  }
  return "No address";
}

export function LocationDetailsClient({ 
  locationId, 
  organizationSlug,
  isSharedView = false,
  viewMode: initialViewMode,
  location: sharedLocation
}: LocationDetailsClientProps) {
  const router = useRouter()
  const { userRoles } = usePermissions()
  
  // State for view mode (admin or client)
  const [viewMode, setViewMode] = useState(initialViewMode || "client")
  
  // State for modals
  const [isShareModalOpen, setIsShareModalOpen] = useState(false)
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false)
  const [isApproveDialogOpen, setIsApproveDialogOpen] = useState(false)
  const [isRejectDialogOpen, setIsRejectDialogOpen] = useState(false)
  
  // Fetch location data if not in shared view
  const { 
    data: location, 
    isLoading, 
    isError, 
    error 
  } = useLocationDetails(isSharedView ? null : locationId)
  
  // Use the location mutations hook
  const { 
    deleteLocation, 
    approveLocation, 
    rejectLocation 
  } = useLocationMutations()
  
  // Determine if user has admin permissions - temporarily set all to true for testing
  const canEdit = true // hasRole("admin") || hasRole("location_manager")
  const canApprove = true // hasRole("admin") || hasRole("location_approver")
  const canDelete = true // hasRole("admin") || hasRole("location_manager")
  const canShare = true // hasAnyRole(["admin", "location_manager", "location_viewer"])
  
  // Log user roles for debugging
  console.log("User roles:", userRoles)
  
  // Use shared location data if in shared view
  const locationData: ExtendedLocation | undefined = isSharedView 
    ? (sharedLocation && isExtendedLocation(sharedLocation) ? sharedLocation : undefined)
    : (location && isExtendedLocation(location) ? location : undefined)
  
  // Handle view mode toggle
  const toggleViewMode = () => {
    setViewMode(viewMode === "admin" ? "client" : "admin")
  }
  
  // Handle edit location
  const handleEditLocation = () => {
    router.push(`/organizations/${organizationSlug}/locations/edit/${locationId}`)
  }
  
  // Handle delete location
  const handleDeleteLocation = async () => {
    try {
      await deleteLocation.mutateAsync(locationId)
      router.push(`/organizations/${organizationSlug}/locations`)
    } catch (error) {
      console.error("Error deleting location:", error)
    }
  }
  
  // Handle approve location
  const handleApproveLocation = async () => {
    try {
      await approveLocation.mutateAsync(locationId)
      setIsApproveDialogOpen(false)
      toast.success("Location approved successfully")
    } catch (error) {
      console.error("Error approving location:", error)
    }
  }
  
  // Handle reject location
  const handleRejectLocation = async () => {
    try {
      await rejectLocation.mutateAsync({ id: locationId })
      setIsRejectDialogOpen(false)
      toast.success("Location rejected successfully")
    } catch (error) {
      console.error("Error rejecting location:", error)
    }
  }
  
  // If loading, show skeleton loader
  if (isLoading && !isSharedView) {
    return (
      <DashboardShell>
        <div className="flex justify-between items-center mb-6">
          <div className="space-y-2">
            <div className="h-8 w-64 bg-gray-200 animate-pulse rounded" />
            <div className="h-4 w-48 bg-gray-200 animate-pulse rounded" />
          </div>
          <div className="flex items-center gap-2">
            <div className="h-10 w-32 bg-gray-200 animate-pulse rounded" />
            <div className="h-10 w-32 bg-gray-200 animate-pulse rounded" />
          </div>
        </div>
        
        <div className="grid gap-6 md:grid-cols-7">
          <div className="md:col-span-5 space-y-6">
            <div className="h-[400px] bg-gray-200 animate-pulse rounded" />
            <div className="h-[300px] bg-gray-200 animate-pulse rounded" />
          </div>
          <div className="md:col-span-2 space-y-6">
            <div className="h-[200px] bg-gray-200 animate-pulse rounded" />
            <div className="h-[200px] bg-gray-200 animate-pulse rounded" />
            <div className="h-[200px] bg-gray-200 animate-pulse rounded" />
          </div>
        </div>
      </DashboardShell>
    )
  }
  
  // If error, show error message
  if (isError && !isSharedView) {
    return (
      <DashboardShell>
        <div className="flex flex-col items-center justify-center py-12">
          <AlertTriangle className="h-12 w-12 text-destructive mb-4" />
          <h2 className="text-2xl font-bold mb-2">Error Loading Location</h2>
          <p className="text-muted-foreground mb-6">
            {error instanceof Error ? error.message : "Failed to load location details"}
          </p>
          <Button onClick={() => router.push(`/organizations/${organizationSlug}/locations`)}>
            Back to Locations
          </Button>
        </div>
      </DashboardShell>
    )
  }
  
  // If no location data, show empty state
  if (!locationData && !isLoading && !isSharedView) {
    return (
      <DashboardShell>
        <div className="flex flex-col items-center justify-center py-12">
          <h2 className="text-2xl font-bold mb-2">Location Not Found</h2>
          <p className="text-muted-foreground mb-6">
            The location you&apos;re looking for doesn&apos;t exist or you don&apos;t have permission to view it.
          </p>
          <Button onClick={() => router.push(`/organizations/${organizationSlug}/locations`)}>
            Back to Locations
          </Button>
        </div>
      </DashboardShell>
    )
  }
  
  // If we have location data, render the location details
  return (
    <>
      <div className="flex justify-between items-center mb-6">
        <div>
          <h1 className="text-2xl font-bold tracking-tight">{locationData?.name}</h1>
          <p className="text-muted-foreground">
            {locationData?.address?.formatted || 
             (typeof locationData?.address === 'object' ? 
              formatAddress(locationData?.address) : 
              locationData?.address || 'No address')}
          </p>
        </div>
        <div className="flex items-center gap-2">
          {!isSharedView && canEdit && (
            <Button variant="outline" onClick={toggleViewMode}>
              <ShieldAlert className="mr-2 h-4 w-4" />
              {viewMode === "admin" ? "Switch to Client View" : "Switch to Admin View"}
            </Button>
          )}
          
          {!isSharedView && canShare && (
            <Button variant="outline" onClick={() => setIsShareModalOpen(true)}>
              <Share2 className="mr-2 h-4 w-4" />
              Share
            </Button>
          )}
          
          {!isSharedView && canEdit && (
            <Button variant="outline" onClick={handleEditLocation}>
              <Edit className="mr-2 h-4 w-4" />
              Edit
            </Button>
          )}
          
          {!isSharedView && canDelete && (
            <Button variant="outline" onClick={() => setIsDeleteDialogOpen(true)}>
              <Trash2 className="mr-2 h-4 w-4" />
              Delete
            </Button>
          )}
          
          {!isSharedView && canApprove && locationData?.status === "pending" && (
            <>
              <Button variant="outline" onClick={() => setIsApproveDialogOpen(true)}>
                <CheckCircle className="mr-2 h-4 w-4" />
                Approve
              </Button>
              <Button variant="outline" onClick={() => setIsRejectDialogOpen(true)}>
                <XCircle className="mr-2 h-4 w-4" />
                Reject
              </Button>
            </>
          )}
          
          {!isSharedView && (
            <Button variant="outline" size="icon">
              <MoreHorizontal className="h-4 w-4" />
            </Button>
          )}
        </div>
      </div>
      
      {viewMode === "client" && !isSharedView && (
        <div className="bg-blue-50 border border-blue-200 text-blue-700 px-4 py-2 rounded-md mb-6 flex items-center">
          <ShieldAlert className="h-4 w-4 mr-2" />
          <p className="text-sm">You are viewing the client version of this location. Some sensitive information is hidden.</p>
        </div>
      )}

      <div className="grid gap-6 md:grid-cols-7">
        <div className="md:col-span-5 space-y-6">
          <Card>
            <div className="aspect-video relative">
              <Image
                src={locationData?.image || "/placeholder.svg"}
                alt={locationData?.name || "Location"}
                fill
                className="object-cover rounded-t-lg"
              />
              <div className="absolute top-4 right-4">
                <Badge
                  variant={
                    locationData?.status === "approved" ? "default" : 
                    locationData?.status === "pending" ? "outline" : "secondary"
                  }
                  className="text-sm px-3 py-1"
                >
                  {locationData?.status === "approved" ? "Approved" : 
                   locationData?.status === "pending" ? "Pending" : 
                   locationData?.status === "rejected" ? "Rejected" : 
                   locationData?.status || "Unknown"}
                </Badge>
              </div>
            </div>
            <CardHeader>
              <div className="flex justify-between items-start">
                <div>
                  <CardTitle>{locationData?.name}</CardTitle>
                  <CardDescription>
                    {locationData?.address?.formatted || 
                     (typeof locationData?.address === 'object' ? 
                      formatAddress(locationData?.address) : 
                      locationData?.address || 'No address')}
                  </CardDescription>
                </div>
                <Badge variant="outline">{locationData?.type}</Badge>
              </div>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <p>{locationData?.description}</p>

                <div className="grid grid-cols-2 md:grid-cols-4 gap-4 pt-2">
                  <div className="space-y-1">
                    <div className="flex items-center text-muted-foreground">
                      <DollarSign className="h-4 w-4 mr-1" />
                      <span className="text-sm">Daily Rate</span>
                    </div>
                    {(viewMode === "admin" || isSharedView && initialViewMode === "admin") ? (
                      <p className="font-medium">{locationData?.dailyRate}</p>
                    ) : (
                      <p className="font-medium text-muted-foreground">Contact for pricing</p>
                    )}
                  </div>
                  <div className="space-y-1">
                    <div className="flex items-center text-muted-foreground">
                      <Ruler className="h-4 w-4 mr-1" />
                      <span className="text-sm">Size</span>
                    </div>
                    <p className="font-medium">{locationData?.size}</p>
                  </div>
                  <div className="space-y-1">
                    <div className="flex items-center text-muted-foreground">
                      <Home className="h-4 w-4 mr-1" />
                      <span className="text-sm">Ceiling Height</span>
                    </div>
                    <p className="font-medium">{locationData?.ceilingHeight}</p>
                  </div>
                  <div className="space-y-1">
                    <div className="flex items-center text-muted-foreground">
                      <Lightbulb className="h-4 w-4 mr-1" />
                      <span className="text-sm">Power</span>
                    </div>
                    <p className="font-medium">{locationData?.powerAvailable}</p>
                  </div>
                  <div className="space-y-1">
                    <div className="flex items-center text-muted-foreground">
                      <Wifi className="h-4 w-4 mr-1" />
                      <span className="text-sm">WiFi</span>
                    </div>
                    <p className="font-medium">{locationData?.wifiAvailable}</p>
                  </div>
                  <div className="space-y-1">
                    <div className="flex items-center text-muted-foreground">
                      <Parking className="h-4 w-4 mr-1" />
                      <span className="text-sm">Parking</span>
                    </div>
                    <p className="font-medium">{locationData?.parking}</p>
                  </div>
                  <div className="space-y-1">
                    <div className="flex items-center text-muted-foreground">
                      <Truck className="h-4 w-4 mr-1" />
                      <span className="text-sm">Loading</span>
                    </div>
                    <p className="font-medium">{locationData?.loadingAccess}</p>
                  </div>
                  <div className="space-y-1">
                    <div className="flex items-center text-muted-foreground">
                      <Clock className="h-4 w-4 mr-1" />
                      <span className="text-sm">Restrictions</span>
                    </div>
                    <p className="font-medium">{locationData?.restrictions}</p>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          <Tabs defaultValue="gallery" className="space-y-4">
            <TabsList>
              <TabsTrigger value="gallery">Gallery</TabsTrigger>
              <TabsTrigger value="map">Map</TabsTrigger>
              <TabsTrigger value="documents">Documents</TabsTrigger>
              <TabsTrigger value="notes">Notes</TabsTrigger>
            </TabsList>
            <TabsContent value="gallery">
              <LocationGallery media={adaptMediaItems(locationData?.media)} />
            </TabsContent>
            <TabsContent value="map">
              {locationData && <LocationMap location={locationData} />}
            </TabsContent>
            <TabsContent value="documents">
              <LocationDocuments documents={adaptDocuments(locationData?.documents)} />
            </TabsContent>
            <TabsContent value="notes">
              <LocationNotes notes={adaptNotes(locationData?.notes)} />
            </TabsContent>
          </Tabs>
        </div>

        <div className="md:col-span-2 space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Location Contact</CardTitle>
            </CardHeader>
            <CardContent>
              {(viewMode === "admin" || isSharedView && initialViewMode === "admin") ? (
                <div className="space-y-4">
                  <div className="flex items-center gap-4">
                    <Avatar className="h-10 w-10">
                      <AvatarImage src="/placeholder.svg?height=40&width=40" alt="Contact" />
                      <AvatarFallback>{locationData?.contactName?.split(' ').map((n: string) => n[0]).join('') || 'JD'}</AvatarFallback>
                    </Avatar>
                    <div>
                      <p className="font-medium">{locationData?.contactName}</p>
                      <p className="text-sm text-muted-foreground">Property Manager</p>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <p className="text-sm">{locationData?.contactPhone}</p>
                    <p className="text-sm">{locationData?.contactEmail}</p>
                  </div>

                  <Button variant="outline" className="w-full">
                    Contact
                  </Button>
                </div>
              ) : (
                <div className="space-y-4">
                  <div className="flex items-center justify-center p-4">
                    <div className="text-center">
                      <p className="text-muted-foreground mb-4">Contact information is only visible to administrators</p>
                      <Button variant="outline" className="w-full">
                        Request Contact Info
                      </Button>
                    </div>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Shooting Schedule</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {locationData?.schedule && locationData.schedule.length > 0 ? (
                locationData.schedule.map((event: LocationScheduleEvent, i: number) => (
                  <div key={`event-${i}-${event.title}`} className="border rounded-md p-3">
                    <div className="flex items-center gap-2 mb-1">
                      <Calendar className="h-4 w-4 text-primary" />
                      <p className="font-medium">{event.title}</p>
                    </div>
                    <p className="text-sm text-muted-foreground mb-1">{event.date} • {event.time}</p>
                    <Badge variant="outline">{event.status}</Badge>
                  </div>
                ))
              ) : (
                <div className="border rounded-md p-3">
                  <div className="flex items-center gap-2 mb-1">
                    <Calendar className="h-4 w-4 text-primary" />
                    <p className="font-medium">No scheduled events</p>
                  </div>
                  <p className="text-sm text-muted-foreground">This location has no scheduled shooting events.</p>
                </div>
              )}
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Project</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {locationData?.project ? (
                  <>
                    <div className="flex items-center gap-4">
                      <div className="h-10 w-10 rounded-md bg-primary/10 flex items-center justify-center">
                        <FileText className="h-5 w-5 text-primary" />
                      </div>
                      <div>
                        <p className="font-medium">{locationData.project?.name || "Unnamed Project"}</p>
                        <p className="text-sm text-muted-foreground">Active Project</p>
                      </div>
                    </div>

                    {locationData?.sceneInfo && (
                      <div className="border-t pt-4">
                        <p className="text-sm text-muted-foreground mb-1">Scene Information</p>
                        <p className="text-sm">{locationData.sceneInfo.scenes}</p>
                        <p className="text-sm">{locationData.sceneInfo.description}</p>
                      </div>
                    )}
                  </>
                ) : (
                  <div className="flex items-center justify-center p-4">
                    <div className="text-center">
                      <p className="text-muted-foreground mb-4">This location is not assigned to any project</p>
                      {(viewMode === "admin" || isSharedView && initialViewMode === "admin") && (
                        <Button variant="outline" className="w-full">
                          Assign to Project
                        </Button>
                      )}
                    </div>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
      
      {/* Share Location Modal */}
      {isShareModalOpen && (
        <ShareLocationModal
          isOpen={isShareModalOpen}
          onClose={() => setIsShareModalOpen(false)}
          locationId={locationId}
          locationName={locationData?.name || ""}
        />
      )}
      
      {/* Delete Confirmation Dialog */}
      <AlertDialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Are you sure you want to delete this location?</AlertDialogTitle>
            <AlertDialogDescription>
              This action cannot be undone. This will permanently delete the location
              and all associated data.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction 
              onClick={handleDeleteLocation}
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
            >
              {deleteLocation.isPending ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Deleting...
                </>
              ) : (
                "Delete"
              )}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
      
      {/* Approve Confirmation Dialog */}
      <AlertDialog open={isApproveDialogOpen} onOpenChange={setIsApproveDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Approve this location?</AlertDialogTitle>
            <AlertDialogDescription>
              This will mark the location as approved and make it available for use in projects.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction onClick={handleApproveLocation}>
              {approveLocation.isPending ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Approving...
                </>
              ) : (
                "Approve"
              )}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
      
      {/* Reject Confirmation Dialog */}
      <AlertDialog open={isRejectDialogOpen} onOpenChange={setIsRejectDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Reject this location?</AlertDialogTitle>
            <AlertDialogDescription>
              This will mark the location as rejected. You can provide a reason for the rejection.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction 
              onClick={handleRejectLocation}
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
            >
              {rejectLocation.isPending ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Rejecting...
                </>
              ) : (
                "Reject"
              )}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  )
}
