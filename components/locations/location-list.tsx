"use client";

import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Skeleton } from "@/components/ui/skeleton";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Eye, ExternalLink, MapPin, Calendar } from "lucide-react";
import Link from "next/link";

import type { Location as LocationModel } from "@/modules/location/model";

interface Location {
  id: string;
  name: string;
  description?: string;
  status: string;
  type: string;
  address: {
    street?: string;
    city?: string;
    state?: string;
    country?: string;
    formatted?: string;
  };
  projectName?: string;
  projectId?: string;
  createdAt: string | Date;
}

type LocationListProps = {
  locations: LocationModel[] | Location[];
  organizationSlug: string;
  isLoading?: boolean;
};

export function LocationList({ 
  locations, 
  organizationSlug, 
  isLoading = false 
}: LocationListProps) {
  if (isLoading) {
    return (
      <div className="rounded-md border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Name</TableHead>
              <TableHead>Type</TableHead>
              <TableHead>Address</TableHead>
              <TableHead>Status</TableHead>
              <TableHead>Project</TableHead>
              <TableHead className="text-right">Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {/* Skeleton rows */}
            {["skeleton-1", "skeleton-2", "skeleton-3", "skeleton-4", "skeleton-5"].map((id) => (
              <TableRow key={id}>
                <TableCell><Skeleton className="h-5 w-[120px]" /></TableCell>
                <TableCell><Skeleton className="h-5 w-[80px]" /></TableCell>
                <TableCell><Skeleton className="h-5 w-[180px]" /></TableCell>
                <TableCell><Skeleton className="h-5 w-[70px]" /></TableCell>
                <TableCell><Skeleton className="h-5 w-[100px]" /></TableCell>
                <TableCell className="text-right"><Skeleton className="h-8 w-[60px] ml-auto" /></TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>
    );
  }

  return (
    <div className="rounded-md border">
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>Name</TableHead>
            <TableHead>Type</TableHead>
            <TableHead>Address</TableHead>
            <TableHead>Status</TableHead>
            <TableHead>Project</TableHead>
            <TableHead className="text-right">Actions</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {locations.length === 0 ? (
            <TableRow>
              <TableCell colSpan={6} className="h-24 text-center">
                <div className="flex flex-col items-center justify-center text-muted-foreground">
                  <MapPin className="h-8 w-8 mb-2" />
                  <p>No locations found</p>
                  <p className="text-sm">Try adjusting your filters or create a new location</p>
                </div>
              </TableCell>
            </TableRow>
          ) : (
            locations.map((location) => (
              <TableRow key={location.id}>
                <TableCell className="font-medium">
                  <div className="flex flex-col">
                    <span>{location.name}</span>
                    {location.description && (
                      <span className="text-xs text-muted-foreground line-clamp-1">
                        {location.description}
                      </span>
                    )}
                  </div>
                </TableCell>
                <TableCell>
                  <span className="capitalize">{location.type}</span>
                </TableCell>
                <TableCell>
                  <div className="flex items-start gap-1">
                    <MapPin className="h-3 w-3 mt-0.5 flex-shrink-0" />
                    <span className="text-sm line-clamp-2">{formatAddress(location.address)}</span>
                  </div>
                </TableCell>
                <TableCell>
                  <Badge variant={getStatusVariant(location.status)}>
                    {location.status.charAt(0).toUpperCase() + location.status.slice(1)}
                  </Badge>
                </TableCell>
                <TableCell>
                  {('projectId' in location && location.projectId) ? (
                    <Link
                      href={`/organizations/${organizationSlug}/projects/${location.projectId}`}
                      className="text-sm hover:underline flex items-center gap-1"
                    >
                      <span>
                        {'projectName' in location && location.projectName 
                          ? location.projectName 
                          : 'Project'}
                      </span>
                      <ExternalLink className="h-3 w-3" />
                    </Link>
                  ) : (
                    <span className="text-sm text-muted-foreground">
                      Not assigned
                    </span>
                  )}
                  {('createdAt' in location) && (
                    <div className="flex items-center text-xs text-muted-foreground mt-1">
                      <Calendar className="h-3 w-3 mr-1" />
                      <span>
                        {new Date(location.createdAt).toLocaleDateString(undefined, {
                          year: "numeric",
                          month: "short",
                          day: "numeric",
                        })}
                      </span>
                    </div>
                  )}
                </TableCell>
                <TableCell className="text-right">
                  <div className="flex justify-end gap-2">
                    <Link href={`/organizations/${organizationSlug}/locations/${location.id}`}>
                      <Button variant="ghost" size="sm">
                        <Eye className="h-4 w-4 mr-1" />
                        View
                      </Button>
                    </Link>
                  </div>
                </TableCell>
              </TableRow>
            ))
          )}
        </TableBody>
      </Table>
    </div>
  );
}

function formatAddress(address: {
  street?: string;
  city?: string;
  state?: string;
  country?: string;
  formatted?: string;
}): string {
  if (address.formatted) return address.formatted;
  
  const parts = [];
  if (address.street) parts.push(address.street);
  if (address.city) parts.push(address.city);
  if (address.state) parts.push(address.state);
  if (address.country) parts.push(address.country);
  
  return parts.join(", ") || "No address";
}

function getStatusVariant(status: string): "default" | "secondary" | "destructive" | "outline" {
  switch (status.toLowerCase()) {
    case "approved":
      return "default";
    case "pending":
      return "secondary";
    case "rejected":
      return "destructive";
    case "secured":
      return "default"; // We could use a custom variant for secured if available
    case "unavailable":
      return "outline";
    default:
      return "outline";
  }
}
