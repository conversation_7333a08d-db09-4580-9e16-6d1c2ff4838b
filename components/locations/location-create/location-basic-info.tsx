import { useState, useEffect, useCallback, useRef } from "react"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { Button } from "@/components/ui/button"
import { geocodeAddress, validateAddress } from "@/lib/utils/geocoding"
import { toast } from "sonner"
import { Loader2, MapPin, Check } from "lucide-react"

interface LocationBasicInfoProps {
  formData: {
    name: string;
    description: string;
    address: {
      street: string;
      city: string;
      state: string;
      country: string;
      postalCode: string;
    };
    coordinates: {
      latitude: number;
      longitude: number;
    };
    type: string;
    category?: string;
  };
  updateFormData: (data: Record<string, unknown>) => void;
}

export function LocationBasicInfo({ formData, updateFormData }: LocationBasicInfoProps) {
  const [isGeocoding, setIsGeocoding] = useState(false);
  const [geocodingSuccess, setGeocodingSuccess] = useState(false);
  const [addressSuggestion, setAddressSuggestion] = useState<string | null>(null);
  const [skipNextValidation, setSkipNextValidation] = useState(false);
  const lastSuggestedAddress = useRef<string | null>(null);
  
  // Function to build full address string
  const getFullAddress = useCallback(() => {
    const { street, city, state, country, postalCode } = formData.address;
    return [street, city, state, postalCode, country]
      .filter(part => part && part.trim() !== '')
      .join(', ');
  }, [formData.address]);
  
  // Function to handle geocoding
  const handleGeocode = useCallback(async () => {
    const fullAddress = getFullAddress();
    
    if (!fullAddress) {
      toast.error("Please enter address details to find coordinates");
      return;
    }
    
    setIsGeocoding(true);
    setGeocodingSuccess(false);
    
    try {
      const result = await geocodeAddress(fullAddress);
      
      // Update coordinates in form data
      updateFormData({
        coordinates: {
          latitude: result.latitude,
          longitude: result.longitude
        }
      });
      
      setGeocodingSuccess(true);
      toast.success("Address geocoded successfully");
    } catch (error) {
      console.error("Geocoding error:", error);
      toast.error(error instanceof Error ? error.message : "Failed to geocode address");
    } finally {
      setIsGeocoding(false);
    }
  }, [getFullAddress, updateFormData]);
  
  // Function to validate address and get suggestions
  const validateAddressInput = useCallback(async () => {
    // If we should skip this validation, reset the flag and return
    if (skipNextValidation) {
      setSkipNextValidation(false);
      return;
    }
    
    const fullAddress = getFullAddress();
    
    if (!fullAddress || fullAddress.length < 5) {
      return; // Don't validate very short addresses
    }
    
    try {
      const result = await validateAddress(fullAddress);
      
      if (result.isValid && result.formattedAddress) {
        // Check if this is the same as the last suggested address
        if (lastSuggestedAddress.current === result.formattedAddress) {
          return; // Don't show the same suggestion again
        }
        
        setAddressSuggestion(result.formattedAddress);
        
        // If we have coordinates, update them
        if (result.coordinates) {
          updateFormData({
            coordinates: {
              latitude: result.coordinates.latitude,
              longitude: result.coordinates.longitude
            }
          });
          setGeocodingSuccess(true);
        }
      }
    } catch (error) {
      console.error("Address validation error:", error);
      // Don't show toast for validation errors to avoid spamming the user
    }
  }, [getFullAddress, updateFormData, skipNextValidation]);
  
  // Apply address suggestion
  const applyAddressSuggestion = useCallback(() => {
    if (!addressSuggestion) return;
    
    // Parse the formatted address into components
    // This is a simple implementation and might need refinement based on address formats
    const parts = addressSuggestion.split(', ');
    
    if (parts.length >= 3) {
      // Attempt to extract components from the formatted address
      const street = parts[0];
      const city = parts[1];
      const statePostal = parts[2].split(' ');
      const state = statePostal[0];
      const postalCode = statePostal.length > 1 ? statePostal[1] : '';
      const country = parts.length > 3 ? parts[3] : '';
      
      updateFormData({
        address: {
          street,
          city,
          state,
          postalCode,
          country
        }
      });
      
      // Store the last suggested address to avoid showing the same suggestion again
      lastSuggestedAddress.current = addressSuggestion;
      
      // Skip the next validation to prevent the suggestion from reappearing
      setSkipNextValidation(true);
      
      setAddressSuggestion(null);
      toast.success("Address updated with suggestion");
    }
  }, [addressSuggestion, updateFormData]);
  
  // Auto-geocode when all address fields are filled
  useEffect(() => {
    const { street, city, state, country, postalCode } = formData.address;
    const allFieldsFilled = street && city && state && country && postalCode;
    
    // Only auto-geocode if all fields are filled and coordinates are not set
    if (allFieldsFilled && 
        formData.coordinates.latitude === 0 && 
        formData.coordinates.longitude === 0) {
      // Add a small delay to avoid geocoding while user is still typing
      const timer = setTimeout(() => {
        handleGeocode();
      }, 1000);
      
      return () => clearTimeout(timer);
    }
  }, [formData.address, formData.coordinates, handleGeocode]);
  
  // Validate address when user stops typing
  useEffect(() => {
    const timer = setTimeout(() => {
      validateAddressInput();
    }, 1500);
    
    return () => clearTimeout(timer);
  }, [validateAddressInput]);
  
  return (
    <div className="space-y-6">
      <div className="space-y-2">
        <Label htmlFor="locationName">
          Location Name <span className="text-destructive">*</span>
        </Label>
        <Input 
          id="locationName" 
          placeholder="Enter location name" 
          value={formData.name}
          onChange={(e) => updateFormData({ name: e.target.value })}
        />
      </div>

      {/* Address Section */}
      <div className="space-y-4 p-4 border rounded-md bg-muted/20">
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-medium">Address Information</h3>
          <Button 
            type="button" 
            variant="outline" 
            size="sm"
            onClick={handleGeocode}
            disabled={isGeocoding}
          >
            {isGeocoding ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Finding Coordinates...
              </>
            ) : (
              <>
                <MapPin className="mr-2 h-4 w-4" />
                Get Coordinates
              </>
            )}
          </Button>
        </div>

        {addressSuggestion && (
          <div className="bg-blue-50 p-3 rounded-md mb-3">
            <div className="flex justify-between items-center">
              <p className="text-sm text-blue-700">Suggested address:</p>
              <Button 
                type="button" 
                variant="ghost" 
                size="sm" 
                onClick={applyAddressSuggestion}
                className="h-8 text-blue-700"
              >
                <Check className="mr-1 h-4 w-4" />
                Use This
              </Button>
            </div>
            <p className="text-sm font-medium">{addressSuggestion}</p>
          </div>
        )}

        <div className="space-y-2">
          <Label htmlFor="address">
            Street Address <span className="text-destructive">*</span>
          </Label>
          <Input 
            id="address" 
            placeholder="Street address" 
            value={formData.address.street}
            onChange={(e) => updateFormData({ 
              address: { ...formData.address, street: e.target.value } 
            })}
          />
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="space-y-2">
            <Label htmlFor="city">
              City <span className="text-destructive">*</span>
            </Label>
            <Input 
              id="city" 
              placeholder="City" 
              value={formData.address.city}
              onChange={(e) => updateFormData({ 
                address: { ...formData.address, city: e.target.value } 
              })}
            />
          </div>
          <div className="space-y-2">
            <Label htmlFor="state">
              State/Province <span className="text-destructive">*</span>
            </Label>
            <Input 
              id="state" 
              placeholder="State/Province" 
              value={formData.address.state}
              onChange={(e) => updateFormData({ 
                address: { ...formData.address, state: e.target.value } 
              })}
            />
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="space-y-2">
            <Label htmlFor="country">
              Country <span className="text-destructive">*</span>
            </Label>
            <Input 
              id="country" 
              placeholder="Country" 
              value={formData.address.country}
              onChange={(e) => updateFormData({ 
                address: { ...formData.address, country: e.target.value } 
              })}
            />
          </div>
          <div className="space-y-2">
            <Label htmlFor="postalCode">
              Postal Code <span className="text-destructive">*</span>
            </Label>
            <Input 
              id="postalCode" 
              placeholder="Postal Code" 
              value={formData.address.postalCode}
              onChange={(e) => updateFormData({ 
                address: { ...formData.address, postalCode: e.target.value } 
              })}
            />
          </div>
        </div>

        {/* Coordinates Display */}
        <div className="mt-4 p-3 bg-muted rounded-md">
          <div className="flex justify-between items-center">
            <span className="text-sm font-medium">Coordinates:</span>
            {geocodingSuccess && (
              <span className="text-xs bg-green-100 text-green-800 px-2 py-1 rounded-full">
                Verified
              </span>
            )}
          </div>
          <div className="mt-1 grid grid-cols-2 gap-2">
            <div>
              <Label htmlFor="latitude" className="text-xs">Latitude</Label>
              <Input 
                id="latitude" 
                value={formData.coordinates.latitude.toString()}
                onChange={(e) => {
                  const value = Number.parseFloat(e.target.value);
                  if (!Number.isNaN(value)) {
                    updateFormData({
                      coordinates: {
                        ...formData.coordinates,
                        latitude: value
                      }
                    });
                  }
                }}
                className="bg-muted/50 text-sm"
              />
            </div>
            <div>
              <Label htmlFor="longitude" className="text-xs">Longitude</Label>
              <Input 
                id="longitude" 
                value={formData.coordinates.longitude.toString()}
                onChange={(e) => {
                  const value = Number.parseFloat(e.target.value);
                  if (!Number.isNaN(value)) {
                    updateFormData({
                      coordinates: {
                        ...formData.coordinates,
                        longitude: value
                      }
                    });
                  }
                }}
                className="bg-muted/50 text-sm"
              />
            </div>
          </div>
        </div>
      </div>

      <div className="space-y-2">
        <Label>Location Type</Label>
        <RadioGroup 
          value={formData.type} 
          onValueChange={(value) => updateFormData({ type: value })}
          className="flex flex-col space-y-1"
        >
          <div className="flex items-center space-x-2">
            <RadioGroupItem value="interior" id="interior" />
            <Label htmlFor="interior" className="font-normal">
              Interior
            </Label>
          </div>
          <div className="flex items-center space-x-2">
            <RadioGroupItem value="exterior" id="exterior" />
            <Label htmlFor="exterior" className="font-normal">
              Exterior
            </Label>
          </div>
          <div className="flex items-center space-x-2">
            <RadioGroupItem value="both" id="both" />
            <Label htmlFor="both" className="font-normal">
              Both Interior & Exterior
            </Label>
          </div>
        </RadioGroup>
      </div>

      <div className="space-y-2">
        <Label htmlFor="category">Category</Label>
        <Select 
          value={formData.category} 
          onValueChange={(value) => updateFormData({ category: value })}
        >
          <SelectTrigger id="category">
            <SelectValue placeholder="Select category" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="residential">Residential</SelectItem>
            <SelectItem value="commercial">Commercial</SelectItem>
            <SelectItem value="industrial">Industrial</SelectItem>
            <SelectItem value="public">Public Space</SelectItem>
            <SelectItem value="natural">Natural Setting</SelectItem>
            <SelectItem value="other">Other</SelectItem>
          </SelectContent>
        </Select>
      </div>

      <div className="space-y-2">
        <Label htmlFor="description">Description</Label>
        <Textarea
          id="description"
          placeholder="Provide a brief description of the location"
          className="min-h-[120px]"
          value={formData.description}
          onChange={(e) => updateFormData({ description: e.target.value })}
        />
      </div>
    </div>
  )
}
