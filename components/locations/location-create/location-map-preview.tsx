"use client"

import { useEffect, useRef, useState } from 'react';
import mapboxgl from 'mapbox-gl';
import 'mapbox-gl/dist/mapbox-gl.css';
import { Card } from "@/components/ui/card";
import { Loader2 } from "lucide-react";

interface LocationMapPreviewProps {
  latitude: number;
  longitude: number;
  zoom?: number;
}

export function LocationMapPreview({ latitude, longitude, zoom = 14 }: LocationMapPreviewProps) {
  const mapContainer = useRef<HTMLDivElement>(null);
  const map = useRef<mapboxgl.Map | null>(null);
  const marker = useRef<mapboxgl.Marker | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Add a resize handler to ensure the map fills the container
  useEffect(() => {
    const handleResize = () => {
      if (map.current) {
        // Trigger a resize event on the map to ensure it fills the container
        map.current.resize();
      }
    };

    // Add resize event listener
    window.addEventListener('resize', handleResize);

    // Clean up
    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, []);

  useEffect(() => {
    // Only initialize if we have valid coordinates
    if (!latitude || !longitude || (latitude === 0 && longitude === 0)) {
      setIsLoading(false);
      return;
    }

    // Fetch Mapbox token
    const fetchToken = async () => {
      try {
        setIsLoading(true);
        const response = await fetch('/api/mapbox-token');
        if (!response.ok) {
          throw new Error('Failed to fetch Mapbox token');
        }
        const { token } = await response.json();
        
        // Initialize map
        if (mapContainer.current && !map.current) {
          mapboxgl.accessToken = token;
          map.current = new mapboxgl.Map({
            container: mapContainer.current,
            style: 'mapbox://styles/mapbox/streets-v12',
            center: [longitude, latitude],
            zoom: zoom
          });

          // Add navigation controls
          map.current.addControl(new mapboxgl.NavigationControl(), 'top-right');
          
          // Add marker
          marker.current = new mapboxgl.Marker()
            .setLngLat([longitude, latitude])
            .addTo(map.current);
            
          // Set loading to false when map is loaded and ensure it fills the container
          map.current.on('load', () => {
            // Ensure the map fills the container
            if (map.current) {
              map.current.resize();
            }
            setIsLoading(false);
          });
        } else if (map.current) {
          // Update marker position
          if (marker.current) {
            marker.current.setLngLat([longitude, latitude]);
          }
          
          // Fly to new location
          map.current.flyTo({
            center: [longitude, latitude],
            zoom: zoom,
            essential: true
          });
          
          setIsLoading(false);
        }
      } catch (error) {
        console.error('Error initializing map:', error);
        setError(error instanceof Error ? error.message : 'Failed to initialize map');
        setIsLoading(false);
      }
    };

    fetchToken();

    // Cleanup
    return () => {
      if (map.current) {
        map.current.remove();
        map.current = null;
      }
    };
  }, [latitude, longitude, zoom]);

  return (
    <Card className="overflow-hidden w-full">
      {isLoading && (
        <div className="flex items-center justify-center h-[300px] md:h-[400px] w-full bg-muted/20">
          <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
        </div>
      )}
      
      {error && (
        <div className="flex items-center justify-center h-[300px] md:h-[400px] w-full bg-muted/20">
          <p className="text-destructive text-sm">{error}</p>
        </div>
      )}
      
      <div 
        ref={mapContainer} 
        className="w-full h-[300px] md:h-[400px]"
        style={{ 
          display: isLoading ? 'none' : 'block'
        }} 
      />
    </Card>
  );
}
