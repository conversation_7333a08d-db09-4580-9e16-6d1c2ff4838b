import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { Checkbox } from "@/components/ui/checkbox"
import { Textarea } from "@/components/ui/textarea"

export function LocationPermissions() {
  return (
    <div className="space-y-6">
      <div className="space-y-2">
        <Label>Assign to Project</Label>
        <Select>
          <SelectTrigger>
            <SelectValue placeholder="Select a project" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="urban">Urban Crime Drama</SelectItem>
            <SelectItem value="mountain">Mountain Documentary</SelectItem>
            <SelectItem value="commercial">Downtown Commercial</SelectItem>
            <SelectItem value="none">No Project</SelectItem>
          </SelectContent>
        </Select>
      </div>

      <div className="space-y-2">
        <Label>Location Visibility</Label>
        <RadioGroup defaultValue="team" className="flex flex-col space-y-1">
          <div className="flex items-center space-x-2">
            <RadioGroupItem value="team" id="team" />
            <Label htmlFor="team" className="font-normal">
              Visible to all team members
            </Label>
          </div>
          <div className="flex items-center space-x-2">
            <RadioGroupItem value="project" id="project" />
            <Label htmlFor="project" className="font-normal">
              Visible only to project members
            </Label>
          </div>
          <div className="flex items-center space-x-2">
            <RadioGroupItem value="custom" id="custom" />
            <Label htmlFor="custom" className="font-normal">
              Custom permissions
            </Label>
          </div>
        </RadioGroup>
      </div>

      <div className="space-y-2">
        <Label>Location Status</Label>
        <Select defaultValue="scouting">
          <SelectTrigger>
            <SelectValue placeholder="Select status" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="scouting">Scouting</SelectItem>
            <SelectItem value="pending">Pending Approval</SelectItem>
            <SelectItem value="approved">Approved</SelectItem>
            <SelectItem value="rejected">Rejected</SelectItem>
          </SelectContent>
        </Select>
      </div>

      <div className="space-y-2">
        <Label>Access Restrictions</Label>
        <div className="flex flex-col space-y-2">
          <div className="flex items-center space-x-2">
            <Checkbox id="restrictEdit" />
            <Label htmlFor="restrictEdit" className="font-normal">
              Only admins can edit this location
            </Label>
          </div>
          <div className="flex items-center space-x-2">
            <Checkbox id="restrictDelete" />
            <Label htmlFor="restrictDelete" className="font-normal">
              Only admins can delete this location
            </Label>
          </div>
          <div className="flex items-center space-x-2">
            <Checkbox id="restrictExport" />
            <Label htmlFor="restrictExport" className="font-normal">
              Prevent exporting location data
            </Label>
          </div>
        </div>
      </div>

      <div className="space-y-2">
        <Label htmlFor="permissionNotes">Permission Notes</Label>
        <Textarea
          id="permissionNotes"
          placeholder="Additional notes about permissions or access restrictions"
          className="min-h-[100px]"
        />
      </div>
    </div>
  )
}

