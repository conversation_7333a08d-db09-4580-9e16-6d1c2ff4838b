import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Switch } from "@/components/ui/switch"
import { Checkbox } from "@/components/ui/checkbox"

export function LocationDetails() {
  return (
    <div className="space-y-6">
      <div className="space-y-2">
        <Label htmlFor="contactName">Contact Name</Label>
        <Input id="contactName" placeholder="Property owner or manager name" />
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div className="space-y-2">
          <Label htmlFor="contactEmail">Contact Email</Label>
          <Input id="contactEmail" type="email" placeholder="Email address" />
        </div>
        <div className="space-y-2">
          <Label htmlFor="contactPhone">Contact Phone</Label>
          <Input id="contactPhone" placeholder="Phone number" />
        </div>
      </div>

      <div className="flex items-center space-x-2">
        <Switch id="permitRequired" />
        <Label htmlFor="permitRequired" className="font-normal">
          Permit required for filming
        </Label>
      </div>

      <div className="space-y-2">
        <Label htmlFor="permitDetails">Permit Details</Label>
        <Textarea
          id="permitDetails"
          placeholder="Information about required permits, restrictions, etc."
          className="min-h-[100px]"
        />
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div className="space-y-2">
          <Label htmlFor="costPerDay">Cost Per Day</Label>
          <div className="relative">
            <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
              <span className="text-muted-foreground">$</span>
            </div>
            <Input id="costPerDay" type="number" min="0" className="pl-7" placeholder="0.00" />
          </div>
        </div>
        <div className="space-y-2">
          <Label htmlFor="currency">Currency</Label>
          <Select defaultValue="usd">
            <SelectTrigger id="currency">
              <SelectValue placeholder="Select currency" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="usd">USD ($)</SelectItem>
              <SelectItem value="eur">EUR (€)</SelectItem>
              <SelectItem value="gbp">GBP (£)</SelectItem>
              <SelectItem value="cad">CAD ($)</SelectItem>
              <SelectItem value="aud">AUD ($)</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      <div className="space-y-2">
        <Label>Amenities Available</Label>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
          <div className="flex items-center space-x-2">
            <Checkbox id="power" />
            <Label htmlFor="power" className="font-normal">
              Power Available
            </Label>
          </div>
          <div className="flex items-center space-x-2">
            <Checkbox id="water" />
            <Label htmlFor="water" className="font-normal">
              Water Available
            </Label>
          </div>
          <div className="flex items-center space-x-2">
            <Checkbox id="restrooms" />
            <Label htmlFor="restrooms" className="font-normal">
              Restrooms
            </Label>
          </div>
          <div className="flex items-center space-x-2">
            <Checkbox id="parking" />
            <Label htmlFor="parking" className="font-normal">
              Parking
            </Label>
          </div>
          <div className="flex items-center space-x-2">
            <Checkbox id="wifi" />
            <Label htmlFor="wifi" className="font-normal">
              WiFi
            </Label>
          </div>
          <div className="flex items-center space-x-2">
            <Checkbox id="kitchen" />
            <Label htmlFor="kitchen" className="font-normal">
              Kitchen
            </Label>
          </div>
        </div>
      </div>

      <div className="space-y-2">
        <Label htmlFor="notes">Additional Notes</Label>
        <Textarea
          id="notes"
          placeholder="Any other important information about this location"
          className="min-h-[100px]"
        />
      </div>
    </div>
  )
}

