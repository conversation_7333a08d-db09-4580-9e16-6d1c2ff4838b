"use client";

import { useState } from "react";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { X, FileText, Upload, CheckCircle } from "lucide-react";
import { UploadDropzone, useUploadThing } from "@/lib/utils/uploadthing";
import Image from "next/image";
import { toast } from "sonner";

interface MediaItem {
  uploadedBy: string;
  url: string;
  name: string;
  size: number;
  key: string;
  type: string;
}

interface LocationMediaProps {
  locationId?: string;
}

export function LocationMedia({ locationId }: LocationMediaProps) {
  // organizationSlug is available from params if needed in the future, but not used here
  const [photos, setPhotos] = useState<MediaItem[]>([]);
  const [documents, setDocuments] = useState<MediaItem[]>([]);
  const [videoLink, setVideoLink] = useState("");
  const [virtualTourLink, setVirtualTourLink] = useState("");
  const [uploadingPhotos, setUploadingPhotos] = useState(false);
  const [uploadingDocuments, setUploadingDocuments] = useState(false);
  const [selectedPhotos, setSelectedPhotos] = useState<File[]>([]);
  const [selectedDocuments, setSelectedDocuments] = useState<File[]>([]);
  const [photoUploadProgress, setPhotoUploadProgress] = useState(0);
  const [documentUploadProgress, setDocumentUploadProgress] = useState(0);
  const [uploadComplete, setUploadComplete] = useState({
    photos: false,
    documents: false
  });

  // Get route config for photos
  const { routeConfig: photoRouteConfig } = useUploadThing("locationPhotos");
  const { routeConfig: documentRouteConfig } = useUploadThing("locationDocuments");

  const handleRemovePhoto = (key: string) => {
    setPhotos(photos.filter(photo => photo.key !== key));
    // If all photos are removed, reset upload complete state
    if (photos.length <= 1) {
      setUploadComplete(prev => ({ ...prev, photos: false }));
    }
  };

  const handleRemoveDocument = (key: string) => {
    setDocuments(documents.filter(doc => doc.key !== key));
    // If all documents are removed, reset upload complete state
    if (documents.length <= 1) {
      setUploadComplete(prev => ({ ...prev, documents: false }));
    }
  };

  // Set up headers for the upload request
  const getUploadHeaders = () => {
    const headers: Record<string, string> = {};
    if (locationId) {
      headers["x-location-id"] = locationId;
    }
    return headers;
  };

  return (
    <div className="space-y-6">
      <div className="space-y-2">
        <Label>Location Photos</Label>
        <UploadDropzone
          endpoint="locationPhotos"
          headers={getUploadHeaders()}
          onBeforeUploadBegin={(files) => {
            // Show the selected files before upload
            setSelectedPhotos(files);
            // You can rename files or perform other preprocessing here
            return files;
          }}
          onUploadBegin={() => {
            setUploadingPhotos(true);
            setPhotoUploadProgress(0);
            setUploadComplete(prev => ({ ...prev, photos: false }));
            // Clear selected files as they're now uploading
            setSelectedPhotos([]);
          }}
          onUploadProgress={(progress) => {
            setPhotoUploadProgress(progress);
          }}
          onClientUploadComplete={(res) => {
            setUploadingPhotos(false);
            setPhotoUploadProgress(100);
            setUploadComplete(prev => ({ ...prev, photos: true }));
            if (res) {
              // Create properly typed objects from the response
              const newPhotos = res.map(item => ({
                uploadedBy: item.serverData?.uploadedBy || "user",
                url: item.url,
                name: item.name,
                size: item.size,
                key: item.key,
                type: "image"
              }));
              setPhotos(prev => [...prev, ...newPhotos]);
              toast.success(`Successfully uploaded ${res.length} photo${res.length === 1 ? '' : 's'}`);
            }
          }}
          onUploadError={(error: Error) => {
            setUploadingPhotos(false);
            setPhotoUploadProgress(0);
            console.error("Error uploading photos:", error);
            toast.error(`Error uploading: ${error.message}`);
          }}
          className="border-2 border-dashed border-border-medium rounded-md p-6 transition-all
            hover:border-neon-green hover:shadow-[0_0_15px_rgba(22,255,0,0.5)] hover:bg-neon-green/5
            ut-label:text-base ut-label:font-medium ut-label:text-foreground
            ut-allowed-content:text-xs ut-allowed-content:text-muted-foreground
            ut-upload-icon:h-10 ut-upload-icon:w-10 ut-upload-icon:text-primary
            ut-button:bg-primary ut-button:text-primary-foreground ut-button:hover:bg-primary-dark
            ut-button:transition-all ut-button:duration-300 ut-button:rounded-md
            ut-button:px-4 ut-button:py-2 ut-button:text-sm ut-button:font-medium
            ut-uploading:border-neon-green ut-uploading:bg-neon-green/5 ut-uploading:shadow-[0_0_15px_rgba(22,255,0,0.5)]"
          content={{
            label: ({ ready }) => (
              <span className="flex flex-col items-center gap-1">
                <span className="text-base font-medium">
                  {!ready ? "Preparing upload..." : 
                   uploadComplete.photos ? "Photos uploaded successfully! Drop more here" : 
                   "Drag & drop photos here"}
                </span>
                <span className="text-xs text-muted-foreground">
                  {uploadComplete.photos ? "or click to add more photos" : "or click to browse"}
                </span>
                {uploadComplete.photos && (
                  <CheckCircle className="h-6 w-6 text-neon-green mt-1" />
                )}
              </span>
            )
          }}
          config={{
            mode: "auto", // Auto upload after selection
            appendOnPaste: true // Allow pasting images
          }}
        />
        <p className="text-xs text-muted-foreground">
          You can upload up to {photoRouteConfig?.image?.maxFileCount || 20} photos. 
          Maximum file size: {photoRouteConfig?.image?.maxFileSize || "8MB"} per image.
        </p>

        {selectedPhotos.length > 0 && (
          <div className="mt-4 p-3 border rounded-md bg-neon-green/5 border-neon-green">
            <h4 className="text-sm font-medium mb-2">Selected Photos ({selectedPhotos.length})</h4>
            <div className="space-y-1">
              {selectedPhotos.map((file) => (
                <div key={`${file.name}-${file.size}-${file.lastModified}`} className="flex items-center text-sm">
                  <FileText className="h-4 w-4 mr-2 text-neon-green" />
                  <span className="truncate">{file.name}</span>
                  <span className="ml-auto text-xs text-muted-foreground">{formatFileSize(file.size)}</span>
                </div>
              ))}
            </div>
            <p className="text-xs text-muted-foreground mt-2">Files will be uploaded automatically</p>
          </div>
        )}

        {photos.length > 0 && (
          <div className="mt-4">
            <h4 className="text-sm font-medium mb-2">Uploaded Photos ({photos.length})</h4>
            <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 gap-3">
              {photos.map((photo) => (
                <div key={photo.key} className="relative group">
                  <div className="aspect-square relative rounded-md overflow-hidden border">
                    <Image
                      src={photo.url}
                      alt={photo.name}
                      fill
                      className="object-cover"
                    />
                  </div>
                  <button
                    type="button"
                    onClick={() => handleRemovePhoto(photo.key)}
                    className="absolute -top-2 -right-2 bg-destructive text-white rounded-full p-1 shadow-md opacity-0 group-hover:opacity-100 transition-opacity"
                  >
                    <X className="h-4 w-4" />
                  </button>
                </div>
              ))}
            </div>
          </div>
        )}

        {uploadingPhotos && (
          <div className="mt-2 space-y-2">
            <div className="flex items-center gap-2 text-sm text-primary">
              <Upload className="h-4 w-4" />
              <span>Uploading photos... {photoUploadProgress}%</span>
            </div>
            <div className="w-full bg-muted rounded-full h-2 overflow-hidden">
              <div 
                className="bg-neon-green h-full transition-all duration-300 rounded-full shadow-[0_0_5px_rgba(22,255,0,0.5)]" 
                style={{ width: `${photoUploadProgress}%` }}
              />
            </div>
          </div>
        )}
      </div>

      <div className="space-y-2">
        <Label>Floor Plans & Documents</Label>
        <UploadDropzone
          endpoint="locationDocuments"
          headers={getUploadHeaders()}
          onBeforeUploadBegin={(files) => {
            // Show the selected files before upload
            setSelectedDocuments(files);
            // You can rename files or perform other preprocessing here
            return files;
          }}
          onUploadBegin={() => {
            setUploadingDocuments(true);
            setDocumentUploadProgress(0);
            setUploadComplete(prev => ({ ...prev, documents: false }));
            // Clear selected files as they're now uploading
            setSelectedDocuments([]);
          }}
          onUploadProgress={(progress) => {
            setDocumentUploadProgress(progress);
          }}
          onClientUploadComplete={(res) => {
            setUploadingDocuments(false);
            setDocumentUploadProgress(100);
            setUploadComplete(prev => ({ ...prev, documents: true }));
            if (res) {
              // Create properly typed objects from the response
              const newDocs = res.map(item => ({
                uploadedBy: item.serverData?.uploadedBy || "user",
                url: item.url,
                name: item.name,
                size: item.size,
                key: item.key,
                type: item.serverData?.type || getFileTypeFromUrl(item.url)
              }));
              setDocuments(prev => [...prev, ...newDocs]);
              toast.success(`Successfully uploaded ${res.length} document${res.length === 1 ? '' : 's'}`);
            }
          }}
          onUploadError={(error: Error) => {
            setUploadingDocuments(false);
            setDocumentUploadProgress(0);
            console.error("Error uploading documents:", error);
            toast.error(`Error uploading: ${error.message}`);
          }}
          className="border-2 border-dashed border-border-medium rounded-md p-6 transition-all
            hover:border-neon-green hover:shadow-[0_0_15px_rgba(22,255,0,0.5)] hover:bg-neon-green/5
            ut-label:text-base ut-label:font-medium ut-label:text-foreground
            ut-allowed-content:text-xs ut-allowed-content:text-muted-foreground
            ut-upload-icon:h-10 ut-upload-icon:w-10 ut-upload-icon:text-secondary
            ut-button:bg-secondary ut-button:text-secondary-foreground ut-button:hover:bg-secondary/80
            ut-button:transition-all ut-button:duration-300 ut-button:rounded-md
            ut-button:px-4 ut-button:py-2 ut-button:text-sm ut-button:font-medium
            ut-uploading:border-neon-green ut-uploading:bg-neon-green/5 ut-uploading:shadow-[0_0_15px_rgba(22,255,0,0.5)]"
          content={{
            label: ({ ready }) => (
              <span className="flex flex-col items-center gap-1">
                <span className="text-base font-medium">
                  {!ready ? "Preparing upload..." : 
                   uploadComplete.documents ? "Documents uploaded successfully! Drop more here" : 
                   "Drag & drop documents here"}
                </span>
                <span className="text-xs text-muted-foreground">
                  {uploadComplete.documents ? "or click to add more documents" : "or click to browse"}
                </span>
                {uploadComplete.documents && (
                  <CheckCircle className="h-6 w-6 text-neon-green mt-1" />
                )}
              </span>
            )
          }}
          config={{
            mode: "auto", // Auto upload after selection
            appendOnPaste: true // Allow pasting images
          }}
        />
        <p className="text-xs text-muted-foreground">
          You can upload up to {documentRouteConfig?.pdf?.maxFileCount || 10} documents (PDF, DOC, DOCX) and {' '}
          {documentRouteConfig?.image?.maxFileCount || 5} floor plan images. 
          Maximum file size: {documentRouteConfig?.pdf?.maxFileSize || "16MB"} per document.
        </p>

        {selectedDocuments.length > 0 && (
          <div className="mt-4 p-3 border rounded-md bg-neon-green/5 border-neon-green">
            <h4 className="text-sm font-medium mb-2">Selected Documents ({selectedDocuments.length})</h4>
            <div className="space-y-1">
              {selectedDocuments.map((file) => (
                <div key={`${file.name}-${file.size}-${file.lastModified}`} className="flex items-center text-sm">
                  <FileText className="h-4 w-4 mr-2 text-neon-green" />
                  <span className="truncate">{file.name}</span>
                  <span className="ml-auto text-xs text-muted-foreground">{formatFileSize(file.size)}</span>
                </div>
              ))}
            </div>
            <p className="text-xs text-muted-foreground mt-2">Files will be uploaded automatically</p>
          </div>
        )}

        {documents.length > 0 && (
          <div className="mt-4">
            <h4 className="text-sm font-medium mb-2">Uploaded Documents ({documents.length})</h4>
            <div className="space-y-2">
              {documents.map((doc) => (
                <div key={doc.key} className="flex items-center justify-between p-3 border rounded-md">
                  <div className="flex items-center gap-3">
                    {doc.type === "image" ? (
                      <div className="h-10 w-10 relative rounded-md overflow-hidden border">
                        <Image
                          src={doc.url}
                          alt={doc.name}
                          fill
                          className="object-cover"
                        />
                      </div>
                    ) : (
                      <div className="h-10 w-10 flex items-center justify-center bg-muted rounded-md">
                        <FileText className="h-5 w-5 text-muted-foreground" />
                      </div>
                    )}
                    <div className="flex-1 min-w-0">
                      <p className="text-sm font-medium truncate">{doc.name}</p>
                      <p className="text-xs text-muted-foreground">
                        {formatFileSize(doc.size)} • {doc.type.toUpperCase()}
                      </p>
                    </div>
                  </div>
                  <button
                    type="button"
                    onClick={() => handleRemoveDocument(doc.key)}
                    className="text-muted-foreground hover:text-destructive"
                  >
                    <X className="h-4 w-4" />
                  </button>
                </div>
              ))}
            </div>
          </div>
        )}

        {uploadingDocuments && (
          <div className="mt-2 space-y-2">
            <div className="flex items-center gap-2 text-sm text-secondary">
              <Upload className="h-4 w-4" />
              <span>Uploading documents... {documentUploadProgress}%</span>
            </div>
            <div className="w-full bg-muted rounded-full h-2 overflow-hidden">
              <div 
                className="bg-neon-green h-full transition-all duration-300 rounded-full shadow-[0_0_5px_rgba(22,255,0,0.5)]" 
                style={{ width: `${documentUploadProgress}%` }}
              />
            </div>
          </div>
        )}
      </div>

      <div className="space-y-2">
        <Label htmlFor="videoLink">Video Tour Link (optional)</Label>
        <Input 
          id="videoLink" 
          placeholder="https://youtube.com/..." 
          value={videoLink}
          onChange={(e) => setVideoLink(e.target.value)}
        />
        <p className="text-xs text-muted-foreground">Add a link to a YouTube or Vimeo video tour of the location.</p>
      </div>

      <div className="space-y-2">
        <Label htmlFor="virtualTourLink">Virtual Tour Link (optional)</Label>
        <Input 
          id="virtualTourLink" 
          placeholder="https://..." 
          value={virtualTourLink}
          onChange={(e) => setVirtualTourLink(e.target.value)}
        />
        <p className="text-xs text-muted-foreground">Add a link to a 360° virtual tour of the location.</p>
      </div>
    </div>
  );
}

// Helper function to format file size
function formatFileSize(bytes: number): string {
  if (bytes === 0) return "0 Bytes";
  
  const k = 1024;
  const sizes = ["Bytes", "KB", "MB", "GB"];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return `${Number.parseFloat((bytes / (k ** i)).toFixed(2))} ${sizes[i]}`;
}

// Helper function to determine file type from URL
function getFileTypeFromUrl(url: string): string {
  const extension = url.split('.').pop()?.toLowerCase() || '';
  
  if (['jpg', 'jpeg', 'png', 'gif', 'webp'].includes(extension)) {
    return 'image';
  }
  
  if (extension === 'pdf') {
    return 'pdf';
  }
  
  if (['doc', 'docx'].includes(extension)) {
    return 'doc';
  }
  
  return 'document';
}
