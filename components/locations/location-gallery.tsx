"use client";

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>itle } from "@/components/ui/card";
import { useState } from "react";
import Image from "next/image";

interface MediaItem {
  id: string;
  url: string;
  filename: string;
  type: "image" | "video";
  thumbnailUrl?: string;
  uploadedBy: {
    id: string;
    name: string;
  };
  uploadedAt: string;
}

interface LocationGalleryProps {
  media: MediaItem[];
}

export function LocationGallery({ media }: LocationGalleryProps) {
  const [activeItem, setActiveItem] = useState<MediaItem | null>(
    media.length > 0 ? media[0] : null
  );

  const images = media.filter((item) => item.type === "image");
  const videos = media.filter((item) => item.type === "video");

  return (
    <Card>
      <CardHeader>
        <CardTitle>Location Gallery</CardTitle>
      </CardHeader>
      <CardContent>
        {media.length === 0 ? (
          <div className="text-center text-sm text-muted-foreground">
            No media available for this location.
          </div>
        ) : (
          <div className="space-y-4">
            {/* Main display area */}
            {activeItem && (
              <div className="overflow-hidden rounded-md border">
                {activeItem.type === "image" ? (
                  <div className="relative aspect-video w-full">
                    <Image
                      src={activeItem.url}
                      alt={activeItem.filename}
                      fill
                      className="object-cover"
                    />
                  </div>
                ) : (
                  <div className="relative aspect-video w-full">
                    <video
                      src={activeItem.url}
                      controls
                      className="h-full w-full"
                    >
                      <track kind="captions" src="" label="English" />
                    </video>
                  </div>
                )}
                <div className="p-2 text-sm">
                  <p className="font-medium">{activeItem.filename}</p>
                  <p className="text-xs text-muted-foreground">
                    Uploaded by {activeItem.uploadedBy.name} on{" "}
                    {new Date(activeItem.uploadedAt).toLocaleDateString()}
                  </p>
                </div>
              </div>
            )}

            {/* Thumbnails */}
            <div className="grid grid-cols-4 gap-2">
              {media.map((item) => (
                <button
                  type="button"
                  key={item.id}
                  className={`relative aspect-square w-full overflow-hidden rounded-md border ${
                    activeItem?.id === item.id
                      ? "ring-2 ring-primary ring-offset-2"
                      : ""
                  }`}
                  onClick={() => setActiveItem(item)}
                >
                  {item.type === "image" ? (
                    <Image
                      src={item.thumbnailUrl || item.url}
                      alt={item.filename}
                      fill
                      className="object-cover"
                    />
                  ) : (
                    <div className="flex h-full w-full items-center justify-center bg-muted">
                      <span className="text-xs">Video</span>
                    </div>
                  )}
                </button>
              ))}
            </div>

            {/* Stats */}
            <div className="flex justify-between text-sm text-muted-foreground">
              <span>{images.length} images</span>
              <span>{videos.length} videos</span>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
