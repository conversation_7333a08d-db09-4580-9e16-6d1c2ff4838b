"use client";

import { Loader2 } from "lucide-react";
import type { Location } from "@/modules/location/model";
import type { ExtendedLocation } from "../locations/location-details-client";
import { LocationMapPreview } from "./location-create/location-map-preview";

interface LocationMapContentProps {
  location: Location | ExtendedLocation;
  zoom?: number;
}

export function LocationMapContent({ location, zoom = 14 }: LocationMapContentProps) {
  // Extract coordinates from location object
  let latitude = 0;
  let longitude = 0;
  let hasValidCoordinates = false;
  
  // Check if coordinates exist and extract them
  if (location.coordinates) {
    console.log("Raw coordinates data:", location.coordinates);
    
    // Handle different possible formats of coordinates
    if (typeof location.coordinates === 'object') {
      // Format 1: { latitude: number, longitude: number }
      if (location.coordinates && 'latitude' in location.coordinates && 'longitude' in location.coordinates) {
        const coords = location.coordinates as { latitude: number | string, longitude: number | string };
        latitude = Number(coords.latitude);
        longitude = Number(coords.longitude);
      }
      // Format 2: PostGIS point format from database
      else if (location.coordinates && 'x' in location.coordinates && 'y' in location.coordinates) {
        const coords = location.coordinates as { x: number | string, y: number | string };
        longitude = Number(coords.x);
        latitude = Number(coords.y);
      }
      // Format 3: Some other object format with lat/lng or similar
      else if (location.coordinates && 'lat' in location.coordinates && 'lng' in location.coordinates) {
        const coords = location.coordinates as { lat: number | string, lng: number | string };
        latitude = Number(coords.lat);
        longitude = Number(coords.lng);
      }
      // Handle JSON string that might be parsed as an object but still needs to be stringified
      else {
        // Try to stringify and then parse to handle potential nested JSON
        try {
          const coordString = JSON.stringify(location.coordinates);
          console.log("Stringified coordinates:", coordString);
          
          // Check if it's a stringified object with latitude/longitude
          const parsedCoords = JSON.parse(coordString);
          if (parsedCoords && typeof parsedCoords === 'object') {
            if ('latitude' in parsedCoords && 'longitude' in parsedCoords) {
              latitude = Number(parsedCoords.latitude);
              longitude = Number(parsedCoords.longitude);
            }
          }
        } catch (error) {
          console.error("Error parsing coordinates:", error);
        }
      }
    }
    // Format 4: String format like "POINT(longitude latitude)" from PostGIS
    else if (typeof location.coordinates === 'string') {
      const coordString = location.coordinates as string;
      if (coordString.startsWith('POINT')) {
        const match = coordString.match(/POINT\(([^ ]+) ([^)]+)\)/);
        if (match && match.length === 3) {
          longitude = Number(match[1]);
          latitude = Number(match[2]);
        }
      } else {
        // Try to parse as JSON string
        try {
          const parsedCoords = JSON.parse(coordString);
          if (parsedCoords && typeof parsedCoords === 'object') {
            if ('latitude' in parsedCoords && 'longitude' in parsedCoords) {
              latitude = Number(parsedCoords.latitude);
              longitude = Number(parsedCoords.longitude);
            }
          }
        } catch (error) {
          console.error("Error parsing coordinates string:", error);
        }
      }
    }
  }
  
  // Check if we have valid coordinates (both must be non-zero and valid numbers)
  hasValidCoordinates = 
    latitude !== 0 && 
    longitude !== 0 && 
    !Number.isNaN(latitude) && 
    !Number.isNaN(longitude) &&
    Math.abs(latitude) <= 90 && 
    Math.abs(longitude) <= 180;
  
  // If coordinates are still invalid, try to get them from the location's address
  if (!hasValidCoordinates && location.address) {
    console.log("Trying to extract coordinates from address:", location.address);
    // Some locations might have coordinates stored in the address object
    if (typeof location.address === 'object' && location.address !== null) {
      if ('coordinates' in location.address) {
        const addressCoords = location.address.coordinates;
        if (typeof addressCoords === 'object' && addressCoords !== null) {
          if ('latitude' in addressCoords && 'longitude' in addressCoords) {
            latitude = Number(addressCoords.latitude);
            longitude = Number(addressCoords.longitude);
            
            // Check if these coordinates are valid
            hasValidCoordinates = 
              latitude !== 0 && 
              longitude !== 0 && 
              !Number.isNaN(latitude) && 
              !Number.isNaN(longitude) &&
              Math.abs(latitude) <= 90 && 
              Math.abs(longitude) <= 180;
          }
        }
      }
    }
  }
  
  // Debug log to help diagnose coordinate issues
  console.log("Location coordinates:", {
    locationId: location.id,
    rawCoordinates: location.coordinates,
    extractedLatitude: latitude,
    extractedLongitude: longitude,
    isValid: hasValidCoordinates
  });

  return (
    <div className="h-full w-full">
      {hasValidCoordinates ? (
        <LocationMapPreview latitude={latitude} longitude={longitude} zoom={zoom} />
      ) : (
        <div className="h-full w-full flex items-center justify-center bg-muted/20">
          <div className="text-center">
            <Loader2 className="h-8 w-8 animate-spin text-muted-foreground mx-auto mb-4" />
            <p className="text-destructive text-sm">Invalid location coordinates</p>
            <p className="text-muted-foreground text-xs mt-2">
              Coordinates: {latitude}, {longitude}
            </p>
          </div>
        </div>
      )}
    </div>
  );
}
