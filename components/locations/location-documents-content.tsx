"use client";

import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { 
  File as FileIcon, 
  Download as DownloadIcon, 
  ExternalLink as ExternalLinkIcon, 
  Eye as EyeIcon,
  X as XIcon,
  Upload as UploadIcon,
  Loader2 as Loader2Icon
} from "lucide-react";
import { DocumentTypeIcon } from "@/components/locations/document-type-icon";
import { UploadButton } from "@/lib/utils/uploadthing";
import { useToast } from "@/components/ui/use-toast";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogClose,
} from "@/components/ui/dialog";

interface Document {
  id: string;
  name: string;
  type: string;
  size: number;
  url: string;
  uploadedBy: {
    id: string;
    name: string;
  };
  uploadedAt: string;
  fileType?: string;
  previewAvailable?: boolean;
}

interface LocationDocumentsContentProps {
  documents: Document[];
  locationId?: string;
  onUploadComplete?: () => void;
  isLoading?: boolean;
}

export function LocationDocumentsContent({ 
  documents, 
  locationId,
  onUploadComplete,
  isLoading = false 
}: LocationDocumentsContentProps) {
  const { toast } = useToast();
  const [isUploading, setIsUploading] = useState(false);
  const [previewUrl, setPreviewUrl] = useState<string | null>(null);
  const [previewTitle, setPreviewTitle] = useState<string>("");
  const [previewError, setPreviewError] = useState<string | null>(null);
  const [isPreviewLoading, setIsPreviewLoading] = useState(false);

  const handleUploadComplete = () => {
    setIsUploading(false);
    toast({
      title: "Document uploaded",
      description: "Your document has been uploaded successfully.",
    });
    if (onUploadComplete) {
      onUploadComplete();
    }
  };

  const handleUploadError = (error: Error) => {
    setIsUploading(false);
    toast({
      title: "Upload failed",
      description: error.message || "There was an error uploading your document.",
      variant: "destructive",
    });
  };

  const handlePreview = (document: Document) => {
    // Reset any previous errors and set loading state
    setPreviewError(null);
    setIsPreviewLoading(true);
    
    // Check if preview is available based on file type
    const url = document.url;
    let previewAvailable = false;
    
    if (url.match(/\.pdf$/i) || url.match(/\.txt$/i)) {
      previewAvailable = true;
    } else if (url.match(/\.(jpg|jpeg|png|gif|webp)$/i)) {
      previewAvailable = true;
    }
    
    if (previewAvailable) {
      setPreviewUrl(document.url);
      setPreviewTitle(document.name);
    } else {
      // If preview is not available, download the file instead
      window.open(document.url, "_blank");
      setIsPreviewLoading(false);
    }
  };

  const handlePreviewLoad = () => {
    setIsPreviewLoading(false);
  };

  const handlePreviewError = () => {
    setPreviewError(`Failed to load preview for ${previewTitle}`);
    toast({
      title: "Preview failed",
      description: `Could not load preview for ${previewTitle}. Try downloading the file instead.`,
      variant: "destructive",
    });
  };

  const closePreview = () => {
    setPreviewUrl(null);
    setPreviewTitle("");
    setPreviewError(null);
  };

  const getFileIcon = (document: Document) => {
    // Determine file type from document type or URL
    let fileType = document.fileType || document.type;
    const url = document.url.toLowerCase();
    
    if (!fileType || fileType === 'unknown') {
      if (url.match(/\.pdf$/i)) fileType = 'pdf';
      else if (url.match(/\.(doc|docx)$/i)) fileType = 'doc';
      else if (url.match(/\.(xls|xlsx|csv)$/i)) fileType = 'spreadsheet';
      else if (url.match(/\.(ppt|pptx)$/i)) fileType = 'presentation';
      else if (url.match(/\.(jpg|jpeg|png|gif|webp)$/i)) fileType = 'image';
      else if (url.match(/\.(mp4|webm|mov|avi)$/i)) fileType = 'video';
      else if (url.match(/\.(mp3|wav|ogg)$/i)) fileType = 'audio';
      else if (url.match(/\.(zip|rar|tar|gz)$/i)) fileType = 'archive';
      else if (url.match(/\.(html|css|js|ts|json|xml)$/i)) fileType = 'code';
      else if (url.match(/\.txt$/i)) fileType = 'text';
    }
    
    return <DocumentTypeIcon fileType={fileType} className="h-4 w-4" />;
  };

  const isPreviewable = (document: Document) => {
    const url = document.url.toLowerCase();
    return url.match(/\.(pdf|txt|jpg|jpeg|png|gif|webp)$/i) !== null;
  };

  if (isLoading) {
    return (
      <div className="h-full w-full p-4 flex items-center justify-center">
        <div className="text-center">
          <Loader2Icon className="h-8 w-8 animate-spin mx-auto mb-4 text-primary" />
          <p className="text-sm text-muted-foreground">Loading documents...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="h-full w-full p-4">
      <div className="flex justify-between items-center mb-4">
        <h3 className="text-lg font-medium">Documents</h3>
        {locationId && (
          <UploadButton
            endpoint="locationDocuments"
            content={{
              button({ ready }) {
                if (ready) {
                  return (
                    <div className="flex items-center gap-1">
                      <UploadIcon className="h-4 w-4" />
                      <span>Upload</span>
                    </div>
                  );
                }
                return "Loading...";
              },
            }}
            onUploadBegin={() => setIsUploading(true)}
            onClientUploadComplete={handleUploadComplete}
            onUploadError={handleUploadError}
            headers={{ "x-location-id": locationId }}
          />
        )}
      </div>

      {isUploading && (
        <div className="mb-4 p-2 bg-primary/10 rounded-md flex items-center gap-2">
          <Loader2Icon className="h-4 w-4 animate-spin text-primary" />
          <span className="text-sm">Uploading document...</span>
        </div>
      )}

      {documents.length === 0 ? (
        <div className="flex h-64 w-full items-center justify-center border rounded-md">
          <div className="text-center">
            <FileIcon className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
            <p className="text-sm text-muted-foreground">
              No documents available for this location.
            </p>
            {locationId && (
              <p className="text-sm text-muted-foreground mt-2">
                Upload documents using the button above.
              </p>
            )}
          </div>
        </div>
      ) : (
        <div className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {documents.map((document) => (
              <Card key={document.id} className="overflow-hidden">
                <CardContent className="p-4">
                  <div className="flex items-start gap-3">
                    <div className="h-10 w-10 flex items-center justify-center bg-muted rounded-md">
                      {getFileIcon(document)}
                    </div>
                    <div className="flex-1 min-w-0">
                      <p className="text-sm font-medium truncate">{document.name}</p>
                      <div className="flex items-center gap-2 mt-1">
                        <p className="text-xs text-muted-foreground">
                          {formatFileSize(document.size || 0)}
                        </p>
                        <Badge variant="outline" className="text-xs">
                          {document.type.toUpperCase()}
                        </Badge>
                      </div>
                      <p className="text-xs text-muted-foreground mt-1">
                        Uploaded {new Date(document.uploadedAt).toLocaleDateString()}
                      </p>
                    </div>
                    <div className="flex items-center gap-1">
                      {isPreviewable(document) && (
                        <Button
                          variant="ghost"
                          size="icon"
                          onClick={() => handlePreview(document)}
                          title="Preview"
                        >
                          <EyeIcon className="h-4 w-4" />
                          <span className="sr-only">Preview</span>
                        </Button>
                      )}
                      <a
                        href={document.url}
                        target="_blank"
                        rel="noopener noreferrer"
                      >
                        <Button variant="ghost" size="icon" title="Open">
                          <ExternalLinkIcon className="h-4 w-4" />
                          <span className="sr-only">Open</span>
                        </Button>
                      </a>
                      <a href={document.url} download>
                        <Button variant="ghost" size="icon" title="Download">
                          <DownloadIcon className="h-4 w-4" />
                          <span className="sr-only">Download</span>
                        </Button>
                      </a>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      )}

      {/* Document Preview Dialog */}
      <Dialog open={!!previewUrl} onOpenChange={(open) => !open && closePreview()}>
        <DialogContent className="max-w-4xl h-[80vh] flex flex-col">
          <DialogHeader className="flex justify-between items-center">
            <DialogTitle className="truncate">{previewTitle}</DialogTitle>
            <DialogClose asChild>
              <Button variant="ghost" size="icon">
                <XIcon className="h-4 w-4" />
              </Button>
            </DialogClose>
          </DialogHeader>
          <div className="flex-1 overflow-hidden">
            {previewError ? (
              <div className="h-full w-full flex flex-col items-center justify-center gap-4 p-8 text-center">
                <FileIcon className="h-12 w-12 text-destructive" />
                <div>
                  <h3 className="text-lg font-medium">Preview Error</h3>
                  <p className="text-sm text-muted-foreground mt-1">{previewError}</p>
                </div>
                <Button
                  variant="outline"
                  onClick={() => previewUrl && window.open(previewUrl, "_blank")}
                >
                  Download Instead
                </Button>
              </div>
            ) : (
              <>
                {isPreviewLoading && (
                  <div className="h-full w-full flex items-center justify-center">
                    <Loader2Icon className="h-8 w-8 animate-spin text-primary" />
                  </div>
                )}
                
                {previewUrl && (
                  previewUrl.match(/\.(jpg|jpeg|png|gif|webp)$/i) ? (
                    <div className={`h-full w-full flex items-center justify-center bg-black/5 ${isPreviewLoading ? 'hidden' : 'block'}`}>
                      <img
                        src={previewUrl}
                        alt={previewTitle}
                        className="max-h-full max-w-full object-contain"
                        onLoad={handlePreviewLoad}
                        onError={handlePreviewError}
                      />
                    </div>
                  ) : (
                    <iframe
                      src={previewUrl}
                      className={`w-full h-full border-0 ${isPreviewLoading ? 'hidden' : 'block'}`}
                      title={previewTitle}
                      onLoad={handlePreviewLoad}
                      onError={handlePreviewError}
                    />
                  )
                )}
              </>
            )}
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
}

function formatFileSize(bytes: number): string {
  if (bytes === 0) return "0 Bytes";
  
  const k = 1024;
  const sizes = ["Bytes", "KB", "MB", "GB"];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return `${Number.parseFloat((bytes / (k ** i)).toFixed(2))} ${sizes[i]}`;
}
