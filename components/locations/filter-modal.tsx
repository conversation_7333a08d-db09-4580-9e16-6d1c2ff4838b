"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>ead<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Footer } from "@/components/ui/dialog"
import { Button } from "@/components/ui/button"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { LOCATION_TYPES, LOCATION_STATUSES } from "@/modules/shared/constants"
import { useOrganization } from "@/hooks/use-organization"

interface FilterModalProps {
  isOpen: boolean
  onClose: () => void
  filters: {
    status: string
    type: string
    projectId: string
  }
  onFilterChange: (filters: {
    status: string
    type: string
    projectId: string
  }) => void
}

export function FilterModal({ isOpen, onClose, filters, onFilterChange }: FilterModalProps) {
  const [localFilters, setLocalFilters] = useState(filters)
  // We'll use organization data later when fetching real projects
  // Get the organization from the context
  const { organization } = useOrganization()
  
  // Reset local filters when the modal opens
  useEffect(() => {
    if (isOpen) {
      setLocalFilters(filters)
    }
  }, [isOpen, filters])
  
  // Handle filter changes
  const handleChange = (key: keyof typeof localFilters, value: string) => {
    setLocalFilters((prev) => ({
      ...prev,
      [key]: value
    }))
  }
  
  // Apply filters and close modal
  const handleApply = () => {
    onFilterChange(localFilters)
    onClose()
  }
  
  // Reset filters
  const handleReset = () => {
    const resetFilters = {
      status: "",
      type: "",
      projectId: "",
    }
    setLocalFilters(resetFilters)
    onFilterChange(resetFilters)
    onClose()
  }

  // Mock projects data - in a real implementation, this would come from an API
  const projects = [
    { id: "1", name: "Downtown Revitalization" },
    { id: "2", name: "Waterfront Development" },
    { id: "3", name: "Historic District Preservation" },
    { id: "4", name: "Community Center Expansion" },
    { id: "5", name: "Park Renovation" },
  ]

  return (
    <Dialog open={isOpen} onOpenChange={(open) => !open && onClose()}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Filter Locations</DialogTitle>
        </DialogHeader>
        
        <div className="grid gap-4 py-4">
          <div className="grid gap-2">
            <Label htmlFor="status">Status</Label>
            <Select
              value={localFilters.status}
              onValueChange={(value) => handleChange("status", value)}
            >
              <SelectTrigger id="status">
                <SelectValue placeholder="All Statuses" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="">All Statuses</SelectItem>
                {LOCATION_STATUSES.map((status) => (
                  <SelectItem key={status} value={status}>
                    {status.charAt(0).toUpperCase() + status.slice(1)}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
          
          <div className="grid gap-2">
            <Label htmlFor="type">Type</Label>
            <Select
              value={localFilters.type}
              onValueChange={(value) => handleChange("type", value)}
            >
              <SelectTrigger id="type">
                <SelectValue placeholder="All Types" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="">All Types</SelectItem>
                {LOCATION_TYPES.map((type) => (
                  <SelectItem key={type} value={type}>
                    {type.charAt(0).toUpperCase() + type.slice(1)}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
          
          <div className="grid gap-2">
            <Label htmlFor="project">Project</Label>
            <Select
              value={localFilters.projectId}
              onValueChange={(value) => handleChange("projectId", value)}
            >
              <SelectTrigger id="project">
                <SelectValue placeholder="All Projects" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="">All Projects</SelectItem>
                {projects.map((project) => (
                  <SelectItem key={project.id} value={project.id}>
                    {project.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </div>
        
        <DialogFooter>
          <Button variant="outline" onClick={handleReset}>
            Reset
          </Button>
          <Button onClick={handleApply}>Apply Filters</Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
