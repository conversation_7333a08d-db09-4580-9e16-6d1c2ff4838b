"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Switch } from "@/components/ui/switch"
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, Ta<PERSON>Trigger } from "@/components/ui/tabs"
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Loader2, Copy, Check, Link as LinkIcon } from "lucide-react"
import { toast } from "sonner"
import { useOrganization } from "@/hooks/use-organization"

interface ShareLocationModalProps {
  locationId: string
  locationName: string
  isOpen: boolean
  onClose: () => void
}

export function ShareLocationModal({ locationId, locationName, isOpen, onClose }: ShareLocationModalProps) {
  const { organization } = useOrganization()
  const [isLoading, setIsLoading] = useState(false)
  const [isCopied, setIsCopied] = useState(false)
  const [shareLink, setShareLink] = useState<string | null>(null)
  const [viewMode, setViewMode] = useState<"client" | "admin">("client")
  const [isPasswordProtected, setIsPasswordProtected] = useState(false)
  const [password, setPassword] = useState("")
  const [expiresIn, setExpiresIn] = useState<string | null>(null)
  
  const handleCreateShareLink = async () => {
    if (!organization?.id) return
    
    setIsLoading(true)
    
    try {
      const response = await fetch("/api/locations/share", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          organizationId: organization.id,
          locationId,
          viewMode,
          password: isPasswordProtected ? password : undefined,
          expiresIn: expiresIn ? Number.parseInt(expiresIn) : undefined,
        }),
      })
      
      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.error || "Failed to create share link")
      }
      
      const data = await response.json()
      setShareLink(data.url)
      toast.success("Share link created successfully")
    } catch (error) {
      console.error("Error creating share link:", error)
      toast.error(error instanceof Error ? error.message : "Failed to create share link")
    } finally {
      setIsLoading(false)
    }
  }
  
  const handleCopyLink = () => {
    if (!shareLink) return
    
    navigator.clipboard.writeText(shareLink)
    setIsCopied(true)
    toast.success("Link copied to clipboard")
    
    setTimeout(() => {
      setIsCopied(false)
    }, 2000)
  }
  
  const handleClose = () => {
    setShareLink(null)
    setViewMode("client")
    setIsPasswordProtected(false)
    setPassword("")
    setExpiresIn(null)
    onClose()
  }
  
  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle className="flex items-center">
            <LinkIcon className="mr-2 h-4 w-4" />
            Share Location
          </DialogTitle>
          <DialogDescription>
            Create a shareable link for &quot;{locationName}&quot;
          </DialogDescription>
        </DialogHeader>
        
        {!shareLink ? (
          <Tabs defaultValue="settings" className="w-full">
            <TabsList className="grid w-full grid-cols-2">
              <TabsTrigger value="settings">Settings</TabsTrigger>
              <TabsTrigger value="security">Security</TabsTrigger>
            </TabsList>
            
            <TabsContent value="settings" className="space-y-4 py-4">
              <div className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="view-mode">View Mode</Label>
                  <Select
                    value={viewMode}
                    onValueChange={(value) => setViewMode(value as "client" | "admin")}
                  >
                    <SelectTrigger id="view-mode">
                      <SelectValue placeholder="Select view mode" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="client">Client View</SelectItem>
                      <SelectItem value="admin">Admin View</SelectItem>
                    </SelectContent>
                  </Select>
                  <p className="text-xs text-muted-foreground">
                    {viewMode === "client" 
                      ? "Client view shows basic location details without sensitive information."
                      : "Admin view shows all location details including sensitive information."}
                  </p>
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="expires-in">Link Expiration</Label>
                  <Select
                    value={expiresIn || "0"}
                    onValueChange={setExpiresIn}
                  >
                    <SelectTrigger id="expires-in">
                      <SelectValue placeholder="Select expiration time" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="0">Never expires</SelectItem>
                      <SelectItem value="3600">1 hour</SelectItem>
                      <SelectItem value="86400">1 day</SelectItem>
                      <SelectItem value="604800">1 week</SelectItem>
                      <SelectItem value="2592000">30 days</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </TabsContent>
            
            <TabsContent value="security" className="space-y-4 py-4">
              <div className="flex items-center space-x-2">
                <Switch
                  id="password-protection"
                  checked={isPasswordProtected}
                  onChange={(e) => setIsPasswordProtected(e.target.checked)}
                />
                <Label htmlFor="password-protection">Password Protection</Label>
              </div>
              
              {isPasswordProtected && (
                <div className="space-y-2">
                  <Label htmlFor="password">Password</Label>
                  <Input
                    id="password"
                    type="password"
                    value={password}
                    onChange={(e) => setPassword(e.target.value)}
                    placeholder="Enter password"
                  />
                  <p className="text-xs text-muted-foreground">
                    Anyone with the link will need this password to access the location.
                  </p>
                </div>
              )}
            </TabsContent>
          </Tabs>
        ) : (
          <div className="space-y-4 py-4">
            <div className="space-y-2">
              <Label htmlFor="share-link">Share Link</Label>
              <div className="flex items-center space-x-2">
                <Input
                  id="share-link"
                  value={shareLink}
                  readOnly
                  className="flex-1"
                />
                <Button
                  size="icon"
                  variant="outline"
                  onClick={handleCopyLink}
                  disabled={isCopied}
                >
                  {isCopied ? (
                    <Check className="h-4 w-4" />
                  ) : (
                    <Copy className="h-4 w-4" />
                  )}
                </Button>
              </div>
              <p className="text-xs text-muted-foreground">
                Share this link with others to give them access to this location.
                {isPasswordProtected && " They will need the password you set to access it."}
                {expiresIn && ` This link will expire ${
                  expiresIn === "3600" ? "in 1 hour" :
                  expiresIn === "86400" ? "in 1 day" :
                  expiresIn === "604800" ? "in 1 week" :
                  expiresIn === "2592000" ? "in 30 days" : ""
                }.`}
              </p>
            </div>
          </div>
        )}
        
        <DialogFooter>
          {!shareLink ? (
            <>
              <Button variant="outline" onClick={handleClose}>
                Cancel
              </Button>
              <Button 
                onClick={handleCreateShareLink} 
                disabled={isLoading || (isPasswordProtected && !password)}
              >
                {isLoading ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Creating...
                  </>
                ) : (
                  "Create Share Link"
                )}
              </Button>
            </>
          ) : (
            <Button onClick={handleClose}>
              Done
            </Button>
          )}
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
