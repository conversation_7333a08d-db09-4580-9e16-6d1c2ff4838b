"use client";

import { Skeleton } from "@/components/ui/skeleton";
import { DashboardShell } from "@/components/dashboard/dashboard-shell";
import { DashboardHeader } from "@/components/dashboard/dashboard-header";

export function LocationsPageSkeleton() {
  return (
    <DashboardShell>
      <DashboardHeader 
        title="Locations" 
        description="Manage and organize your filming locations"
      >
        <Skeleton className="h-10 w-[140px]" />
      </DashboardHeader>

      {/* Search and filter bar skeleton */}
      <div className="flex flex-col md:flex-row gap-4 mb-6 mt-6">
        <Skeleton className="h-10 flex-1" />
        <Skeleton className="h-10 w-[100px]" />
        <Skeleton className="h-10 w-[120px]" />
      </div>

      {/* Grid skeleton */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
        {Array.from({ length: 6 }).map((_, i) => (
          <LocationCardSkeleton key={i} />
        ))}
      </div>
    </DashboardShell>
  );
}

function LocationCardSkeleton() {
  return (
    <div className="rounded-lg border bg-card text-card-foreground shadow-sm overflow-hidden">
      <Skeleton className="aspect-video w-full" />
      <div className="p-4">
        <Skeleton className="h-6 w-3/4 mb-2" />
        <Skeleton className="h-4 w-full mb-1" />
        <Skeleton className="h-4 w-2/3 mb-2" />
        <div className="mt-2 flex items-center">
          <Skeleton className="h-3 w-3 mr-1 rounded-full" />
          <Skeleton className="h-3 w-1/2" />
        </div>
      </div>
      <div className="flex justify-between border-t p-4 pt-2">
        <Skeleton className="h-3 w-1/4" />
        <Skeleton className="h-3 w-1/4" />
      </div>
    </div>
  );
}
