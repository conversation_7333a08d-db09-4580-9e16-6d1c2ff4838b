"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Badge } from "@/components/ui/badge"
import { ArrowLeftRight, Check, X } from "lucide-react"
import Image from "next/image"

interface LocationData {
  id: string
  name: string
  address: string
  type: string
  status: string
  project: string
  image: string
  features: string[]
  size: string
  cost: string
  availability: string[]
  permitRequired: boolean
  permitStatus?: string
  restrictions: string[]
  amenities: string[]
  parking: string
  notes: string
}

export function LocationComparison() {
  const [location1, setLocation1] = useState<string | null>(null)
  const [location2, setLocation2] = useState<string | null>(null)

  // Mock location data
  const locations: LocationData[] = [
    {
      id: "1",
      name: "Downtown Loft",
      address: "123 Main St, New York, NY",
      type: "Interior",
      status: "Approved",
      project: "Urban Crime Drama",
      image: "/placeholder.svg?height=300&width=500",
      features: ["High ceilings", "Industrial look", "Large windows", "Open floor plan"],
      size: "2,500 sq ft",
      cost: "$3,500/day",
      availability: ["Jun 15-20, 2025", "Jul 5-15, 2025"],
      permitRequired: true,
      permitStatus: "Approved",
      restrictions: ["No overnight shooting", "Limited parking"],
      amenities: ["Restrooms", "Kitchen", "Loading dock", "Elevator"],
      parking: "Street parking only, 2 reserved spots",
      notes: "Owner prefers weekend shoots. Building has freight elevator for equipment.",
    },
    {
      id: "2",
      name: "Riverside Park",
      address: "Riverside Dr, New York, NY",
      type: "Exterior",
      status: "Pending",
      project: "Mountain Documentary",
      image: "/placeholder.svg?height=300&width=500",
      features: ["Waterfront views", "Green space", "Walking paths", "Benches"],
      size: "5 acres accessible",
      cost: "$2,000/day",
      availability: ["Jun 25-30, 2025", "Jul 10-20, 2025", "Aug 1-15, 2025"],
      permitRequired: true,
      permitStatus: "Pending",
      restrictions: ["No disruption to public", "Limited hours (5am-10pm)", "No vehicles on paths"],
      amenities: ["Public restrooms", "Parking nearby"],
      parking: "Public parking lot, 200 yards away",
      notes: "High public traffic on weekends. Early morning recommended for fewer people.",
    },
    {
      id: "3",
      name: "Historic Theater",
      address: "456 Broadway, New York, NY",
      type: "Interior",
      status: "Scouting",
      project: "Urban Crime Drama",
      image: "/placeholder.svg?height=300&width=500",
      features: ["Ornate architecture", "Stage", "Seating for 500", "Balcony"],
      size: "10,000 sq ft",
      cost: "$5,000/day",
      availability: ["Jul 1-10, 2025", "Aug 15-30, 2025"],
      permitRequired: true,
      permitStatus: "Not submitted",
      restrictions: ["No alterations to historic elements", "Fire marshal required on set"],
      amenities: ["Dressing rooms", "Restrooms", "Loading area", "Lighting grid"],
      parking: "Nearby garage, 5 reserved spots included",
      notes: "Historic building requires special care. Additional insurance required.",
    },
    {
      id: "4",
      name: "City Rooftop",
      address: "789 5th Ave, New York, NY",
      type: "Exterior",
      status: "Approved",
      project: "Downtown Commercial",
      image: "/placeholder.svg?height=300&width=500",
      features: ["360° city views", "Open space", "Modern design", "Covered area"],
      size: "1,800 sq ft",
      cost: "$4,200/day",
      availability: ["Jun 20-25, 2025", "Jul 15-25, 2025"],
      permitRequired: true,
      permitStatus: "Approved",
      restrictions: ["Weight limits", "No open flames", "Limited hours (8am-9pm)"],
      amenities: ["Restroom access", "Elevator access", "Power outlets"],
      parking: "Building garage, 3 spots included",
      notes: "Weather dependent. Building requires 48hr notice for scheduling.",
    },
  ]

  const getLocationById = (id: string): LocationData | undefined => {
    return locations.find((loc) => loc.id === id)
  }

  const selectedLocation1 = location1 ? getLocationById(location1) : null
  const selectedLocation2 = location2 ? getLocationById(location2) : null

  const renderComparisonItem = (label: string, value1: any, value2: any) => {
    const isMatch = JSON.stringify(value1) === JSON.stringify(value2)

    return (
      <div className="grid grid-cols-[1fr_2fr_2fr] gap-4 py-3 border-b">
        <div className="text-sm font-medium">{label}</div>
        <div className="text-sm">
          {Array.isArray(value1) ? (
            <ul className="list-disc pl-5 space-y-1">
              {value1.map((item, i) => (
                <li key={i}>{item}</li>
              ))}
            </ul>
          ) : typeof value1 === "boolean" ? (
            value1 ? (
              <Check className="h-4 w-4 text-green-500" />
            ) : (
              <X className="h-4 w-4 text-red-500" />
            )
          ) : (
            value1
          )}
        </div>
        <div className="text-sm">
          {Array.isArray(value2) ? (
            <ul className="list-disc pl-5 space-y-1">
              {value2.map((item, i) => (
                <li key={i}>{item}</li>
              ))}
            </ul>
          ) : typeof value2 === "boolean" ? (
            value2 ? (
              <Check className="h-4 w-4 text-green-500" />
            ) : (
              <X className="h-4 w-4 text-red-500" />
            )
          ) : (
            value2
          )}
        </div>
      </div>
    )
  }

  return (
    <Dialog>
      <DialogTrigger asChild>
        <Button variant="outline" className="gap-2">
          <ArrowLeftRight className="h-4 w-4" />
          Compare Locations
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-[900px]">
        <DialogHeader>
          <DialogTitle>Compare Locations</DialogTitle>
          <DialogDescription>Select two locations to compare their features and details.</DialogDescription>
        </DialogHeader>

        <div className="grid grid-cols-2 gap-4 mt-4">
          <div>
            <label className="text-sm font-medium mb-2 block">Location 1</label>
            <Select value={location1 || ""} onValueChange={setLocation1}>
              <SelectTrigger>
                <SelectValue placeholder="Select a location" />
              </SelectTrigger>
              <SelectContent>
                {locations.map((loc) => (
                  <SelectItem key={loc.id} value={loc.id}>
                    {loc.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
          <div>
            <label className="text-sm font-medium mb-2 block">Location 2</label>
            <Select value={location2 || ""} onValueChange={setLocation2}>
              <SelectTrigger>
                <SelectValue placeholder="Select a location" />
              </SelectTrigger>
              <SelectContent>
                {locations.map((loc) => (
                  <SelectItem key={loc.id} value={loc.id} disabled={loc.id === location1}>
                    {loc.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </div>

        {selectedLocation1 && selectedLocation2 && (
          <div className="mt-6">
            <Tabs defaultValue="overview">
              <TabsList className="w-full">
                <TabsTrigger value="overview" className="flex-1">
                  Overview
                </TabsTrigger>
                <TabsTrigger value="details" className="flex-1">
                  Details
                </TabsTrigger>
                <TabsTrigger value="logistics" className="flex-1">
                  Logistics
                </TabsTrigger>
                <TabsTrigger value="restrictions" className="flex-1">
                  Restrictions
                </TabsTrigger>
              </TabsList>

              <TabsContent value="overview" className="mt-4">
                <div className="grid grid-cols-2 gap-6">
                  <div>
                    <div className="aspect-video relative rounded-md overflow-hidden mb-3">
                      <Image
                        src={selectedLocation1.image || "/placeholder.svg"}
                        alt={selectedLocation1.name}
                        fill
                        className="object-cover"
                      />
                    </div>
                    <h3 className="text-lg font-semibold">{selectedLocation1.name}</h3>
                    <p className="text-sm text-muted-foreground mb-2">{selectedLocation1.address}</p>
                    <div className="flex gap-2 mb-3">
                      <Badge variant="outline">{selectedLocation1.type}</Badge>
                      <Badge
                        variant={
                          selectedLocation1.status === "Approved"
                            ? "default"
                            : selectedLocation1.status === "Pending"
                              ? "outline"
                              : "secondary"
                        }
                      >
                        {selectedLocation1.status}
                      </Badge>
                    </div>
                    <p className="text-sm">
                      <span className="font-medium">Project:</span> {selectedLocation1.project}
                    </p>
                  </div>
                  <div>
                    <div className="aspect-video relative rounded-md overflow-hidden mb-3">
                      <Image
                        src={selectedLocation2.image || "/placeholder.svg"}
                        alt={selectedLocation2.name}
                        fill
                        className="object-cover"
                      />
                    </div>
                    <h3 className="text-lg font-semibold">{selectedLocation2.name}</h3>
                    <p className="text-sm text-muted-foreground mb-2">{selectedLocation2.address}</p>
                    <div className="flex gap-2 mb-3">
                      <Badge variant="outline">{selectedLocation2.type}</Badge>
                      <Badge
                        variant={
                          selectedLocation2.status === "Approved"
                            ? "default"
                            : selectedLocation2.status === "Pending"
                              ? "outline"
                              : "secondary"
                        }
                      >
                        {selectedLocation2.status}
                      </Badge>
                    </div>
                    <p className="text-sm">
                      <span className="font-medium">Project:</span> {selectedLocation2.project}
                    </p>
                  </div>
                </div>
              </TabsContent>

              <TabsContent value="details" className="mt-4">
                <div className="grid grid-cols-[1fr_2fr_2fr] gap-4 py-2 border-b font-medium">
                  <div>Feature</div>
                  <div>{selectedLocation1.name}</div>
                  <div>{selectedLocation2.name}</div>
                </div>
                {renderComparisonItem("Type", selectedLocation1.type, selectedLocation2.type)}
                {renderComparisonItem("Size", selectedLocation1.size, selectedLocation2.size)}
                {renderComparisonItem("Features", selectedLocation1.features, selectedLocation2.features)}
                {renderComparisonItem("Amenities", selectedLocation1.amenities, selectedLocation2.amenities)}
                {renderComparisonItem("Notes", selectedLocation1.notes, selectedLocation2.notes)}
              </TabsContent>

              <TabsContent value="logistics" className="mt-4">
                <div className="grid grid-cols-[1fr_2fr_2fr] gap-4 py-2 border-b font-medium">
                  <div>Feature</div>
                  <div>{selectedLocation1.name}</div>
                  <div>{selectedLocation2.name}</div>
                </div>
                {renderComparisonItem("Cost", selectedLocation1.cost, selectedLocation2.cost)}
                {renderComparisonItem("Availability", selectedLocation1.availability, selectedLocation2.availability)}
                {renderComparisonItem("Parking", selectedLocation1.parking, selectedLocation2.parking)}
                {renderComparisonItem(
                  "Permit Required",
                  selectedLocation1.permitRequired,
                  selectedLocation2.permitRequired,
                )}
                {renderComparisonItem(
                  "Permit Status",
                  selectedLocation1.permitStatus || "N/A",
                  selectedLocation2.permitStatus || "N/A",
                )}
              </TabsContent>

              <TabsContent value="restrictions" className="mt-4">
                <div className="grid grid-cols-[1fr_2fr_2fr] gap-4 py-2 border-b font-medium">
                  <div>Feature</div>
                  <div>{selectedLocation1.name}</div>
                  <div>{selectedLocation2.name}</div>
                </div>
                {renderComparisonItem("Restrictions", selectedLocation1.restrictions, selectedLocation2.restrictions)}
              </TabsContent>
            </Tabs>
          </div>
        )}
      </DialogContent>
    </Dialog>
  )
}

