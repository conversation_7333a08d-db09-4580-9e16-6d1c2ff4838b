"use client"

import { useState } from "react"
import { <PERSON>, CardContent } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Textarea } from "@/components/ui/textarea"
import { ScrollArea } from "@/components/ui/scroll-area"
import { MessageSquare, ThumbsUp, MapPin, ImageIcon, Check } from "lucide-react"
import Image from "next/image"
import { usePermissions, hasRequiredRole } from "@/hooks/usePermissions";

interface LocationClientViewProps {
  locationId: string
  // sceneId?: string // Removed unused prop
}

export function LocationClientView({ locationId }: LocationClientViewProps) { // Removed sceneId from destructuring
  const [activeTab, setActiveTab] = useState("overview")
  const [newNote, setNewNote] = useState("")
  const userRoles = usePermissions();
  const canAddComment = hasRequiredRole(userRoles, ['admin', 'manager', 'scout']);

  // Mock location data - in a real app, this would be fetched based on the locationId
  const location = {
    id: locationId,
    name: "Downtown Loft",
    address: "123 Main St, New York, NY",
    type: "Interior",
    status: "Approved",
    description:
      "A spacious industrial loft with exposed brick walls, large windows, and hardwood floors. Perfect for urban residential scenes.",
    features: [
      "High ceilings",
      "Industrial look",
      "Large windows",
      "Open floor plan",
      "Exposed brick",
      "Hardwood floors",
    ],
    amenities: ["Restrooms", "Kitchen", "Loading dock", "Elevator", "WiFi"],
    images: [
      "/placeholder.svg?height=300&width=500",
      "/placeholder.svg?height=300&width=500",
      "/placeholder.svg?height=300&width=500",
      "/placeholder.svg?height=300&width=500",
    ],
    notes: [
      {
        id: "note1",
        user: {
          name: "David Rodriguez",
          avatar: "/placeholder.svg?height=40&width=40",
          initials: "DR",
        },
        timestamp: "2 days ago",
        content:
          "The lighting in this space is perfect for our morning scenes. The large windows provide great natural light.",
      },
      {
        id: "note2",
        user: {
          name: "Sarah Johnson",
          avatar: "/placeholder.svg?height=40&width=40",
          initials: "SJ",
        },
        timestamp: "1 day ago",
        content: "I agree with David. Also, the open floor plan gives us flexibility for camera setups.",
      },
    ],
    votes: {
      thumbsUp: 5,
      thumbsDown: 1,
    },
  }

  const handleAddNote = () => {
    if (!newNote) return

    // In a real app, this would send the note to an API
    console.log("Adding note:", newNote)
    setNewNote("")

    // For demo purposes, we'd update the UI here
    // This would normally happen after the API call succeeds
  }

  return (
    <div className="space-y-6">
      <div className="flex flex-col md:flex-row md:items-start md:justify-between gap-4">
        <div>
          <h2 className="text-2xl font-bold">{location.name}</h2>
          <p className="text-muted-foreground">{location.address}</p>
          <div className="flex gap-2 mt-2">
            <Badge variant="outline">{location.type}</Badge>
            <Badge
              variant={
                location.status === "Approved" ? "default" : location.status === "Pending" ? "outline" : "secondary"
              }
            >
              {location.status}
            </Badge>
          </div>
        </div>

        <div className="flex items-center gap-3">
          <div className="flex items-center gap-1">
            <Button variant="outline" size="sm" className="h-8 w-8 p-0">
              <ThumbsUp className="h-4 w-4 text-green-500" />
            </Button>
            <span className="text-sm font-medium">{location.votes.thumbsUp}</span>
          </div>
          <div className="flex items-center gap-1">
            <Button variant="outline" size="sm" className="h-8 w-8 p-0">
              <ThumbsUp className="h-4 w-4 text-red-500 rotate-180" />
            </Button>
            <span className="text-sm font-medium">{location.votes.thumbsDown}</span>
          </div>
          <Button variant="outline" size="sm">
            <MapPin className="h-4 w-4 mr-2" />
            View on Map
          </Button>
        </div>
      </div>

      <Tabs defaultValue={activeTab} onValueChange={setActiveTab} className="space-y-4">
        <TabsList>
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="gallery">Gallery</TabsTrigger>
          <TabsTrigger value="discussion">Discussion</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-4">
          <Card>
            <CardContent className="p-6">
              <h3 className="text-lg font-medium mb-2">Description</h3>
              <p>{location.description}</p>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mt-6">
                <div>
                  <h4 className="font-medium mb-2">Features</h4>
                  <ul className="space-y-1">
                    {/* TODO: Use unique key like feature.id when using real data */}
                    {location.features.map((feature, index) => (
                      // biome-ignore lint/suspicious/noArrayIndexKey: <explanation>Using index for mock data</explanation>
                      <li key={index} className="flex items-center gap-2">
                        <Check className="h-4 w-4 text-green-500" />
                        <span>{feature}</span>
                      </li>
                    ))}
                  </ul>
                </div>

                <div>
                  <h4 className="font-medium mb-2">Amenities</h4>
                  <ul className="space-y-1">
                    {/* TODO: Use unique key like amenity.id when using real data */}
                    {location.amenities.map((amenity, index) => (
                      // biome-ignore lint/suspicious/noArrayIndexKey: <explanation>Using index for mock data</explanation>
                      <li key={index} className="flex items-center gap-2">
                        <Check className="h-4 w-4 text-green-500" />
                        <span>{amenity}</span>
                      </li>
                    ))}
                  </ul>
                </div>
              </div>
            </CardContent>
          </Card>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Card>
              <CardContent className="p-0 aspect-video relative">
                <Image
                  src={location.images[0] || "/placeholder.svg"}
                  alt={`${location.name} main image`}
                  fill
                  className="object-cover rounded-md"
                />
              </CardContent>
            </Card>

            <div className="grid grid-cols-2 gap-4">
              {/* TODO: Use unique key like image.id when using real data */}
              {location.images.slice(1, 4).map((image, index) => (
                // biome-ignore lint/suspicious/noArrayIndexKey: <explanation>Using index for mock data</explanation>
                <Card key={index}>
                  <CardContent className="p-0 aspect-square relative">
                    <Image
                      src={image || "/placeholder.svg"}
                      alt={`${location.name} image ${index + 2}`}
                      fill
                      className="object-cover rounded-md"
                    />
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </TabsContent>

        <TabsContent value="gallery">
          <Card>
            <CardContent className="p-6">
              <div className="flex justify-between items-center mb-4">
                <h3 className="text-lg font-medium">Location Gallery</h3>
                <Button variant="outline" size="sm">
                  <ImageIcon className="h-4 w-4 mr-2" />
                  View All Photos
              </Button>
            </div>

            <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
              {/* TODO: Use unique key like image.id when using real data */}
              {[...location.images, ...location.images].map((image, index) => (
                // biome-ignore lint/suspicious/noArrayIndexKey: <explanation>Using index for mock data</explanation>
                <div key={index} className="aspect-square relative rounded-md overflow-hidden">
                  <Image
                    src={image || "/placeholder.svg"}
                    alt={`${location.name} gallery image ${index + 1}`}
                      fill
                      className="object-cover transition-all hover:scale-105"
                    />
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="discussion">
          <Card>
            <CardContent className="p-6">
              <h3 className="text-lg font-medium mb-4">Discussion</h3>

              <ScrollArea className="h-[300px] pr-4">
                {location.notes.length > 0 ? (
                  <div className="space-y-4">
                    {location.notes.map((note) => (
                      <div key={note.id} className="flex gap-3">
                        <Avatar className="h-8 w-8">
                          <AvatarImage src={note.user.avatar} alt={note.user.name} />
                          <AvatarFallback>{note.user.initials}</AvatarFallback>
                        </Avatar>
                        <div className="flex-1">
                          <div className="flex justify-between">
                            <div className="font-medium text-sm">{note.user.name}</div>
                            <div className="text-xs text-muted-foreground">{note.timestamp}</div>
                          </div>
                          <div className="mt-1 text-sm">{note.content}</div>
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-8">
                    <MessageSquare className="h-8 w-8 mx-auto text-muted-foreground mb-2" />
                    <h3 className="font-medium mb-1">No Comments Yet</h3>
                    <p className="text-sm text-muted-foreground">Be the first to add a comment about this location.</p>
                  </div>
                )}
              </ScrollArea>

              {/* Conditionally render Add Comment section */}
              {canAddComment && (
                <div className="mt-4 pt-4 border-t">
                  <div className="flex gap-3">
                    <Avatar className="h-8 w-8">
                      {/* TODO: Replace with actual user initials/avatar */}
                      <AvatarFallback>YO</AvatarFallback>
                    </Avatar>
                    <div className="flex-1 space-y-2">
                      <Textarea
                        placeholder="Add your thoughts about this location..."
                        className="min-h-[80px]"
                        value={newNote}
                        onChange={(e) => setNewNote(e.target.value)}
                      />
                      <div className="flex justify-end">
                        <Button size="sm" onClick={handleAddNote} disabled={!newNote}>
                          Add Comment
                        </Button>
                      </div>
                    </div>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
