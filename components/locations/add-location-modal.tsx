"use client"

import { useState } from "react"
import { useLocationMutations } from "@/hooks/use-location-mutations"
import type { LocationType, LocationStatus } from "@/modules/location/model"
import { Button } from "@/components/ui/button"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Trigger } from "@/components/ui/tabs"
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { LocationBasicInfo } from "@/components/locations/location-create/location-basic-info"
import { LocationDetails } from "@/components/locations/location-create/location-details"
import { LocationMedia } from "@/components/locations/location-create/location-media"
import { LocationPermissions } from "@/components/locations/location-create/location-permissions"
import { LocationMapPreview } from "@/components/locations/location-create/location-map-preview"
import { Save, Loader2, MapPin, ArrowLeft, ArrowRight } from "lucide-react"
import { toast } from "sonner"

interface AddLocationModalProps {
  isOpen: boolean
  onClose: () => void
}

export function AddLocationModal({ isOpen, onClose }: AddLocationModalProps) {
  
  const [activeTab, setActiveTab] = useState("basic-info")
  const [isLoading, setIsLoading] = useState(false)
  const [locationId, setLocationId] = useState<string | null>(null)
  const [formData, setFormData] = useState({
    name: "",
    description: "",
    address: {
      street: "",
      city: "",
      state: "",
      country: "",
      postalCode: ""
    },
    coordinates: {
      latitude: 0,
      longitude: 0
    },
    type: "interior" as LocationType,
    status: "pending" as LocationStatus,
    category: "residential",
    projectId: "00000000-0000-0000-0000-000000000000"
  })

  const tabs = [
    { id: "basic-info", label: "Basic Info" },
    { id: "details", label: "Details" },
    { id: "media", label: "Media" },
    { id: "permissions", label: "Permissions" }
  ]

  // Function to update form data
  const updateFormData = (data: Record<string, unknown>) => {
    setFormData(prev => ({
      ...prev,
      ...data
    }))
  }

  const handleTabChange = (value: string) => {
    // If trying to move past basic-info and no locationId exists, prevent tab change
    if (value !== "basic-info" && !locationId) {
      toast.error("Please save basic information first")
      return
    }
    setActiveTab(value)
  }

  const handleNext = async () => {
    const currentIndex = tabs.findIndex((tab) => tab.id === activeTab)
    
    // If moving from basic-info to details and no locationId exists, save the location first
    if (activeTab === "basic-info" && !locationId) {
      const success = await saveBasicInfo()
      if (!success) return // Don't proceed if saving failed
    } else {
      // For other tabs, show a success notification when moving to the next tab
      const currentTabName = tabs[currentIndex].label
      const nextTabName = currentIndex < tabs.length - 1 ? tabs[currentIndex + 1].label : ""
      
      if (nextTabName) {
        toast.success(`${currentTabName} information saved! Moving to ${nextTabName}`, {
          duration: 2000
        })
      }
    }
    
    if (currentIndex < tabs.length - 1 && (activeTab !== "basic-info" || locationId)) {
      setActiveTab(tabs[currentIndex + 1].id)
    }
  }

  const handlePrevious = () => {
    const currentIndex = tabs.findIndex((tab) => tab.id === activeTab)
    if (currentIndex > 0) {
      setActiveTab(tabs[currentIndex - 1].id)
    }
  }

  // Use the location mutations hook
  const { createLocation } = useLocationMutations()

  const saveBasicInfo = async () => {
    // Enhanced validation to check all required fields
    if (!formData.name) {
      toast.error("Location name is required")
      return false
    }
    
    if (!formData.address.street) {
      toast.error("Street address is required")
      return false
    }
    
    // Validate other required address fields
    if (!formData.address.city || !formData.address.state || !formData.address.country || !formData.address.postalCode) {
      toast.error("All address fields are required")
      return false
    }
    
    // Check if coordinates are set
    if (formData.coordinates.latitude === 0 && formData.coordinates.longitude === 0) {
      // Try to geocode the address
      try {
        const fullAddress = [
          formData.address.street,
          formData.address.city,
          formData.address.state,
          formData.address.postalCode,
          formData.address.country
        ].filter(part => part && part.trim() !== '').join(', ');
        
        toast.info("Geocoding address to get coordinates...");
        
        // Import the geocoding utility
        const { geocodeAddress } = await import("@/lib/utils/geocoding");
        const result = await geocodeAddress(fullAddress);
        
        // Update form data with coordinates
        setFormData(prev => ({
          ...prev,
          coordinates: {
            latitude: result.latitude,
            longitude: result.longitude
          }
        }));
        
        toast.success("Address geocoded successfully");
      } catch (error) {
        console.error("Error geocoding address:", error);
        toast.error("Failed to get coordinates for the address. Please try again or enter coordinates manually.");
        return false;
      }
    }

    setIsLoading(true)
    try {
      // Use the createLocation mutation instead of direct fetch
      const location = await createLocation.mutateAsync(formData)
      
      setLocationId(location.id)
      
      // Show a prominent success notification
      toast.success("Location created successfully!", {
        duration: 3000,
        position: "top-center"
      })
      
      // Close the modal after successful creation
      handleClose()
      
      return true
    } catch (error) {
      console.error("Error creating location:", error)
      toast.error(error instanceof Error ? error.message : "Failed to save location information")
      return false
    } finally {
      setIsLoading(false)
    }
  }

  // Use the location mutations hook for updates too
  const { updateLocation } = useLocationMutations()

  const handleSaveAsDraft = async () => {
    if (!locationId) {
      await saveBasicInfo()
    } else {
      // Update existing location
      setIsLoading(true)
      try {
        await updateLocation.mutateAsync({
          id: locationId,
          data: {
            ...formData,
            status: "pending" as LocationStatus
          }
        })

        toast.success("Location saved as draft successfully!", {
          duration: 3000,
          position: "top-center"
        })
        handleClose()
      } catch (error) {
        console.error("Error updating location:", error)
        toast.error(error instanceof Error ? error.message : "Failed to save location")
      } finally {
        setIsLoading(false)
      }
    }
  }

  const handleCreateLocation = async () => {
    if (!locationId) {
      const success = await saveBasicInfo()
      if (!success) return
    }

    setIsLoading(true)
    try {
      // Update the location status to approved using the mutation
      // Ensure locationId exists before using it
      if (locationId) {
        await updateLocation.mutateAsync({
          id: locationId,
          data: {
            status: "approved" as LocationStatus
          }
        })

        toast.success("Location created and published successfully!", {
          duration: 3000,
          position: "top-center"
        })
        handleClose()
      } else {
        throw new Error("Location ID is missing")
      }
    } catch (error) {
      console.error("Error publishing location:", error)
      toast.error(error instanceof Error ? error.message : "Failed to create location")
    } finally {
      setIsLoading(false)
    }
  }

  // Use the location mutations hook for deletion too
  const { deleteLocation } = useLocationMutations()

  const handleClose = () => {
    // If we've created a location but are canceling, delete it
    if (locationId) {
      deleteLocation.mutate(locationId, {
        onError: (err) => console.error("Error deleting canceled location:", err)
      })
    }
    
    // Reset form state
    setFormData({
      name: "",
      description: "",
      address: {
        street: "",
        city: "",
        state: "",
        country: "",
        postalCode: ""
      },
      coordinates: {
        latitude: 0,
        longitude: 0
      },
      type: "interior" as LocationType,
      status: "pending" as LocationStatus,
      category: "residential",
      projectId: "00000000-0000-0000-0000-000000000000"
    })
    setLocationId(null)
    setActiveTab("basic-info")
    
    // Close the modal
    onClose()
  }

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-[800px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center">
            <MapPin className="mr-2 h-5 w-5" />
            Add New Location
          </DialogTitle>
          <DialogDescription>
            Fill out the details below to add a new location to your database.
          </DialogDescription>
        </DialogHeader>
        
        <Tabs value={activeTab} onValueChange={handleTabChange}>
          <TabsList className="grid grid-cols-2 md:grid-cols-4 mb-6">
            {tabs.map((tab) => (
              <TabsTrigger key={tab.id} value={tab.id} disabled={isLoading}>
                {tab.label}
              </TabsTrigger>
            ))}
          </TabsList>
          <TabsContent value="basic-info">
            <div className="space-y-6">
              <LocationBasicInfo 
                formData={formData} 
                updateFormData={updateFormData} 
              />
              
              {/* Only show map preview if coordinates are set */}
              {formData.coordinates.latitude !== 0 && formData.coordinates.longitude !== 0 && (
                <div className="mt-6">
                  <h3 className="text-lg font-medium mb-2">Location Preview</h3>
                  <LocationMapPreview 
                    latitude={formData.coordinates.latitude} 
                    longitude={formData.coordinates.longitude} 
                  />
                </div>
              )}
            </div>
          </TabsContent>
          <TabsContent value="details">
            <LocationDetails />
          </TabsContent>
          <TabsContent value="media">
            <LocationMedia locationId={locationId || undefined} />
          </TabsContent>
          <TabsContent value="permissions">
            <LocationPermissions />
          </TabsContent>
        </Tabs>

        <DialogFooter className="flex justify-between mt-8 pt-6 border-t">
          <div>
            <Button variant="outline" onClick={handleClose} disabled={isLoading}>
              Cancel
            </Button>
          </div>
          <div className="flex gap-2">
            <Button variant="outline" onClick={handlePrevious} disabled={activeTab === "basic-info" || isLoading}>
              <ArrowLeft className="mr-2 h-4 w-4" />
              Previous
            </Button>
            <Button variant="outline" onClick={handleSaveAsDraft} disabled={isLoading}>
              {isLoading ? <Loader2 className="mr-2 h-4 w-4 animate-spin" /> : <Save className="mr-2 h-4 w-4" />}
              Save as Draft
            </Button>
            {activeTab === tabs[tabs.length - 1].id ? (
              <Button onClick={handleCreateLocation} disabled={isLoading}>
                {isLoading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                Add Location
              </Button>
            ) : (
              <Button onClick={handleNext} disabled={isLoading}>
                Next
                <ArrowRight className="ml-2 h-4 w-4" />
              </Button>
            )}
          </div>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
