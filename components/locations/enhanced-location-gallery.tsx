"use client";

import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { useState } from "react";
import Image from "next/image";
import { ChevronLeft, ChevronRight, ImageIcon } from "lucide-react";

interface MediaItem {
  id: string;
  url: string;
  filename: string;
  type: "image" | "video";
  thumbnailUrl?: string;
  uploadedBy: {
    id: string;
    name: string;
  };
  uploadedAt: string;
}

interface EnhancedLocationGalleryProps {
  media: MediaItem[];
  showControls?: boolean;
  className?: string;
}

export function EnhancedLocationGallery({ 
  media, 
  showControls = true,
  className = ""
}: EnhancedLocationGalleryProps) {
  const [activeIndex, setActiveIndex] = useState(0);
  const activeItem = media.length > 0 ? media[activeIndex] : null;

  const images = media.filter((item) => item.type === "image");
  const videos = media.filter((item) => item.type === "video");

  const goToPrevious = () => {
    setActiveIndex((prev) => (prev === 0 ? media.length - 1 : prev - 1));
  };

  const goToNext = () => {
    setActiveIndex((prev) => (prev === media.length - 1 ? 0 : prev + 1));
  };

  const handleThumbnailClick = (index: number) => {
    setActiveIndex(index);
  };

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle>Location Gallery</CardTitle>
      </CardHeader>
      <CardContent>
        {media.length === 0 ? (
          <div className="flex flex-col items-center justify-center p-8 space-y-4 border rounded-md bg-muted/20">
            <div className="p-4 rounded-full bg-muted">
              <ImageIcon className="h-10 w-10 text-muted-foreground" />
            </div>
            <div className="text-center">
              <h3 className="font-medium">No Images Available</h3>
              <p className="text-sm text-muted-foreground">
                Images for this location will appear here once uploaded.
              </p>
            </div>
          </div>
        ) : (
          <div className="space-y-4">
            {/* Main display area */}
            {activeItem && (
              <div className="overflow-hidden rounded-md border relative">
                {activeItem.type === "image" ? (
                  <div className="relative aspect-video w-full">
                    <Image
                      src={activeItem.url}
                      alt={activeItem.filename}
                      fill
                      className="object-cover"
                    />
                  </div>
                ) : (
                  <div className="relative aspect-video w-full">
                    <video
                      src={activeItem.url}
                      controls
                      className="h-full w-full"
                    >
                      <track kind="captions" src="" label="English" />
                    </video>
                  </div>
                )}
                
                {/* Navigation controls */}
                {showControls && media.length > 1 && (
                  <>
                    <Button
                      variant="ghost"
                      size="icon"
                      className="absolute left-2 top-1/2 -translate-y-1/2 bg-black/30 hover:bg-black/50 rounded-full h-8 w-8 p-1.5"
                      onClick={goToPrevious}
                    >
                      <ChevronLeft className="h-5 w-5 text-white" />
                      <span className="sr-only">Previous</span>
                    </Button>
                    <Button
                      variant="ghost"
                      size="icon"
                      className="absolute right-2 top-1/2 -translate-y-1/2 bg-black/30 hover:bg-black/50 rounded-full h-8 w-8 p-1.5"
                      onClick={goToNext}
                    >
                      <ChevronRight className="h-5 w-5 text-white" />
                      <span className="sr-only">Next</span>
                    </Button>
                    <div className="absolute bottom-2 left-1/2 -translate-x-1/2 flex gap-1">
                      {media.map((item, index) => (
                        <Button
                          key={item.id}
                          variant="ghost"
                          size="icon"
                          className={`h-2 w-2 rounded-full p-0 ${
                            index === activeIndex
                              ? "bg-white"
                              : "bg-white/50 hover:bg-white/75"
                          }`}
                          onClick={() => handleThumbnailClick(index)}
                        >
                          <span className="sr-only">Go to slide {index + 1}</span>
                        </Button>
                      ))}
                    </div>
                  </>
                )}
                
                <div className="p-2 text-sm">
                  <p className="font-medium">{activeItem.filename}</p>
                  <p className="text-xs text-muted-foreground">
                    Uploaded by {activeItem.uploadedBy.name} on{" "}
                    {new Date(activeItem.uploadedAt).toLocaleDateString()}
                  </p>
                </div>
              </div>
            )}

            {/* Thumbnails */}
            <div className="grid grid-cols-4 gap-2">
              {media.map((item, index) => (
                <button
                  type="button"
                  key={item.id}
                  className={`relative aspect-square w-full overflow-hidden rounded-md border ${
                    index === activeIndex
                      ? "ring-2 ring-primary ring-offset-2"
                      : ""
                  }`}
                  onClick={() => handleThumbnailClick(index)}
                >
                  {item.type === "image" ? (
                    <Image
                      src={item.thumbnailUrl || item.url}
                      alt={item.filename}
                      fill
                      className="object-cover"
                    />
                  ) : (
                    <div className="flex h-full w-full items-center justify-center bg-muted">
                      <span className="text-xs">Video</span>
                    </div>
                  )}
                </button>
              ))}
            </div>

            {/* Stats */}
            <div className="flex justify-between text-sm text-muted-foreground">
              <span>{images.length} images</span>
              <span>{videos.length} videos</span>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
