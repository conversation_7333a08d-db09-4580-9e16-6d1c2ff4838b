"use client";

import { 
  File as FileIcon, 
  FileText as FileTextIcon, 
  FileImage as FileImageIcon, 
  Archive as FileArchiveIcon,
  FileText as FilePdfIcon,
  FileCode as FileCodeIcon,
  FileSpreadsheet as FileSpreadsheetIcon,
  FileAudio as FileAudioIcon,
  FileVideo as FileVideoIcon,
  Presentation as PresentationIcon
} from "lucide-react";

interface DocumentTypeIconProps {
  fileType: string;
  className?: string;
}

export function DocumentTypeIcon({ fileType, className = "h-5 w-5" }: DocumentTypeIconProps) {
  const type = fileType.toLowerCase();
  
  if (type.includes('pdf') || type === 'pdf') {
    return <FilePdfIcon className={`${className} text-red-500`} />;
  }
  
  if (type.includes('doc') || type.includes('docx') || type === 'doc' || type === 'docx') {
    return <FileTextIcon className={`${className} text-blue-500`} />;
  }
  
  if (type.includes('xls') || type.includes('xlsx') || type.includes('csv') || 
      type.includes('tsv') || type === 'xls' || type === 'xlsx' || 
      type === 'csv' || type === 'tsv' || type === 'spreadsheet') {
    return <FileSpreadsheetIcon className={`${className} text-green-500`} />;
  }
  
  if (type.includes('ppt') || type.includes('pptx') || type === 'ppt' || 
      type === 'pptx' || type === 'presentation') {
    return <PresentationIcon className={`${className} text-orange-500`} />;
  }
  
  if (type.includes('jpg') || type.includes('jpeg') || type.includes('png') || 
      type.includes('gif') || type.includes('webp') || type.includes('svg') || 
      type === 'image') {
    return <FileImageIcon className={`${className} text-purple-500`} />;
  }
  
  if (type.includes('mp4') || type.includes('webm') || type.includes('mov') || 
      type.includes('avi') || type === 'video') {
    return <FileVideoIcon className={`${className} text-pink-500`} />;
  }
  
  if (type.includes('mp3') || type.includes('wav') || type.includes('ogg') || 
      type === 'audio') {
    return <FileAudioIcon className={`${className} text-yellow-500`} />;
  }
  
  if (type.includes('html') || type.includes('css') || type.includes('js') || 
      type.includes('ts') || type.includes('json') || type.includes('xml') || 
      type === 'code') {
    return <FileCodeIcon className={`${className} text-cyan-500`} />;
  }
  
  if (type.includes('zip') || type.includes('rar') || type.includes('tar') || 
      type.includes('gz') || type === 'archive') {
    return <FileArchiveIcon className={`${className} text-gray-500`} />;
  }
  
  return <FileIcon className={`${className} text-muted-foreground`} />;
}
