"use client";

import { Badge } from "@/components/ui/badge";
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON>Footer } from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";
import { MapPin, Calendar, Building } from "lucide-react";
import Link from "next/link";
import Image from "next/image";

import type { Location as LocationModel } from "@/modules/location/model";

interface Location {
  id: string;
  name: string;
  description?: string;
  status: string;
  type: string;
  address: {
    street?: string;
    city?: string;
    state?: string;
    country?: string;
    formatted?: string;
  };
  imageUrl?: string;
  projectId?: string;
  projectName?: string;
  createdAt: string | Date;
}

type LocationGridProps = {
  locations: LocationModel[] | Location[];
  organizationSlug: string;
  isLoading?: boolean;
};

export function LocationGrid({ 
  locations, 
  organizationSlug, 
  isLoading = false 
}: LocationGridProps) {
  if (isLoading) {
    return (
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
        {/* Create an array of unique IDs for skeletons */}
        {[
          "skeleton-1", 
          "skeleton-2", 
          "skeleton-3", 
          "skeleton-4", 
          "skeleton-5", 
          "skeleton-6"
        ].map((id) => (
          <LocationCardSkeleton key={id} />
        ))}
      </div>
    );
  }

  return (
    <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
      {locations.map((location) => (
        <Link key={location.id} href={`/organizations/${organizationSlug}/locations/${location.id}`}>
          <Card className="h-full cursor-pointer overflow-hidden transition-all hover:shadow-md">
            <div className="aspect-video w-full relative">
              {/* Use Image component for better performance */}
              <Image
                src={'imageUrl' in location && location.imageUrl ? location.imageUrl : "/placeholder-location.jpg"}
                alt={location.name}
                fill
                className="object-cover"
                sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
                placeholder="blur"
                blurDataURL="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNk+P+/HgAEtAJJXF+wHAAAAABJRU5ErkJggg=="
                loading="lazy"
              />
              {/* Status badge overlay */}
              <div className="absolute top-2 right-2">
                <Badge variant={getStatusVariant(location.status)}>
                  {location.status.charAt(0).toUpperCase() + location.status.slice(1)}
                </Badge>
              </div>
            </div>
            <CardContent className="p-4">
              <h3 className="line-clamp-1 font-medium text-lg">{location.name}</h3>
              <p className="mt-1 line-clamp-2 text-sm text-muted-foreground">
                {location.description || "No description available"}
              </p>
              <div className="mt-2 flex items-center text-xs text-muted-foreground">
                <MapPin className="h-3 w-3 mr-1" />
                <span className="line-clamp-1">{formatAddress(location.address)}</span>
              </div>
            </CardContent>
            <CardFooter className="flex justify-between border-t p-4 pt-2 text-xs text-muted-foreground">
              <div className="flex items-center">
                <Building className="h-3 w-3 mr-1" />
                <span>{location.type.charAt(0).toUpperCase() + location.type.slice(1)}</span>
              </div>
              <div className="flex items-center">
                <Calendar className="h-3 w-3 mr-1" />
                <span>
                  {new Date(location.createdAt).toLocaleDateString(undefined, {
                    year: "numeric",
                    month: "short",
                    day: "numeric",
                  })}
                </span>
              </div>
            </CardFooter>
          </Card>
        </Link>
      ))}
    </div>
  );
}

function LocationCardSkeleton() {
  return (
    <Card className="h-full overflow-hidden">
      <Skeleton className="aspect-video w-full" />
      <CardContent className="p-4">
        <Skeleton className="h-6 w-3/4 mb-2" />
        <Skeleton className="h-4 w-full mb-1" />
        <Skeleton className="h-4 w-2/3" />
        <div className="mt-2 flex items-center">
          <Skeleton className="h-3 w-3 mr-1 rounded-full" />
          <Skeleton className="h-3 w-1/2" />
        </div>
      </CardContent>
      <CardFooter className="flex justify-between border-t p-4 pt-2">
        <Skeleton className="h-3 w-1/4" />
        <Skeleton className="h-3 w-1/4" />
      </CardFooter>
    </Card>
  );
}

function formatAddress(address: {
  street?: string;
  city?: string;
  state?: string;
  country?: string;
  formatted?: string;
}): string {
  if (address.formatted) return address.formatted;
  
  if (address.city && address.state) {
    return `${address.city}, ${address.state}`;
  }
  if (address.city) {
    return address.city;
  }
  if (address.state) {
    return address.state;
  }
  if (address.country) {
    return address.country;
  }
  return "No address";
}

function getStatusVariant(status: string): "default" | "secondary" | "destructive" | "outline" {
  switch (status.toLowerCase()) {
    case "approved":
      return "default";
    case "pending":
      return "secondary";
    case "rejected":
      return "destructive";
    case "secured":
      return "default"; // We could use a custom variant for secured if available
    case "unavailable":
      return "outline";
    default:
      return "outline";
  }
}
