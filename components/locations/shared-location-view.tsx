"use client"

import { useState, useEffect, useCallback } from "react"
import { use<PERSON><PERSON><PERSON> } from "next/navigation"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { <PERSON><PERSON>, <PERSON>alogContent, <PERSON>alogHeader, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Footer } from "@/components/ui/dialog"
import { Loader2, Lock, ArrowLeft } from "lucide-react"
import { toast } from "sonner"
import { LocationDetailsClient } from "@/components/locations/location-details-client"

interface SharedLocationViewProps {
  token: string
}

type SharedLocationData = {
  id: string
  organizationId: string
  location: any // Use proper type from location model
  viewMode: string
  isPasswordProtected: boolean
}

export function SharedLocationView({ token }: SharedLocationViewProps) {
  const router = useRouter()
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [locationData, setLocationData] = useState<SharedLocationData | null>(null)
  const [isPasswordModalOpen, setIsPasswordModalOpen] = useState(false)
  const [password, setPassword] = useState("")
  const [isSubmitting, setIsSubmitting] = useState(false)

  const fetchLocationData = useCallback(async (pwd?: string) => {
    try {
      setIsLoading(true)
      setError(null)
      
      const url = pwd 
        ? `/api/locations/shared/${token}?password=${encodeURIComponent(pwd)}`
        : `/api/locations/shared/${token}`
      
      const response = await fetch(url)
      
      if (!response.ok) {
        const data = await response.json()
        
        if (response.status === 401 && data.isPasswordProtected) {
          setIsPasswordModalOpen(true)
          return
        }
        
        throw new Error(data.error || "Failed to load shared location")
      }
      
      const data = await response.json()
      setLocationData(data)
    } catch (err) {
      console.error("Error loading shared location:", err)
      setError(err instanceof Error ? err.message : "Failed to load shared location")
      toast.error(err instanceof Error ? err.message : "Failed to load shared location")
    } finally {
      setIsLoading(false)
    }
  }, [token])
  
  const handlePasswordSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!password.trim()) {
      toast.error("Please enter a password")
      return
    }
    
    setIsSubmitting(true)
    
    try {
      await fetchLocationData(password)
      setIsPasswordModalOpen(false)
    } finally {
      setIsSubmitting(false)
    }
  }
  
  const handleBackToApp = () => {
    router.push("/")
  }

  useEffect(() => {
    fetchLocationData()
  }, [fetchLocationData])

  if (isLoading) {
    return (
      <div className="flex flex-col items-center justify-center min-h-screen">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
        <p className="mt-4 text-muted-foreground">Loading shared location...</p>
      </div>
    )
  }

  if (error) {
    return (
      <div className="flex flex-col items-center justify-center min-h-screen">
        <div className="text-center max-w-md mx-auto p-6 space-y-4">
          <h2 className="text-2xl font-bold">Error Loading Location</h2>
          <p className="text-muted-foreground">{error}</p>
          <Button onClick={handleBackToApp}>
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to App
          </Button>
        </div>
      </div>
    )
  }

  return (
    <>
      <div className="flex flex-col min-h-screen">
        <header className="bg-background border-b p-4 flex items-center justify-between">
          <div className="flex items-center">
            <h1 className="text-xl font-bold">Scene-o-matic Shared Location</h1>
          </div>
          <Button variant="outline" onClick={handleBackToApp}>
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to App
          </Button>
        </header>
        
        <main className="flex-1 container py-6">
          {locationData ? (
            <LocationDetailsClient 
              locationId={locationData.location.id}
              organizationSlug={locationData.organizationId}
              isSharedView={true}
              viewMode={locationData.viewMode}
              location={locationData.location}
            />
          ) : (
            <div className="flex items-center justify-center h-full">
              <p className="text-muted-foreground">No location data available</p>
            </div>
          )}
        </main>
      </div>
      
      <Dialog open={isPasswordModalOpen} onOpenChange={setIsPasswordModalOpen}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle className="flex items-center">
              <Lock className="mr-2 h-4 w-4" />
              Password Protected Location
            </DialogTitle>
          </DialogHeader>
          
          <form onSubmit={handlePasswordSubmit} className="space-y-4 py-4">
            <p className="text-sm text-muted-foreground">
              This shared location is password protected. Please enter the password to view it.
            </p>
            
            <Input
              type="password"
              placeholder="Enter password"
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              disabled={isSubmitting}
              autoFocus
            />
            
            <DialogFooter>
              <Button type="button" variant="outline" onClick={handleBackToApp} disabled={isSubmitting}>
                Cancel
              </Button>
              <Button type="submit" disabled={isSubmitting || !password.trim()}>
                {isSubmitting ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Verifying...
                  </>
                ) : (
                  "Access Location"
                )}
              </Button>
            </DialogFooter>
          </form>
        </DialogContent>
      </Dialog>
    </>
  )
}
