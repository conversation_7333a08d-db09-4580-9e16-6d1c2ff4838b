"use client"

import { useState, useEffect } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, Card<PERSON>ooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Switch } from "@/components/ui/switch"
import { Label } from "@/components/ui/label"
// Remove the toast import since it's not available
import { useOrganization } from "@/hooks/use-organization"

export function OrganizationAuthSettings() {
  const { organization } = useOrganization()
  // Create a simple toast function
  const toast = (props: { title: string; description: string; variant?: string }) => {
    console.log(`${props.title}: ${props.description}`)
    // In a real app, you would use a toast library
    alert(`${props.title}: ${props.description}`)
  }
  const [isLoading, setIsLoading] = useState(false)
  const [settings, setSettings] = useState<{
    email_jit_provisioning: string;
    email_invites: string;
    email_allowed_domains?: string[];
  }>({
    email_jit_provisioning: "NOT_ALLOWED",
    email_invites: "NOT_ALLOWED",
    email_allowed_domains: [],
  })

  // Fetch current settings when component mounts
  useEffect(() => {
    if (organization?.id) {
      fetchSettings()
    }
  }, [organization?.id])

  const fetchSettings = async () => {
    if (!organization?.id) return

    setIsLoading(true)
    try {
      const response = await fetch(`/api/organizations/${organization.id}/auth-settings`)
      const data = await response.json()

      if (data.success && data.organization) {
        setSettings({
          email_jit_provisioning: data.organization.email_jit_provisioning || "NOT_ALLOWED",
          email_invites: data.organization.email_invites || "NOT_ALLOWED",
          email_allowed_domains: data.organization.email_allowed_domains || [],
        })
      }
    } catch (error) {
      console.error("Error fetching organization auth settings:", error)
      toast({
        title: "Error",
        description: "Failed to load organization authentication settings",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  const updateSettings = async () => {
    if (!organization?.id) return

    setIsLoading(true)
    try {
      const response = await fetch(`/api/organizations/${organization.id}/auth-settings`, {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(settings),
      })

      const data = await response.json()

      if (data.success) {
        toast({
          title: "Settings Updated",
          description: "Organization authentication settings have been updated successfully",
        })
      } else {
        throw new Error(data.error || "Failed to update settings")
      }
    } catch (error) {
      console.error("Error updating organization auth settings:", error)
      toast({
        title: "Error",
        description: "Failed to update organization authentication settings",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  // We removed the JIT provisioning option since it doesn't support common domains

  const handleInvitesChange = (checked: boolean) => {
    setSettings(prev => ({
      ...prev,
      // For invites, we can use ALL_ALLOWED directly
      email_invites: checked ? "ALL_ALLOWED" : "NOT_ALLOWED",
    }))
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Organization Authentication Settings</CardTitle>
        <CardDescription>
          Configure how users can join your organization
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* JIT provisioning with common domains is not supported by Stytch, so we hide this option */}
        
        <div className="flex items-center justify-between space-x-2">
          <div className="space-y-0.5">
            <Label htmlFor="email-invites">Allow Any Email Domain for Invitations</Label>
            <p className="text-sm text-muted-foreground">
              When enabled, you can invite users with any email domain to your organization
            </p>
          </div>
          <Switch
            id="email-invites"
            checked={settings.email_invites === "ALL_ALLOWED"}
            onChange={(e) => handleInvitesChange(e.target.checked)}
            disabled={isLoading}
          />
        </div>
      </CardContent>
      <CardFooter>
        <Button onClick={updateSettings} disabled={isLoading}>
          {isLoading ? "Saving..." : "Save Settings"}
        </Button>
      </CardFooter>
    </Card>
  )
}
