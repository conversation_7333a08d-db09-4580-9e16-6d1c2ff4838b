import { DashboardShell } from "@/components/dashboard/dashboard-shell"
import { DashboardHeader } from "@/components/dashboard/dashboard-header"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { Skeleton } from "@/components/ui/skeleton"
import { Plus, Layers, MapPin, Share } from "lucide-react"

export function MapPageSkeleton() {
  return (
    <DashboardShell>
      <DashboardHeader title="Map">
        <div className="flex items-center gap-2">
          <Button variant="outline" disabled>
            <Share className="mr-2 h-4 w-4" />
            Share
          </Button>
          <Button disabled>
            <Plus className="mr-2 h-4 w-4" />
            Add Location
          </Button>
        </div>
      </DashboardHeader>

      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div className="md:col-span-1 space-y-6">
          <Card>
            <CardContent className="p-4 space-y-4">
              <div className="relative">
                <Skeleton className="h-9 w-full" />
              </div>

              <Tabs defaultValue="locations" className="w-full">
                <TabsList className="w-full">
                  <TabsTrigger 
                    value="locations" 
                    className="flex-1"
                    disabled
                  >
                    <MapPin className="mr-2 h-4 w-4" />
                    Locations
                  </TabsTrigger>
                  <TabsTrigger 
                    value="layers" 
                    className="flex-1"
                    disabled
                  >
                    <Layers className="mr-2 h-4 w-4" />
                    Layers
                  </TabsTrigger>
                </TabsList>
                <TabsContent value="locations" className="mt-4 space-y-4">
                  <div className="space-y-3">
                    {[
                      'skeleton-location-1',
                      'skeleton-location-2',
                      'skeleton-location-3',
                      'skeleton-location-4',
                      'skeleton-location-5'
                    ].map((id) => (
                      <Skeleton key={id} className="h-24 w-full" />
                    ))}
                  </div>
                </TabsContent>
              </Tabs>

              <div className="pt-2 space-y-4">
                <div className="flex gap-2">
                  <Skeleton className="h-9 w-full" />
                  <Skeleton className="h-9 w-full" />
                </div>
                
                <div className="flex justify-between items-center mt-4 px-1">
                  <Skeleton className="h-8 w-20" />
                  <Skeleton className="h-8 w-16" />
                  <Skeleton className="h-8 w-20" />
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        <div className="md:col-span-3">
          <Card className="overflow-hidden">
            <CardContent className="p-0">
              <Skeleton className="h-[600px] w-full" />
            </CardContent>
          </Card>
        </div>
      </div>
    </DashboardShell>
  )
}
