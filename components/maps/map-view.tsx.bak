"use client"

import { useState, useEffect, useRef, useCallback } from "react"
import mapboxgl from "mapbox-gl"
import "mapbox-gl/dist/mapbox-gl.css"
import { Loader2 } from "lucide-react"
import { MapFilterModal } from "./map-filter-modal"
import { MapControls } from "./map-controls"
import type { ClusterSettings } from "@/modules/map/model"
import { defaultClusterSettings } from "@/modules/map/model"
import type { Location } from "@/modules/location/model"
import { useMap } from "@/providers/map-provider"
import { toast } from "sonner"
import { LocationDetailModal } from "./location-detail-modal"

type MapViewProps = {
  height?: string
  width?: string
  interactive?: boolean
  showControls?: boolean
  className?: string
  clusterSettings?: ClusterSettings
  // Add props for standalone usage outside of MapProvider
  locations?: Location[]
  onLocationSelect?: (location: Location) => void
}

export function MapView({
  height = "100%",
  width = "100%",
  interactive = true,
  className,
  clusterSettings = defaultClusterSettings,
  // Add props with defaults from context
  locations: propLocations,
  onLocationSelect,
}: MapViewProps) {
  // Get locations and state from map context
  const { 
    locations: contextLocations, 
    isLoading: isDataLoading, 
    error: dataError,
    selectedLocation,
    setSelectedLocation: contextSetSelectedLocation,
    viewSettings,
    setViewSettings,
    clusterSettings: contextClusterSettings,
  } = useMap()
  
  // Use prop locations if provided, otherwise use context locations
  const locations = propLocations || contextLocations
  
  // Use prop onLocationSelect if provided, otherwise use context setSelectedLocation
  const handleLocationSelect = onLocationSelect || contextSetSelectedLocation

  const mapContainer = useRef<HTMLDivElement>(null)
  const map = useRef<mapboxgl.Map | null>(null)
  const markersRef = useRef<{ [key: string]: mapboxgl.Marker }>({})
  const popupsRef = useRef<{ [key: string]: mapboxgl.Popup }>({})
  const clusterSourceRef = useRef<string | null>(null)

  const [mapLoaded, setMapLoaded] = useState(false)
  const [isLoading, setIsLoading] = useState(true)
  const [mapboxToken, setMapboxToken] = useState<string | null>(null)
  const [tokenError, setTokenError] = useState<string | null>(null)
  // This function is used in event handlers but we don't need to track the state
  // It's kept for future use if needed
  const setHoveredLocationId = () => {
    // No-op function that doesn't use parameters
    // This is just to maintain compatibility with the existing code
  }
  const [isClustering, setIsClustering] = useState(clusterSettings.enabled)
  const [isDetailModalOpen, setIsDetailModalOpen] = useState(false)
  const [isFilterModalOpen, setIsFilterModalOpen] = useState(false)

  // Merge cluster settings from props and context
  const mergedClusterSettings = {
    ...defaultClusterSettings,
    ...clusterSettings,
    ...contextClusterSettings,
  }

  // Fetch Mapbox token from API
  useEffect(() => {
    async function fetchMapboxToken() {
      try {
        const response = await fetch("/api/mapbox-token")
        if (!response.ok) {
          throw new Error("Failed to fetch Mapbox token")
        }
        const data = await response.json()
        setMapboxToken(data.token)
      } catch (error) {
        console.error("Error fetching Mapbox token:", error)
        setTokenError("Failed to load map: API key error")
        setIsLoading(false)
        toast.error("Failed to load map: API key error")
      }
    }

    fetchMapboxToken()
  }, [])

  // Initialize map
  useEffect(() => {
    if (!mapContainer.current || map.current || !mapboxToken) return

    setIsLoading(true)

    // Set the token
    mapboxgl.accessToken = mapboxToken

    // Create map with performance optimizations
    map.current = new mapboxgl.Map({
      container: mapContainer.current,
      style: viewSettings.styleId || "mapbox://styles/mapbox/dark-v11", // Use style from settings or default to dark
      center: [viewSettings.center.longitude, viewSettings.center.latitude],
      zoom: viewSettings.zoom,
      pitch: viewSettings.pitch,
      bearing: viewSettings.bearing,
      interactive,
      renderWorldCopies: false, // Improves performance
      antialias: true, // Better visual quality
      attributionControl: false, // Remove attribution control for cleaner UI
      fadeDuration: 100, // Faster fade transitions
      maxZoom: 18, // Limit max zoom to prevent performance issues
    })

    // We're not adding the default Mapbox navigation controls as we have our own custom controls

    map.current.on("load", () => {
      setMapLoaded(true)
      setIsLoading(false)

      // Save map view settings when map moves - debounced to improve performance
      if (map.current) {
        // Use a timeout to debounce frequent updates
        let moveEndTimeout: NodeJS.Timeout | null = null;
        
        map.current.on("moveend", () => {
          if (!map.current) return;
          
          // Clear previous timeout if it exists
          if (moveEndTimeout) clearTimeout(moveEndTimeout);
          
          // Set a new timeout
          moveEndTimeout = setTimeout(() => {
            const center = map.current?.getCenter();
            const zoom = map.current?.getZoom();
            const pitch = map.current?.getPitch();
            const bearing = map.current?.getBearing();
            
            if (center && typeof zoom === 'number' && typeof pitch === 'number' && typeof bearing === 'number') {
              setViewSettings({
                center: {
                  latitude: center.lat,
                  longitude: center.lng,
                },
                zoom,
                pitch,
                bearing,
              });
            }
          }, 300); // 300ms debounce
        });
      }
    })

    // Clean up on unmount
    return () => {
      if (map.current) {
        // Remove all event listeners to prevent memory leaks
        map.current.remove();
        map.current = null;
      }
    }
  }, [
    interactive, 
    mapboxToken, 
    viewSettings.styleId, 
    viewSettings.center.longitude,
    viewSettings.center.latitude,
    viewSettings.zoom,
    viewSettings.pitch,
    viewSettings.bearing,
    setViewSettings
  ])

  // Setup clustering
  const setupClustering = useCallback(() => {
    if (!map.current || !mapLoaded || !mergedClusterSettings.enabled) return

    try {
      // Remove existing cluster layers and source in the correct order
      const layersToRemove = ['unclustered-point', 'cluster-count', 'clusters'];
      
      // First remove all layers that might be using the source
      for (const layerId of layersToRemove) {
        if (map.current?.getLayer(layerId)) {
          map.current.removeLayer(layerId);
        }
      }
      
      // Then remove the source after all layers are removed
      if (clusterSourceRef.current && map.current.getSource(clusterSourceRef.current)) {
        map.current.removeSource(clusterSourceRef.current);
      }
      
      // Clear existing markers
      for (const marker of Object.values(markersRef.current)) {
        marker.remove();
      }
      markersRef.current = {}
      for (const popup of Object.values(popupsRef.current)) {
        popup.remove();
      }
      popupsRef.current = {}
      
      clusterSourceRef.current = null;

      // Create a new source ID
      const sourceId = `locations-${Date.now()}`;
      clusterSourceRef.current = sourceId;

      // Create GeoJSON features from locations
      const features = locations
        .filter(location => {
          // Filter out locations with invalid coordinates
          return location.coordinates && 
                 typeof location.coordinates.latitude === 'number' && 
                 typeof location.coordinates.longitude === 'number' &&
                 !Number.isNaN(location.coordinates.latitude) && 
                 !Number.isNaN(location.coordinates.longitude);
        })
        .map(location => ({
          type: 'Feature' as const,
          properties: {
            id: location.id,
            name: location.name,
            type: location.type,
            status: location.status,
            color: getMarkerColor(location.type),
            dailyRate: location.dailyRate,
            hourlyRate: location.hourlyRate,
            address: location.address ? JSON.stringify(location.address) : null,
          },
          geometry: {
            type: 'Point' as const,
            coordinates: [location.coordinates.longitude, location.coordinates.latitude]
          }
        }));

      // Add a new source with clustering enabled
      map.current.addSource(sourceId, {
        type: 'geojson',
        data: {
          type: 'FeatureCollection',
          features
        },
        cluster: true,
        clusterMaxZoom: mergedClusterSettings.maxZoom,
        clusterRadius: mergedClusterSettings.radius
      });

      // Check if layers already exist before adding them
      if (!map.current.getLayer('clusters')) {
        // Add a layer for the clusters
        map.current.addLayer({
          id: 'clusters',
          type: 'circle',
          source: sourceId,
          filter: ['has', 'point_count'],
          paint: {
            'circle-color': [
              'step',
              ['get', 'point_count'],
              '#2323FF', // Neon blue for small clusters
              10,
              '#16FF00', // Neon green for medium clusters
              30,
              '#F61981'  // Neon pink for large clusters
            ],
            'circle-radius': [
              'step',
              ['get', 'point_count'],
              20,  // Size for small clusters
              10,
              30,  // Size for medium clusters
              30,
              40   // Size for large clusters
            ],
            'circle-stroke-width': 2,
            'circle-stroke-color': '#FFFFFF',
            'circle-opacity': 0.8,
          }
        });
      }

      if (!map.current.getLayer('cluster-count')) {
        // Add a layer for the cluster counts
        map.current.addLayer({
          id: 'cluster-count',
          type: 'symbol',
          source: sourceId,
          filter: ['has', 'point_count'],
          layout: {
            'text-field': '{point_count_abbreviated}',
            'text-font': ['DIN Offc Pro Medium', 'Arial Unicode MS Bold'],
            'text-size': 14
          },
          paint: {
            'text-color': '#FFFFFF'
          }
        });
      }

      // We'll create custom markers for unclustered points instead of using a layer
      // This allows us to have the same marker appearance as when clustering is disabled

      // Remove any existing event handlers to prevent duplicates
      if (map.current) {
        // Type the event handlers properly
        const clusterClickHandler = (e: mapboxgl.MapMouseEvent) => {
          const features = map.current?.queryRenderedFeatures(e.point, { layers: ['clusters'] });
          if (!features || features.length === 0 || !map.current) return;

          const clusterId = features[0].properties?.cluster_id;
          if (!clusterId) return;

          // Use clusterSourceRef.current instead of sourceId
          if (!clusterSourceRef.current) return;
          
          const source = map.current.getSource(clusterSourceRef.current) as mapboxgl.GeoJSONSource;
          source.getClusterExpansionZoom(clusterId, (err, zoom) => {
            if (err || !map.current) return;

            // Cast to a more specific type to avoid TypeScript errors
            const geometry = features[0].geometry as GeoJSON.Point;
            const coordinates = geometry.coordinates.slice() as [number, number];
            
            map.current.easeTo({
              center: coordinates,
              zoom: zoom || 14, // Default to zoom level 14 if zoom is null or undefined
              duration: 500, // Add smooth animation
            });
          });
        };

        const mouseEnterHandler = () => {
          if (map.current) map.current.getCanvas().style.cursor = 'pointer';
        };
        
        const mouseLeaveHandler = () => {
          if (map.current) map.current.getCanvas().style.cursor = '';
        };

        // Remove existing handlers individually to avoid type issues
        map.current.off('click', 'clusters');
        map.current.off('mouseenter', 'clusters');
        map.current.off('mouseleave', 'clusters');

        // Add new handlers with proper typing
        map.current.on('click', 'clusters', clusterClickHandler);
        map.current.on('mouseenter', 'clusters', mouseEnterHandler);
        map.current.on('mouseleave', 'clusters', mouseLeaveHandler);
      }

      // Add a listener for when the map data becomes idle (after loading)
      // This is when we'll add our custom markers for unclustered points
      map.current.on('idle', () => {
        if (!map.current || !clusterSourceRef.current) return;
        
        // Get all unclustered points
        const unclusteredFeatures = map.current.querySourceFeatures(clusterSourceRef.current, {
          filter: ['!', ['has', 'point_count']]
        });
        
        // Create a Set to track which location IDs we've already created markers for
        const markerIds = new Set(Object.keys(markersRef.current));
        
        // Create markers for each unclustered point
        for (const feature of unclusteredFeatures) {
          const props = feature.properties;
          if (!props || !props.id) continue;
          
          // Skip if we already have a marker for this location
          if (markerIds.has(props.id)) continue;
          
          // Create marker element with custom styling
          const markerElement = document.createElement("div");
          markerElement.className = "marker";
          markerElement.style.width = "40px";
          markerElement.style.height = "40px";
          markerElement.style.borderRadius = "8px";
          markerElement.style.backgroundColor = "white";
          markerElement.style.border = `2px solid ${props.color}`;
          markerElement.style.boxShadow = `0 0 10px ${props.color}80`; // Add neon glow
          markerElement.style.cursor = "pointer";
          markerElement.style.transition = "all 0.3s ease";
          markerElement.style.display = "flex";
          markerElement.style.alignItems = "center";
          markerElement.style.justifyContent = "center";
          markerElement.style.fontSize = "14px";
          markerElement.style.fontWeight = "bold";
          markerElement.style.color = "#333";
          
          // Add price text to the marker
          const price = props.dailyRate ? `$${Math.round(props.dailyRate / 1000)}K` : (
            props.hourlyRate ? `$${Math.round(props.hourlyRate)}` : "N/A"
          );
          markerElement.textContent = price;
          
          // Store the location ID on the element for reference
          markerElement.dataset.locationId = props.id;
          
          // Create popup for hover effect
          const popup = new mapboxgl.Popup({
            closeButton: false,
            closeOnClick: false,
            offset: 25,
            className: "map-popup"
          });
          
          // Parse address if it exists
          let address = '';
          if (props.address) {
            try {
              const addressObj = JSON.parse(props.address);
              address = addressObj.formatted || '';
            } catch (e) {
              console.error("Error parsing address:", e);
            }
          }
          
          // Set popup content with enhanced styling
          popup.setHTML(`
            <div class="p-3 rounded-md bg-white border-2 border-${getMarkerBorderClass(props.type)} shadow-lg max-w-xs">
              <div class="flex justify-between items-start">
                <div>
                  <h3 class="font-medium text-gray-900">${props.name}</h3>
                  <p class="text-xs text-gray-600">${props.type} • ${props.status}</p>
                  <p class="text-xs text-gray-600 mt-1">${address}</p>
                </div>
                <div class="flex items-center">
                  <span class="text-lg font-bold text-gray-900">
                    ${props.dailyRate ? `$${Math.round(props.dailyRate / 1000)}K` : (
                      props.hourlyRate ? `$${Math.round(props.hourlyRate)}` : "N/A"
                    )}
                  </span>
                </div>
              </div>
            </div>
          `);
          
          // Get coordinates from the feature - ensure proper type casting
          const coordinates = (feature.geometry as GeoJSON.Point).coordinates.slice() as [number, number];
          
          // Create marker
          if (map.current) {
            const marker = new mapboxgl.Marker(markerElement)
              .setLngLat(coordinates)
              .addTo(map.current);
            
            // Store references
            markersRef.current[props.id] = marker;
            popupsRef.current[props.id] = popup;
            markerIds.add(props.id);
          }
          
          // Add hover events
          markerElement.addEventListener("mouseenter", () => {
            // Enlarge marker
            markerElement.style.width = "48px";
            markerElement.style.height = "48px";
            markerElement.style.boxShadow = `0 0 15px ${props.color}`;
            markerElement.style.zIndex = "10";
            
            // Show popup
            if (map.current) {
              popup.setLngLat(coordinates)
                .addTo(map.current);
            }
            
            setHoveredLocationId();
          });
          
          markerElement.addEventListener("mouseleave", () => {
            // Reset marker size
            markerElement.style.width = "40px";
            markerElement.style.height = "40px";
            markerElement.style.boxShadow = `0 0 10px ${props.color}80`;
            markerElement.style.zIndex = "0";
            
            // Hide popup
            popup.remove();
            
            setHoveredLocationId();
          });
          
          // Add click event
          markerElement.addEventListener("click", () => {
            const location = locations.find(loc => loc.id === props.id);
            if (location && handleLocationSelect) {
              handleLocationSelect(location);
              
              // Center map on selected location
              map.current?.flyTo({
                center: coordinates,
                zoom: 14,
                essential: true
              });
              
              // Open detail modal
              setIsDetailModalOpen(true);
            }
          });
        }
      });

    } catch (error) {
      console.error("Error setting up clustering:", error);
      // If there's an error, try to clean up
      try {
        if (map.current) {
          const layersToRemove = ['unclustered-point', 'cluster-count', 'clusters'];
          for (const layerId of layersToRemove) {
            if (map.current?.getLayer(layerId)) {
              map.current.removeLayer(layerId);
            }
          }
          
          if (clusterSourceRef.current && map.current.getSource(clusterSourceRef.current)) {
            map.current.removeSource(clusterSourceRef.current);
          }
          
          // Clear markers
          for (const marker of Object.values(markersRef.current)) {
            marker.remove();
          }
          markersRef.current = {};
        }
      } catch (cleanupError) {
        console.error("Error during cleanup:", cleanupError);
      }
    }
  }, [locations, mapLoaded, mergedClusterSettings.enabled, mergedClusterSettings.maxZoom, mergedClusterSettings.radius, handleLocationSelect])

  // Add markers for locations when not using clustering
  useEffect(() => {
    // Debug logging
    console.log("Map markers effect running");
    console.log("Map loaded:", mapLoaded);
    console.log("Clustering enabled:", mergedClusterSettings.enabled);
    console.log("Number of locations:", locations.length);
    if (locations.length > 0) {
      console.log("Sample location:", locations[0]);
      console.log("Sample coordinates:", locations[0].coordinates);
    }
    
    if (!map.current || !mapLoaded || mergedClusterSettings.enabled) {
      // Clear existing markers if clustering is enabled
      if (mergedClusterSettings.enabled) {
        console.log("Clearing markers because clustering is enabled");
        for (const marker of Object.values(markersRef.current)) {
          marker.remove();
        }
        markersRef.current = {}
        for (const popup of Object.values(popupsRef.current)) {
          popup.remove();
        }
        popupsRef.current = {}
      }
      return
    }

    // Clear existing markers
    for (const marker of Object.values(markersRef.current)) {
      marker.remove();
    }
    markersRef.current = {}
    for (const popup of Object.values(popupsRef.current)) {
      popup.remove();
    }
    popupsRef.current = {}

    // Add markers for locations
    for (const location of locations) {
      const { coordinates } = location

      const markerColor = getMarkerColor(location.type)

      // Create marker element with custom styling to match the design
      const markerElement = document.createElement("div")
      markerElement.className = "marker"
      markerElement.style.width = "40px"
      markerElement.style.height = "40px"
      markerElement.style.borderRadius = "8px"
      markerElement.style.backgroundColor = "white"
      markerElement.style.border = `2px solid ${markerColor}`
      markerElement.style.boxShadow = `0 0 10px ${markerColor}80` // Add neon glow
      markerElement.style.cursor = "pointer"
      markerElement.style.transition = "all 0.3s ease"
      markerElement.style.display = "flex"
      markerElement.style.alignItems = "center"
      markerElement.style.justifyContent = "center"
      markerElement.style.fontSize = "14px"
      markerElement.style.fontWeight = "bold"
      markerElement.style.color = "#333"
      
      // Add price text to the marker
      const price = location.dailyRate ? `$${Math.round(location.dailyRate / 1000)}K` : (
        location.hourlyRate ? `$${Math.round(location.hourlyRate)}` : "N/A"
      )
      markerElement.textContent = price

      // Create popup for hover effect
      const popup = new mapboxgl.Popup({
        closeButton: false,
        closeOnClick: false,
        offset: 25,
        className: "map-popup"
      })
      
      // Set popup content with enhanced styling to match the design
      popup.setHTML(`
        <div class="p-3 rounded-md bg-white border-2 border-${getMarkerBorderClass(location.type)} shadow-lg max-w-xs">
          <div class="flex justify-between items-start">
            <div>
              <h3 class="font-medium text-gray-900">${location.name}</h3>
              <p class="text-xs text-gray-600">${location.type} • ${location.status}</p>
              <p class="text-xs text-gray-600 mt-1">${location.address?.formatted || ''}</p>
            </div>
            <div class="flex items-center">
              <span class="text-lg font-bold text-gray-900">
                ${location.dailyRate ? `$${Math.round(location.dailyRate / 1000)}K` : (
                  location.hourlyRate ? `$${Math.round(location.hourlyRate)}` : "N/A"
                )}
              </span>
            </div>
          </div>
        </div>
      `)

      // Create marker
      const marker = new mapboxgl.Marker(markerElement)
        .setLngLat([coordinates.longitude, coordinates.latitude])
        .addTo(map.current)

      // Add hover events
      markerElement.addEventListener("mouseenter", () => {
        // Enlarge marker
        markerElement.style.width = "48px"
        markerElement.style.height = "48px"
        markerElement.style.boxShadow = `0 0 15px ${markerColor}`
        markerElement.style.zIndex = "10"
        
        // Show popup
        if (map.current) {
          popup.setLngLat([coordinates.longitude, coordinates.latitude])
            .addTo(map.current)
        }
        
        setHoveredLocationId()
      })

      markerElement.addEventListener("mouseleave", () => {
        // Reset marker size
        markerElement.style.width = "40px"
        markerElement.style.height = "40px"
        markerElement.style.boxShadow = `0 0 10px ${markerColor}80`
        markerElement.style.zIndex = "0"
        
        // Hide popup
        popup.remove()
        
        setHoveredLocationId()
      })

      // Add click event
      markerElement.addEventListener("click", () => {
        if (handleLocationSelect) {
          handleLocationSelect(location)
          
          // Center map on selected location
          map.current?.flyTo({
            center: [coordinates.longitude, coordinates.latitude],
            zoom: 14,
            essential: true
          })
          
          // Open detail modal
          setIsDetailModalOpen(true)
        }
      })

      // Store references
      markersRef.current[location.id] = marker
      popupsRef.current[location.id] = popup
    })

    // Fit map to show all locations if there are any
    if (locations.length > 0) {
      fitMapToLocations()
    }
  }, [locations, mapLoaded, mergedClusterSettings.enabled, handleLocationSelect])

  // Setup clustering when clustering state changes
  useEffect(() => {
    // Debug logging
    console.log("Clustering effect running");
    console.log("isClustering:", isClustering);
    console.log("Number of locations for clustering:", locations.length);
    
    if (isClustering) {
      setupClustering()
    } else {
      // Remove clustering layers if they exist
      if (map.current && clusterSourceRef.current) {
        console.log("Removing clustering layers");
        if (map.current.getLayer('clusters')) {
          map.current.removeLayer('clusters')
        }
        if (map.current.getLayer('cluster-count')) {
          map.current.removeLayer('cluster-count')
        }
        if (map.current.getLayer('unclustered-point')) {
          map.current.removeLayer('unclustered-point')
        }
        if (map.current.getSource(clusterSourceRef.current)) {
          map.current.removeSource(clusterSourceRef.current)
        }
        clusterSourceRef.current = null
      }
    }
  }, [isClustering, setupClustering, locations.length])

  // Update map when view settings change
  useEffect(() => {
    if (!map.current || !mapLoaded) return

    map.current.jumpTo({
      center: [viewSettings.center.longitude, viewSettings.center.latitude],
      zoom: viewSettings.zoom,
      pitch: viewSettings.pitch,
      bearing: viewSettings.bearing,
    })
  }, [mapLoaded, viewSettings])
  
  // Update map style when styleId changes
  useEffect(() => {
    if (!map.current || !mapLoaded || !viewSettings.styleId) return
    
    map.current.setStyle(viewSettings.styleId)
  }, [mapLoaded, viewSettings.styleId])

  // Highlight selected location
  useEffect(() => {
    if (!selectedLocation) return

    // Highlight the selected marker
    const markerElement = markersRef.current[selectedLocation.id]?.getElement()
    if (markerElement) {
      // Reset all markers first
      for (const marker of Object.values(markersRef.current)) {
        const el = marker.getElement()
        el.style.width = "24px"
        el.style.height = "24px"
        el.style.zIndex = "0"
        const color = getMarkerColor(locations.find(loc => loc.id === marker.getElement().dataset.locationId)?.type || "")
        el.style.boxShadow = `0 0 10px ${color}80`
      }

      // Highlight selected marker
      markerElement.style.width = "32px"
      markerElement.style.height = "32px"
      markerElement.style.zIndex = "10"
      const color = getMarkerColor(selectedLocation.type)
      markerElement.style.boxShadow = `0 0 15px ${color}`
    }

    // If using clustering, fly to the selected location
    if (mergedClusterSettings.enabled && map.current) {
      map.current.flyTo({
        center: [selectedLocation.coordinates.longitude, selectedLocation.coordinates.latitude],
        zoom: Math.max(map.current.getZoom(), 14),
        essential: true
      })
    }
  }, [selectedLocation, locations, mergedClusterSettings.enabled])

  // Get marker color based on location type
  const getMarkerColor = (type: string) => {
    switch (type) {
      case "interior":
        return "#2323FF" // Neon Blue
      case "exterior":
        return "#16FF00" // Neon Green
      case "studio":
        return "#F61981" // Neon Pink
      case "house":
      case "apartment":
      case "residential":
        return "#FFED00" // Neon Yellow
      case "office":
        return "#3b82f6" // blue
      case "warehouse":
        return "#f97316" // orange
      case "retail":
      case "restaurant":
      case "bar":
        return "#a855f7" // purple
      case "hotel":
        return "#ec4899" // pink
      case "park":
      case "beach":
      case "forest":
        return "#10b981" // emerald
      case "urban":
      case "rural":
        return "#6366f1" // indigo
      case "industrial":
        return "#f43f5e" // rose
      case "historic":
        return "#d97706" // amber
      case "modern":
        return "#0ea5e9" // sky
      default:
        return "#6b7280" // gray
    }
  }

  // Get marker border class based on location type
  const getMarkerBorderClass = (type: string) => {
    switch (type) {
      case "interior":
        return "neon-blue"
      case "exterior":
        return "neon-green"
      case "studio":
        return "neon-pink"
      case "house":
      case "apartment":
      case "residential":
        return "neon-yellow"
      default:
        return "white"
    }
  }

  // Fit map to show all locations
  const fitMapToLocations = () => {
    if (!map.current || locations.length === 0 || !mapLoaded) return

    const bounds = new mapboxgl.LngLatBounds()

    for (const location of locations) {
      bounds.extend([location.coordinates.longitude, location.coordinates.latitude])
    }

    map.current.fitBounds(bounds, {
      padding: 50,
      maxZoom: 15,
    })
  }

  // Toggle clustering
  const toggleClustering = () => {
    setIsClustering(!isClustering)
  }

  return (
    <div style={{ position: "relative", height, width }} className={className}>
      <div ref={mapContainer} style={{ height: "100%", width: "100%" }} />
      
      {/* Modals */}
      <LocationDetailModal 
        open={isDetailModalOpen} 
        onOpenChange={setIsDetailModalOpen} 
      />
      
      <MapFilterModal
        open={isFilterModalOpen}
        onOpenChange={setIsFilterModalOpen}
      />

      {(isLoading || isDataLoading) && (
        <div className="absolute inset-0 bg-background/50 flex items-center justify-center">
          <Loader2 className="h-8 w-8 animate-spin text-neon-blue" />
        </div>
      )}

      {(tokenError || dataError) && (
        <div className="absolute inset-0 bg-background/50 flex items-center justify-center">
          <div className="text-center p-4">
            <p className="text-neon-pink font-medium">{tokenError || dataError}</p>
            <p className="text-sm text-muted-foreground mt-2">Please check your configuration</p>
          </div>
        </div>
      )}

      {interactive && mapLoaded && (
        <MapControls 
          onToggleClustering={toggleClustering}
          isClustering={isClustering}
          onFitToLocations={fitMapToLocations}
          className="absolute bottom-[calc(50%-120px)] right-4 z-10"
        />
      )}
    </div>
  )
}
