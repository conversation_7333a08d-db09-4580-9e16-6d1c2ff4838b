"use client"

import { useState } from "react"
import { MapView } from "@/components/maps/map-view"
import { MapFilterModal } from "@/components/maps/map-filter-modal"
import { Button } from "@/components/ui/button"
import { Maximize } from "lucide-react"

interface FullMapProps {
  onFullScreen?: () => void
}

export function FullMap({ onFullScreen }: FullMapProps) {
  const [isFilterModalOpen, setIsFilterModalOpen] = useState(false)

  return (
    <div className="relative h-[600px] w-full rounded-lg overflow-hidden border border-border">
      <MapView 
        height="100%" 
        width="100%" 
        interactive={true} 
        showControls={true}
      />
      
      {/* Control Buttons */}
      <div className="absolute top-4 left-4 z-10">
        <Button 
          variant="secondary" 
          size="sm" 
          className="bg-white/30 backdrop-blur-md border border-white/40 shadow-md hover:bg-white/40 text-white"
          onClick={onFullScreen}
        >
          <Maximize className="mr-2 h-4 w-4" />
          Full Screen
        </Button>
      </div>
      
      <MapFilterModal
        open={isFilterModalOpen}
        onOpenChange={setIsFilterModalOpen}
      />
    </div>
  )
}
