"use client"

import { <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/scroll-area"
import { Badge } from "@/components/ui/badge"
import { But<PERSON> } from "@/components/ui/button"
import { 
  MapPin, 
  Star, 
  ChevronRight, 
  Filter, 
  ArrowUpDown, 
  ChevronLeft, 
  ChevronDown, 
  ChevronUp 
} from "lucide-react"
import { useMap } from "@/providers/map-provider"
import { useState, useMemo, useEffect } from "react"
import { MapFilterModal } from "./map-filter-modal"
import { 
  DropdownMenu, 
  DropdownMenuContent, 
  DropdownMenuItem, 
  DropdownMenuTrigger 
} from "@/components/ui/dropdown-menu"

// Number of locations to show per page
const LOCATIONS_PER_PAGE = 10

type SortField = 'name' | 'type' | 'status' | 'createdAt'
type SortDirection = 'asc' | 'desc'

interface MapLocationsProps {
  height?: string;
  showPagination?: boolean;
  onPaginationChange?: (currentPage: number, totalPages: number) => void;
}

export function MapLocations({ 
  height = "400px", 
  showPagination = true,
  onPaginationChange
}: MapLocationsProps) {
  const { 
    locations, 
    isLoading, 
    error, 
    selectedLocation, 
    setSelectedLocation,
    favorites
  } = useMap()
  
  const [isFilterModalOpen, setIsFilterModalOpen] = useState(false)
  // Pagination and sorting state
  const [currentPage, setCurrentPage] = useState(1)
  const [sortField, setSortField] = useState<SortField>('name')
  const [sortDirection, setSortDirection] = useState<SortDirection>('asc')
  
  // Check if a location is in any favorite list
  const isFavorite = (locationId: string) => {
    return favorites.some(list => 
      list.locations.some(loc => loc.id === locationId)
    )
  }
  
  // Sort locations based on current sort field and direction
  const sortedLocations = useMemo(() => {
    if (!locations.length) return []
    
    return [...locations].sort((a, b) => {
      let comparison = 0
      
      switch (sortField) {
        case 'name': {
          comparison = a.name.localeCompare(b.name)
          break
        }
        case 'type': {
          comparison = a.type.localeCompare(b.type)
          break
        }
        case 'status': {
          comparison = a.status.localeCompare(b.status)
          break
        }
        case 'createdAt': {
          // Convert dates to timestamps for comparison
          const dateA = new Date(a.createdAt).getTime()
          const dateB = new Date(b.createdAt).getTime()
          comparison = dateA - dateB
          break
        }
      }
      
      return sortDirection === 'asc' ? comparison : -comparison
    })
  }, [locations, sortField, sortDirection])
  
  // Calculate pagination
  const totalPages = Math.ceil(sortedLocations.length / LOCATIONS_PER_PAGE)
  const paginatedLocations = useMemo(() => {
    const startIndex = (currentPage - 1) * LOCATIONS_PER_PAGE
    return sortedLocations.slice(startIndex, startIndex + LOCATIONS_PER_PAGE)
  }, [sortedLocations, currentPage])
  
  // Notify parent component about pagination changes
  useEffect(() => {
    if (onPaginationChange) {
      onPaginationChange(currentPage, totalPages);
    }
  }, [currentPage, totalPages, onPaginationChange]);
  
  // Handle sort change
  const handleSortChange = (field: SortField) => {
    if (sortField === field) {
      // Toggle direction if clicking the same field
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc')
    } else {
      // Set new field and default to ascending
      setSortField(field)
      setSortDirection('asc')
    }
  }
  
  // Get sort icon based on current sort field and direction
  const getSortIcon = (field: SortField) => {
    if (sortField !== field) return <ArrowUpDown className="h-3 w-3 ml-1 opacity-50" />
    return sortDirection === 'asc' 
      ? <ChevronUp className="h-3 w-3 ml-1" /> 
      : <ChevronDown className="h-3 w-3 ml-1" />
  }
  
  if (isLoading) {
    return <div className="flex justify-center p-4">Loading locations...</div>
  }
  
  if (error) {
    return <div className="text-neon-pink p-4">Error loading locations: {error}</div>
  }
  
  if (locations.length === 0) {
    return (
      <div className="flex flex-col items-center justify-center p-4 space-y-4">
        <p className="text-muted-foreground">No locations found</p>
        <Button 
          variant="outline" 
          size="sm"
          onClick={() => setIsFilterModalOpen(true)}
        >
          <Filter className="mr-2 h-4 w-4" />
          Adjust Filters
        </Button>
        <MapFilterModal
          open={isFilterModalOpen}
          onOpenChange={setIsFilterModalOpen}
        />
      </div>
    )
  }

  return (
    <>
      <div className="flex justify-between items-center mb-3">
        <p className="text-sm text-muted-foreground">{locations.length} locations</p>
        <div className="flex items-center space-x-2">
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="sm" className="h-8 px-2">
                <ArrowUpDown className="h-3.5 w-3.5 mr-1" />
                Sort
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-48">
              <DropdownMenuItem onClick={() => handleSortChange('name')}>
                Name {getSortIcon('name')}
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => handleSortChange('type')}>
                Type {getSortIcon('type')}
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => handleSortChange('status')}>
                Status {getSortIcon('status')}
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => handleSortChange('createdAt')}>
                Date Added {getSortIcon('createdAt')}
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
          <Button 
            variant="ghost" 
            size="sm"
            onClick={() => setIsFilterModalOpen(true)}
          >
            <Filter className="h-4 w-4 mr-1" />
            Filter
          </Button>
        </div>
      </div>
      
      <ScrollArea className={`h-[${height}] pr-3`} style={{ height }}>
        <div className="space-y-3">
          {paginatedLocations.map((location) => (
            <Button
              key={location.id}
              variant="ghost"
              className={`flex w-full items-center justify-between rounded-lg border p-4 hover:bg-accent/20 cursor-pointer transition-colors h-auto ${
                selectedLocation?.id === location.id ? 'bg-accent/30 border-neon-blue' : ''
              }`}
              onClick={() => setSelectedLocation(location)}
              aria-pressed={selectedLocation?.id === location.id}
            >
              <div className="flex items-center space-x-4">
                <div className={`flex h-10 w-10 items-center justify-center rounded-full bg-neon-blue/10 shadow-[0_0_8px_rgba(35,35,255,0.3)] ${
                  location.type.toLowerCase() === 'interior' ? 'bg-neon-blue/10' :
                  location.type.toLowerCase() === 'exterior' ? 'bg-neon-green/10' :
                  location.type.toLowerCase() === 'studio' ? 'bg-neon-pink/10' :
                  'bg-neon-blue/10'
                }`}>
                  <MapPin className={`h-5 w-5 ${
                    location.type.toLowerCase() === 'interior' ? 'text-neon-blue' :
                    location.type.toLowerCase() === 'exterior' ? 'text-neon-green' :
                    location.type.toLowerCase() === 'studio' ? 'text-neon-pink' :
                    'text-neon-blue'
                  }`} />
                </div>
                <div>
                  <div className="flex items-center">
                    <p className="font-medium">{location.name}</p>
                    {isFavorite(location.id) && (
                      <Star className="ml-2 h-4 w-4 fill-neon-yellow text-neon-yellow" />
                    )}
                  </div>
                  <p className="text-xs text-muted-foreground mt-1">
                    {location.address?.street && `${location.address.street}, `}
                    {location.address?.city && `${location.address.city}, `}
                    {location.address?.state && `${location.address.state}`}
                  </p>
                  <div className="mt-2 flex items-center space-x-2">
                    <Badge variant="neonBlue" size="sm">{location.type}</Badge>
                    <Badge 
                      variant={
                        location.status.toLowerCase() === "approved" ? "neonGreen" : 
                        location.status.toLowerCase() === "pending" ? "neonYellow" : 
                        location.status.toLowerCase() === "secured" ? "neonGreen" :
                        location.status.toLowerCase() === "rejected" ? "neonPink" :
                        location.status.toLowerCase() === "unavailable" ? "neonPink" :
                        "neonYellow"
                      }
                      size="sm"
                    >
                      {location.status}
                    </Badge>
                    <Badge variant="outline" size="sm">
                      {new Date(location.createdAt).toLocaleDateString()}
                    </Badge>
                  </div>
                </div>
              </div>
              <div className="flex items-center space-x-2">
                <div className="h-8 w-8 rounded-full hover:bg-neon-blue/10 flex items-center justify-center cursor-pointer">
                  <ChevronRight className="h-5 w-5" />
                </div>
              </div>
            </Button>
          ))}
        </div>
        
        {/* Pagination controls - only shown if showPagination is true */}
        {showPagination && totalPages > 1 && (
          <div className="flex justify-between items-center mt-4 px-1">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
              disabled={currentPage === 1}
              className="h-8 px-2 transition-all hover:text-white hover:shadow-[0_0_10px_rgba(35,35,255,0.5)] hover:border-neon-blue"
            >
              <ChevronLeft className="h-4 w-4 mr-1" />
              Previous
            </Button>
            <span className="text-xs text-muted-foreground">
              Page {currentPage} of {totalPages}
            </span>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages))}
              disabled={currentPage === totalPages}
              className="h-8 px-2 transition-all hover:text-white hover:shadow-[0_0_10px_rgba(35,35,255,0.5)] hover:border-neon-blue"
            >
              Next
              <ChevronRight className="h-4 w-4 ml-1" />
            </Button>
          </div>
        )}
      </ScrollArea>
      
      <MapFilterModal
        open={isFilterModalOpen}
        onOpenChange={setIsFilterModalOpen}
      />
    </>
  )
}
