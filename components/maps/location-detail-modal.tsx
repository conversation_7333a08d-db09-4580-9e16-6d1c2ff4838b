"use client"

import { useState, useEffect } from "react"
import { useMap } from "@/providers/map-provider"
import { usePermissions, hasRequiredRole } from "@/hooks/usePermissions"
import { 
  <PERSON><PERSON>, 
  DialogContent, 
  Di<PERSON>Header, 
  <PERSON><PERSON><PERSON><PERSON>le,
  DialogFooter
} from "@/components/ui/dialog"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON>bsList, TabsTrigger } from "@/components/ui/tabs"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { 
  MapPin, 
  Star, 
  Edit, 
  Trash, 
  ExternalLink,
  ImageIcon,
  FileText,
  MessageSquare
} from "lucide-react"
import { EnhancedLocationGallery } from "@/components/locations/enhanced-location-gallery"
import { LocationMap } from "@/components/locations/location-map"
import { LocationDocuments } from "@/components/locations/location-documents"
import { LocationNotes } from "@/components/locations/location-notes"
import { toast } from "sonner"
import type { Location, LocationMedia } from "@/modules/location/model"

// Extended LocationMedia type that includes the uploader relation
interface LocationMediaWithUploader extends LocationMedia {
  uploader?: {
    id: string;
    name: string;
  };
}

// Define the expected format for gallery media items
interface GalleryMediaItem {
  id: string;
  url: string;
  filename: string;
  type: 'image' | 'video';
  thumbnailUrl?: string;
  uploadedBy: {
    id: string;
    name: string;
  };
  uploadedAt: string;
}

interface LocationDetailModalProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  location?: Location
}

export function LocationDetailModal({ open, onOpenChange, location: propLocation }: LocationDetailModalProps) { // Renamed location prop to propLocation
  // selectedLocation from useMap is no longer the primary source for this modal's display.
  // We will use propLocation.
  const { favorites, addLocationToFavorites, createFavoriteList } = useMap()
  const [activeTab, setActiveTab] = useState("overview")
  const [showFavoritesModal, setShowFavoritesModal] = useState(false)
  const [newFavoriteListName, setNewFavoriteListName] = useState("")
  const userRoles = usePermissions()
  
  // Reset active tab when modal opens with a new location
  useEffect(() => {
    if (open && propLocation) { // Use propLocation
      setActiveTab("overview")
    }
  }, [open, propLocation]) // Use propLocation

  // Close modal handler
  const handleClose = () => {
    onOpenChange(false)
    // Don't clear selected location immediately to prevent UI flicker
    // The map component will still show the selected location
  }

  // Add to favorites handler
  const handleAddToFavorites = async (listId: string) => {
    if (!propLocation) return // Use propLocation
    
    const success = await addLocationToFavorites(propLocation.id, listId) // Use propLocation
    
    if (success) {
      setShowFavoritesModal(false)
      toast.success("Location added to favorites")
    }
  }

  // Create new favorites list handler
  const handleCreateFavoriteList = async () => {
    if (!newFavoriteListName.trim()) return
    
    const newList = await createFavoriteList(newFavoriteListName, "custom")
    
    if (newList && propLocation) { // Use propLocation
      await addLocationToFavorites(propLocation.id, newList.id) // Use propLocation
      setNewFavoriteListName("")
      setShowFavoritesModal(false)
    }
  }

  // Check permissions for sensitive information
  const canViewSensitiveInfo = hasRequiredRole(userRoles, ['admin', 'manager', 'scout'])
  const canEditLocation = hasRequiredRole(userRoles, ['admin', 'manager'])
  const canDeleteLocation = hasRequiredRole(userRoles, ['admin'])

  // If no propLocation is provided, don't render the modal content
  if (!propLocation) { // Use propLocation
    return (
      <Dialog open={open} onOpenChange={onOpenChange}>
        <DialogContent className="sm:max-w-[900px] max-h-[90vh] flex flex-col">
          <DialogHeader>
            <DialogTitle>Location Details</DialogTitle>
          </DialogHeader>
          <div className="flex items-center justify-center h-40">
            <p className="text-muted-foreground">No location selected</p>
          </div>
        </DialogContent>
      </Dialog>
    )
  }

  // Enhanced logging for debugging
  console.log('Location in LocationDetailModal:', propLocation);
  console.log('Media array in LocationDetailModal:', propLocation.media);
  
  // Function to adapt location media items for the gallery
  function adaptMediaItemsForGallery(mediaItems: LocationMedia[] = []): GalleryMediaItem[] {
    console.log('Adapting media items for gallery, count:', mediaItems.length);
    
    return mediaItems.map(media => {
      // Log each media item for debugging
      console.log('Processing media item:', media);
      console.log('Media item type:', media.type);
      
      // Determine the media type
      const mediaType = media.type.toLowerCase().includes('image') ? 'image' as const : 'video' as const;
      
      // Handle uploader information safely
      let uploaderId = 'unknown';
      let uploaderName = 'Unknown User';
      
      // Check if media has an extended property with uploader info
      const extendedMedia = media as unknown as LocationMediaWithUploader;
      if (extendedMedia.uploader && typeof extendedMedia.uploader === 'object') {
        if ('id' in extendedMedia.uploader) uploaderId = extendedMedia.uploader.id;
        if ('name' in extendedMedia.uploader) uploaderName = extendedMedia.uploader.name;
      } 
      // Fall back to uploadedBy if available
      else if (media.uploadedBy) {
        uploaderId = media.uploadedBy;
        uploaderName = `User ${media.uploadedBy.substring(0, 8)}`;
      }
      
      // Create the adapted media item
      const adaptedItem = {
        id: media.id,
        url: media.url,
        filename: media.title || 'Untitled',
        type: mediaType,
        thumbnailUrl: media.thumbnailUrl,
        uploadedBy: {
          id: uploaderId,
          name: uploaderName
        },
        uploadedAt: media.uploadedAt ? new Date(media.uploadedAt).toISOString() : new Date().toISOString()
      };
      
      console.log('Adapted media item:', adaptedItem);
      return adaptedItem;
    });
  }
  
  // Transform location media to the format expected by EnhancedLocationGallery
  const mediaItems = adaptMediaItemsForGallery(propLocation.media || []);
  
  // Log the final transformed media items
  console.log('Final transformed mediaItems in LocationDetailModal:', mediaItems);

  const mockDocuments = [
    {
      id: "1",
      name: "Location Permit",
      type: "pdf",
      size: 1024 * 1024 * 2.5, // 2.5 MB
      url: "#",
      uploadedBy: { id: "user1", name: "John Doe" },
      uploadedAt: new Date().toISOString(),
    },
    {
      id: "2",
      name: "Location Contract",
      type: "docx",
      size: 1024 * 512, // 512 KB
      url: "#",
      uploadedBy: { id: "user1", name: "John Doe" },
      uploadedAt: new Date().toISOString(),
    },
  ]

  const mockNotes = [
    {
      id: "1",
      content: "This location has great natural lighting in the morning. Perfect for the opening scene.",
      createdAt: new Date(Date.now() - 86400000).toISOString(), // 1 day ago
      createdBy: { id: "user1", name: "John Doe" },
    },
    {
      id: "2",
      content: "Parking might be an issue. We should arrange for additional parking nearby.",
      createdAt: new Date(Date.now() - 43200000).toISOString(), // 12 hours ago
      createdBy: { id: "user2", name: "Jane Smith" },
    },
  ]

  // Format address helper function
  const formatAddress = (address: Location["address"]) => {
    if (address.formatted) return address.formatted
    
    const parts = []
    if (address.street) parts.push(address.street)
    if (address.city) parts.push(address.city)
    if (address.state) parts.push(address.state)
    if (address.postalCode) parts.push(address.postalCode)
    if (address.country) parts.push(address.country)
    
    return parts.join(", ")
  }

  // Get status badge variant based on location status
  const getStatusBadgeVariant = (status: string) => {
    switch (status) {
      case "approved":
        return "default"
      case "pending":
        return "secondary"
      case "rejected":
        return "destructive"
      case "secured":
        return "neonGreen" // Using neonGreen instead of success as it's available in the UI library
      case "unavailable":
        return "outline"
      default:
        return "outline"
    }
  }

  // Handle adding a note
  const handleAddNote = (content: string) => {
    // In a real implementation, this would call an API to save the note
    console.log("Adding note:", content)
    toast.success(`Note added: "${content.substring(0, 30)}${content.length > 30 ? '...' : ''}"`)
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[900px] max-h-[90vh] flex flex-col">
        <DialogHeader>
          <div className="flex flex-col md:flex-row md:items-start md:justify-between gap-4">
            <div>
              <DialogTitle className="text-2xl font-bold">{propLocation.name}</DialogTitle>
              <p className="text-muted-foreground">{formatAddress(propLocation.address)}</p>
              <div className="flex gap-2 mt-2">
                <Badge variant="outline">{propLocation.type}</Badge>
                <Badge variant={getStatusBadgeVariant(propLocation.status)}>
                  {propLocation.status}
                </Badge>
              </div>
            </div>

            <div className="flex items-center gap-2">
              <Button 
                variant="outline" 
                size="sm" 
                onClick={() => setShowFavoritesModal(true)}
              >
                <Star className="h-4 w-4 mr-2" />
                Add to Favorites
              </Button>
              
              {canEditLocation && (
                <Button variant="outline" size="sm">
                  <Edit className="h-4 w-4 mr-2" />
                  Edit
                </Button>
              )}
              
              {canDeleteLocation && (
                <Button variant="outline" size="sm" className="text-destructive">
                  <Trash className="h-4 w-4 mr-2" />
                  Delete
                </Button>
              )}
            </div>
          </div>
        </DialogHeader>
        
        <Separator className="my-2" />
        
        <div className="flex-1 overflow-hidden">
          <Tabs defaultValue={activeTab} onValueChange={setActiveTab} className="h-full flex flex-col">
            <TabsList className="mb-2">
              <TabsTrigger value="overview">Overview</TabsTrigger>
              <TabsTrigger value="gallery">
                <ImageIcon className="h-4 w-4 mr-2" />
                Gallery
              </TabsTrigger>
              <TabsTrigger value="map">
                <MapPin className="h-4 w-4 mr-2" />
                Map
              </TabsTrigger>
              <TabsTrigger value="documents">
                <FileText className="h-4 w-4 mr-2" />
                Documents
              </TabsTrigger>
              <TabsTrigger value="notes">
                <MessageSquare className="h-4 w-4 mr-2" />
                Notes
              </TabsTrigger>
            </TabsList>
            
            <div className="flex-1 overflow-auto pr-2">
              <TabsContent value="overview" className="h-full">
                <div className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <h3 className="text-lg font-medium mb-2">Details</h3>
                      <div className="space-y-2">
                        <div>
                          <span className="text-sm font-medium">Type:</span>
                          <span className="text-sm ml-2">{propLocation.type}</span>
                        </div>
                        <div>
                          <span className="text-sm font-medium">Status:</span>
                          <span className="text-sm ml-2">{propLocation.status}</span>
                        </div>
                        <div>
                          <span className="text-sm font-medium">Size:</span>
                          <span className="text-sm ml-2">{propLocation.locationSize || "Not specified"}</span>
                        </div>
                        {propLocation.locationFeatures && propLocation.locationFeatures.length > 0 && (
                          <div>
                            <span className="text-sm font-medium">Features:</span>
                            <span className="text-sm ml-2">{propLocation.locationFeatures.join(", ")}</span>
                          </div>
                        )}
                        {propLocation.locationTags && propLocation.locationTags.length > 0 && (
                          <div>
                            <span className="text-sm font-medium">Tags:</span>
                            <span className="text-sm ml-2">{propLocation.locationTags.join(", ")}</span>
                          </div>
                        )}
                        <div>
                          <span className="text-sm font-medium">Accessibility:</span>
                          <span className="text-sm ml-2">{propLocation.accessibility || "Not specified"}</span>
                        </div>
                        <div>
                          <span className="text-sm font-medium">Parking:</span>
                          <span className="text-sm ml-2">{propLocation.parkingInfo || "Not specified"}</span>
                        </div>
                      </div>
                    </div>
                    
                    {canViewSensitiveInfo && (
                      <div>
                        <h3 className="text-lg font-medium mb-2">Sensitive Information</h3>
                        <div className="space-y-2">
                          <div>
                            <span className="text-sm font-medium">Contact Name:</span>
                            <span className="text-sm ml-2">{propLocation.contactName || "Not specified"}</span>
                          </div>
                          <div>
                            <span className="text-sm font-medium">Contact Email:</span>
                            <span className="text-sm ml-2">{propLocation.contactEmail || "Not specified"}</span>
                          </div>
                          <div>
                            <span className="text-sm font-medium">Contact Phone:</span>
                            <span className="text-sm ml-2">{propLocation.contactPhone || "Not specified"}</span>
                          </div>
                          <div>
                            <span className="text-sm font-medium">Hourly Rate:</span>
                            <span className="text-sm ml-2">
                              {propLocation.hourlyRate ? `$${propLocation.hourlyRate}` : "Not specified"}
                            </span>
                          </div>
                          <div>
                            <span className="text-sm font-medium">Daily Rate:</span>
                            <span className="text-sm ml-2">
                              {propLocation.dailyRate ? `$${propLocation.dailyRate}` : "Not specified"}
                            </span>
                          </div>
                          <div>
                            <span className="text-sm font-medium">Permits Required:</span>
                            <span className="text-sm ml-2">{propLocation.permitsRequired ? "Yes" : "No"}</span>
                          </div>
                          <div>
                            <span className="text-sm font-medium">Restrictions:</span>
                            <span className="text-sm ml-2">{propLocation.restrictions || "None"}</span>
                          </div>
                        </div>
                      </div>
                    )}
                  </div>
                  
                  <div>
                    <h3 className="text-lg font-medium mb-2">Description</h3>
                    <p className="text-sm">{propLocation.description || "No description provided."}</p>
                  </div>
                  
                  {canViewSensitiveInfo && (
                    <div>
                      <h3 className="text-lg font-medium mb-2">Lighting Information</h3>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                          <span className="text-sm font-medium">Golden Morning:</span>
                          <span className="text-sm ml-2">
                            {propLocation.goldenMorningStart && propLocation.goldenMorningEnd
                              ? `${propLocation.goldenMorningStart} - ${propLocation.goldenMorningEnd}`
                              : "Not specified"}
                          </span>
                        </div>
                        <div>
                          <span className="text-sm font-medium">Golden Evening:</span>
                          <span className="text-sm ml-2">
                            {propLocation.goldenEveningStart && propLocation.goldenEveningEnd
                              ? `${propLocation.goldenEveningStart} - ${propLocation.goldenEveningEnd}`
                              : "Not specified"}
                          </span>
                        </div>
                      </div>
                    </div>
                  )}
                  
                  <div>
                    <h3 className="text-lg font-medium mb-2">Metadata</h3>
                    <div className="text-sm">
                      <div>
                        <span className="font-medium">Created:</span>
                        <span className="ml-2">
                          {new Date(propLocation.createdAt).toLocaleDateString()} by {propLocation.createdBy || "Unknown"}
                        </span>
                      </div>
                      <div>
                        <span className="font-medium">Last Updated:</span>
                        <span className="ml-2">
                          {new Date(propLocation.updatedAt).toLocaleDateString()}
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
              </TabsContent>
              
              <TabsContent value="gallery" className="h-full">
                <EnhancedLocationGallery media={mediaItems} />
              </TabsContent>
              
              <TabsContent value="map" className="h-full">
                <LocationMap location={propLocation} />
              </TabsContent>
              
              <TabsContent value="documents" className="h-full">
                <LocationDocuments documents={mockDocuments} />
              </TabsContent>
              
              <TabsContent value="notes" className="h-full">
                <LocationNotes 
                  notes={mockNotes} 
                  onAddNote={canViewSensitiveInfo ? handleAddNote : undefined} 
                />
              </TabsContent>
            </div>
          </Tabs>
        </div>
        
        <Separator className="my-2" />
        
        <DialogFooter>
          <Button variant="outline" onClick={handleClose}>Close</Button>
          <Button asChild>
            <a href={`/organizations/${propLocation.organizationId}/locations/${propLocation.id}`} target="_blank" rel="noopener noreferrer">
              <ExternalLink className="h-4 w-4 mr-2" />
              View Full Details
            </a>
          </Button>
        </DialogFooter>
      </DialogContent>
      
      {/* Favorites Modal - In a real implementation, this would be a separate component */}
      {showFavoritesModal && (
        <Dialog open={showFavoritesModal} onOpenChange={setShowFavoritesModal}>
          <DialogContent className="sm:max-w-[500px]">
            <DialogHeader>
              <DialogTitle>Add to Favorites</DialogTitle>
            </DialogHeader>
            
            <div className="space-y-4">
              <div>
                <h3 className="text-sm font-medium mb-2">Select a favorites list:</h3>
                {favorites.length === 0 ? (
                  <p className="text-sm text-muted-foreground">No favorites lists found. Create one below.</p>
                ) : (
                  <div className="space-y-2">
                    {favorites.map((list) => (
                      <Button 
                        key={list.id} 
                        variant="outline" 
                        className="w-full justify-start"
                        onClick={() => handleAddToFavorites(list.id)}
                      >
                        <Star className="h-4 w-4 mr-2" />
                        {list.name}
                        <Badge variant="secondary" className="ml-auto">
                          {list.locations.length} locations
                        </Badge>
                      </Button>
                    ))}
                  </div>
                )}
              </div>
              
              <Separator />
              
              <div>
                <h3 className="text-sm font-medium mb-2">Create a new list:</h3>
                <div className="flex gap-2">
                  <input
                    type="text"
                    placeholder="New list name"
                    className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                    value={newFavoriteListName}
                    onChange={(e) => setNewFavoriteListName(e.target.value)}
                  />
                  <Button onClick={handleCreateFavoriteList} disabled={!newFavoriteListName.trim()}>
                    Create
                  </Button>
                </div>
              </div>
            </div>
            
            <DialogFooter>
              <Button variant="outline" onClick={() => setShowFavoritesModal(false)}>
                Cancel
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      )}
    </Dialog>
  )
}
