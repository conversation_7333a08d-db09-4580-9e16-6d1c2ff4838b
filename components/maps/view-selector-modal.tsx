"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>ead<PERSON>, DialogTitle } from "@/components/ui/dialog"
import { But<PERSON> } from "@/components/ui/button"
import { Command, CommandEmpty, CommandGroup, CommandInput, CommandItem, CommandList } from "@/components/ui/command"
import { Check, Loader2 } from "lucide-react"
import { useMap } from "@/providers/map-provider"
import { toast } from "sonner"

// Define a simplified view type that matches what we get from the API
type ViewItem = {
  id: string
  name: string
  description?: string
}

interface ViewSelectorModalProps {
  isOpen: boolean
  onClose: () => void
}

export function ViewSelectorModal({ isOpen, onClose }: ViewSelectorModalProps) {
  const { savedViews, loadView, refreshSavedViews, isSavedViewsLoading } = useMap()
  const [selectedView, setSelectedView] = useState<ViewItem | null>(null)
  const [isLoading, setIsLoading] = useState(false)

  // Refresh saved views when the modal opens
  useEffect(() => {
    if (isOpen) {
      refreshSavedViews()
    }
  }, [isOpen, refreshSavedViews])

  const handleSelectView = async (view: ViewItem) => {
    // Store only the necessary properties for display
    setSelectedView({
      id: view.id,
      name: view.name,
      description: view.description
    })
    setIsLoading(true)

    try {
      const success = await loadView(view.id)
      
      if (success) {
        toast.success(`View "${view.name}" loaded`)
        onClose()
      } else {
        toast.error("Failed to load view")
      }
    } catch (error) {
      console.error("Error loading view:", error)
      toast.error("An error occurred while loading the view")
    } finally {
      setIsLoading(false)
    }
  }

  const handleClose = () => {
    setSelectedView(null)
    onClose()
  }

  return (
    <Dialog open={isOpen} onOpenChange={(open) => !open && handleClose()}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Load Saved View</DialogTitle>
        </DialogHeader>
        
        <div className="py-4">
          <Command className="rounded-lg border shadow-md">
            <CommandInput placeholder="Search saved views..." />
            <CommandList>
              <CommandEmpty>No saved views found.</CommandEmpty>
              <CommandGroup>
                {isSavedViewsLoading ? (
                  <div className="flex items-center justify-center py-6">
                    <Loader2 className="h-6 w-6 animate-spin text-muted-foreground" />
                  </div>
                ) : savedViews.length === 0 ? (
                  <div className="p-4 text-center text-sm text-muted-foreground">
                    No saved views yet. Save your current view to see it here.
                  </div>
                ) : (
                  savedViews.map((view) => (
                    <CommandItem
                      key={view.id}
                      value={view.name}
                      onSelect={() => handleSelectView(view)}
                      className="flex items-center justify-between"
                      disabled={isLoading}
                    >
                      <div className="flex flex-col">
                        <span>{view.name}</span>
                        {view.description && (
                          <span className="text-xs text-muted-foreground">{view.description}</span>
                        )}
                      </div>
                      {selectedView?.id === view.id && (
                        <Check className="ml-2 h-4 w-4 shrink-0" />
                      )}
                    </CommandItem>
                  ))
                )}
              </CommandGroup>
            </CommandList>
          </Command>
        </div>
        
        <div className="flex justify-end">
          <Button variant="outline" onClick={handleClose} disabled={isLoading}>
            Cancel
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  )
}
