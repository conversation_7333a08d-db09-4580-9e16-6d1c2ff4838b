"use client"

import { useState } from "react"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>nt, <PERSON><PERSON><PERSON>ead<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, DialogFooter } from "@/components/ui/dialog"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Checkbox } from "@/components/ui/checkbox"
import { Ta<PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Loader2, Copy, Check, Link, Lock } from "lucide-react"
import { useMap } from "@/providers/map-provider"
import { useOrganization } from "@/hooks/use-organization"
import { toast } from "sonner"
import type { MapViewSettings, MapFilter } from "@/modules/map/model"

interface ShareMapModalProps {
  isOpen: boolean
  onClose: () => void
}

export function ShareMapModal({ isO<PERSON>, onClose }: ShareMapModalProps) {
  const { organization } = useOrganization()
  const { viewSettings, filters, selectedLocation, savedViews } = useMap()
  
  const [isLoading, setIsLoading] = useState(false)
  const [shareUrl, setShareUrl] = useState<string | null>(null)
  const [copied, setCopied] = useState(false)
  const [selectedView, setSelectedView] = useState<string>("")
  const [password, setPassword] = useState("")
  const [isPasswordProtected, setIsPasswordProtected] = useState(false)
  const [expirationOption, setExpirationOption] = useState<string>("never")
  
  // Reset state when modal opens/closes
  const handleOpenChange = (open: boolean) => {
    if (!open) {
      setShareUrl(null)
      setCopied(false)
      setSelectedView("")
      setPassword("")
      setIsPasswordProtected(false)
      setExpirationOption("never")
      onClose()
    }
  }
  
  // Calculate expiration time in seconds based on selected option
  const getExpirationTime = (): number | undefined => {
    switch (expirationOption) {
      case "1hour":
        return 60 * 60 // 1 hour in seconds
      case "1day":
        return 60 * 60 * 24 // 1 day in seconds
      case "7days":
        return 60 * 60 * 24 * 7 // 7 days in seconds
      case "30days":
        return 60 * 60 * 24 * 30 // 30 days in seconds
      default:
        return undefined // No expiration
    }
  }
  
  const handleCreateShareLink = async () => {
    if (!organization) return
    
    setIsLoading(true)
    
    try {
      const payload: {
        organizationId: string;
        viewSettings: MapViewSettings;
        filters: MapFilter;
        mapViewId?: string;
        password?: string;
        expiresIn?: number;
        locationIds?: string[];
      } = {
        organizationId: organization.id,
        viewSettings,
        filters,
      }
      
      // Add selected view ID if one is selected
      if (selectedView) {
        payload.mapViewId = selectedView
      }
      
      // Add password if enabled
      if (isPasswordProtected && password) {
        payload.password = password
      }
      
      // Add expiration time if not "never"
      const expiresIn = getExpirationTime()
      if (expiresIn) {
        payload.expiresIn = expiresIn
      }
      
      // Add selected location if any
      if (selectedLocation) {
        payload.locationIds = [selectedLocation.id]
      }
      
      const response = await fetch('/api/map/share', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(payload),
      })
      
      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}))
        throw new Error(errorData.error || "Failed to create share link")
      }
      
      const data = await response.json()
      setShareUrl(data.url)
      toast.success("Share link created successfully")
    } catch (error) {
      console.error("Error creating share link:", error)
      toast.error(error instanceof Error ? error.message : "Failed to create share link")
    } finally {
      setIsLoading(false)
    }
  }
  
  const handleCopyLink = () => {
    if (!shareUrl) return
    
    navigator.clipboard.writeText(shareUrl)
      .then(() => {
        setCopied(true)
        toast.success("Link copied to clipboard")
        
        // Reset copied state after 2 seconds
        setTimeout(() => {
          setCopied(false)
        }, 2000)
      })
      .catch((error) => {
        console.error("Error copying link:", error)
        toast.error("Failed to copy link")
      })
  }

  return (
    <Dialog open={isOpen} onOpenChange={handleOpenChange}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>Share Map</DialogTitle>
        </DialogHeader>
        
        {!shareUrl ? (
          <Tabs defaultValue="current" className="w-full">
            <TabsList className="grid w-full grid-cols-2">
              <TabsTrigger value="current">Current View</TabsTrigger>
              <TabsTrigger value="saved">Saved View</TabsTrigger>
            </TabsList>
            
            <TabsContent value="current" className="space-y-4 py-4">
              <div className="text-sm text-muted-foreground">
                Share your current map view, including filters and selected location.
              </div>
              
              <div className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="expiration">Expiration</Label>
                  <Select value={expirationOption} onValueChange={setExpirationOption}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select expiration time" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="never">Never</SelectItem>
                      <SelectItem value="1hour">1 Hour</SelectItem>
                      <SelectItem value="1day">1 Day</SelectItem>
                      <SelectItem value="7days">7 Days</SelectItem>
                      <SelectItem value="30days">30 Days</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                
                <div className="space-y-2">
                  <div className="flex items-center space-x-2">
                    <Checkbox 
                      id="password-protection" 
                      checked={isPasswordProtected}
                      onCheckedChange={(checked) => setIsPasswordProtected(checked === true)}
                    />
                    <Label htmlFor="password-protection">Password Protection</Label>
                  </div>
                  
                  {isPasswordProtected && (
                    <Input
                      type="password"
                      placeholder="Enter password"
                      value={password}
                      onChange={(e) => setPassword(e.target.value)}
                    />
                  )}
                </div>
              </div>
            </TabsContent>
            
            <TabsContent value="saved" className="space-y-4 py-4">
              <div className="text-sm text-muted-foreground">
                Share one of your saved map views.
              </div>
              
              <div className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="saved-view">Saved View</Label>
                  <Select value={selectedView} onValueChange={setSelectedView}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select a saved view" />
                    </SelectTrigger>
                    <SelectContent>
                      {savedViews.length === 0 ? (
                        <SelectItem value="" disabled>No saved views</SelectItem>
                      ) : (
                        savedViews.map((view) => (
                          <SelectItem key={view.id} value={view.id}>
                            {view.name}
                          </SelectItem>
                        ))
                      )}
                    </SelectContent>
                  </Select>
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="expiration">Expiration</Label>
                  <Select value={expirationOption} onValueChange={setExpirationOption}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select expiration time" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="never">Never</SelectItem>
                      <SelectItem value="1hour">1 Hour</SelectItem>
                      <SelectItem value="1day">1 Day</SelectItem>
                      <SelectItem value="7days">7 Days</SelectItem>
                      <SelectItem value="30days">30 Days</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                
                <div className="space-y-2">
                  <div className="flex items-center space-x-2">
                    <Checkbox 
                      id="password-protection" 
                      checked={isPasswordProtected}
                      onCheckedChange={(checked) => setIsPasswordProtected(checked === true)}
                    />
                    <Label htmlFor="password-protection">Password Protection</Label>
                  </div>
                  
                  {isPasswordProtected && (
                    <Input
                      type="password"
                      placeholder="Enter password"
                      value={password}
                      onChange={(e) => setPassword(e.target.value)}
                    />
                  )}
                </div>
              </div>
            </TabsContent>
          </Tabs>
        ) : (
          <div className="py-6 space-y-4">
            <div className="text-sm text-muted-foreground">
              Your map is now shared! Use the link below to share it with others.
            </div>
            
            <div className="flex items-center space-x-2">
              <div className="relative flex-1">
                <Input
                  value={shareUrl}
                  readOnly
                  className="pr-10"
                />
                <Link className="absolute right-3 top-2.5 h-4 w-4 text-muted-foreground" />
              </div>
              <Button 
                size="icon" 
                onClick={handleCopyLink}
                disabled={copied}
              >
                {copied ? <Check className="h-4 w-4" /> : <Copy className="h-4 w-4" />}
              </Button>
            </div>
            
            {isPasswordProtected && (
              <div className="text-sm text-amber-500 flex items-center space-x-2">
                <Lock className="h-4 w-4" />
                <span>This link is password protected. Recipients will need the password to access it.</span>
              </div>
            )}
          </div>
        )}
        
        <DialogFooter>
          {!shareUrl ? (
            <>
              <Button variant="outline" onClick={onClose} disabled={isLoading}>
                Cancel
              </Button>
              <Button 
                onClick={handleCreateShareLink} 
                disabled={isLoading || (selectedView === "" && expirationOption === "saved")}
              >
                {isLoading ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Creating...
                  </>
                ) : (
                  "Create Share Link"
                )}
              </Button>
            </>
          ) : (
            <Button onClick={onClose}>
              Done
            </Button>
          )}
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
