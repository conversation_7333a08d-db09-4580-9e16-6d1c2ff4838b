"use client"

import { useState, useEffect, useRef, useCallback } from "react"
import mapboxgl from "mapbox-gl"
import "mapbox-gl/dist/mapbox-gl.css"
import { Loader2 } from "lucide-react"
import { MapFilterModal } from "./map-filter-modal"
import { MapControls } from "./map-controls"
import type { ClusterSettings } from "@/modules/map/model"
import { defaultClusterSettings } from "@/modules/map/model"
import type { Location } from "@/modules/location/model"
import { useMap } from "@/providers/map-provider"
import { toast } from "sonner"
import { LocationDetailModal } from "./location-detail-modal"

type MapViewProps = {
  height?: string
  width?: string
  interactive?: boolean
  showControls?: boolean
  className?: string
  clusterSettings?: ClusterSettings
  // Add props for standalone usage outside of MapProvider
  locations?: Location[]
  onLocationSelect?: (location: Location) => void
}

export function MapView({
  height = "100%",
  width = "100%",
  interactive = true,
  className,
  clusterSettings = defaultClusterSettings,
  // Add props with defaults from context
  locations: propLocations,
  onLocationSelect,
}: MapViewProps) {
  // Get locations and state from map context
  const { 
    locations: contextLocations, 
    isLoading: isDataLoading, 
    error: dataError,
    selectedLocation,
    setSelectedLocation: contextSetSelectedLocation,
    viewSettings,
    setViewSettings,
    clusterSettings: contextClusterSettings,
  } = useMap()
  
  // Use prop locations if provided, otherwise use context locations
  const locations = propLocations || contextLocations
  
  // Use prop onLocationSelect if provided, otherwise use context setSelectedLocation
  const handleLocationSelect = onLocationSelect || contextSetSelectedLocation

  const mapContainer = useRef<HTMLDivElement>(null)
  const map = useRef<mapboxgl.Map | null>(null)
  const markersRef = useRef<{ [key: string]: mapboxgl.Marker }>({})
  const popupsRef = useRef<{ [key: string]: mapboxgl.Popup }>({})
  const clusterSourceRef = useRef<string | null>(null)

  const [mapLoaded, setMapLoaded] = useState(false)
  const [isLoading, setIsLoading] = useState(true)
  const [tokenError, setTokenError] = useState<string | null>(null)
  // This function is used in event handlers but we don't need to track the state
  // It's kept for future use if needed
  const setHoveredLocationId = () => {
    // No-op function that doesn't use parameters
    // This is just to maintain compatibility with the existing code
  }
  const [isClustering, setIsClustering] = useState(clusterSettings.enabled)
  const [isDetailModalOpen, setIsDetailModalOpen] = useState(false)
  const [isFilterModalOpen, setIsFilterModalOpen] = useState(false)

const SELECTED_MARKER_KEY = '__selected_location_marker__';

  // Merge cluster settings from props and context
  const mergedClusterSettings = {
    ...defaultClusterSettings,
    ...clusterSettings,
    ...contextClusterSettings,
  }

  // Get mapboxToken from context instead of using React Query
  const contextMapboxToken = useMap().mapboxToken;
  
  // Use the token from context or props
  const mapboxToken = contextMapboxToken || null;
  
  // Get marker color based on location type - memoized to avoid dependency issues
  const getMarkerColor = useCallback((type: string) => {
    switch (type) {
      case "interior":
        return "#2323FF" // Neon Blue
      case "exterior":
        return "#16FF00" // Neon Green
      case "studio":
        return "#F61981" // Neon Pink
      case "house":
      case "apartment":
      case "residential":
        return "#FFED00" // Neon Yellow
      case "office":
        return "#3b82f6" // blue
      case "warehouse":
        return "#f97316" // orange
      case "retail":
      case "restaurant":
      case "bar":
        return "#a855f7" // purple
      case "hotel":
        return "#ec4899" // pink
      case "park":
      case "beach":
      case "forest":
        return "#10b981" // emerald
      case "urban":
      case "rural":
        return "#6366f1" // indigo
      case "industrial":
        return "#f43f5e" // rose
      case "historic":
        return "#d97706" // amber
      case "modern":
        return "#0ea5e9" // sky
      default:
        return "#6b7280" // gray
    }
  }, [])

  // Get marker border class based on location type - memoized to avoid dependency issues
  const getMarkerBorderClass = useCallback((type: string) => {
    switch (type) {
      case "interior":
        return "neon-blue"
      case "exterior":
        return "neon-green"
      case "studio":
        return "neon-pink"
      case "house":
      case "apartment":
      case "residential":
        return "neon-yellow"
      default:
        return "white"
    }
  }, [])
  
  // Handle token error
  useEffect(() => {
    if (!mapboxToken) {
      console.error("Mapbox token not available")
      setTokenError("Failed to load map: API key error")
      setIsLoading(false)
      toast.error("Failed to load map: API key error")
    }
  }, [mapboxToken])

  // Initialize map
  useEffect(() => {
    if (!mapContainer.current || map.current || !mapboxToken) return

    setIsLoading(true)

    // Set the token
    mapboxgl.accessToken = mapboxToken

    // Create map with performance optimizations
    map.current = new mapboxgl.Map({
      container: mapContainer.current,
      style: viewSettings.styleId || "mapbox://styles/mapbox/dark-v11", // Use style from settings or default to dark
      center: [viewSettings.center.longitude, viewSettings.center.latitude],
      zoom: viewSettings.zoom,
      pitch: viewSettings.pitch,
      bearing: viewSettings.bearing,
      interactive,
      renderWorldCopies: false, // Improves performance
      antialias: true, // Better visual quality
      attributionControl: false, // Remove attribution control for cleaner UI
      fadeDuration: 100, // Faster fade transitions
      maxZoom: 18, // Limit max zoom to prevent performance issues
    })

    // We're not adding the default Mapbox navigation controls as we have our own custom controls

    map.current.on("load", () => {
      setMapLoaded(true)
      setIsLoading(false)

      // Save map view settings when map moves - debounced to improve performance
      if (map.current) {
        // Use a timeout to debounce frequent updates
        let moveEndTimeout: NodeJS.Timeout | null = null;
        
        map.current.on("moveend", () => {
          if (!map.current) return;
          
          // Clear previous timeout if it exists
          if (moveEndTimeout) clearTimeout(moveEndTimeout);
          
          // Set a new timeout
          moveEndTimeout = setTimeout(() => {
            const center = map.current?.getCenter();
            const zoom = map.current?.getZoom();
            const pitch = map.current?.getPitch();
            const bearing = map.current?.getBearing();
            
            if (center && typeof zoom === 'number' && typeof pitch === 'number' && typeof bearing === 'number') {
              setViewSettings({
                center: {
                  latitude: center.lat,
                  longitude: center.lng,
                },
                zoom,
                pitch,
                bearing,
              });
            }
          }, 300); // 300ms debounce
        });
      }
    })

    // Clean up on unmount
    return () => {
      if (map.current) {
        // Remove all event listeners to prevent memory leaks
        map.current.remove();
        map.current = null;
      }
    }
  }, [
    interactive, 
    mapboxToken, 
    viewSettings.styleId, 
    viewSettings.center.longitude,
    viewSettings.center.latitude,
    viewSettings.zoom,
    viewSettings.pitch,
    viewSettings.bearing,
    setViewSettings
  ])

  // Setup clustering
  const setupClustering = useCallback(() => {
    if (!map.current || !mapLoaded || !mergedClusterSettings.enabled) return

    // Check if the map style is loaded
    if (!map.current.isStyleLoaded()) {
      // If style is not loaded, wait for the style.load event
      const setupClusteringOnStyleLoad = () => {
        setupClustering();
        // Remove the event listener after it's called to prevent multiple setups
        map.current?.off('style.load', setupClusteringOnStyleLoad);
      };
      
      // Add event listener for style.load
      map.current.on('style.load', setupClusteringOnStyleLoad);
      return;
    }

    try {
      // Remove existing cluster layers and source in the correct order
      const layersToRemove = ['unclustered-point', 'cluster-count', 'clusters'];
      
      // First remove all layers that might be using the source
      for (const layerId of layersToRemove) {
        if (map.current?.getLayer(layerId)) {
          map.current.removeLayer(layerId);
        }
      }
      
      // Then remove the source after all layers are removed
      if (clusterSourceRef.current && map.current.getSource(clusterSourceRef.current)) {
        map.current.removeSource(clusterSourceRef.current);
      }
      
      // Clear existing markers - this is critical to prevent markers showing on top of clusters
      // Preserve the selected location marker
      const selectedMarker = markersRef.current[SELECTED_MARKER_KEY];
      for (const key in markersRef.current) {
        if (key !== SELECTED_MARKER_KEY) {
          markersRef.current[key].remove();
          delete markersRef.current[key];
        }
      }
      // If selectedMarker existed, markersRef.current might only contain it now, or be empty.
      // If it didn't exist, markersRef.current is now empty.
      // We want to ensure markersRef.current is an empty object if selectedMarker is not present,
      // or only contains selectedMarker if it is.
      const newMarkers: { [key: string]: mapboxgl.Marker } = {};
      if (selectedMarker) {
        newMarkers[SELECTED_MARKER_KEY] = selectedMarker;
      }
      markersRef.current = newMarkers;
      for (const popup of Object.values(popupsRef.current)) {
        popup.remove();
      }
      popupsRef.current = {}
      
      clusterSourceRef.current = null;

      // Create a new source ID with timestamp to ensure uniqueness
      const sourceId = `locations-${Date.now()}`;
      clusterSourceRef.current = sourceId;

      // Create GeoJSON features from locations, excluding the selectedLocation
      const features = locations
        .filter(location => {
          // Exclude selectedLocation from cluster data source if it's defined
          if (selectedLocation && typeof selectedLocation.id !== 'undefined' && location.id === selectedLocation.id) {
            return false;
          }
          // Filter out locations with invalid coordinates
          return location.coordinates &&
                 typeof location.coordinates.latitude === 'number' &&
                 typeof location.coordinates.longitude === 'number' &&
                 !Number.isNaN(location.coordinates.latitude) &&
                 !Number.isNaN(location.coordinates.longitude);
        })
        .map(location => ({
          type: 'Feature' as const,
          properties: {
            id: location.id,
            name: location.name,
            type: location.type,
            status: location.status,
            color: getMarkerColor(location.type),
            dailyRate: location.dailyRate,
            hourlyRate: location.hourlyRate,
            address: location.address ? JSON.stringify(location.address) : null,
          },
          geometry: {
            type: 'Point' as const,
            coordinates: [location.coordinates.longitude, location.coordinates.latitude]
          }
        }));

      // Add a new source with clustering enabled
      map.current.addSource(sourceId, {
        type: 'geojson',
        data: {
          type: 'FeatureCollection',
          features
        },
        cluster: true,
        clusterMaxZoom: mergedClusterSettings.maxZoom,
        clusterRadius: mergedClusterSettings.radius
      });

      // Check if layers already exist before adding them
      if (!map.current.getLayer('clusters')) {
        // Add a layer for the clusters
        map.current.addLayer({
          id: 'clusters',
          type: 'circle',
          source: sourceId,
          filter: ['has', 'point_count'],
          paint: {
            'circle-color': [
              'step',
              ['get', 'point_count'],
              '#2323FF', // Neon blue for small clusters
              10,
              '#16FF00', // Neon green for medium clusters
              30,
              '#F61981'  // Neon pink for large clusters
            ],
            'circle-radius': [
              'step',
              ['get', 'point_count'],
              20,  // Size for small clusters
              10,
              30,  // Size for medium clusters
              30,
              40   // Size for large clusters
            ],
            'circle-stroke-width': 2,
            'circle-stroke-color': '#FFFFFF',
            'circle-opacity': 0.8,
          }
        });
      }

      if (!map.current.getLayer('cluster-count')) {
        // Add a layer for the cluster counts
        map.current.addLayer({
          id: 'cluster-count',
          type: 'symbol',
          source: sourceId,
          filter: ['has', 'point_count'],
          layout: {
            'text-field': '{point_count_abbreviated}',
            'text-font': ['DIN Offc Pro Medium', 'Arial Unicode MS Bold'],
            'text-size': 14
          },
          paint: {
            'text-color': '#FFFFFF'
          }
        });
      }

      // Add a layer for unclustered points with improved visibility
      if (!map.current.getLayer('unclustered-point')) {
        map.current.addLayer({
          id: 'unclustered-point',
          type: 'circle',
          source: sourceId,
          filter: ['!', ['has', 'point_count']],
          paint: {
            'circle-color': '#FFFFFF',
            'circle-radius': 10, // Increased size for better visibility
            'circle-stroke-width': 3, // Increased stroke width
            'circle-stroke-color': ['get', 'color'], // Use the color property from the feature
            'circle-opacity': 1,
            'circle-stroke-opacity': 1
          }
        });
      }

      // Remove any existing event handlers to prevent duplicates
      if (map.current) {
        // Type the event handlers properly
        const clusterClickHandler = (e: mapboxgl.MapMouseEvent) => {
          const features = map.current?.queryRenderedFeatures(e.point, { layers: ['clusters'] });
          if (!features || features.length === 0 || !map.current) return;

          const clusterId = features[0].properties?.cluster_id;
          if (!clusterId) return;

          // Use clusterSourceRef.current instead of sourceId
          if (!clusterSourceRef.current) return;
          
          const source = map.current.getSource(clusterSourceRef.current) as mapboxgl.GeoJSONSource;
          source.getClusterExpansionZoom(clusterId, (err, zoom) => {
            if (err || !map.current) return;

            // Cast to a more specific type to avoid TypeScript errors
            const geometry = features[0].geometry as GeoJSON.Point;
            const coordinates = geometry.coordinates.slice() as [number, number];
            
            // Calculate a more appropriate zoom level - ensure we don't zoom too far
            const targetZoom = Math.min(zoom || 14, mergedClusterSettings.maxZoom - 0.5);
            
            // Use flyTo with a longer duration for smoother animation
            map.current.flyTo({
              center: coordinates,
              zoom: targetZoom,
              duration: 1000, // Slightly shorter animation for better responsiveness
              essential: true, // Mark as essential to ensure it completes
              easing: t => t // Linear easing for more predictable animation
            });
            
          });
        };

        const unclusteredPointClickHandler = (e: mapboxgl.MapMouseEvent) => {
          const features = map.current?.queryRenderedFeatures(e.point, { layers: ['unclustered-point'] });
          if (!features || features.length === 0 || !map.current) return;
          
          const props = features[0].properties;
          if (!props || !props.id) return;
          
          const location = locations.find(loc => loc.id === props.id);
          if (location && handleLocationSelect) {
            handleLocationSelect(location);
            
            // Get coordinates from the feature
            const geometry = features[0].geometry as GeoJSON.Point;
            const coordinates = geometry.coordinates.slice() as [number, number];
            
            // Center map on selected location
            map.current.flyTo({
              center: coordinates,
              zoom: 14,
              essential: true
            });
            
            // Open detail modal
            setIsDetailModalOpen(true);
          }
        };

        const mouseEnterHandler = () => {
          if (map.current) map.current.getCanvas().style.cursor = 'pointer';
        };
        
        const mouseLeaveHandler = () => {
          if (map.current) map.current.getCanvas().style.cursor = '';
        };

        // Remove existing handlers individually to avoid type issues
        // @ts-expect-error - Mapbox types are not fully compatible with our usage
        map.current.off('click', 'clusters');
        // @ts-expect-error - Mapbox types are not fully compatible with our usage
        map.current.off('click', 'unclustered-point');
        // @ts-expect-error - Mapbox types are not fully compatible with our usage
        map.current.off('mouseenter', 'clusters');
        // @ts-expect-error - Mapbox types are not fully compatible with our usage
        map.current.off('mouseenter', 'unclustered-point');
        // @ts-expect-error - Mapbox types are not fully compatible with our usage
        map.current.off('mouseleave', 'clusters');
        // @ts-expect-error - Mapbox types are not fully compatible with our usage
        map.current.off('mouseleave', 'unclustered-point');

        // Add new handlers with proper typing
        // We need to disable ESLint and TypeScript errors here because the Mapbox types
        // don't match our usage pattern, but the code works correctly at runtime
        // eslint-disable-next-line @typescript-eslint/ban-ts-comment
        // @ts-ignore
        map.current.on('click', 'clusters', clusterClickHandler);
        // eslint-disable-next-line @typescript-eslint/ban-ts-comment
        // @ts-ignore
        map.current.on('click', 'unclustered-point', unclusteredPointClickHandler);
        // eslint-disable-next-line @typescript-eslint/ban-ts-comment
        // @ts-ignore
        map.current.on('mouseenter', 'clusters', mouseEnterHandler);
        // eslint-disable-next-line @typescript-eslint/ban-ts-comment
        // @ts-ignore
        map.current.on('mouseenter', 'unclustered-point', mouseEnterHandler);
        // eslint-disable-next-line @typescript-eslint/ban-ts-comment
        // @ts-ignore
        map.current.on('mouseleave', 'clusters', mouseLeaveHandler);
        // eslint-disable-next-line @typescript-eslint/ban-ts-comment
        // @ts-ignore
        map.current.on('mouseleave', 'unclustered-point', mouseLeaveHandler);
      }

      // When clustering is enabled, we don't create custom markers for individual locations
      // This prevents the issue of markers showing on top of clusters
      // Individual locations will be shown through the unclustered-point layer instead

    } catch (error) {
      console.error("Error setting up clustering:", error);
      // If there's an error, try to clean up
      try {
        if (map.current) {
          const layersToRemove = ['unclustered-point', 'cluster-count', 'clusters'];
          for (const layerId of layersToRemove) {
            if (map.current?.getLayer(layerId)) {
              map.current.removeLayer(layerId);
            }
          }
          
          if (clusterSourceRef.current && map.current.getSource(clusterSourceRef.current)) {
            map.current.removeSource(clusterSourceRef.current);
          }
          
          // Clear markers, preserving the selected one
          const selectedMarkerOnError = markersRef.current[SELECTED_MARKER_KEY];
          for (const key in markersRef.current) {
            if (key !== SELECTED_MARKER_KEY) {
              markersRef.current[key].remove();
              delete markersRef.current[key];
            }
          }
          const newMarkersOnError: { [key: string]: mapboxgl.Marker } = {};
          if (selectedMarkerOnError) {
            newMarkersOnError[SELECTED_MARKER_KEY] = selectedMarkerOnError;
          }
          markersRef.current = newMarkersOnError;
        }
      } catch (cleanupError) {
        console.error("Error during cleanup:", cleanupError);
      }
    }
  }, [locations, mapLoaded, mergedClusterSettings.enabled, mergedClusterSettings.maxZoom, mergedClusterSettings.radius, handleLocationSelect, getMarkerColor, selectedLocation])

  // Add markers for locations when not using clustering
  useEffect(() => {
    // Skip if map isn't loaded or if clustering is enabled
    if (!map.current || !mapLoaded) return;
    
    // Always clear existing markers first to prevent duplicates, preserving selected
    const selectedMarkerNoCluster = markersRef.current[SELECTED_MARKER_KEY];
    for (const key in markersRef.current) {
      if (key !== SELECTED_MARKER_KEY) {
        markersRef.current[key].remove();
        delete markersRef.current[key];
      }
    }
    const newMarkersNoCluster: { [key: string]: mapboxgl.Marker } = {};
    if (selectedMarkerNoCluster) {
      newMarkersNoCluster[SELECTED_MARKER_KEY] = selectedMarkerNoCluster;
    }
    markersRef.current = newMarkersNoCluster;
    for (const popup of Object.values(popupsRef.current)) {
      popup.remove();
    }
    popupsRef.current = {}
    
    // If clustering is enabled, don't add individual markers
    if (mergedClusterSettings.enabled) return;

    // This loop is redundant if the above clearing logic is comprehensive.
    // However, to be safe and ensure no other markers slip through if logic changes:
    const selectedMarkerAgain = markersRef.current[SELECTED_MARKER_KEY];
    for (const key in markersRef.current) {
        if (key !== SELECTED_MARKER_KEY) {
            markersRef.current[key].remove();
            delete markersRef.current[key];
        }
    }
    // Reset markersRef to only contain the selected marker if it exists
    markersRef.current = selectedMarkerAgain ? { [SELECTED_MARKER_KEY]: selectedMarkerAgain } : {};
    // Line `markersRef.current = {}` was removed as it would clear the preserved selected marker.
    for (const popup of Object.values(popupsRef.current)) {
      popup.remove();
    }
    popupsRef.current = {}

    // Add markers for locations
    for (const location of locations) {
      // Skip if this location is the currently selected one (it has its own dedicated marker)
      // Ensure selectedLocation and its id are defined before comparing
      if (selectedLocation && typeof selectedLocation.id !== 'undefined' && location.id === selectedLocation.id) {
        continue;
      }

      const { coordinates } = location;
      const markerColor = getMarkerColor(location.type);

      // Create marker element with custom styling to match the design
      const markerElement = document.createElement("div")
      markerElement.className = "marker"
      markerElement.style.width = "40px"
      markerElement.style.height = "40px"
      markerElement.style.borderRadius = "8px"
      markerElement.style.backgroundColor = "white"
      markerElement.style.border = `2px solid ${markerColor}`
      markerElement.style.boxShadow = `0 0 10px ${markerColor}80` // Add neon glow
      markerElement.style.cursor = "pointer"
      markerElement.style.transition = "all 0.3s ease"
      markerElement.style.display = "flex"
      markerElement.style.alignItems = "center"
      markerElement.style.justifyContent = "center"
      markerElement.style.fontSize = "14px"
      markerElement.style.fontWeight = "bold"
      markerElement.style.color = "#333"
      
      // Add price text to the marker
      const price = location.dailyRate ? `$${Math.round(location.dailyRate / 1000)}K` : (
        location.hourlyRate ? `$${Math.round(location.hourlyRate)}` : "N/A"
      )
      markerElement.textContent = price

      // Create popup for hover effect
      const popup = new mapboxgl.Popup({
        closeButton: false,
        closeOnClick: false,
        offset: 25,
        className: "map-popup"
      })
      
      // Set popup content with enhanced styling to match the design
      popup.setHTML(`
        <div class="p-3 rounded-md bg-white border-2 border-${getMarkerBorderClass(location.type)} shadow-lg max-w-xs">
          <div class="flex justify-between items-start">
            <div>
              <h3 class="font-medium text-gray-900">${location.name}</h3>
              <p class="text-xs text-gray-600">${location.type} • ${location.status}</p>
              <p class="text-xs text-gray-600 mt-1">${location.address?.formatted || ''}</p>
            </div>
            <div class="flex items-center">
              <span class="text-lg font-bold text-gray-900">
                ${location.dailyRate ? `$${Math.round(location.dailyRate / 1000)}K` : (
                  location.hourlyRate ? `$${Math.round(location.hourlyRate)}` : "N/A"
                )}
              </span>
            </div>
          </div>
        </div>
      `)

      // Create marker
      const marker = new mapboxgl.Marker(markerElement)
        .setLngLat([coordinates.longitude, coordinates.latitude])
        .addTo(map.current)

      // Add hover events
      markerElement.addEventListener("mouseenter", () => {
        // Enlarge marker
        markerElement.style.width = "48px"
        markerElement.style.height = "48px"
        markerElement.style.boxShadow = `0 0 15px ${markerColor}`
        markerElement.style.zIndex = "10"
        
        // Show popup
        if (map.current) {
          popup.setLngLat([coordinates.longitude, coordinates.latitude])
            .addTo(map.current)
        }
        
        setHoveredLocationId()
      })

      markerElement.addEventListener("mouseleave", () => {
        // Reset marker size
        markerElement.style.width = "40px"
        markerElement.style.height = "40px"
        markerElement.style.boxShadow = `0 0 10px ${markerColor}80`
        markerElement.style.zIndex = "0"
        
        // Hide popup
        popup.remove()
        
        setHoveredLocationId()
      })

      // Add click event
      markerElement.addEventListener("click", () => {
        if (handleLocationSelect) {
          handleLocationSelect(location)
          
          // Center map on selected location
          map.current?.flyTo({
            center: [coordinates.longitude, coordinates.latitude],
            zoom: 14,
            essential: true
          })
          
          // Open detail modal
          setIsDetailModalOpen(true)
        }
      })

      // Store references
      markersRef.current[location.id] = marker
      popupsRef.current[location.id] = popup
    }

    // Fit map to show all locations if there are any
    if (locations.length > 0) {
      fitMapToLocations()
    }
  }, [locations, mapLoaded, mergedClusterSettings.enabled, handleLocationSelect, getMarkerColor, getMarkerBorderClass])

  // Setup clustering when clustering state changes
  useEffect(() => {
    if (isClustering) {
      // Only setup clustering if the map is loaded
      if (mapLoaded) {
        setupClustering()
      }
    } else {
      // Remove clustering layers if they exist
      if (map.current && mapLoaded) {
        // Check if the map style is loaded before removing layers
        if (map.current.isStyleLoaded() && clusterSourceRef.current) {
          if (map.current.getLayer('clusters')) {
            map.current.removeLayer('clusters')
          }
          if (map.current.getLayer('cluster-count')) {
            map.current.removeLayer('cluster-count')
          }
          if (map.current.getLayer('unclustered-point')) {
            map.current.removeLayer('unclustered-point')
          }
          if (map.current.getSource(clusterSourceRef.current)) {
            map.current.removeSource(clusterSourceRef.current)
          }
          clusterSourceRef.current = null
        }
      }
    }
  }, [isClustering, setupClustering, mapLoaded])

  // Update map when view settings change
  useEffect(() => {
    if (!map.current || !mapLoaded) return

    const currentMapCenter = map.current.getCenter();
    const currentMapZoom = map.current.getZoom();
    const targetCenter = viewSettings.center;
    const targetZoom = viewSettings.zoom;

    const LngLatTolerance = 0.0001; // Tolerance for longitude/latitude comparison
    const zoomTolerance = 0.1;    // Tolerance for zoom level comparison

    const centerChangedSignificantly =
      Math.abs(currentMapCenter.lng - targetCenter.longitude) > LngLatTolerance ||
      Math.abs(currentMapCenter.lat - targetCenter.latitude) > LngLatTolerance;

    const zoomChangedSignificantly = Math.abs(currentMapZoom - targetZoom) > zoomTolerance;

    // Only flyTo if the view has significantly changed, to avoid interrupting ongoing animations
    if (centerChangedSignificantly || zoomChangedSignificantly) {
      // Use flyTo instead of jumpTo for smoother transitions
      map.current.flyTo({
        center: [viewSettings.center.longitude, viewSettings.center.latitude],
        zoom: viewSettings.zoom,
        pitch: viewSettings.pitch,
        bearing: viewSettings.bearing,
        duration: 500, // Add smooth animation
        essential: true // Mark as essential to ensure it completes
      });
    }
    
    // Force a repaint after the animation to ensure all elements are properly displayed
    setTimeout(() => {
      if (map.current) {
        map.current.triggerRepaint();
        
        const currentZoom = map.current.getZoom();
        if (currentZoom > mergedClusterSettings.maxZoom && mergedClusterSettings.enabled) {
          if (map.current.isStyleLoaded()) {
            setupClustering(); // This can clear markersRef.current
          }
        }

        // The selectedLocation useEffect is now solely responsible for the highlighted marker.
        // No need to recreate it here.
      }
    }, 550);
  }, [mapLoaded, viewSettings, mergedClusterSettings.enabled, mergedClusterSettings.maxZoom, setupClustering])

  // Update map style when styleId changes
  useEffect(() => {
    if (!map.current || !mapLoaded || !viewSettings.styleId) return
    
    // Set the style and handle re-adding sources and layers after style load
    map.current.setStyle(viewSettings.styleId)
    
    // When style changes, we need to re-setup clustering after the style loads
    const handleStyleLoad = () => {
      if (isClustering && mergedClusterSettings.enabled) {
        setupClustering();
      }
      // Remove the event listener after it's called
      map.current?.off('style.load', handleStyleLoad);
    };
    
    // Add event listener for style.load
    map.current.on('style.load', handleStyleLoad);
  }, [mapLoaded, viewSettings.styleId, isClustering, mergedClusterSettings.enabled, setupClustering])

  // Helper function to create the DOM element for a highlighted marker
  const createHighlightedMarkerElement = useCallback((location: Location) => {
    const markerColor = getMarkerColor(location.type);
    const markerElement = document.createElement("div");
    markerElement.className = "marker selected-marker";
    markerElement.style.width = "48px";
    markerElement.style.height = "48px";
    markerElement.style.borderRadius = "8px";
    markerElement.style.backgroundColor = "white";
    markerElement.style.border = `3px solid ${markerColor}`;
    markerElement.style.boxShadow = `0 0 15px ${markerColor}`;
    markerElement.style.cursor = "pointer";
    markerElement.style.transition = "all 0.3s ease";
    markerElement.style.display = "flex";
    markerElement.style.alignItems = "center";
    markerElement.style.justifyContent = "center";
    markerElement.style.fontSize = "16px";
    markerElement.style.fontWeight = "bold";
    markerElement.style.color = "#333";
    markerElement.style.zIndex = "1000"; // Ensure very high z-index for selected marker

    const price = location.dailyRate ? `$${Math.round(location.dailyRate / 1000)}K` : (
      location.hourlyRate ? `$${Math.round(location.hourlyRate)}` : "N/A"
    );
    markerElement.textContent = price;
    return markerElement;
  }, [getMarkerColor]);

  // Manage highlighted selected location marker
  useEffect(() => {
    if (!map.current || !mapLoaded) return;

    const existingSelectedMarker = markersRef.current[SELECTED_MARKER_KEY];

    if (selectedLocation) {
      const newCoords: [number, number] = [selectedLocation.coordinates.longitude, selectedLocation.coordinates.latitude];

      if (existingSelectedMarker) {
        existingSelectedMarker.setLngLat(newCoords);
        // Ensure its element is correctly styled (in case it was an old regular marker somehow)
        // This might be overkill if createHighlightedMarkerElement is always used for its creation.
        const el = existingSelectedMarker.getElement();
        el.style.zIndex = "1000"; // Re-assert z-index
      } else {
        const markerElement = createHighlightedMarkerElement(selectedLocation);
        markerElement.addEventListener("click", () => {
          setIsDetailModalOpen(true);
        });
        const newMarker = new mapboxgl.Marker(markerElement)
          .setLngLat(newCoords)
          .addTo(map.current);
        markersRef.current[SELECTED_MARKER_KEY] = newMarker;
      }

      // Update viewSettings to trigger map centering animation
      setViewSettings({
        ...viewSettings,
        center: {
          latitude: selectedLocation.coordinates.latitude,
          longitude: selectedLocation.coordinates.longitude,
        },
        zoom: 14, // Or a more dynamic zoom level if needed
      });
    } else {
      // No location selected, remove the highlighted marker if it exists
      if (existingSelectedMarker) {
        existingSelectedMarker.remove();
        delete markersRef.current[SELECTED_MARKER_KEY];
      }
    }
  }, [selectedLocation, mapLoaded, setViewSettings, getMarkerColor, createHighlightedMarkerElement]);

  // Function to fit map to show all locations
  const fitMapToLocations = useCallback(() => {
    if (!map.current || locations.length === 0) return
    
    // Create a bounds object
    const bounds = new mapboxgl.LngLatBounds()
    
    // Extend the bounds to include all locations
    for (const location of locations) {
      if (location.coordinates) {
        bounds.extend([location.coordinates.longitude, location.coordinates.latitude])
      }
    }
    
    // Fit the map to the bounds with padding
    map.current.fitBounds(bounds, {
      padding: 50,
      maxZoom: 15, // Limit max zoom level when fitting bounds
      duration: 1000 // Smooth animation
    })
  }, [locations])

  // Toggle clustering
  const toggleClustering = useCallback(() => {
    setIsClustering(!isClustering)
  }, [isClustering])

  // Remove the dummy effect since we now have properly memoized functions

  // Render component
  return (
    <div className={className}>
      {/* Map container */}
      <div
        ref={mapContainer}
        style={{
          width,
          height,
          position: "absolute",
          inset: 0,
          borderRadius: "0.5rem",
          overflow: "hidden",
        }}
      >
        {/* Loading overlay */}
        {(isLoading || isDataLoading) && (
          <div className="absolute inset-0 flex items-center justify-center bg-black/30 z-10">
            <Loader2 className="h-8 w-8 animate-spin text-white" />
          </div>
        )}

        {/* Error message */}
        {(tokenError || dataError) && (
          <div className="absolute inset-0 flex items-center justify-center bg-black/30 z-10">
            <div className="bg-white p-4 rounded-md shadow-lg max-w-md">
              <h3 className="text-lg font-medium text-red-600">Error</h3>
              <p className="text-gray-700">{tokenError || "Failed to load map data"}</p>
            </div>
          </div>
        )}

        {/* Map controls */}
        {interactive && (
          <MapControls
            onToggleClustering={toggleClustering}
            isClustering={isClustering}
            onFitToLocations={fitMapToLocations}
            onOpenFilterModal={() => setIsFilterModalOpen(true)}
          />
        )}
      </div>

      {/* Location detail modal */}
      {selectedLocation && (
        <LocationDetailModal
          open={isDetailModalOpen}
          onOpenChange={(open) => setIsDetailModalOpen(open)}
          location={selectedLocation}
        />
      )}

      {/* Filter modal */}
      <MapFilterModal
        open={isFilterModalOpen}
        onOpenChange={(open) => setIsFilterModalOpen(open)}
      />
    </div>
  )
}
