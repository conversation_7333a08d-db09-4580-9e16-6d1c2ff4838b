"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { Command, CommandEmpty, CommandGroup, CommandInput, CommandItem, CommandList } from "@/components/ui/command"
import { Check, ChevronDown, Loader2, Save } from "lucide-react"
import { useMap } from "@/providers/map-provider"
import { SaveViewModal } from "./save-view-modal"
import { toast } from "sonner"

// Define a simplified view type that matches what we get from the API
type ViewItem = {
  id: string
  name: string
  description?: string
}

export function ViewSelector() {
  const { savedViews, loadView, refreshSavedViews, isSavedViewsLoading } = useMap()
  const [open, setOpen] = useState(false)
  const [saveModalOpen, setSaveModalOpen] = useState(false)
  const [selectedView, setSelectedView] = useState<ViewItem | null>(null)
  const [isLoading, setIsLoading] = useState(false)

  // Refresh saved views when the component mounts
  useEffect(() => {
    refreshSavedViews()
  }, [refreshSavedViews])

  const handleSelectView = async (view: ViewItem) => {
    // Store only the necessary properties for display
    setSelectedView({
      id: view.id,
      name: view.name,
      description: view.description
    })
    setOpen(false)
    setIsLoading(true)

    try {
      const success = await loadView(view.id)
      
      if (success) {
        toast.success(`View "${view.name}" loaded`)
      } else {
        toast.error("Failed to load view")
      }
    } catch (error) {
      console.error("Error loading view:", error)
      toast.error("An error occurred while loading the view")
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <>
      <div className="flex items-center gap-2">
        <Popover open={open} onOpenChange={setOpen}>
          <PopoverTrigger asChild>
            <Button
              variant="outline"
              aria-haspopup="listbox"
              aria-expanded={open}
              className="w-[200px] justify-between"
              disabled={isLoading}
            >
              {isLoading ? (
                <Loader2 className="h-4 w-4 animate-spin" />
              ) : selectedView ? (
                selectedView.name
              ) : (
                "Select view"
              )}
              <ChevronDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
            </Button>
          </PopoverTrigger>
          <PopoverContent className="w-[200px] p-0">
            <Command>
              <CommandInput placeholder="Search views..." />
              <CommandList>
                <CommandEmpty>No saved views found.</CommandEmpty>
                <CommandGroup>
                  {isSavedViewsLoading ? (
                    <div className="flex items-center justify-center py-6">
                      <Loader2 className="h-6 w-6 animate-spin text-muted-foreground" />
                    </div>
                  ) : (
                    savedViews.map((view) => (
                      <CommandItem
                        key={view.id}
                        value={view.name}
                        onSelect={() => handleSelectView(view)}
                        className="flex items-center justify-between"
                      >
                        <div className="flex-1 truncate" title={view.description || ""}>
                          {view.name}
                        </div>
                        {selectedView?.id === view.id && (
                          <Check className="ml-2 h-4 w-4 shrink-0" />
                        )}
                      </CommandItem>
                    ))
                  )}
                </CommandGroup>
              </CommandList>
            </Command>
          </PopoverContent>
        </Popover>

        <Button
          variant="outline"
          size="icon"
          onClick={() => setSaveModalOpen(true)}
          title="Save current view"
        >
          <Save className="h-4 w-4" />
        </Button>
      </div>

      <SaveViewModal isOpen={saveModalOpen} onClose={() => setSaveModalOpen(false)} />
    </>
  )
}
