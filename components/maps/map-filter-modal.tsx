"use client"

import { useState, useEffect } from "react"
import { useMap } from "@/providers/map-provider"
import { <PERSON><PERSON>, <PERSON>alog<PERSON>ontent, DialogHeader, <PERSON><PERSON><PERSON><PERSON><PERSON>, DialogFooter } from "@/components/ui/dialog"
import { Button } from "@/components/ui/button"
import { Label } from "@/components/ui/label"
import { Input } from "@/components/ui/input"
import { Checkbox } from "@/components/ui/checkbox"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { Tabs, <PERSON>bs<PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Calendar } from "@/components/ui/calendar"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
// import { Slider } from "@/components/ui/slider" // Uncomment if needed for future enhancements
import { ScrollArea } from "@/components/ui/scroll-area"
import { CalendarIcon, FilterIcon, X } from "lucide-react"
import { cn } from "@/lib/utils"
import { format } from "date-fns"
import { LOCATION_TYPES, LOCATION_STATUSES } from "@/modules/shared/constants"
import type { MapFilter } from "@/modules/map/model"

interface MapFilterModalProps {
  open: boolean
  onOpenChange: (open: boolean) => void
}

export function MapFilterModal({ open, onOpenChange }: MapFilterModalProps) {
  const { filters, setFilters, projects, favorites, refreshLocations } = useMap()
  
  // Local state for filter values
  const [localFilters, setLocalFilters] = useState<MapFilter>(filters)
  const [activeTab, setActiveTab] = useState("basic")
  
  // Reset local filters when the modal opens
  useEffect(() => {
    if (open) {
      setLocalFilters(filters)
    }
  }, [open, filters])
  
  // Apply filters and close modal
  const handleApplyFilters = async () => {
    setFilters(localFilters)
    await refreshLocations()
    onOpenChange(false)
  }
  
  // Reset all filters
  const handleResetFilters = () => {
    setLocalFilters({})
  }
  
  // Update local filter state
  const updateFilter = <T,>(key: keyof MapFilter, value: T | undefined) => {
    setLocalFilters(prev => ({ ...prev, [key]: value }))
  }
  
  // Toggle location type in filter
  const toggleLocationType = (type: string) => {
    const currentTypes = localFilters.locationTypes || []
    const newTypes = currentTypes.includes(type)
      ? currentTypes.filter(t => t !== type)
      : [...currentTypes, type]
    
    updateFilter('locationTypes', newTypes.length > 0 ? newTypes : undefined)
  }
  
  // Toggle location status in filter
  const toggleLocationStatus = (status: string) => {
    const currentStatuses = localFilters.status || []
    const newStatuses = currentStatuses.includes(status)
      ? currentStatuses.filter(s => s !== status)
      : [...currentStatuses, status]
    
    updateFilter('status', newStatuses.length > 0 ? newStatuses : undefined)
  }
  
  // Toggle project in filter
  const toggleProject = (projectId: string) => {
    const currentProjects = localFilters.projectIds || []
    const newProjects = currentProjects.includes(projectId)
      ? currentProjects.filter(p => p !== projectId)
      : [...currentProjects, projectId]
    
    updateFilter('projectIds', newProjects.length > 0 ? newProjects : undefined)
  }
  
  // Format date for display
  const formatDate = (dateString?: string) => {
    if (!dateString) return ""
    return format(new Date(dateString), "PPP")
  }
  
  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-3xl max-h-[90vh] overflow-hidden flex flex-col">
        <DialogHeader>
          <DialogTitle className="flex items-center">
            <FilterIcon className="h-5 w-5 mr-2 text-neon-blue" />
            Advanced Filtering
          </DialogTitle>
        </DialogHeader>
        
        <Tabs value={activeTab} onValueChange={setActiveTab} className="flex-1 overflow-hidden flex flex-col">
          <TabsList className="grid grid-cols-3 mb-4">
            <TabsTrigger value="basic">Basic Filters</TabsTrigger>
            <TabsTrigger value="advanced">Advanced Filters</TabsTrigger>
            <TabsTrigger value="favorites">Favorites</TabsTrigger>
          </TabsList>
          
          <ScrollArea className="flex-1">
            <TabsContent value="basic" className="space-y-6 p-1">
              {/* Search Query */}
              <div className="space-y-2">
                <Label htmlFor="searchQuery">Search</Label>
                <Input
                  id="searchQuery"
                  placeholder="Search by name or description"
                  value={localFilters.searchQuery || ""}
                  onChange={(e) => updateFilter("searchQuery", e.target.value || undefined)}
                />
              </div>
              
              {/* Location Types */}
              <div className="space-y-2">
                <Label>Location Types</Label>
                <div className="grid grid-cols-2 sm:grid-cols-3 gap-2">
                  {LOCATION_TYPES.map((type) => (
                    <div key={type} className="flex items-center space-x-2">
                      <Checkbox
                        id={`type-${type}`}
                        checked={(localFilters.locationTypes || []).includes(type)}
                        onCheckedChange={() => toggleLocationType(type)}
                      />
                      <Label htmlFor={`type-${type}`} className="capitalize">
                        {type}
                      </Label>
                    </div>
                  ))}
                </div>
              </div>
              
              {/* Location Status */}
              <div className="space-y-2">
                <Label>Location Status</Label>
                <div className="grid grid-cols-2 sm:grid-cols-3 gap-2">
                  {LOCATION_STATUSES.map((status) => (
                    <div key={status} className="flex items-center space-x-2">
                      <Checkbox
                        id={`status-${status}`}
                        checked={(localFilters.status || []).includes(status)}
                        onCheckedChange={() => toggleLocationStatus(status)}
                      />
                      <Label htmlFor={`status-${status}`} className="capitalize">
                        {status}
                      </Label>
                    </div>
                  ))}
                </div>
              </div>
              
              {/* Projects */}
              <div className="space-y-2">
                <Label>Projects</Label>
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-2">
                  {projects.map((project) => (
                    <div key={project.id} className="flex items-center space-x-2">
                      <Checkbox
                        id={`project-${project.id}`}
                        checked={(localFilters.projectIds || []).includes(project.id)}
                        onCheckedChange={() => toggleProject(project.id)}
                      />
                      <Label htmlFor={`project-${project.id}`}>
                        {project.name}
                      </Label>
                    </div>
                  ))}
                </div>
              </div>
              
              {/* Date Range */}
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label>From Date</Label>
                  <Popover>
                    <PopoverTrigger asChild>
                      <Button
                        variant="outline"
                        className={cn(
                          "w-full justify-start text-left font-normal",
                          !localFilters.dateFrom && "text-muted-foreground"
                        )}
                      >
                        <CalendarIcon className="mr-2 h-4 w-4" />
                        {localFilters.dateFrom ? formatDate(localFilters.dateFrom) : "Select date"}
                        {localFilters.dateFrom && (
                          <X
                            className="ml-auto h-4 w-4 opacity-50 hover:opacity-100"
                            onClick={(e) => {
                              e.stopPropagation()
                              updateFilter("dateFrom", undefined)
                            }}
                          />
                        )}
                      </Button>
                    </PopoverTrigger>
                    <PopoverContent className="w-auto p-0">
                      <Calendar
                        mode="single"
                        selected={localFilters.dateFrom ? new Date(localFilters.dateFrom) : undefined}
                        onSelect={(date) => updateFilter("dateFrom", date ? format(date, "yyyy-MM-dd") : undefined)}
                        initialFocus
                      />
                    </PopoverContent>
                  </Popover>
                </div>
                
                <div className="space-y-2">
                  <Label>To Date</Label>
                  <Popover>
                    <PopoverTrigger asChild>
                      <Button
                        variant="outline"
                        className={cn(
                          "w-full justify-start text-left font-normal",
                          !localFilters.dateTo && "text-muted-foreground"
                        )}
                      >
                        <CalendarIcon className="mr-2 h-4 w-4" />
                        {localFilters.dateTo ? formatDate(localFilters.dateTo) : "Select date"}
                        {localFilters.dateTo && (
                          <X
                            className="ml-auto h-4 w-4 opacity-50 hover:opacity-100"
                            onClick={(e) => {
                              e.stopPropagation()
                              updateFilter("dateTo", undefined)
                            }}
                          />
                        )}
                      </Button>
                    </PopoverTrigger>
                    <PopoverContent className="w-auto p-0">
                      <Calendar
                        mode="single"
                        selected={localFilters.dateTo ? new Date(localFilters.dateTo) : undefined}
                        onSelect={(date) => updateFilter("dateTo", date ? format(date, "yyyy-MM-dd") : undefined)}
                        initialFocus
                      />
                    </PopoverContent>
                  </Popover>
                </div>
              </div>
            </TabsContent>
            
            <TabsContent value="advanced" className="space-y-6 p-1">
              {/* Permits Required */}
              <div className="space-y-2">
                <Label>Permits</Label>
                <RadioGroup
                  value={localFilters.permitsRequired === undefined 
                    ? "any" 
                    : localFilters.permitsRequired ? "required" : "not-required"}
                  onValueChange={(value) => {
                    if (value === "any") {
                      updateFilter("permitsRequired", undefined)
                    } else {
                      updateFilter("permitsRequired", value === "required")
                    }
                  }}
                  className="flex space-x-4"
                >
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem value="any" id="permits-any" />
                    <Label htmlFor="permits-any">Any</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem value="required" id="permits-required" />
                    <Label htmlFor="permits-required">Required</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem value="not-required" id="permits-not-required" />
                    <Label htmlFor="permits-not-required">Not Required</Label>
                  </div>
                </RadioGroup>
              </div>
              
              {/* Location Tags */}
              <div className="space-y-2">
                <Label htmlFor="locationTags">Location Tags (comma separated)</Label>
                <Input
                  id="locationTags"
                  placeholder="e.g. historic, waterfront, quiet"
                  value={(localFilters.locationTags || []).join(", ")}
                  onChange={(e) => {
                    const tags = e.target.value
                      .split(",")
                      .map(tag => tag.trim())
                      .filter(Boolean)
                    updateFilter("locationTags", tags.length > 0 ? tags : undefined)
                  }}
                />
              </div>
              
              {/* Location Features */}
              <div className="space-y-2">
                <Label htmlFor="locationFeatures">Location Features (comma separated)</Label>
                <Input
                  id="locationFeatures"
                  placeholder="e.g. parking, elevator, kitchen"
                  value={(localFilters.locationFeatures || []).join(", ")}
                  onChange={(e) => {
                    const features = e.target.value
                      .split(",")
                      .map(feature => feature.trim())
                      .filter(Boolean)
                    updateFilter("locationFeatures", features.length > 0 ? features : undefined)
                  }}
                />
              </div>
              
              {/* Hourly Rate Range */}
              <div className="space-y-4">
                <div className="flex justify-between items-center">
                  <Label>Hourly Rate Range</Label>
                  <div className="text-sm">
                    {localFilters.hourlyRateMin !== undefined && `$${localFilters.hourlyRateMin}`}
                    {localFilters.hourlyRateMin !== undefined && localFilters.hourlyRateMax !== undefined && " - "}
                    {localFilters.hourlyRateMax !== undefined && `$${localFilters.hourlyRateMax}`}
                    {localFilters.hourlyRateMin === undefined && localFilters.hourlyRateMax === undefined && "Any"}
                  </div>
                </div>
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="hourlyRateMin">Min ($)</Label>
                    <Input
                      id="hourlyRateMin"
                      type="number"
                      min={0}
                      placeholder="Min"
                      value={localFilters.hourlyRateMin || ""}
                      onChange={(e) => updateFilter("hourlyRateMin", e.target.value ? Number(e.target.value) : undefined)}
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="hourlyRateMax">Max ($)</Label>
                    <Input
                      id="hourlyRateMax"
                      type="number"
                      min={0}
                      placeholder="Max"
                      value={localFilters.hourlyRateMax || ""}
                      onChange={(e) => updateFilter("hourlyRateMax", e.target.value ? Number(e.target.value) : undefined)}
                    />
                  </div>
                </div>
              </div>
              
              {/* Daily Rate Range */}
              <div className="space-y-4">
                <div className="flex justify-between items-center">
                  <Label>Daily Rate Range</Label>
                  <div className="text-sm">
                    {localFilters.dailyRateMin !== undefined && `$${localFilters.dailyRateMin}`}
                    {localFilters.dailyRateMin !== undefined && localFilters.dailyRateMax !== undefined && " - "}
                    {localFilters.dailyRateMax !== undefined && `$${localFilters.dailyRateMax}`}
                    {localFilters.dailyRateMin === undefined && localFilters.dailyRateMax === undefined && "Any"}
                  </div>
                </div>
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="dailyRateMin">Min ($)</Label>
                    <Input
                      id="dailyRateMin"
                      type="number"
                      min={0}
                      placeholder="Min"
                      value={localFilters.dailyRateMin || ""}
                      onChange={(e) => updateFilter("dailyRateMin", e.target.value ? Number(e.target.value) : undefined)}
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="dailyRateMax">Max ($)</Label>
                    <Input
                      id="dailyRateMax"
                      type="number"
                      min={0}
                      placeholder="Max"
                      value={localFilters.dailyRateMax || ""}
                      onChange={(e) => updateFilter("dailyRateMax", e.target.value ? Number(e.target.value) : undefined)}
                    />
                  </div>
                </div>
              </div>
            </TabsContent>
            
            <TabsContent value="favorites" className="space-y-6 p-1">
              <div className="space-y-2">
                <Label>Filter by Favorite List</Label>
                <RadioGroup
                  value={localFilters.favoriteListId || "none"}
                  onValueChange={(value) => {
                    updateFilter("favoriteListId", value === "none" ? undefined : value)
                  }}
                >
                  <div className="flex items-center space-x-2 mb-2">
                    <RadioGroupItem value="none" id="favorite-none" />
                    <Label htmlFor="favorite-none">No filter</Label>
                  </div>
                  
                  {favorites.map((list) => (
                    <div key={list.id} className="flex items-center space-x-2 mb-2">
                      <RadioGroupItem value={list.id} id={`favorite-${list.id}`} />
                      <Label htmlFor={`favorite-${list.id}`}>
                        {list.name} ({list.locations.length} locations)
                      </Label>
                    </div>
                  ))}
                  
                  {favorites.length === 0 && (
                    <div className="text-muted-foreground text-sm">
                      No favorite lists found. Create a favorite list to filter by favorites.
                    </div>
                  )}
                </RadioGroup>
              </div>
            </TabsContent>
          </ScrollArea>
        </Tabs>
        
        <DialogFooter className="pt-4 border-t">
          <div className="flex justify-between w-full">
            <Button variant="outline" onClick={handleResetFilters}>
              Reset Filters
            </Button>
            <div className="space-x-2">
              <Button variant="outline" onClick={() => onOpenChange(false)}>
                Cancel
              </Button>
              <Button onClick={handleApplyFilters} data-filter-button="true">
                Apply Filters
              </Button>
            </div>
          </div>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
