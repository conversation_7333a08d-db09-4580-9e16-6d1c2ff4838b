"use client"

import { useState, useEffect } from "react"
import { MapView } from "@/components/maps/map-view"
import { MapLocations } from "@/components/maps/map-locations"
import { MapLayers } from "@/components/maps/map-layers"
import { MapFilterModal } from "@/components/maps/map-filter-modal"
import { SaveViewModal } from "@/components/maps/save-view-modal"
import { ViewSelectorModal } from "@/components/maps/view-selector-modal"
import { ShareMapModal } from "@/components/maps/share-map-modal"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { 
  ChevronLeft, 
  ChevronRight, 
  Search, 
  Filter, 
  MapPin, 
  Layers, 
  Save, 
  BookOpen,
  Minimize,
  Share
} from "lucide-react"
import { cn } from "@/lib/utils"
import { useMap } from "@/providers/map-provider"

interface FullScreenMapProps {
  onClose: () => void
}

export function FullScreenMap({ onClose }: FullScreenMapProps) {
  const [sidebarOpen, setSidebarOpen] = useState(true)
  const [isFilterModalOpen, setIsFilterModalOpen] = useState(false)
  const [isSaveViewModalOpen, setIsSaveViewModalOpen] = useState(false)
  const [isViewSelectorModalOpen, setIsViewSelectorModalOpen] = useState(false)
  const [isShareMapModalOpen, setIsShareMapModalOpen] = useState(false)
  const [searchQuery, setSearchQuery] = useState("")
  const { setFilters, filters, refreshLocations } = useMap()

  // Handle search input
  const handleSearch = async () => {
    setFilters({ ...filters, searchQuery: searchQuery || undefined })
    await refreshLocations()
  }

  // Handle key press for search
  const handleKeyPress = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === "Enter") {
      handleSearch()
    }
  }

  // Handle ESC key to exit full-screen mode
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === "Escape") {
        onClose()
      }
    }

    window.addEventListener("keydown", handleKeyDown)
    return () => window.removeEventListener("keydown", handleKeyDown)
  }, [onClose])

  return (
    <div className="fixed inset-0 z-50 bg-background flex">
      {/* Collapsible Sidebar */}
      <div 
        className={cn(
          "h-full bg-background border-r transition-all duration-300 flex flex-col",
          sidebarOpen ? "w-80" : "w-0 overflow-hidden"
        )}
      >
        <div className="p-4 border-b flex items-center justify-between">
          <h2 className="font-semibold">Map Controls</h2>
          <Button variant="ghost" size="icon" onClick={() => setSidebarOpen(false)}>
            <ChevronLeft className="h-4 w-4" />
          </Button>
        </div>

        <div className="flex-1 overflow-auto p-4 space-y-4">
          <div className="relative">
            <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input 
              type="search" 
              placeholder="Search locations..." 
              className="w-full pl-8" 
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              onKeyDown={handleKeyPress}
            />
          </div>

          <div className="flex items-center justify-between">
            <Button 
              variant="outline" 
              size="sm" 
              className="w-full"
              onClick={() => setIsFilterModalOpen(true)}
            >
              <Filter className="mr-2 h-4 w-4" />
              Advanced Filters
            </Button>
          </div>

          <Tabs defaultValue="locations" className="w-full">
            <TabsList className="w-full">
              <TabsTrigger value="locations" className="flex-1">
                <MapPin className="mr-2 h-4 w-4" />
                Locations
              </TabsTrigger>
              <TabsTrigger value="layers" className="flex-1">
                <Layers className="mr-2 h-4 w-4" />
                Layers
              </TabsTrigger>
            </TabsList>
            <TabsContent value="locations" className="mt-4 space-y-4">
              <MapLocations />
            </TabsContent>
            <TabsContent value="layers" className="mt-4 space-y-4">
              <MapLayers />
            </TabsContent>
          </Tabs>

          <div className="pt-2 space-y-2">
            <Button 
              variant="outline" 
              size="sm" 
              className="w-full"
              onClick={() => setIsSaveViewModalOpen(true)}
            >
              <Save className="mr-2 h-4 w-4" />
              Save View
            </Button>
            <Button 
              variant="outline" 
              size="sm" 
              className="w-full"
              onClick={() => setIsViewSelectorModalOpen(true)}
            >
              <BookOpen className="mr-2 h-4 w-4" />
              Load View
            </Button>
            <Button 
              variant="outline" 
              size="sm" 
              className="w-full"
              onClick={() => setIsShareMapModalOpen(true)}
            >
              <Share className="mr-2 h-4 w-4" />
              Share Map
            </Button>
          </div>
        </div>
      </div>

      {/* Map Container */}
      <div className="flex-1 relative">
        <MapView 
          height="100%" 
          width="100%" 
          interactive={true} 
          showControls={true}
        />

        {/* Top Controls */}
        <div className="absolute top-4 left-4 z-10 flex flex-col space-y-2">
          {!sidebarOpen && (
            <Button 
              variant="secondary" 
              size="sm" 
              className="bg-white/30 backdrop-blur-md border border-white/40 shadow-md hover:bg-white/40 text-white"
              onClick={() => setSidebarOpen(true)}
            >
              <ChevronRight className="mr-2 h-4 w-4" />
              Show Sidebar
            </Button>
          )}
          <Button 
            variant="secondary" 
            size="sm" 
            className="bg-white/30 backdrop-blur-md border border-white/40 shadow-md hover:bg-white/40 text-white"
            onClick={onClose}
          >
            <Minimize className="mr-2 h-4 w-4" />
            Exit Full Screen
          </Button>
        </div>
      </div>

      {/* Modals */}
      <MapFilterModal
        open={isFilterModalOpen}
        onOpenChange={setIsFilterModalOpen}
      />
      
      <SaveViewModal 
        isOpen={isSaveViewModalOpen} 
        onClose={() => setIsSaveViewModalOpen(false)} 
      />
      
      <ViewSelectorModal 
        isOpen={isViewSelectorModalOpen} 
        onClose={() => setIsViewSelectorModalOpen(false)} 
      />
      
      <ShareMapModal
        isOpen={isShareMapModalOpen}
        onClose={() => setIsShareMapModalOpen(false)}
      />
    </div>
  )
}
