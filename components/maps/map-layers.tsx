"use client"

import { useState, useEffect, useRef } from "react"
import { Checkbox } from "@/components/ui/checkbox"
import { Label } from "@/components/ui/label"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Slider } from "@/components/ui/slider"
import { Badge } from "@/components/ui/badge"
import { Card } from "@/components/ui/card"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { useMap } from "@/providers/map-provider"

// Define map style types
type MapStyle = {
  id: string
  name: string
  description: string
}

// Define overlay types
type MapOverlay = {
  id: string
  name: string
  layerId: string
  sourceId?: string
  active: boolean
}

// Define property filter types
type PropertyFilter = {
  id: string
  name: string
  value: string
  active: boolean
}

export function MapLayers() {
  const { viewSettings, setViewSettings } = useMap()
  
  // State for map styles
  const [selectedStyle, setSelectedStyle] = useState<string>(
    viewSettings.styleId || "mapbox://styles/mapbox/dark-v11"
  )
  
  // State for overlays
  const [overlays, setOverlays] = useState<MapOverlay[]>([
    { id: "traffic", name: "Traffic", layerId: "traffic", active: false },
    { id: "transit", name: "Transit", layerId: "transit", active: false },
    { id: "poi", name: "Points of Interest", layerId: "poi", active: true }
  ])
  
  // State for property filters
  const [propertyFilters, setPropertyFilters] = useState<PropertyFilter[]>([
    { id: "residential", name: "Residential", value: "residential", active: true },
    { id: "commercial", name: "Commercial", value: "commercial", active: true },
    { id: "industrial", name: "Industrial", value: "industrial", active: true },
    { id: "land", name: "Land", value: "land", active: false }
  ])
  
  // State for layer opacity
  const [opacity, setOpacity] = useState<number>(75)
  
  // Map styles
  const mapStyles: MapStyle[] = [
    { id: "mapbox://styles/mapbox/dark-v11", name: "Dark", description: "Dark theme with neon accents" },
    { id: "mapbox://styles/mapbox/light-v11", name: "Light", description: "Light theme for better readability" },
    { id: "mapbox://styles/mapbox/streets-v12", name: "Streets", description: "Detailed street map" },
    { id: "mapbox://styles/mapbox/satellite-streets-v12", name: "Satellite", description: "Satellite imagery with streets" },
    { id: "mapbox://styles/mapbox/navigation-day-v1", name: "Navigation", description: "Optimized for navigation" },
  ]
  
  // Use ref to track previous style to avoid infinite loops
  const prevStyleRef = useRef(selectedStyle);
  
  // Update map style when selected style changes
  useEffect(() => {
    // Only update if the style has actually changed
    if (prevStyleRef.current !== selectedStyle) {
      setViewSettings({
        ...viewSettings,
        styleId: selectedStyle
      });
      // Update ref with current style
      prevStyleRef.current = selectedStyle;
    }
  }, [selectedStyle, setViewSettings, viewSettings]);
  
  // Handle style change
  const handleStyleChange = (styleId: string) => {
    setSelectedStyle(styleId)
  }
  
  // Handle overlay toggle
  const handleOverlayToggle = (id: string, checked: boolean) => {
    setOverlays(overlays.map(overlay => 
      overlay.id === id ? { ...overlay, active: checked } : overlay
    ))
    
    // This would typically trigger a map layer update
    // For now, we'll just update the state
  }
  
  // Handle property filter toggle
  const handlePropertyFilterToggle = (id: string, checked: boolean) => {
    setPropertyFilters(propertyFilters.map(filter => 
      filter.id === id ? { ...filter, active: checked } : filter
    ))
    
    // This would typically trigger a map filter update
    // For now, we'll just update the state
  }
  
  // Handle opacity change
  const handleOpacityChange = (values: number[]) => {
    setOpacity(values[0])
    
    // This would typically update the opacity of map layers
    // For now, we'll just update the state
  }
  
  // Count active overlays
  const activeOverlaysCount = overlays.filter(overlay => overlay.active).length
  
  // Count active property filters
  const activePropertyFiltersCount = propertyFilters.filter(filter => filter.active).length

  return (
    <ScrollArea className="h-[400px] pr-3">
      <div className="space-y-6">
        <div className="space-y-3">
          <div className="flex items-center justify-between">
            <h4 className="font-medium text-sm">Base Maps</h4>
            <Badge variant="neonBlue" size="sm">Active</Badge>
          </div>
          <Card variant="neon" className="p-3 space-y-2">
            <RadioGroup value={selectedStyle} onValueChange={handleStyleChange}>
              {mapStyles.map((style) => (
                <div key={style.id} className="flex items-start space-x-2 p-2 rounded-md hover:bg-neon-blue/5 cursor-pointer">
                  <RadioGroupItem value={style.id} id={style.id} className="border-neon-blue data-[state=checked]:bg-neon-blue data-[state=checked]:text-white" />
                  <div className="grid gap-0.5">
                    <Label htmlFor={style.id} className="cursor-pointer font-medium">{style.name}</Label>
                    <span className="text-xs text-muted-foreground">{style.description}</span>
                  </div>
                </div>
              ))}
            </RadioGroup>
          </Card>
        </div>
        
        <div className="space-y-3">
          <div className="flex items-center justify-between">
            <h4 className="font-medium text-sm">Overlays</h4>
            <Badge variant="neonGreen" size="sm">{activeOverlaysCount} Active</Badge>
          </div>
          <Card variant="neonGreen" className="p-3 space-y-2">
            {overlays.map((overlay) => (
              <div key={overlay.id} className="flex items-center space-x-3 p-2 rounded-md hover:bg-neon-green/5 cursor-pointer">
                <Checkbox 
                  id={overlay.id} 
                  checked={overlay.active}
                  onCheckedChange={(checked) => handleOverlayToggle(overlay.id, checked as boolean)}
                  className="border-neon-green data-[state=checked]:bg-neon-green data-[state=checked]:text-deep-black" 
                />
                <Label 
                  htmlFor={overlay.id} 
                  className={`cursor-pointer ${overlay.active ? 'font-medium' : ''}`}
                >
                  {overlay.name}
                </Label>
              </div>
            ))}
          </Card>
        </div>
        
        <div className="space-y-3">
          <div className="flex items-center justify-between">
            <h4 className="font-medium text-sm">Property Filters</h4>
            <Badge variant="neonPink" size="sm">{activePropertyFiltersCount} Active</Badge>
          </div>
          <Card variant="neonPink" className="p-3 space-y-2">
            {propertyFilters.map((filter) => (
              <div key={filter.id} className="flex items-center space-x-3 p-2 rounded-md hover:bg-neon-pink/5 cursor-pointer">
                <Checkbox 
                  id={filter.id} 
                  checked={filter.active}
                  onCheckedChange={(checked) => handlePropertyFilterToggle(filter.id, checked as boolean)}
                  className="border-neon-pink data-[state=checked]:bg-neon-pink data-[state=checked]:text-white" 
                />
                <Label 
                  htmlFor={filter.id} 
                  className={`cursor-pointer ${filter.active ? 'font-medium' : ''}`}
                >
                  {filter.name}
                </Label>
              </div>
            ))}
          </Card>
        </div>
        
        <div className="space-y-3">
          <div className="flex items-center justify-between">
            <Label htmlFor="opacity" className="font-medium text-sm">Layer Opacity</Label>
            <span className="text-xs text-muted-foreground">{opacity}%</span>
          </div>
          <Slider 
            id="opacity" 
            value={[opacity]} 
            max={100} 
            step={1}
            onValueChange={handleOpacityChange}
            className="[&_[role=slider]]:bg-neon-blue [&_[role=slider]]:border-neon-blue/50 [&_[role=slider]]:shadow-[0_0_5px_rgba(35,35,255,0.5)]" 
          />
        </div>
      </div>
    </ScrollArea>
  )
}
