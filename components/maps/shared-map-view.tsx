"use client"

import { useState, useEffect, useCallback } from "react"
import { useRouter } from "next/navigation"
import { MapView } from "@/components/maps/map-view"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { <PERSON><PERSON>, <PERSON>alogContent, DialogHeader, <PERSON>alogTitle, DialogFooter } from "@/components/ui/dialog"
import { Loader2, Lock, ArrowLeft } from "lucide-react"
import { toast } from "sonner"
import type { MapViewSettings } from "@/modules/map/model"
import type { Location } from "@/modules/location/model"
import { SharedMapProvider } from "@/providers/shared-map-provider"

interface SharedMapViewProps {
  token: string
}

type SharedMapData = {
  id: string
  organizationId: string
  viewSettings: MapViewSettings
  locations?: {
    id: string
    name: string
    description?: string
    address: {
      formatted: string
    }
    coordinates: {
      latitude: number
      longitude: number
    }
    type: string
    status: string
  }[]
  isPasswordProtected: boolean
}

export function SharedMapView({ token }: SharedMapViewProps) {
  const router = useRouter()
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [mapData, setMapData] = useState<SharedMapData | null>(null)
  const [isPasswordModalOpen, setIsPasswordModalOpen] = useState(false)
  const [password, setPassword] = useState("")
  const [isSubmitting, setIsSubmitting] = useState(false)

  const fetchMapData = useCallback(async (pwd?: string) => {
    try {
      setIsLoading(true)
      setError(null)
      
      const url = pwd 
        ? `/api/map/shared/${token}?password=${encodeURIComponent(pwd)}`
        : `/api/map/shared/${token}`
      
      const response = await fetch(url)
      
      if (!response.ok) {
        const data = await response.json()
        
        if (response.status === 401 && data.isPasswordProtected) {
          setIsPasswordModalOpen(true)
          return
        }
        
        throw new Error(data.error || "Failed to load shared map")
      }
      
      const data = await response.json()
      setMapData(data)
    } catch (err) {
      console.error("Error loading shared map:", err)
      setError(err instanceof Error ? err.message : "Failed to load shared map")
      toast.error(err instanceof Error ? err.message : "Failed to load shared map")
    } finally {
      setIsLoading(false)
    }
  }, [token])
  
  const handlePasswordSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!password.trim()) {
      toast.error("Please enter a password")
      return
    }
    
    setIsSubmitting(true)
    
    try {
      await fetchMapData(password)
      setIsPasswordModalOpen(false)
    } finally {
      setIsSubmitting(false)
    }
  }
  
  const handleBackToApp = () => {
    router.push("/")
  }

  useEffect(() => {
    fetchMapData()
  }, [fetchMapData])

  if (isLoading) {
    return (
      <div className="flex flex-col items-center justify-center min-h-screen">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
        <p className="mt-4 text-muted-foreground">Loading shared map...</p>
      </div>
    )
  }

  if (error) {
    return (
      <div className="flex flex-col items-center justify-center min-h-screen">
        <div className="text-center max-w-md mx-auto p-6 space-y-4">
          <h2 className="text-2xl font-bold">Error Loading Map</h2>
          <p className="text-muted-foreground">{error}</p>
          <Button onClick={handleBackToApp}>
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to App
          </Button>
        </div>
      </div>
    )
  }

  return (
    <>
      <div className="flex flex-col h-screen">
        <header className="bg-background border-b p-4 flex items-center justify-between">
          <div className="flex items-center">
            <h1 className="text-xl font-bold">Scene-o-matic Shared Map</h1>
          </div>
          <Button variant="outline" onClick={handleBackToApp}>
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to App
          </Button>
        </header>
        
        <main className="flex-1 relative">
          {mapData ? (
            <SharedMapProvider
              initialLocations={mapData.locations as unknown as Location[]}
              initialViewSettings={mapData.viewSettings}
            >
              <MapView 
                height="100%" 
                width="100%" 
                interactive={true}
                showControls={true}
              />
            </SharedMapProvider>
          ) : (
            <div className="flex items-center justify-center h-full">
              <p className="text-muted-foreground">No map data available</p>
            </div>
          )}
        </main>
      </div>
      
      <Dialog open={isPasswordModalOpen} onOpenChange={setIsPasswordModalOpen}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle className="flex items-center">
              <Lock className="mr-2 h-4 w-4" />
              Password Protected Map
            </DialogTitle>
          </DialogHeader>
          
          <form onSubmit={handlePasswordSubmit} className="space-y-4 py-4">
            <p className="text-sm text-muted-foreground">
              This shared map is password protected. Please enter the password to view it.
            </p>
            
            <Input
              type="password"
              placeholder="Enter password"
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              disabled={isSubmitting}
              autoFocus
            />
            
            <DialogFooter>
              <Button type="button" variant="outline" onClick={handleBackToApp} disabled={isSubmitting}>
                Cancel
              </Button>
              <Button type="submit" disabled={isSubmitting || !password.trim()}>
                {isSubmitting ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Verifying...
                  </>
                ) : (
                  "Access Map"
                )}
              </Button>
            </DialogFooter>
          </form>
        </DialogContent>
      </Dialog>
    </>
  )
}
