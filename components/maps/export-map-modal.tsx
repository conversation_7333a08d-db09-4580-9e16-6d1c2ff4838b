import { useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>nt, <PERSON><PERSON><PERSON>eader, <PERSON><PERSON>Title, DialogDescription, DialogFooter } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import { Label } from "@/components/ui/label";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Loader2, Download, FileText } from "lucide-react";
import { useToast } from "@/components/ui/use-toast";
import type { Location } from "@/modules/location/model";

interface ExportMapModalProps {
  isOpen: boolean;
  onClose: () => void;
  locations: Location[];
  selectedLocations: Location[];
  onExport: (options: ExportOptions) => Promise<string>;
}

export interface ExportOptions {
  format: "pdf" | "csv";
  includeAllLocations: boolean;
  includeImages: boolean;
  includeContactInfo: boolean;
  includeNotes: boolean;
  includeDocuments: boolean;
  pageSize: "a4" | "letter" | "legal";
  orientation: "portrait" | "landscape";
}

export function ExportMapModal({
  isOpen,
  onClose,
  locations,
  selectedLocations,
  onExport,
}: ExportMapModalProps) {
  const { toast } = useToast();
  const [isExporting, setIsExporting] = useState(false);
  const [exportOptions, setExportOptions] = useState<ExportOptions>({
    format: "pdf",
    includeAllLocations: false,
    includeImages: true,
    includeContactInfo: false,
    includeNotes: false,
    includeDocuments: false,
    pageSize: "a4",
    orientation: "landscape",
  });

  const handleExport = async () => {
    setIsExporting(true);
    
    try {
      const downloadUrl = await onExport(exportOptions);
      
      // Create a temporary link element to trigger the download
      const link = document.createElement("a");
      link.href = downloadUrl;
      link.download = `scene-o-matic-map-export-${new Date().toISOString().split("T")[0]}.${exportOptions.format}`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      
      toast({
        title: "Export successful",
        description: `Your ${exportOptions.format.toUpperCase()} has been downloaded.`,
      });
      
      onClose();
    } catch (error) {
      console.error("Export failed:", error);
      toast({
        title: "Export failed",
        description: "There was an error exporting your map. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsExporting(false);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={(open) => !open && onClose()}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>Export Map</DialogTitle>
          <DialogDescription>
            Export your map and location data in various formats.
          </DialogDescription>
        </DialogHeader>
        
        <div className="grid gap-4 py-4">
          <div className="space-y-2">
            <h3 className="text-sm font-medium">Export Format</h3>
            <RadioGroup
              value={exportOptions.format}
              onValueChange={(value) => setExportOptions({ ...exportOptions, format: value as "pdf" | "csv" })}
              className="flex flex-row space-x-4"
            >
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="pdf" id="pdf" />
                <Label htmlFor="pdf" className="flex items-center">
                  <FileText className="h-4 w-4 mr-2" />
                  PDF
                </Label>
              </div>
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="csv" id="csv" />
                <Label htmlFor="csv">CSV</Label>
              </div>
            </RadioGroup>
          </div>
          
          <div className="space-y-2">
            <h3 className="text-sm font-medium">Locations to Include</h3>
            <div className="flex items-center space-x-2">
              <Checkbox
                id="includeAllLocations"
                checked={exportOptions.includeAllLocations}
                onCheckedChange={(checked) => 
                  setExportOptions({ 
                    ...exportOptions, 
                    includeAllLocations: checked === true 
                  })
                }
              />
              <Label htmlFor="includeAllLocations">
                Include all locations ({locations.length})
              </Label>
            </div>
            {!exportOptions.includeAllLocations && (
              <div className="text-sm text-muted-foreground pl-6">
                Only selected locations will be included ({selectedLocations.length})
              </div>
            )}
          </div>
          
          {exportOptions.format === "pdf" && (
            <>
              <div className="space-y-2">
                <h3 className="text-sm font-medium">Content Options</h3>
                <div className="space-y-2">
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="includeImages"
                      checked={exportOptions.includeImages}
                      onCheckedChange={(checked) => 
                        setExportOptions({ 
                          ...exportOptions, 
                          includeImages: checked === true 
                        })
                      }
                    />
                    <Label htmlFor="includeImages">Include images</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="includeContactInfo"
                      checked={exportOptions.includeContactInfo}
                      onCheckedChange={(checked) => 
                        setExportOptions({ 
                          ...exportOptions, 
                          includeContactInfo: checked === true 
                        })
                      }
                    />
                    <Label htmlFor="includeContactInfo">Include contact information</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="includeNotes"
                      checked={exportOptions.includeNotes}
                      onCheckedChange={(checked) => 
                        setExportOptions({ 
                          ...exportOptions, 
                          includeNotes: checked === true 
                        })
                      }
                    />
                    <Label htmlFor="includeNotes">Include notes</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="includeDocuments"
                      checked={exportOptions.includeDocuments}
                      onCheckedChange={(checked) => 
                        setExportOptions({ 
                          ...exportOptions, 
                          includeDocuments: checked === true 
                        })
                      }
                    />
                    <Label htmlFor="includeDocuments">Include document list</Label>
                  </div>
                </div>
              </div>
              
              <div className="space-y-2">
                <h3 className="text-sm font-medium">Page Options</h3>
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="pageSize" className="text-xs">Page Size</Label>
                    <RadioGroup
                      id="pageSize"
                      value={exportOptions.pageSize}
                      onValueChange={(value) => 
                        setExportOptions({ 
                          ...exportOptions, 
                          pageSize: value as "a4" | "letter" | "legal" 
                        })
                      }
                      className="flex flex-col space-y-1 mt-1"
                    >
                      <div className="flex items-center space-x-2">
                        <RadioGroupItem value="a4" id="a4" />
                        <Label htmlFor="a4" className="text-sm">A4</Label>
                      </div>
                      <div className="flex items-center space-x-2">
                        <RadioGroupItem value="letter" id="letter" />
                        <Label htmlFor="letter" className="text-sm">Letter</Label>
                      </div>
                      <div className="flex items-center space-x-2">
                        <RadioGroupItem value="legal" id="legal" />
                        <Label htmlFor="legal" className="text-sm">Legal</Label>
                      </div>
                    </RadioGroup>
                  </div>
                  
                  <div>
                    <Label htmlFor="orientation" className="text-xs">Orientation</Label>
                    <RadioGroup
                      id="orientation"
                      value={exportOptions.orientation}
                      onValueChange={(value) => 
                        setExportOptions({ 
                          ...exportOptions, 
                          orientation: value as "portrait" | "landscape" 
                        })
                      }
                      className="flex flex-col space-y-1 mt-1"
                    >
                      <div className="flex items-center space-x-2">
                        <RadioGroupItem value="portrait" id="portrait" />
                        <Label htmlFor="portrait" className="text-sm">Portrait</Label>
                      </div>
                      <div className="flex items-center space-x-2">
                        <RadioGroupItem value="landscape" id="landscape" />
                        <Label htmlFor="landscape" className="text-sm">Landscape</Label>
                      </div>
                    </RadioGroup>
                  </div>
                </div>
              </div>
            </>
          )}
        </div>
        
        <DialogFooter>
          <Button variant="outline" onClick={onClose} disabled={isExporting}>
            Cancel
          </Button>
          <Button onClick={handleExport} disabled={isExporting}>
            {isExporting ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Exporting...
              </>
            ) : (
              <>
                <Download className="mr-2 h-4 w-4" />
                Export
              </>
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
