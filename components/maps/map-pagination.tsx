"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { ChevronLeft, ChevronRight } from "lucide-react"
import { cn } from "@/lib/utils"

interface MapPaginationProps {
  currentPage: number
  totalPages: number
  onPageChange: (page: number) => void
  className?: string
}

export function MapPagination({
  currentPage,
  totalPages,
  onPageChange,
  className
}: MapPaginationProps) {
  if (totalPages <= 1) return null

  return (
    <div className={cn("flex justify-between items-center mt-4 px-1", className)}>
      <Button
        variant="ghost"
        size="sm"
        onClick={() => onPageChange(Math.max(currentPage - 1, 1))}
        disabled={currentPage === 1}
        className="h-8 px-2 transition-all hover:shadow-[0_0_10px_rgba(35,35,255,0.5)] hover:border-neon-blue"
      >
        <ChevronLeft className="h-4 w-4 mr-1" />
        Previous
      </Button>
      <span className="text-xs text-muted-foreground">
        Page {currentPage} of {totalPages}
      </span>
      <Button
        variant="ghost"
        size="sm"
        onClick={() => onPageChange(Math.min(currentPage + 1, totalPages))}
        disabled={currentPage === totalPages}
        className="h-8 px-2 transition-all hover:shadow-[0_0_10px_rgba(35,35,255,0.5)] hover:border-neon-blue"
      >
        Next
        <ChevronRight className="h-4 w-4 ml-1" />
      </Button>
    </div>
  )
}
