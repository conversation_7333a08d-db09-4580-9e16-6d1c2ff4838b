"use client"

import { useState } from "react"
import { Button } from "@/components/ui/button"
import { 
  ZoomIn, 
  ZoomOut, 
  Layers, 
  MapPin, 
  RotateCcw,
  Map as MapIcon,
  Compass
} from "lucide-react"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { Slider } from "@/components/ui/slider"
import { Label } from "@/components/ui/label"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { useMap } from "@/providers/map-provider"
import { cn } from "@/lib/utils"
import { defaultMapViewSettings } from "@/modules/map/model"

interface MapControlsProps {
  onToggleClustering: () => void
  isClustering: boolean
  onFitToLocations: () => void
  onOpenFilterModal?: () => void
  className?: string
}

export function MapControls({ 
  onToggleClustering, 
  isClustering, 
  onFitToLocations,
  className 
}: MapControlsProps) {
  const { 
    viewSettings, 
    setViewSettings, 
    clusterSettings, 
    setClusterSettings 
  } = useMap()
  
  const [isStylePopoverOpen, setIsStylePopoverOpen] = useState(false)
  const [isClusterPopoverOpen, setIsClusterPopoverOpen] = useState(false)
  const [isViewPopoverOpen, setIsViewPopoverOpen] = useState(false)
  
  // Map styles
  const mapStyles = [
    { id: "mapbox://styles/mapbox/dark-v11", name: "Dark", description: "Dark theme with neon accents" },
    { id: "mapbox://styles/mapbox/light-v11", name: "Light", description: "Light theme for better readability" },
    { id: "mapbox://styles/mapbox/streets-v12", name: "Streets", description: "Detailed street map" },
    { id: "mapbox://styles/mapbox/satellite-streets-v12", name: "Satellite", description: "Satellite imagery with streets" },
    { id: "mapbox://styles/mapbox/navigation-day-v1", name: "Navigation", description: "Optimized for navigation" },
  ]
  
  // Handle zoom in
  const handleZoomIn = () => {
    setViewSettings({
      ...viewSettings,
      zoom: Math.min(viewSettings.zoom + 1, 20)
    })
  }
  
  // Handle zoom out
  const handleZoomOut = () => {
    setViewSettings({
      ...viewSettings,
      zoom: Math.max(viewSettings.zoom - 1, 0)
    })
  }
  
  // Handle pitch change
  const handlePitchChange = (values: number[]) => {
    setViewSettings({
      ...viewSettings,
      pitch: values[0]
    })
  }
  
  // Handle bearing change
  const handleBearingChange = (values: number[]) => {
    setViewSettings({
      ...viewSettings,
      bearing: values[0]
    })
  }
  
  // Handle map style change
  const handleMapStyleChange = (styleId: string) => {
    // This will be handled by the MapView component
    // We'll pass the style ID to the parent component
    setViewSettings({
      ...viewSettings,
      styleId: styleId
    })
    setIsStylePopoverOpen(false)
  }
  
  // Handle cluster radius change
  const handleClusterRadiusChange = (values: number[]) => {
    setClusterSettings({
      ...clusterSettings,
      radius: values[0]
    })
  }
  
  // Handle cluster max zoom change
  const handleClusterMaxZoomChange = (values: number[]) => {
    setClusterSettings({
      ...clusterSettings,
      maxZoom: values[0]
    })
  }
  
  // Reset view to default
  const handleResetView = () => {
    setViewSettings(defaultMapViewSettings)
  }

  return (
    <div className={cn("flex flex-col gap-2", className)}>
      {/* Zoom Controls */}
      <div className="flex flex-col gap-1">
        <Button
          variant="secondary"
          size="icon"
          className="bg-white/30 backdrop-blur-md border border-white/40 shadow-md hover:bg-white/40 text-white"
          onClick={handleZoomIn}
          aria-label="Zoom in"
        >
          <ZoomIn className="h-4 w-4" />
        </Button>
        <Button
          variant="secondary"
          size="icon"
          className="bg-white/30 backdrop-blur-md border border-white/40 shadow-md hover:bg-white/40 text-white"
          onClick={handleZoomOut}
          aria-label="Zoom out"
        >
          <ZoomOut className="h-4 w-4" />
        </Button>
      </div>
      
      {/* Map Style Selector */}
      <Popover open={isStylePopoverOpen} onOpenChange={setIsStylePopoverOpen}>
        <PopoverTrigger asChild>
          <Button
            variant="secondary"
            size="icon"
            className="bg-white/30 backdrop-blur-md border border-white/40 shadow-md hover:bg-white/40 text-white mt-4"
            aria-label="Map style"
          >
            <MapIcon className="h-4 w-4" />
          </Button>
        </PopoverTrigger>
        <PopoverContent side="left" className="w-80">
          <div className="space-y-4">
            <h4 className="font-medium">Map Style</h4>
            <RadioGroup 
              defaultValue={viewSettings.styleId || "mapbox://styles/mapbox/dark-v11"}
              onValueChange={handleMapStyleChange}
            >
              {mapStyles.map((style) => (
                <div key={style.id} className="flex items-start space-x-2 p-2 rounded-md hover:bg-muted">
                  <RadioGroupItem value={style.id} id={style.id} />
                  <div className="grid gap-0.5">
                    <Label htmlFor={style.id} className="font-medium">{style.name}</Label>
                    <span className="text-xs text-muted-foreground">{style.description}</span>
                  </div>
                </div>
              ))}
            </RadioGroup>
          </div>
        </PopoverContent>
      </Popover>
      
      {/* Clustering Controls */}
      <Popover open={isClusterPopoverOpen} onOpenChange={setIsClusterPopoverOpen}>
        <PopoverTrigger asChild>
          <Button
            variant="secondary"
            size="icon"
            className={cn(
              "bg-white/30 backdrop-blur-md border shadow-md hover:bg-white/40 text-white mt-2",
              isClustering ? "border-neon-blue" : "border-white/40"
            )}
            aria-label="Clustering options"
          >
            <Layers className={cn("h-4 w-4", isClustering ? "text-neon-blue" : "text-white")} />
          </Button>
        </PopoverTrigger>
        <PopoverContent side="left" className="w-80">
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <h4 className="font-medium">Clustering</h4>
              <Button 
                variant={isClustering ? "neonBlue" : "outline"} 
                size="sm"
                onClick={onToggleClustering}
              >
                {isClustering ? "Enabled" : "Disabled"}
              </Button>
            </div>
            
            {isClustering && (
              <>
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <Label htmlFor="cluster-radius" className="text-sm">Cluster Radius</Label>
                    <span className="text-xs text-muted-foreground">{clusterSettings.radius}px</span>
                  </div>
                  <Slider 
                    id="cluster-radius" 
                    value={[clusterSettings.radius]} 
                    min={20} 
                    max={100} 
                    step={5} 
                    onValueChange={handleClusterRadiusChange}
                    className="[&_[role=slider]]:bg-neon-blue [&_[role=slider]]:border-neon-blue/50 [&_[role=slider]]:shadow-[0_0_5px_rgba(35,35,255,0.5)]" 
                  />
                </div>
                
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <Label htmlFor="cluster-max-zoom" className="text-sm">Max Zoom Level</Label>
                    <span className="text-xs text-muted-foreground">{clusterSettings.maxZoom}</span>
                  </div>
                  <Slider 
                    id="cluster-max-zoom" 
                    value={[clusterSettings.maxZoom]} 
                    min={10} 
                    max={18} 
                    step={1} 
                    onValueChange={handleClusterMaxZoomChange}
                    className="[&_[role=slider]]:bg-neon-blue [&_[role=slider]]:border-neon-blue/50 [&_[role=slider]]:shadow-[0_0_5px_rgba(35,35,255,0.5)]" 
                  />
                </div>
              </>
            )}
          </div>
        </PopoverContent>
      </Popover>
      
      {/* View Controls */}
      <Popover open={isViewPopoverOpen} onOpenChange={setIsViewPopoverOpen}>
        <PopoverTrigger asChild>
          <Button
            variant="secondary"
            size="icon"
            className="bg-white/30 backdrop-blur-md border border-white/40 shadow-md hover:bg-white/40 text-white mt-2"
            aria-label="View settings"
          >
            <Compass className="h-4 w-4" />
          </Button>
        </PopoverTrigger>
        <PopoverContent side="left" className="w-80">
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <h4 className="font-medium">View Settings</h4>
              <Button 
                variant="outline" 
                size="sm"
                onClick={handleResetView}
              >
                <RotateCcw className="h-3 w-3 mr-1" />
                Reset
              </Button>
            </div>
            
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <Label htmlFor="pitch" className="text-sm">Pitch (Tilt)</Label>
                <span className="text-xs text-muted-foreground">{viewSettings.pitch}°</span>
              </div>
              <Slider 
                id="pitch" 
                value={[viewSettings.pitch]} 
                min={0} 
                max={60} 
                step={5} 
                onValueChange={handlePitchChange}
                className="[&_[role=slider]]:bg-neon-blue [&_[role=slider]]:border-neon-blue/50 [&_[role=slider]]:shadow-[0_0_5px_rgba(35,35,255,0.5)]" 
              />
            </div>
            
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <Label htmlFor="bearing" className="text-sm">Bearing (Rotation)</Label>
                <span className="text-xs text-muted-foreground">{viewSettings.bearing}°</span>
              </div>
              <Slider 
                id="bearing" 
                value={[viewSettings.bearing]} 
                min={0} 
                max={360} 
                step={15} 
                onValueChange={handleBearingChange}
                className="[&_[role=slider]]:bg-neon-blue [&_[role=slider]]:border-neon-blue/50 [&_[role=slider]]:shadow-[0_0_5px_rgba(35,35,255,0.5)]" 
              />
            </div>
            
            <div className="pt-2">
              <Button 
                variant="outline" 
                size="sm" 
                className="w-full"
                onClick={onFitToLocations}
              >
                <MapPin className="h-3 w-3 mr-2" />
                Fit to All Locations
              </Button>
            </div>
          </div>
        </PopoverContent>
      </Popover>
      
      {/* Fit to Locations Button */}
      <Button
        variant="secondary"
        size="icon"
        className="bg-white/30 backdrop-blur-md border border-white/40 shadow-md hover:bg-white/40 text-white mt-2"
        onClick={onFitToLocations}
        aria-label="Fit to locations"
      >
        <MapPin className="h-4 w-4" />
      </Button>
    </div>
  )
}
