"use client";

import { Avatar } from "@/components/ui/avatar";
import { <PERSON>, Card<PERSON>ontent, Card<PERSON>eader, CardTitle } from "@/components/ui/card";

interface ActivityItem {
  id: string;
  user: {
    name: string;
    avatar?: string;
  };
  action: string;
  target: string;
  date: Date;
}

interface ActivityFeedProps {
  items: ActivityItem[];
}

export function ActivityFeed({ items }: ActivityFeedProps) {
  return (
    <Card>
      <CardHeader>
        <CardTitle>Recent Activity</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {items.map((item) => (
            <div key={item.id} className="flex items-start gap-4">
              <Avatar className="h-8 w-8">
                <span className="text-xs font-medium">
                  {item.user.name.substring(0, 2).toUpperCase()}
                </span>
              </Avatar>
              <div className="grid gap-1">
                <p className="text-sm font-medium leading-none">
                  <span className="font-semibold">{item.user.name}</span>{" "}
                  {item.action}{" "}
                  <span className="font-semibold">{item.target}</span>
                </p>
                <p className="text-xs text-muted-foreground">
                  {new Date(item.date).toLocaleDateString()} at{" "}
                  {new Date(item.date).toLocaleTimeString()}
                </p>
              </div>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
}
