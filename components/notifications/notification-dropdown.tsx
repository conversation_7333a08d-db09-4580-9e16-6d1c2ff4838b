"use client"

import { useEffect, useRef, useState } from "react"
import { useNotifications } from "@/providers/notification-provider"
import { formatDistanceToNow } from "date-fns"
import { Check } from "lucide-react"
import { 
  Card, 
  CardContent, 
  CardDescription, 
  Card<PERSON>ooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Separator } from "@/components/ui/separator"
import type { NotificationWithRelations } from "@/modules/notification/model"
import { NotificationModal } from "./notification-modal"

interface NotificationDropdownProps {
  onClose: () => void
}

export function NotificationDropdown({ onClose }: NotificationDropdownProps) {
  const [modalOpen, setModalOpen] = useState(false)
  const { 
    notifications, 
    unreadCount,
    loading, 
    markAsRead, 
    markAllAsRead 
  } = useNotifications()
  
  const dropdownRef = useRef<HTMLDivElement>(null)

  // Close dropdown when clicking outside
  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        onClose()
      }
    }

    document.addEventListener("mousedown", handleClickOutside)
    return () => {
      document.removeEventListener("mousedown", handleClickOutside)
    }
  }, [onClose])

  // Format notification timestamp
  const formatTimestamp = (date: Date) => {
    return formatDistanceToNow(new Date(date), { addSuffix: true })
  }

  // Get notification icon based on type
  const getNotificationIcon = (notification: NotificationWithRelations) => {
    // You can customize this based on notification types
    if (notification.status === "read") {
      return <Check className="h-4 w-4 text-green-500" />
    }
    return <div className="h-2 w-2 rounded-full bg-destructive" />
  }

  return (
    <div ref={dropdownRef}>
      <Card className="absolute right-0 mt-2 w-80 md:w-96 z-50 shadow-lg">
        <CardHeader className="pb-2">
          <div className="flex justify-between items-center">
            <CardTitle className="text-lg">Notifications</CardTitle>
            <CardDescription>
              {unreadCount} unread
            </CardDescription>
          </div>
        </CardHeader>
        
        <ScrollArea className="h-[300px]">
          <CardContent className="p-0">
            {loading ? (
              <div className="flex justify-center items-center h-20">
                <p className="text-sm text-muted-foreground">Loading notifications...</p>
              </div>
            ) : notifications.length === 0 ? (
              <div className="flex justify-center items-center h-20">
                <p className="text-sm text-muted-foreground">No notifications</p>
              </div>
            ) : (
              <ul className="space-y-1 p-1">
                {notifications.map((notification) => (
                  <li key={notification.id} className={notification.status === "unread" ? "bg-muted/50" : ""}>
                    <button
                      type="button"
                      className="w-full text-left p-3 rounded-md hover:bg-muted cursor-pointer"
                      onClick={() => markAsRead(notification.id)}
                      aria-label={`${notification.message} - ${notification.status === "unread" ? "Unread" : "Read"}`}
                    >
                      <div className="flex gap-2">
                        <div className="mt-0.5">
                          {getNotificationIcon(notification)}
                        </div>
                        <div className="flex-1 space-y-1">
                          <p className="text-sm font-medium leading-none">
                            {notification.message}
                          </p>
                          <p className="text-xs text-muted-foreground">
                            {formatTimestamp(notification.createdAt)}
                          </p>
                        </div>
                      </div>
                    </button>
                  </li>
                ))}
              </ul>
            )}
          </CardContent>
        </ScrollArea>
        
        <Separator />
        
        <CardFooter className="flex justify-between p-3">
          <Button 
            variant="ghost" 
            size="sm" 
            onClick={markAllAsRead}
            disabled={unreadCount === 0}
          >
            Mark all as read
          </Button>
          <Button 
            variant="outline" 
            size="sm" 
            onClick={() => setModalOpen(true)}
          >
            View all
          </Button>
          <NotificationModal 
            open={modalOpen} 
            onOpenChange={setModalOpen} 
          />
        </CardFooter>
      </Card>
    </div>
  )
}
