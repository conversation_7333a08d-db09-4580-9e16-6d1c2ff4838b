"use client"

import { useEffect } from "react"
import { useNotifications } from "@/providers/notification-provider"
import { formatDistanceToNow } from "date-fns"
import { Check, ArrowLeft } from "lucide-react"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Separator } from "@/components/ui/separator"
import Link from "next/link"
import type { NotificationWithRelations } from "@/modules/notification/model"

export default function NotificationsPageClient() {
  const { 
    notifications, 
    loading, 
    error, 
    fetchNotifications, 
    markAsRead, 
    markAllAsRead 
  } = useNotifications()

  useEffect(() => {
    fetchNotifications()
  }, [fetchNotifications])

  // Format notification timestamp
  const formatTimestamp = (date: Date) => {
    return formatDistanceToNow(new Date(date), { addSuffix: true })
  }

  // Get notification icon based on type
  const getNotificationIcon = (notification: NotificationWithRelations) => {
    // You can customize this based on notification types
    if (notification.status === "read") {
      return <Check className="h-4 w-4 text-green-500" />
    }
    return <div className="h-2 w-2 rounded-full bg-destructive" />
  }

  return (
    <div className="container max-w-4xl py-10">
      <div className="flex items-center mb-6">
        <Button variant="ghost" size="icon" asChild className="mr-2">
          <Link href="/">
            <ArrowLeft className="h-5 w-5" />
            <span className="sr-only">Back</span>
          </Link>
        </Button>
        <h1 className="text-3xl font-bold">Notifications</h1>
      </div>

      <Card>
        <CardHeader className="flex flex-row items-center justify-between">
          <div>
            <CardTitle>All Notifications</CardTitle>
            <CardDescription>
              View and manage your notifications
            </CardDescription>
          </div>
          <Button 
            variant="outline" 
            onClick={markAllAsRead}
            disabled={!notifications.some(n => n.status === "unread")}
          >
            Mark all as read
          </Button>
        </CardHeader>
        
        <Separator />
        
        <CardContent className="pt-6">
          {loading ? (
            <div className="flex justify-center items-center h-40">
              <p className="text-muted-foreground">Loading notifications...</p>
            </div>
          ) : error ? (
            <div className="flex justify-center items-center h-40">
              <p className="text-destructive">Error loading notifications: {error}</p>
            </div>
          ) : notifications.length === 0 ? (
            <div className="flex justify-center items-center h-40">
              <p className="text-muted-foreground">No notifications</p>
            </div>
          ) : (
            <ul className="space-y-4">
              {notifications.map((notification) => (
                <li 
                  key={notification.id}
                  className={`
                    border rounded-lg p-4
                    ${notification.status === "unread" ? "bg-muted/50" : ""}
                  `}
                >
                  <div className="flex items-start gap-3">
                    <div className="mt-1">
                      {getNotificationIcon(notification)}
                    </div>
                    <div className="flex-1">
                      <div className="flex justify-between items-start">
                        <p className="font-medium">
                          {notification.message}
                        </p>
                        {notification.status === "unread" && (
                          <Button 
                            variant="ghost" 
                            size="sm" 
                            onClick={() => markAsRead(notification.id)}
                          >
                            Mark as read
                          </Button>
                        )}
                      </div>
                      <p className="text-sm text-muted-foreground mt-1">
                        {formatTimestamp(notification.createdAt)}
                      </p>
                    </div>
                  </div>
                </li>
              ))}
            </ul>
          )}
        </CardContent>
        
        <CardFooter className="flex justify-center border-t p-4">
          <p className="text-sm text-muted-foreground">
            End of notifications
          </p>
        </CardFooter>
      </Card>
    </div>
  )
}
