"use client"

import { useEffect } from "react"
import { useNotifications } from "@/providers/notification-provider"
import { formatDistanceToNow } from "date-fns"
import { Check } from "lucide-react"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { Button } from "@/components/ui/button"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Separator } from "@/components/ui/separator"
import type { NotificationWithRelations } from "@/modules/notification/model"

interface NotificationModalProps {
  open: boolean
  onOpenChange: (open: boolean) => void
}

export function NotificationModal({ open, onOpenChange }: NotificationModalProps) {
  const { 
    notifications, 
    loading, 
    error, 
    fetchNotifications, 
    markAsRead, 
    markAllAsRead 
  } = useNotifications()

  useEffect(() => {
    if (open) {
      fetchNotifications()
    }
  }, [open, fetchNotifications])

  // Format notification timestamp
  const formatTimestamp = (date: Date) => {
    return formatDistanceToNow(new Date(date), { addSuffix: true })
  }

  // Get notification icon based on type
  const getNotificationIcon = (notification: NotificationWithRelations) => {
    // You can customize this based on notification types
    if (notification.status === "read") {
      return <Check className="h-4 w-4 text-green-500" />
    }
    return <div className="h-2 w-2 rounded-full bg-destructive" />
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[600px] max-h-[80vh] flex flex-col">
        <DialogHeader>
          <div className="flex items-center justify-between">
            <div>
              <DialogTitle className="text-xl">All Notifications</DialogTitle>
              <DialogDescription>
                View and manage your notifications
              </DialogDescription>
            </div>
            <Button 
              variant="outline" 
              onClick={markAllAsRead}
              disabled={!notifications.some(n => n.status === "unread")}
              className="ml-auto"
            >
              Mark all as read
            </Button>
          </div>
        </DialogHeader>
        
        <Separator className="my-2" />
        
        <ScrollArea className="flex-1 pr-4">
          {loading ? (
            <div className="flex justify-center items-center h-40">
              <p className="text-muted-foreground">Loading notifications...</p>
            </div>
          ) : error ? (
            <div className="flex justify-center items-center h-40">
              <p className="text-destructive">Error loading notifications: {error}</p>
            </div>
          ) : notifications.length === 0 ? (
            <div className="flex justify-center items-center h-40">
              <p className="text-muted-foreground">No notifications</p>
            </div>
          ) : (
            <ul className="space-y-4 py-2">
              {notifications.map((notification) => (
                <li 
                  key={notification.id}
                  className={`
                    border rounded-lg p-4
                    ${notification.status === "unread" ? "bg-muted/50" : ""}
                  `}
                >
                  <div className="flex items-start gap-3">
                    <div className="mt-1">
                      {getNotificationIcon(notification)}
                    </div>
                    <div className="flex-1">
                      <div className="flex justify-between items-start">
                        <p className="font-medium">
                          {notification.message}
                        </p>
                        {notification.status === "unread" && (
                          <Button 
                            variant="ghost" 
                            size="sm" 
                            onClick={() => markAsRead(notification.id)}
                          >
                            Mark as read
                          </Button>
                        )}
                      </div>
                      <p className="text-sm text-muted-foreground mt-1">
                        {formatTimestamp(notification.createdAt)}
                      </p>
                    </div>
                  </div>
                </li>
              ))}
            </ul>
          )}
        </ScrollArea>
        
        <Separator className="my-2" />
        
        <div className="flex justify-center pt-2">
          <p className="text-sm text-muted-foreground">
            End of notifications
          </p>
        </div>
      </DialogContent>
    </Dialog>
  )
}
