"use client"

import { useState } from "react"
import { Bell } from "lucide-react"
import { useNotifications } from "@/providers/notification-provider"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { NotificationDropdown } from "./notification-dropdown"

export function NotificationBell() {
  const { unreadCount, fetchNotifications } = useNotifications()
  const [isOpen, setIsOpen] = useState(false)

  const toggleDropdown = async () => {
    if (!isOpen) {
      // Fetch notifications when opening the dropdown
      await fetchNotifications()
    }
    setIsOpen(!isOpen)
  }

  return (
    <div className="relative">
      <Button
        variant="ghost"
        size="icon"
        className="relative"
        onClick={toggleDropdown}
        aria-label={`Notifications ${unreadCount > 0 ? `(${unreadCount} unread)` : ''}`}
      >
        <Bell className="h-5 w-5" />
        {unreadCount > 0 && (
          <Badge 
            variant="destructive" 
            className="absolute -top-1 -right-1 h-5 w-5 flex items-center justify-center p-0 text-xs"
          >
            {unreadCount > 99 ? '99+' : unreadCount}
          </Badge>
        )}
      </Button>

      {isOpen && (
        <NotificationDropdown onClose={() => setIsOpen(false)} />
      )}
    </div>
  )
}
