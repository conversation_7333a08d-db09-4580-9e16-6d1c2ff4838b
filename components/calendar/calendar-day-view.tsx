"use client"

import { useState } from "react"
import { cn } from "@/lib/utils"

export function CalendarDayView() {
  const [currentDate] = useState(new Date(2025, 5, 15)) // June 15, 2025

  // Generate time slots for the day (hourly from 6 AM to 10 PM)
  const timeSlots = Array.from({ length: 17 }, (_, i) => {
    const hour = i + 6 // Start from 6 AM
    const hourFormatted = hour % 12 === 0 ? 12 : hour % 12
    const amPm = hour < 12 ? "AM" : "PM"
    return `${hourFormatted}:00 ${amPm}`
  })

  // Sample events for the day
  const events = [
    {
      id: 1,
      title: "Crew Call Time",
      startTime: "7:00 AM",
      endTime: "7:30 AM",
      location: "Downtown Loft",
      project: "Urban Crime Drama",
      type: "shooting",
      color: "bg-red-500",
    },
    {
      id: 2,
      title: "Setup and Lighting",
      startTime: "7:30 AM",
      endTime: "9:00 AM",
      location: "Downtown Loft",
      project: "Urban Crime Drama",
      type: "shooting",
      color: "bg-red-500",
    },
    {
      id: 3,
      title: "Scene 12 Shooting",
      startTime: "9:00 AM",
      endTime: "12:00 PM",
      location: "Downtown Loft",
      project: "Urban Crime Drama",
      type: "shooting",
      color: "bg-red-500",
    },
    {
      id: 4,
      title: "Lunch Break",
      startTime: "12:00 PM",
      endTime: "1:00 PM",
      location: "Downtown Loft",
      project: "Urban Crime Drama",
      type: "break",
      color: "bg-green-500",
    },
    {
      id: 5,
      title: "Scene 15 Shooting",
      startTime: "1:00 PM",
      endTime: "4:00 PM",
      location: "Downtown Loft",
      project: "Urban Crime Drama",
      type: "shooting",
      color: "bg-red-500",
    },
    {
      id: 6,
      title: "Scene 23 Shooting",
      startTime: "4:30 PM",
      endTime: "7:00 PM",
      location: "Downtown Loft",
      project: "Urban Crime Drama",
      type: "shooting",
      color: "bg-red-500",
    },
    {
      id: 7,
      title: "Wrap Up",
      startTime: "7:00 PM",
      endTime: "8:00 PM",
      location: "Downtown Loft",
      project: "Urban Crime Drama",
      type: "shooting",
      color: "bg-red-500",
    },
  ]

  // Helper function to position events in the timeline
  const getEventPosition = (startTime: string) => {
    const [hourStr, minuteStr, period] = startTime.match(/(\d+):(\d+)\s*(AM|PM)/)?.slice(1) || []
    if (!hourStr || !minuteStr || !period) return 0

    let hour = Number.parseInt(hourStr)
    const minute = Number.parseInt(minuteStr)

    // Convert to 24-hour format
    if (period === "PM" && hour < 12) hour += 12
    if (period === "AM" && hour === 12) hour = 0

    // Calculate position (each hour is 60px, starting from 6 AM)
    return (hour - 6) * 60 + minute
  }

  // Helper function to calculate event duration in minutes
  const getEventDuration = (startTime: string, endTime: string) => {
    const startPosition = getEventPosition(startTime)
    const endPosition = getEventPosition(endTime)
    return endPosition - startPosition
  }

  const formattedDate = currentDate.toLocaleDateString("en-US", {
    weekday: "long",
    month: "long",
    day: "numeric",
    year: "numeric",
  })

  return (
    <div className="w-full p-4">
      <h3 className="text-lg font-medium mb-4">{formattedDate}</h3>

      <div className="flex">
        {/* Time column */}
        <div className="w-20 flex-shrink-0">
          {timeSlots.map((time, index) => (
            <div key={index} className="h-16 border-t flex items-start pt-1">
              <span className="text-xs text-muted-foreground">{time}</span>
            </div>
          ))}
        </div>

        {/* Events column */}
        <div className="flex-1 relative border-l">
          {/* Time grid lines */}
          {timeSlots.map((_, index) => (
            <div key={index} className="h-16 border-t"></div>
          ))}

          {/* Events */}
          {events.map((event) => (
            <div
              key={event.id}
              className={cn("absolute left-1 right-2 rounded p-2 text-white", event.color)}
              style={{
                top: `${getEventPosition(event.startTime)}px`,
                height: `${getEventDuration(event.startTime, event.endTime)}px`,
              }}
            >
              <div className="text-sm font-medium">{event.title}</div>
              <div className="text-xs">
                {event.startTime} - {event.endTime}
              </div>
              <div className="text-xs mt-1">{event.location}</div>
            </div>
          ))}
        </div>
      </div>
    </div>
  )
}

