"use client"

import { useState } from "react"
import { cn } from "@/lib/utils"

export function CalendarView() {
  const [currentDate] = useState(new Date(2025, 5, 15)) // June 15, 2025

  // Generate calendar days for the current month
  const generateCalendarDays = () => {
    const year = currentDate.getFullYear()
    const month = currentDate.getMonth()

    // First day of the month
    const firstDay = new Date(year, month, 1)
    // Last day of the month
    const lastDay = new Date(year, month + 1, 0)

    // Day of the week for the first day (0 = Sunday, 6 = Saturday)
    const firstDayOfWeek = firstDay.getDay()

    // Total days in the month
    const daysInMonth = lastDay.getDate()

    // Calendar array to hold all days
    const calendarDays = []

    // Add empty cells for days before the first day of the month
    for (let i = 0; i < firstDayOfWeek; i++) {
      calendarDays.push({ day: null, isCurrentMonth: false })
    }

    // Add days of the current month
    for (let day = 1; day <= daysInMonth; day++) {
      calendarDays.push({ day, isCurrentMonth: true })
    }

    // Add empty cells to complete the last week if needed
    const remainingCells = 7 - (calendarDays.length % 7)
    if (remainingCells < 7) {
      for (let i = 0; i < remainingCells; i++) {
        calendarDays.push({ day: null, isCurrentMonth: false })
      }
    }

    return calendarDays
  }

  const calendarDays = generateCalendarDays()

  // Sample events data
  const events = [
    {
      id: 1,
      day: 5,
      title: "Tech Scout - Downtown Loft",
      time: "9:00 AM - 12:00 PM",
      project: "Urban Crime Drama",
      type: "scouting",
      color: "bg-blue-500",
    },
    {
      id: 2,
      day: 10,
      title: "Permit Deadline - Riverside Park",
      time: "5:00 PM",
      project: "Mountain Documentary",
      type: "permit",
      color: "bg-amber-500",
    },
    {
      id: 3,
      day: 12,
      title: "Production Meeting",
      time: "10:00 AM - 11:30 AM",
      project: "Urban Crime Drama",
      type: "meeting",
      color: "bg-purple-500",
    },
    {
      id: 4,
      day: 15,
      title: "Shoot Day - Downtown Loft",
      time: "7:00 AM - 8:00 PM",
      project: "Urban Crime Drama",
      type: "shooting",
      color: "bg-red-500",
    },
    {
      id: 5,
      day: 16,
      title: "Shoot Day - Downtown Loft",
      time: "7:00 AM - 8:00 PM",
      project: "Urban Crime Drama",
      type: "shooting",
      color: "bg-red-500",
    },
    {
      id: 6,
      day: 20,
      title: "Location Scout - Mountain Cabin",
      time: "9:00 AM - 3:00 PM",
      project: "Mountain Documentary",
      type: "scouting",
      color: "bg-blue-500",
    },
    {
      id: 7,
      day: 22,
      title: "Tech Scout - City Rooftop",
      time: "2:00 PM - 5:00 PM",
      project: "Downtown Commercial",
      type: "scouting",
      color: "bg-blue-500",
    },
    {
      id: 8,
      day: 25,
      title: "Shoot Day - City Rooftop",
      time: "6:00 AM - 7:00 PM",
      project: "Downtown Commercial",
      type: "shooting",
      color: "bg-red-500",
    },
    {
      id: 9,
      day: 28,
      title: "Wrap Meeting",
      time: "10:00 AM - 11:00 AM",
      project: "Downtown Commercial",
      type: "meeting",
      color: "bg-purple-500",
    },
  ]

  // Get events for a specific day
  const getEventsForDay = (day: number | null) => {
    if (!day) return []
    return events.filter((event) => event.day === day)
  }

  // Days of the week
  const daysOfWeek = ["Sun", "Mon", "Tue", "Wed", "Thu", "Fri", "Sat"]

  return (
    <div className="w-full">
      {/* Calendar header - days of the week */}
      <div className="grid grid-cols-7 border-b">
        {daysOfWeek.map((day, index) => (
          <div key={index} className="py-2 text-center text-sm font-medium text-muted-foreground">
            {day}
          </div>
        ))}
      </div>

      {/* Calendar grid */}
      <div className="grid grid-cols-7 border-b">
        {calendarDays.map((day, index) => {
          const dayEvents = getEventsForDay(day.day)
          const isToday = day.day === 15 // Assuming today is the 15th

          return (
            <div
              key={index}
              className={cn(
                "min-h-[120px] border-r border-b p-1 relative",
                !day.isCurrentMonth && "bg-muted/50",
                isToday && "bg-primary/5",
              )}
            >
              {day.day && (
                <>
                  <div
                    className={cn(
                      "flex h-6 w-6 items-center justify-center rounded-full text-sm",
                      isToday && "bg-primary text-primary-foreground font-medium",
                    )}
                  >
                    {day.day}
                  </div>
                  <div className="mt-1 space-y-1 max-h-[90px] overflow-y-auto">
                    {dayEvents.map((event) => (
                      <div
                        key={event.id}
                        className={cn("text-xs p-1 rounded truncate", event.color, "text-white")}
                        title={`${event.title} - ${event.time}`}
                      >
                        {event.title}
                      </div>
                    ))}
                  </div>
                </>
              )}
            </div>
          )
        })}
      </div>
    </div>
  )
}
