"use client"

import { useState } from "react"
import { cn } from "@/lib/utils"
import { ScrollArea } from "@/components/ui/scroll-area"

export function CalendarWeekView() {
  const [currentDate] = useState(new Date(2025, 5, 15)) // June 15, 2025

  // Generate days for the week (assuming Sunday is the first day)
  const generateWeekDays = () => {
    const date = new Date(currentDate)
    const day = currentDate.getDay() // 0 = Sunday, 6 = Saturday

    // Set date to the beginning of the week (Sunday)
    date.setDate(date.getDate() - day)

    const weekDays = []

    // Generate 7 days starting from Sunday
    for (let i = 0; i < 7; i++) {
      const currentDay = new Date(date)
      weekDays.push({
        date: currentDay,
        dayOfMonth: currentDay.getDate(),
        dayOfWeek: currentDay.toLocaleDateString("en-US", { weekday: "short" }),
        isToday:
          currentDay.getDate() === currentDate.getDate() &&
          currentDay.getMonth() === currentDate.getMonth() &&
          currentDay.getFullYear() === currentDate.getFullYear(),
      })
      date.setDate(date.getDate() + 1)
    }

    return weekDays
  }

  const weekDays = generateWeekDays()

  // Generate time slots for the day (hourly from 6 AM to 10 PM)
  const timeSlots = Array.from({ length: 17 }, (_, i) => {
    const hour = i + 6 // Start from 6 AM
    const hourFormatted = hour % 12 === 0 ? 12 : hour % 12
    const amPm = hour < 12 ? "AM" : "PM"
    return `${hourFormatted}:00 ${amPm}`
  })

  // Sample events for the week
  const events = [
    {
      id: 1,
      day: 0, // Sunday
      title: "Location Prep",
      startTime: "2:00 PM",
      endTime: "5:00 PM",
      location: "Downtown Loft",
      project: "Urban Crime Drama",
      type: "prep",
      color: "bg-blue-500",
    },
    {
      id: 2,
      day: 1, // Monday
      title: "Tech Scout",
      startTime: "10:00 AM",
      endTime: "12:00 PM",
      location: "City Rooftop",
      project: "Downtown Commercial",
      type: "scouting",
      color: "bg-blue-500",
    },
    {
      id: 3,
      day: 2, // Tuesday
      title: "Production Meeting",
      startTime: "9:00 AM",
      endTime: "10:30 AM",
      location: "Office",
      project: "Urban Crime Drama",
      type: "meeting",
      color: "bg-purple-500",
    },
    {
      id: 4,
      day: 3, // Wednesday
      title: "Permit Submission",
      startTime: "11:00 AM",
      endTime: "12:00 PM",
      location: "City Hall",
      project: "Mountain Documentary",
      type: "permit",
      color: "bg-amber-500",
    },
    {
      id: 5,
      day: 4, // Thursday
      title: "Location Scout",
      startTime: "1:00 PM",
      endTime: "4:00 PM",
      location: "Riverside Park",
      project: "Mountain Documentary",
      type: "scouting",
      color: "bg-blue-500",
    },
    {
      id: 6,
      day: 5, // Friday
      title: "Shoot Day",
      startTime: "7:00 AM",
      endTime: "8:00 PM",
      location: "Downtown Loft",
      project: "Urban Crime Drama",
      type: "shooting",
      color: "bg-red-500",
    },
    {
      id: 7,
      day: 6, // Saturday
      title: "Shoot Day",
      startTime: "7:00 AM",
      endTime: "8:00 PM",
      location: "Downtown Loft",
      project: "Urban Crime Drama",
      type: "shooting",
      color: "bg-red-500",
    },
  ]

  // Helper function to position events in the timeline
  const getEventPosition = (startTime: string) => {
    const [hourStr, minuteStr, period] = startTime.match(/(\d+):(\d+)\s*(AM|PM)/)?.slice(1) || []
    if (!hourStr || !minuteStr || !period) return 0

    let hour = Number.parseInt(hourStr)
    const minute = Number.parseInt(minuteStr)

    // Convert to 24-hour format
    if (period === "PM" && hour < 12) hour += 12
    if (period === "AM" && hour === 12) hour = 0

    // Calculate position (each hour is 60px, starting from 6 AM)
    return (hour - 6) * 60 + minute
  }

  // Helper function to calculate event duration in minutes
  const getEventDuration = (startTime: string, endTime: string) => {
    const startPosition = getEventPosition(startTime)
    const endPosition = getEventPosition(endTime)
    return endPosition - startPosition
  }

  return (
    <div className="w-full p-4">
      <div className="flex mb-2">
        {/* Empty cell for time column */}
        <div className="w-20 flex-shrink-0"></div>

        {/* Day headers */}
        {weekDays.map((day, index) => (
          <div key={index} className="flex-1 text-center">
            <div className="text-sm font-medium">{day.dayOfWeek}</div>
            <div
              className={cn(
                "mx-auto flex h-6 w-6 items-center justify-center rounded-full text-sm",
                day.isToday && "bg-primary text-primary-foreground font-medium",
              )}
            >
              {day.dayOfMonth}
            </div>
          </div>
        ))}
      </div>

      <ScrollArea className="h-[600px]">
        <div className="flex">
          {/* Time column */}
          <div className="w-20 flex-shrink-0">
            {timeSlots.map((time, index) => (
              <div key={index} className="h-16 border-t flex items-start pt-1">
                <span className="text-xs text-muted-foreground">{time}</span>
              </div>
            ))}
          </div>

          {/* Days columns */}
          {weekDays.map((day, dayIndex) => (
            <div key={dayIndex} className="flex-1 relative border-l">
              {/* Time grid lines */}
              {timeSlots.map((_, index) => (
                <div key={index} className="h-16 border-t"></div>
              ))}

              {/* Events for this day */}
              {events
                .filter((event) => event.day === dayIndex)
                .map((event) => (
                  <div
                    key={event.id}
                    className={cn("absolute left-1 right-1 rounded p-2 text-white", event.color)}
                    style={{
                      top: `${getEventPosition(event.startTime)}px`,
                      height: `${getEventDuration(event.startTime, event.endTime)}px`,
                    }}
                  >
                    <div className="text-sm font-medium truncate">{event.title}</div>
                    <div className="text-xs truncate">
                      {event.startTime} - {event.endTime}
                    </div>
                    <div className="text-xs truncate mt-1">{event.location}</div>
                  </div>
                ))}
            </div>
          ))}
        </div>
      </ScrollArea>
    </div>
  )
}

