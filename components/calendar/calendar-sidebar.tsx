import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Checkbox } from "@/components/ui/checkbox"
import { Label } from "@/components/ui/label"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Calendar, MapPin, Clock, Film, Users } from "lucide-react"

export function CalendarSidebar() {
  const upcomingEvents = [
    {
      id: 1,
      title: "Tech Scout - Downtown Loft",
      date: "Jun 5, 2025",
      time: "9:00 AM - 12:00 PM",
      project: "Urban Crime Drama",
      type: "scouting",
    },
    {
      id: 2,
      title: "Permit Deadline - Riverside Park",
      date: "Jun 10, 2025",
      time: "5:00 PM",
      project: "Mountain Documentary",
      type: "permit",
    },
    {
      id: 3,
      title: "Production Meeting",
      date: "Jun 12, 2025",
      time: "10:00 AM - 11:30 AM",
      project: "Urban Crime Drama",
      type: "meeting",
    },
    {
      id: 4,
      title: "Shoot Day - Downtown Loft",
      date: "Jun 15, 2025",
      time: "7:00 AM - 8:00 PM",
      project: "Urban Crime Drama",
      type: "shooting",
    },
    {
      id: 5,
      title: "Shoot Day - Downtown Loft",
      date: "Jun 16, 2025",
      time: "7:00 AM - 8:00 PM",
      project: "Urban Crime Drama",
      type: "shooting",
    },
  ]

  const getEventIcon = (type: string) => {
    switch (type) {
      case "scouting":
        return <MapPin className="h-4 w-4 text-blue-500" />
      case "permit":
        return <Calendar className="h-4 w-4 text-amber-500" />
      case "meeting":
        return <Users className="h-4 w-4 text-purple-500" />
      case "shooting":
        return <Film className="h-4 w-4 text-red-500" />
      default:
        return <Clock className="h-4 w-4 text-muted-foreground" />
    }
  }

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader className="pb-3">
          <CardTitle>Upcoming Events</CardTitle>
        </CardHeader>
        <CardContent>
          <ScrollArea className="h-[300px] pr-4">
            <div className="space-y-4">
              {upcomingEvents.map((event) => (
                <div key={event.id} className="border rounded-md p-3">
                  <div className="flex items-center gap-2 mb-1">
                    {getEventIcon(event.type)}
                    <p className="font-medium text-sm">{event.title}</p>
                  </div>
                  <div className="flex items-center gap-2 text-xs text-muted-foreground mb-1">
                    <Calendar className="h-3.5 w-3.5" />
                    <span>{event.date}</span>
                  </div>
                  <div className="flex items-center gap-2 text-xs text-muted-foreground">
                    <Clock className="h-3.5 w-3.5" />
                    <span>{event.time}</span>
                  </div>
                  <p className="text-xs text-muted-foreground mt-2">{event.project}</p>
                </div>
              ))}
            </div>
          </ScrollArea>
        </CardContent>
      </Card>

      <Card>
        <CardHeader className="pb-3">
          <CardTitle>Filters</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="space-y-2">
              <h4 className="text-sm font-medium">Event Types</h4>
              <div className="space-y-2">
                <div className="flex items-center space-x-2">
                  <Checkbox id="shooting" defaultChecked />
                  <Label htmlFor="shooting" className="text-sm flex items-center gap-1">
                    <Badge className="bg-red-500">Shooting</Badge>
                  </Label>
                </div>
                <div className="flex items-center space-x-2">
                  <Checkbox id="scouting" defaultChecked />
                  <Label htmlFor="scouting" className="text-sm flex items-center gap-1">
                    <Badge className="bg-blue-500">Scouting</Badge>
                  </Label>
                </div>
                <div className="flex items-center space-x-2">
                  <Checkbox id="meetings" defaultChecked />
                  <Label htmlFor="meetings" className="text-sm flex items-center gap-1">
                    <Badge className="bg-purple-500">Meetings</Badge>
                  </Label>
                </div>
                <div className="flex items-center space-x-2">
                  <Checkbox id="permits" defaultChecked />
                  <Label htmlFor="permits" className="text-sm flex items-center gap-1">
                    <Badge className="bg-amber-500">Permits</Badge>
                  </Label>
                </div>
              </div>
            </div>

            <div className="space-y-2">
              <h4 className="text-sm font-medium">Projects</h4>
              <div className="space-y-2">
                <div className="flex items-center space-x-2">
                  <Checkbox id="urban-crime" defaultChecked />
                  <Label htmlFor="urban-crime" className="text-sm">
                    Urban Crime Drama
                  </Label>
                </div>
                <div className="flex items-center space-x-2">
                  <Checkbox id="mountain-doc" defaultChecked />
                  <Label htmlFor="mountain-doc" className="text-sm">
                    Mountain Documentary
                  </Label>
                </div>
                <div className="flex items-center space-x-2">
                  <Checkbox id="downtown-commercial" defaultChecked />
                  <Label htmlFor="downtown-commercial" className="text-sm">
                    Downtown Commercial
                  </Label>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}

