import { Card, CardContent } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Folder, ChevronRight, FileIcon as FilePdf, FileImage, FileSpreadsheet, Plus } from "lucide-react"
import Link from "next/link"

export function DocumentFolders() {
  const folders = [
    {
      id: 1,
      name: "Contracts & Agreements",
      count: 12,
      icon: <Folder className="h-10 w-10 text-blue-500" />,
    },
    {
      id: 2,
      name: "Permits & Applications",
      count: 8,
      icon: <Folder className="h-10 w-10 text-green-500" />,
    },
    {
      id: 3,
      name: "Location Photos",
      count: 45,
      icon: <Folder className="h-10 w-10 text-amber-500" />,
    },
    {
      id: 4,
      name: "Scripts & Storyboards",
      count: 6,
      icon: <Folder className="h-10 w-10 text-red-500" />,
    },
    {
      id: 5,
      name: "Budgets & Schedules",
      count: 10,
      icon: <Folder className="h-10 w-10 text-purple-500" />,
    },
    {
      id: 6,
      name: "Insurance Documents",
      count: 5,
      icon: <Folder className="h-10 w-10 text-cyan-500" />,
    },
    {
      id: 7,
      name: "Technical Specifications",
      count: 14,
      icon: <Folder className="h-10 w-10 text-indigo-500" />,
    },
    {
      id: 8,
      name: "Archived Documents",
      count: 32,
      icon: <Folder className="h-10 w-10 text-gray-500" />,
    },
  ]

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div className="flex items-center gap-2 text-sm text-muted-foreground">
          <Link href="#" className="hover:underline">
            Home
          </Link>
          <ChevronRight className="h-4 w-4" />
          <Link href="#" className="hover:underline">
            Documents
          </Link>
        </div>
        <Button size="sm">
          <Plus className="h-4 w-4 mr-2" />
          New Folder
        </Button>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        {folders.map((folder) => (
          <Card key={folder.id} className="overflow-hidden transition-all hover:shadow-md">
            <Link href="#">
              <CardContent className="p-4">
                <div className="flex flex-col items-center text-center py-4">
                  {folder.icon}
                  <h3 className="font-medium mt-3">{folder.name}</h3>
                  <p className="text-sm text-muted-foreground mt-1">{folder.count} items</p>
                </div>
              </CardContent>
            </Link>
          </Card>
        ))}
      </div>

      <div className="mt-8">
        <h3 className="text-lg font-medium mb-4">Recent Documents</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          <Card className="overflow-hidden">
            <CardContent className="p-4">
              <div className="flex items-start gap-3">
                <FilePdf className="h-10 w-10 text-red-500 flex-shrink-0" />
                <div className="flex-1">
                  <h4 className="font-medium line-clamp-1">Location Agreement - Downtown Loft.pdf</h4>
                  <p className="text-xs text-muted-foreground mt-1">2.4 MB • Updated 2 hours ago</p>
                  <p className="text-xs text-muted-foreground mt-1">Contracts & Agreements</p>
                </div>
              </div>
            </CardContent>
          </Card>
          <Card className="overflow-hidden">
            <CardContent className="p-4">
              <div className="flex items-start gap-3">
                <FileSpreadsheet className="h-10 w-10 text-green-500 flex-shrink-0" />
                <div className="flex-1">
                  <h4 className="font-medium line-clamp-1">Production Schedule.xlsx</h4>
                  <p className="text-xs text-muted-foreground mt-1">1.2 MB • Updated yesterday</p>
                  <p className="text-xs text-muted-foreground mt-1">Budgets & Schedules</p>
                </div>
              </div>
            </CardContent>
          </Card>
          <Card className="overflow-hidden">
            <CardContent className="p-4">
              <div className="flex items-start gap-3">
                <FileImage className="h-10 w-10 text-blue-500 flex-shrink-0" />
                <div className="flex-1">
                  <h4 className="font-medium line-clamp-1">Historic Theater Photos.zip</h4>
                  <p className="text-xs text-muted-foreground mt-1">15.2 MB • Updated 3 days ago</p>
                  <p className="text-xs text-muted-foreground mt-1">Location Photos</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}

