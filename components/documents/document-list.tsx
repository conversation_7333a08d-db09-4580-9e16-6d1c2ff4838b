import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Download, Eye, MoreHorizontal, FileIcon as FilePdf, FileImage, FileSpreadsheet, FileText } from "lucide-react"

export function DocumentList() {
  const documents = [
    {
      id: 1,
      name: "Location Agreement - Downtown Loft.pdf",
      type: "pdf",
      size: "2.4 MB",
      project: "Urban Crime Drama",
      location: "Downtown Loft",
      uploadedBy: "Michael Chen",
      uploadedDate: "Jun 2, 2025",
      status: "Signed",
    },
    {
      id: 2,
      name: "Riverside Park Permit Application.pdf",
      type: "pdf",
      size: "1.8 MB",
      project: "Mountain Documentary",
      location: "Riverside Park",
      uploadedBy: "Sarah Johnson",
      uploadedDate: "Jun 1, 2025",
      status: "Pending",
    },
    {
      id: 3,
      name: "Historic Theater Photos.zip",
      type: "image",
      size: "15.2 MB",
      project: "Urban Crime Drama",
      location: "Historic Theater",
      uploadedBy: "<PERSON>",
      uploadedDate: "May 28, 2025",
      status: "Approved",
    },
    {
      id: 4,
      name: "Production Schedule.xlsx",
      type: "spreadsheet",
      size: "1.2 MB",
      project: "Urban Crime Drama",
      location: "All Locations",
      uploadedBy: "Jane Smith",
      uploadedDate: "May 25, 2025",
      status: "Approved",
    },
    {
      id: 5,
      name: "Location Budget.xlsx",
      type: "spreadsheet",
      size: "0.9 MB",
      project: "Urban Crime Drama",
      location: "All Locations",
      uploadedBy: "Michael Chen",
      uploadedDate: "May 22, 2025",
      status: "Approved",
    },
    {
      id: 6,
      name: "Script - Draft 3.pdf",
      type: "pdf",
      size: "3.5 MB",
      project: "Urban Crime Drama",
      location: "All Locations",
      uploadedBy: "Jane Smith",
      uploadedDate: "May 20, 2025",
      status: "Approved",
    },
  ]

  const getFileIcon = (type: string) => {
    switch (type) {
      case "pdf":
        return <FilePdf className="h-5 w-5 text-red-500" />
      case "image":
        return <FileImage className="h-5 w-5 text-blue-500" />
      case "spreadsheet":
        return <FileSpreadsheet className="h-5 w-5 text-green-500" />
      default:
        return <FileText className="h-5 w-5 text-gray-500" />
    }
  }

  return (
    <div className="rounded-md border">
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>Name</TableHead>
            <TableHead>Project</TableHead>
            <TableHead>Location</TableHead>
            <TableHead>Status</TableHead>
            <TableHead>Uploaded By</TableHead>
            <TableHead>Date</TableHead>
            <TableHead>Size</TableHead>
            <TableHead className="text-right">Actions</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {documents.map((document) => (
            <TableRow key={document.id}>
              <TableCell>
                <div className="flex items-center gap-2">
                  {getFileIcon(document.type)}
                  <span className="font-medium">{document.name}</span>
                </div>
              </TableCell>
              <TableCell>{document.project}</TableCell>
              <TableCell>{document.location}</TableCell>
              <TableCell>
                <Badge
                  variant={
                    document.status === "Approved" ? "default" : document.status === "Signed" ? "secondary" : "outline"
                  }
                >
                  {document.status}
                </Badge>
              </TableCell>
              <TableCell>{document.uploadedBy}</TableCell>
              <TableCell>{document.uploadedDate}</TableCell>
              <TableCell>{document.size}</TableCell>
              <TableCell className="text-right">
                <div className="flex justify-end gap-2">
                  <Button variant="ghost" size="icon">
                    <Eye className="h-4 w-4" />
                    <span className="sr-only">View</span>
                  </Button>
                  <Button variant="ghost" size="icon">
                    <Download className="h-4 w-4" />
                    <span className="sr-only">Download</span>
                  </Button>
                  <Button variant="ghost" size="icon">
                    <MoreHorizontal className="h-4 w-4" />
                    <span className="sr-only">More</span>
                  </Button>
                </div>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </div>
  )
}

