"use client"; // <PERSON> as client component

import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Download, Eye, MoreHorizontal, FileIcon as FilePdf, FileImage, FileSpreadsheet, FileText } from "lucide-react"
import { usePermissions, hasRequiredRole } from "@/hooks/usePermissions";

export function DocumentGrid() {
  const userRoles = usePermissions();
  // Show 'More' button if user can perform actions beyond view/download
  const canPerformMoreActions = hasRequiredRole(userRoles, ['admin', 'manager', 'scout']);

  // Mock data - replace with actual data fetching
  const documents = [
    {
      id: 1,
      name: "Location Agreement - Downtown Loft.pdf",
      type: "pdf",
      size: "2.4 MB",
      project: "Urban Crime Drama",
      location: "Downtown Loft",
      uploadedBy: "<PERSON>",
      uploadedDate: "Jun 2, 2025",
      status: "Signed",
    },
    {
      id: 2,
      name: "Riverside Park Permit Application.pdf",
      type: "pdf",
      size: "1.8 MB",
      project: "Mountain Documentary",
      location: "Riverside Park",
      uploadedBy: "Sarah Johnson",
      uploadedDate: "Jun 1, 2025",
      status: "Pending",
    },
    {
      id: 3,
      name: "Historic Theater Photos.zip",
      type: "image",
      size: "15.2 MB",
      project: "Urban Crime Drama",
      location: "Historic Theater",
      uploadedBy: "David Rodriguez",
      uploadedDate: "May 28, 2025",
      status: "Approved",
    },
    {
      id: 4,
      name: "Production Schedule.xlsx",
      type: "spreadsheet",
      size: "1.2 MB",
      project: "Urban Crime Drama",
      location: "All Locations",
      uploadedBy: "Jane Smith",
      uploadedDate: "May 25, 2025",
      status: "Approved",
    },
    {
      id: 5,
      name: "Location Budget.xlsx",
      type: "spreadsheet",
      size: "0.9 MB",
      project: "Urban Crime Drama",
      location: "All Locations",
      uploadedBy: "Michael Chen",
      uploadedDate: "May 22, 2025",
      status: "Approved",
    },
    {
      id: 6,
      name: "Script - Draft 3.pdf",
      type: "pdf",
      size: "3.5 MB",
      project: "Urban Crime Drama",
      location: "All Locations",
      uploadedBy: "Jane Smith",
      uploadedDate: "May 20, 2025",
      status: "Approved",
    },
    {
      id: 7,
      name: "City Rooftop Technical Specs.pdf",
      type: "pdf",
      size: "4.2 MB",
      project: "Downtown Commercial",
      location: "City Rooftop",
      uploadedBy: "Alex Rodriguez",
      uploadedDate: "May 18, 2025",
      status: "Approved",
    },
    {
      id: 8,
      name: "Insurance Documentation.pdf",
      type: "pdf",
      size: "1.5 MB",
      project: "All Projects",
      location: "All Locations",
      uploadedBy: "Emily Wilson",
      uploadedDate: "May 15, 2025",
      status: "Signed",
    },
  ]

  const getFileIcon = (type: string) => {
    switch (type) {
      case "pdf":
        return <FilePdf className="h-12 w-12 text-red-500" />
      case "image":
        return <FileImage className="h-12 w-12 text-blue-500" />
      case "spreadsheet":
        return <FileSpreadsheet className="h-12 w-12 text-green-500" />
      default:
        return <FileText className="h-12 w-12 text-gray-500" />
    }
  }

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
      {documents.map((document) => (
        <Card key={document.id} className="overflow-hidden">
          <CardContent className="p-4">
            <div className="flex flex-col items-center text-center mb-4 pt-4">
              {getFileIcon(document.type)}
              <h3 className="font-medium mt-2 line-clamp-1">{document.name}</h3>
              <p className="text-xs text-muted-foreground mt-1">{document.size}</p>
            </div>

            <div className="flex justify-between items-center mb-3">
              <Badge
                variant={
                  document.status === "Approved" ? "default" : document.status === "Signed" ? "secondary" : "outline"
                }
              >
                {document.status}
              </Badge>
              <p className="text-xs text-muted-foreground">{document.uploadedDate}</p>
            </div>

            <div className="text-xs text-muted-foreground mb-4">
              <p>Project: {document.project}</p>
              <p>Location: {document.location}</p>
            </div>

            <div className="flex gap-2">
              <Button variant="outline" size="sm" className="flex-1">
                <Eye className="h-3.5 w-3.5 mr-1" />
                View
              </Button>
              <Button variant="outline" size="sm" className="flex-1">
                <Download className="h-3.5 w-3.5 mr-1" />
                Download
              </Button>
              {/* Conditionally render the More button */}
              {canPerformMoreActions && (
                <Button variant="ghost" size="sm" className="w-8 px-0">
                  {/* TODO: Implement dropdown menu with Edit, Delete, Approve actions based on specific permissions */}
                  <MoreHorizontal className="h-4 w-4" />
                </Button>
              )}
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  )
}
