"use client";

import { useState, useEffect } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
  DialogClose, // Import DialogClose
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

// TODO: Replace with actual member type from modules/user/model.ts or similar
// Ensure the 'role' property matches the expected Stytch role IDs ('admin', 'manager', etc.)
interface TeamMember {
  id: string;
  name: string;
  email: string;
  role: string;
}

interface EditMemberModalProps {
  member: TeamMember;
  triggerButton?: React.ReactNode; // Allow custom trigger component
  onSave: (memberId: string, newRole: string) => Promise<void>; // Async function to call on save
}

// Define available roles based on Stytch configuration reference
// These should match the 'Role API ID' in your Stytch dashboard
const availableRoles = ['admin', 'manager', 'scout', 'viewer'];

export function EditMemberModal({ member, triggerButton, onSave }: EditMemberModalProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [selectedRole, setSelectedRole] = useState<string>('');
  const [isSaving, setIsSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Reset role when member changes or modal opens/closes
  useEffect(() => {
    if (isOpen) {
      setSelectedRole(member.role); // Initialize with current member role
      setError(null); // Clear previous errors
    }
  }, [isOpen, member.role]);

  const handleSave = async () => {
    if (!selectedRole || selectedRole === member.role) return; // Prevent saving if role hasn't changed

    setIsSaving(true);
    setError(null);
    try {
      // Call the provided onSave function (which should handle the API call)
      await onSave(member.id, selectedRole);
      setIsOpen(false); // Close modal on successful save
    } catch (err) {
      console.error("Failed to save member role:", err);
      // Display a user-friendly error message
      setError(err instanceof Error ? `Error: ${err.message}` : "An unknown error occurred. Please try again.");
    } finally {
      setIsSaving(false);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        {/* Use the provided trigger or a default button */}
        {triggerButton ? triggerButton : <Button variant="outline">Edit Member</Button>}
      </DialogTrigger>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Edit Team Member</DialogTitle>
          <DialogDescription>
            Modify the role for {member.name}. Other details are managed elsewhere.
          </DialogDescription>
        </DialogHeader>
        <div className="grid gap-4 py-4">
          {/* Display Name (Read-only) */}
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="name" className="text-right">
              Name
            </Label>
            <Input id="name" value={member.name} disabled className="col-span-3" />
          </div>
          {/* Display Email (Read-only) */}
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="email" className="text-right">
              Email
            </Label>
            <Input id="email" value={member.email} disabled className="col-span-3" />
          </div>
          {/* Role Selection Dropdown */}
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="role" className="text-right">
              Role
            </Label>
            <Select
              value={selectedRole}
              onValueChange={setSelectedRole}
              disabled={isSaving} // Disable while saving
            >
              <SelectTrigger className="col-span-3 capitalize">
                <SelectValue placeholder="Select a role" />
              </SelectTrigger>
              <SelectContent>
                {availableRoles.map((role) => (
                  <SelectItem key={role} value={role} className="capitalize">
                    {/* Display role names nicely */}
                    {role}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
          {/* Error Message Area */}
          {error && (
            <p className="col-span-4 text-sm text-red-600 text-center px-4">{error}</p>
          )}
        </div>
        <DialogFooter>
           {/* Cancel Button */}
           <DialogClose asChild>
             <Button type="button" variant="outline" disabled={isSaving}>Cancel</Button>
           </DialogClose>
           {/* Save Button */}
          <Button
            type="button"
            onClick={handleSave}
            disabled={isSaving || selectedRole === member.role || !selectedRole}
          >
            {isSaving ? "Saving..." : "Save Changes"}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
