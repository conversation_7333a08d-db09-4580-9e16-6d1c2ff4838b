"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import type { ButtonProps } from "@/components/ui/button"
import { HelpCircle } from "lucide-react"
import { useOnboarding } from "@/providers/onboarding-provider"

export function OnboardingButton({ 
  children, 
  variant = "default", 
  size = "default", 
  className,
  ...props 
}: ButtonProps) {
  const { startOnboarding } = useOnboarding()

  return (
    <Button
      variant={variant}
      size={size}
      className={className}
      onClick={() => startOnboarding()}
      {...props}
    >
      {children || (
        <>
          <HelpCircle className="mr-2 h-4 w-4" />
          Take a Tour
        </>
      )}
    </Button>
  )
}
