"use client"

import { Building, Camera, MapPin, Star } from "lucide-react"

export function OnboardingLocations() {
  return (
    <div className="space-y-4">
      <div className="flex items-start gap-3">
        <div className="flex items-center justify-center w-10 h-10 rounded-full bg-primary/10 mt-1">
          <Building className="h-5 w-5 text-primary" />
        </div>
        <div>
          <h3 className="text-lg font-medium">Location Management</h3>
          <p className="text-muted-foreground">
            Scout, organize, and evaluate potential filming locations.
          </p>
        </div>
      </div>
      
      <div className="border rounded-lg p-4 bg-muted/20">
        <div className="flex justify-between items-center mb-3">
          <div className="text-sm font-medium">Location Gallery</div>
          <div className="flex items-center gap-1 text-xs text-primary">
            <Camera className="h-3 w-3" />
            <span>Add Location</span>
          </div>
        </div>
        
        <div className="grid grid-cols-2 gap-3">
          <div className="bg-background rounded-md overflow-hidden border">
            <div className="h-24 bg-muted flex items-center justify-center">
              <MapPin className="h-8 w-8 text-muted-foreground/50" />
            </div>
            <div className="p-2">
              <div className="flex justify-between items-center">
                <div className="font-medium text-sm">City Park</div>
                <div className="flex">
                  <Star className="h-3 w-3 fill-primary text-primary" />
                  <Star className="h-3 w-3 fill-primary text-primary" />
                  <Star className="h-3 w-3 fill-primary text-primary" />
                  <Star className="h-3 w-3 fill-primary text-primary" />
                  <Star className="h-3 w-3 text-muted-foreground" />
                </div>
              </div>
              <div className="text-xs text-muted-foreground mt-1">Downtown, 3 miles</div>
            </div>
          </div>
          
          <div className="bg-background rounded-md overflow-hidden border">
            <div className="h-24 bg-muted flex items-center justify-center">
              <MapPin className="h-8 w-8 text-muted-foreground/50" />
            </div>
            <div className="p-2">
              <div className="flex justify-between items-center">
                <div className="font-medium text-sm">Waterfront</div>
                <div className="flex">
                  <Star className="h-3 w-3 fill-primary text-primary" />
                  <Star className="h-3 w-3 fill-primary text-primary" />
                  <Star className="h-3 w-3 fill-primary text-primary" />
                  <Star className="h-3 w-3 text-muted-foreground" />
                  <Star className="h-3 w-3 text-muted-foreground" />
                </div>
              </div>
              <div className="text-xs text-muted-foreground mt-1">Riverside, 5 miles</div>
            </div>
          </div>
        </div>
      </div>
      
      <div className="flex items-start gap-3 mt-2">
        <div className="flex h-6 w-6 items-center justify-center rounded-full bg-primary/10">
          <span className="text-xs font-medium text-primary">?</span>
        </div>
        <div className="text-sm text-muted-foreground">
          <p>
            <span className="font-medium text-foreground">Pro Tip:</span> Locations can be organized by project, 
            rated for suitability, and shared with your team for feedback.
          </p>
        </div>
      </div>
      
      <div className="flex flex-col gap-1 mt-2">
        <div className="text-sm font-medium">Location features:</div>
        <ul className="text-sm text-muted-foreground space-y-1 list-disc pl-5">
          <li>Upload photos and videos of potential locations</li>
          <li>Add detailed notes and requirements</li>
          <li>Rate locations based on various criteria</li>
          <li>View locations on interactive maps</li>
          <li>Share location details with clients and crew</li>
        </ul>
      </div>
    </div>
  )
}
