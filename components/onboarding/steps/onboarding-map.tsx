"use client"

import { Map as MapIcon, Layers, MapPin } from "lucide-react"

export function OnboardingMap() {
  return (
    <div className="space-y-4">
      <div className="flex items-start gap-3">
        <div className="flex items-center justify-center w-10 h-10 rounded-full bg-primary/10 mt-1">
          <MapIcon className="h-5 w-5 text-primary" />
        </div>
        <div>
          <h3 className="text-lg font-medium">Interactive Maps</h3>
          <p className="text-muted-foreground">
            Visualize and plan your locations on interactive maps.
          </p>
        </div>
      </div>
      
      <div className="border rounded-lg overflow-hidden">
        {/* Map preview */}
        <div className="h-48 bg-muted relative">
          {/* Simulated map with pins */}
          <div className="absolute inset-0 flex items-center justify-center">
            <div className="w-full h-full bg-slate-200 flex items-center justify-center">
              <span className="text-muted-foreground text-sm">Map Preview</span>
            </div>
          </div>
          
          {/* Map pins */}
          <div className="absolute top-1/4 left-1/3">
            <MapPin className="h-5 w-5 text-red-500" />
          </div>
          <div className="absolute top-1/2 right-1/3">
            <MapPin className="h-5 w-5 text-blue-500" />
          </div>
          <div className="absolute bottom-1/4 left-1/2">
            <MapPin className="h-5 w-5 text-green-500" />
          </div>
          
          {/* Map controls */}
          <div className="absolute top-2 right-2 bg-background/80 rounded-md p-1 backdrop-blur-sm">
            <div className="flex flex-col gap-1">
              <button type="button" className="h-6 w-6 flex items-center justify-center rounded hover:bg-muted">
                <Layers className="h-4 w-4" />
              </button>
              <button type="button" className="h-6 w-6 flex items-center justify-center rounded hover:bg-muted">
                <span className="text-xs font-medium">+</span>
              </button>
              <button type="button" className="h-6 w-6 flex items-center justify-center rounded hover:bg-muted">
                <span className="text-xs font-medium">-</span>
              </button>
            </div>
          </div>
        </div>
        
        {/* Map legend */}
        <div className="p-3 border-t">
          <div className="text-sm font-medium mb-2">Map Layers</div>
          <div className="flex flex-wrap gap-3">
            <div className="flex items-center gap-1.5">
              <div className="w-3 h-3 rounded-full bg-red-500" />
              <span className="text-xs">Downtown Locations</span>
            </div>
            <div className="flex items-center gap-1.5">
              <div className="w-3 h-3 rounded-full bg-blue-500" />
              <span className="text-xs">Riverside Locations</span>
            </div>
            <div className="flex items-center gap-1.5">
              <div className="w-3 h-3 rounded-full bg-green-500" />
              <span className="text-xs">Park Locations</span>
            </div>
          </div>
        </div>
      </div>
      
      <div className="flex items-start gap-3 mt-2">
        <div className="flex h-6 w-6 items-center justify-center rounded-full bg-primary/10">
          <span className="text-xs font-medium text-primary">?</span>
        </div>
        <div className="text-sm text-muted-foreground">
          <p>
            <span className="font-medium text-foreground">Pro Tip:</span> Use maps to visualize location 
            proximity, plan logistics, and optimize your shooting schedule.
          </p>
        </div>
      </div>
      
      <div className="flex flex-col gap-1 mt-2">
        <div className="text-sm font-medium">Map features:</div>
        <ul className="text-sm text-muted-foreground space-y-1 list-disc pl-5">
          <li>View all locations on an interactive map</li>
          <li>Filter locations by project, status, or type</li>
          <li>Measure distances between locations</li>
          <li>Create custom map layers for different production needs</li>
          <li>Share maps with your team for planning</li>
        </ul>
      </div>
    </div>
  )
}
