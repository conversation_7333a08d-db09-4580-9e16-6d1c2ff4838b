"use client"

import { FileText, File, Upload, Download } from "lucide-react"

export function OnboardingDocuments() {
  return (
    <div className="space-y-4">
      <div className="flex items-start gap-3">
        <div className="flex items-center justify-center w-10 h-10 rounded-full bg-primary/10 mt-1">
          <FileText className="h-5 w-5 text-primary" />
        </div>
        <div>
          <h3 className="text-lg font-medium">Document Management</h3>
          <p className="text-muted-foreground">
            Store, organize, and share all your production documents.
          </p>
        </div>
      </div>
      
      <div className="border rounded-lg p-4 bg-muted/20">
        <div className="flex justify-between items-center mb-3">
          <div className="text-sm font-medium">Project Documents</div>
          <div className="flex items-center gap-2">
            <button type="button" className="flex items-center gap-1 text-xs text-primary">
              <Upload className="h-3 w-3" />
              <span>Upload</span>
            </button>
            <button type="button" className="flex items-center gap-1 text-xs text-muted-foreground">
              <Download className="h-3 w-3" />
              <span>Download</span>
            </button>
          </div>
        </div>
        
        <div className="space-y-2">
          <div className="bg-background rounded-md p-3 border flex items-center gap-3">
            <div className="flex-shrink-0">
              <File className="h-8 w-8 text-blue-500" />
            </div>
            <div className="flex-grow min-w-0">
              <div className="font-medium text-sm truncate">Production_Schedule.pdf</div>
              <div className="text-xs text-muted-foreground mt-1">Added 2 days ago • 1.2 MB</div>
            </div>
            <div className="flex-shrink-0 text-xs px-1.5 py-0.5 rounded bg-blue-100 text-blue-700">
              PDF
            </div>
          </div>
          
          <div className="bg-background rounded-md p-3 border flex items-center gap-3">
            <div className="flex-shrink-0">
              <File className="h-8 w-8 text-green-500" />
            </div>
            <div className="flex-grow min-w-0">
              <div className="font-medium text-sm truncate">Location_Requirements.xlsx</div>
              <div className="text-xs text-muted-foreground mt-1">Added 5 days ago • 845 KB</div>
            </div>
            <div className="flex-shrink-0 text-xs px-1.5 py-0.5 rounded bg-green-100 text-green-700">
              XLSX
            </div>
          </div>
          
          <div className="bg-background rounded-md p-3 border flex items-center gap-3">
            <div className="flex-shrink-0">
              <File className="h-8 w-8 text-purple-500" />
            </div>
            <div className="flex-grow min-w-0">
              <div className="font-medium text-sm truncate">Script_Draft_Final.docx</div>
              <div className="text-xs text-muted-foreground mt-1">Added 1 week ago • 2.4 MB</div>
            </div>
            <div className="flex-shrink-0 text-xs px-1.5 py-0.5 rounded bg-purple-100 text-purple-700">
              DOCX
            </div>
          </div>
        </div>
      </div>
      
      <div className="flex items-start gap-3 mt-2">
        <div className="flex h-6 w-6 items-center justify-center rounded-full bg-primary/10">
          <span className="text-xs font-medium text-primary">?</span>
        </div>
        <div className="text-sm text-muted-foreground">
          <p>
            <span className="font-medium text-foreground">Pro Tip:</span> Documents can be organized by project, 
            tagged for easy searching, and shared with specific team members.
          </p>
        </div>
      </div>
      
      <div className="flex flex-col gap-1 mt-2">
        <div className="text-sm font-medium">Document features:</div>
        <ul className="text-sm text-muted-foreground space-y-1 list-disc pl-5">
          <li>Upload and store production documents</li>
          <li>Organize files by project, type, or custom categories</li>
          <li>Control access permissions for team members</li>
          <li>Preview documents directly in the browser</li>
          <li>Track document versions and changes</li>
          <li>Comment and collaborate on documents</li>
        </ul>
      </div>
    </div>
  )
}
