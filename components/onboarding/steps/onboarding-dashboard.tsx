"use client"

import { LayoutDashboard, BarChart3, Activity } from "lucide-react"

export function OnboardingDashboard() {
  return (
    <div className="space-y-4">
      <div className="flex items-start gap-3">
        <div className="flex items-center justify-center w-10 h-10 rounded-full bg-primary/10 mt-1">
          <LayoutDashboard className="h-5 w-5 text-primary" />
        </div>
        <div>
          <h3 className="text-lg font-medium">Dashboard Overview</h3>
          <p className="text-muted-foreground">
            Your central hub for managing all production activities.
          </p>
        </div>
      </div>
      
      <div className="border rounded-lg p-4 bg-muted/20">
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4 mb-4">
          <div className="bg-background rounded-md p-3 border">
            <div className="flex flex-col gap-1">
              <div className="text-xs text-muted-foreground">Total Projects</div>
              <div className="text-2xl font-bold">12</div>
              <div className="text-xs text-green-600">+2 from last month</div>
            </div>
          </div>
          
          <div className="bg-background rounded-md p-3 border">
            <div className="flex flex-col gap-1">
              <div className="text-xs text-muted-foreground">Locations</div>
              <div className="text-2xl font-bold">34</div>
              <div className="text-xs text-green-600">+8 from last month</div>
            </div>
          </div>
          
          <div className="bg-background rounded-md p-3 border">
            <div className="flex flex-col gap-1">
              <div className="text-xs text-muted-foreground">Team Members</div>
              <div className="text-2xl font-bold">16</div>
              <div className="text-xs text-green-600">+3 from last month</div>
            </div>
          </div>
          
          <div className="bg-background rounded-md p-3 border">
            <div className="flex flex-col gap-1">
              <div className="text-xs text-muted-foreground">Upcoming Shoots</div>
              <div className="text-2xl font-bold">5</div>
              <div className="text-xs text-amber-600">Next: Jun 12</div>
            </div>
          </div>
        </div>
        
        <div className="grid gap-4 md:grid-cols-2">
          <div className="bg-background rounded-md p-3 border">
            <div className="flex items-center justify-between mb-2">
              <div className="font-medium">Recent Projects</div>
              <BarChart3 className="h-4 w-4 text-muted-foreground" />
            </div>
            <div className="space-y-2">
              <div className="text-sm">Project 1</div>
              <div className="text-sm">Project 2</div>
              <div className="text-sm">Project 3</div>
            </div>
          </div>
          
          <div className="bg-background rounded-md p-3 border">
            <div className="flex items-center justify-between mb-2">
              <div className="font-medium">Recent Activity</div>
              <Activity className="h-4 w-4 text-muted-foreground" />
            </div>
            <div className="space-y-2">
              <div className="text-sm">User 1 updated Project 1</div>
              <div className="text-sm">User 2 added a new location</div>
              <div className="text-sm">User 3 completed a task</div>
            </div>
          </div>
        </div>
      </div>
      
      <div className="flex items-start gap-3 mt-2">
        <div className="flex h-6 w-6 items-center justify-center rounded-full bg-primary/10">
          <span className="text-xs font-medium text-primary">?</span>
        </div>
        <div className="text-sm text-muted-foreground">
          <p>
            <span className="font-medium text-foreground">Pro Tip:</span> The dashboard gives you a quick overview of your production status. 
            Click on any card to see more details.
          </p>
        </div>
      </div>
      
      <div className="flex flex-col gap-1 mt-2">
        <div className="text-sm font-medium">Dashboard features:</div>
        <ul className="text-sm text-muted-foreground space-y-1 list-disc pl-5">
          <li>View key metrics and statistics at a glance</li>
          <li>Access recent projects and activities</li>
          <li>Navigate to different sections of the platform</li>
          <li>Monitor team performance and project progress</li>
          <li>Get notifications about important updates</li>
        </ul>
      </div>
    </div>
  )
}
