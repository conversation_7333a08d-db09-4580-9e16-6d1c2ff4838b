"use client"

import { <PERSON><PERSON><PERSON>, User, Credit<PERSON><PERSON>, <PERSON>, Key } from "lucide-react"

export function OnboardingSettings() {
  return (
    <div className="space-y-4">
      <div className="flex items-start gap-3">
        <div className="flex items-center justify-center w-10 h-10 rounded-full bg-primary/10 mt-1">
          <Settings className="h-5 w-5 text-primary" />
        </div>
        <div>
          <h3 className="text-lg font-medium">Settings & Preferences</h3>
          <p className="text-muted-foreground">
            Customize your account and organization settings.
          </p>
        </div>
      </div>
      
      <div className="border rounded-lg p-4 bg-muted/20">
        <div className="text-sm font-medium mb-3">Settings Categories</div>
        
        <div className="space-y-2">
          <div className="bg-background rounded-md p-3 border flex items-center gap-3">
            <div className="flex-shrink-0 w-8 h-8 rounded-full bg-blue-100 flex items-center justify-center">
              <User className="h-4 w-4 text-blue-700" />
            </div>
            <div className="flex-grow">
              <div className="font-medium text-sm">Profile Settings</div>
              <div className="text-xs text-muted-foreground mt-0.5">
                Update your personal information and preferences
              </div>
            </div>
          </div>
          
          <div className="bg-background rounded-md p-3 border flex items-center gap-3">
            <div className="flex-shrink-0 w-8 h-8 rounded-full bg-purple-100 flex items-center justify-center">
              <Bell className="h-4 w-4 text-purple-700" />
            </div>
            <div className="flex-grow">
              <div className="font-medium text-sm">Notification Settings</div>
              <div className="text-xs text-muted-foreground mt-0.5">
                Configure email and in-app notification preferences
              </div>
            </div>
          </div>
          
          <div className="bg-background rounded-md p-3 border flex items-center gap-3">
            <div className="flex-shrink-0 w-8 h-8 rounded-full bg-green-100 flex items-center justify-center">
              <CreditCard className="h-4 w-4 text-green-700" />
            </div>
            <div className="flex-grow">
              <div className="font-medium text-sm">Billing & Subscription</div>
              <div className="text-xs text-muted-foreground mt-0.5">
                Manage your subscription plan and payment methods
              </div>
            </div>
          </div>
          
          <div className="bg-background rounded-md p-3 border flex items-center gap-3">
            <div className="flex-shrink-0 w-8 h-8 rounded-full bg-amber-100 flex items-center justify-center">
              <Key className="h-4 w-4 text-amber-700" />
            </div>
            <div className="flex-grow">
              <div className="font-medium text-sm">API Keys & Integrations</div>
              <div className="text-xs text-muted-foreground mt-0.5">
                Set up API access and third-party integrations
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <div className="flex items-start gap-3 mt-2">
        <div className="flex h-6 w-6 items-center justify-center rounded-full bg-primary/10">
          <span className="text-xs font-medium text-primary">?</span>
        </div>
        <div className="text-sm text-muted-foreground">
          <p>
            <span className="font-medium text-foreground">Pro Tip:</span> Take some time to configure your 
            notification settings to ensure you receive important updates without being overwhelmed.
          </p>
        </div>
      </div>
      
      <div className="flex flex-col gap-1 mt-2">
        <div className="text-sm font-medium">Settings features:</div>
        <ul className="text-sm text-muted-foreground space-y-1 list-disc pl-5">
          <li>Customize your user profile and preferences</li>
          <li>Configure notification settings for different events</li>
          <li>Manage organization details and branding</li>
          <li>Set up billing information and subscription plans</li>
          <li>Generate API keys for external integrations</li>
          <li>Configure security settings and access controls</li>
        </ul>
      </div>
    </div>
  )
}
