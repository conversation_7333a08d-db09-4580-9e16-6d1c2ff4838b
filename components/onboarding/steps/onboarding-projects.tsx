"use client"

import { Briefcase, Plus, Calendar, Users, MapPin } from "lucide-react"

export function OnboardingProjects() {
  return (
    <div className="space-y-4">
      <div className="flex items-start gap-3">
        <div className="flex items-center justify-center w-10 h-10 rounded-full bg-primary/10 mt-1">
          <Briefcase className="h-5 w-5 text-primary" />
        </div>
        <div>
          <h3 className="text-lg font-medium">Project Management</h3>
          <p className="text-muted-foreground">
            Organize your production work into projects for better coordination.
          </p>
        </div>
      </div>
      
      <div className="border rounded-lg p-4 bg-muted/20">
        <div className="flex justify-between items-center mb-3">
          <div className="text-sm font-medium">Your Projects</div>
          <button type="button" className="flex items-center gap-1 text-xs text-primary">
            <Plus className="h-3 w-3" />
            <span>New Project</span>
          </button>
        </div>
        
        <div className="grid gap-3 md:grid-cols-2">
          <div className="bg-background rounded-md p-3 border">
            <div className="flex justify-between items-start">
              <div>
                <div className="font-medium">Downtown Drama</div>
                <div className="text-xs text-muted-foreground mt-1">Feature Film</div>
              </div>
              <div className="text-xs px-1.5 py-0.5 rounded bg-green-100 text-green-700">
                Active
              </div>
            </div>
            <div className="mt-3 flex items-center gap-3 text-xs text-muted-foreground">
              <div className="flex items-center gap-1">
                <Calendar className="h-3 w-3" />
                <span>Jun 2025</span>
              </div>
              <div className="flex items-center gap-1">
                <MapPin className="h-3 w-3" />
                <span>12 Locations</span>
              </div>
              <div className="flex items-center gap-1">
                <Users className="h-3 w-3" />
                <span>8 Members</span>
              </div>
            </div>
          </div>
          
          <div className="bg-background rounded-md p-3 border">
            <div className="flex justify-between items-start">
              <div>
                <div className="font-medium">Mountain Commercial</div>
                <div className="text-xs text-muted-foreground mt-1">TV Commercial</div>
              </div>
              <div className="text-xs px-1.5 py-0.5 rounded bg-amber-100 text-amber-700">
                Planning
              </div>
            </div>
            <div className="mt-3 flex items-center gap-3 text-xs text-muted-foreground">
              <div className="flex items-center gap-1">
                <Calendar className="h-3 w-3" />
                <span>Jul 2025</span>
              </div>
              <div className="flex items-center gap-1">
                <MapPin className="h-3 w-3" />
                <span>5 Locations</span>
              </div>
              <div className="flex items-center gap-1">
                <Users className="h-3 w-3" />
                <span>6 Members</span>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <div className="flex items-start gap-3 mt-2">
        <div className="flex h-6 w-6 items-center justify-center rounded-full bg-primary/10">
          <span className="text-xs font-medium text-primary">?</span>
        </div>
        <div className="text-sm text-muted-foreground">
          <p>
            <span className="font-medium text-foreground">Pro Tip:</span> Create separate projects for each production 
            to keep your work organized and track progress independently.
          </p>
        </div>
      </div>
      
      <div className="flex flex-col gap-1 mt-2">
        <div className="text-sm font-medium">Project features:</div>
        <ul className="text-sm text-muted-foreground space-y-1 list-disc pl-5">
          <li>Create and manage multiple production projects</li>
          <li>Organize locations, documents, and tasks by project</li>
          <li>Set project timelines and milestones</li>
          <li>Assign team members to specific projects</li>
          <li>Track project status and progress</li>
          <li>Generate project reports and analytics</li>
        </ul>
      </div>
    </div>
  )
}
