"use client"

import { CheckCircle2 } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { useOnboarding } from "@/providers/onboarding-provider"

export function OnboardingComplete() {
  const { closeOnboarding } = useOnboarding()
  
  return (
    <div className="flex flex-col items-center text-center space-y-4">
      <div className="flex items-center justify-center w-16 h-16 rounded-full bg-green-100 mb-2">
        <CheckCircle2 className="h-8 w-8 text-green-600" />
      </div>
      
      <h3 className="text-xl font-medium">You&apos;re All Set!</h3>
      
      <p className="text-muted-foreground max-w-md">
        Congratulations! You&apos;ve completed the tour of Scene-o-matic.
        You&apos;re now ready to start managing your production locations like a pro.
      </p>
      
      <div className="grid gap-4 w-full max-w-md mt-4">
        <div className="bg-muted/50 p-4 rounded-lg border">
          <h4 className="font-medium mb-2">What&apos;s Next?</h4>
          <ul className="text-sm text-muted-foreground space-y-2 list-disc pl-5">
            <li>Create your first project</li>
            <li>Add team members to collaborate</li>
            <li>Start scouting and adding locations</li>
            <li>Set up your production schedule</li>
            <li>Manage your tasks and documents</li>
          </ul>
        </div>
        
        <div className="bg-muted/50 p-4 rounded-lg border">
          <h4 className="font-medium mb-2">Need Help?</h4>
          <p className="text-sm text-muted-foreground mb-4">
            If you have any questions or need assistance, our support team is here to help.
          </p>
          <div className="flex flex-col sm:flex-row gap-2">
            <Button variant="outline" className="flex-1">
              View Documentation
            </Button>
            <Button variant="outline" className="flex-1">
              Contact Support
            </Button>
          </div>
        </div>
      </div>
      
      <div className="pt-4">
        <Button onClick={closeOnboarding}>
          Start Using Scene-o-matic
        </Button>
      </div>
    </div>
  )
}
