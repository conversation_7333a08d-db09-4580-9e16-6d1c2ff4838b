"use client"

import { Users, UserPlus, Mail, Shield } from "lucide-react"

export function OnboardingTeam() {
  return (
    <div className="space-y-4">
      <div className="flex items-start gap-3">
        <div className="flex items-center justify-center w-10 h-10 rounded-full bg-primary/10 mt-1">
          <Users className="h-5 w-5 text-primary" />
        </div>
        <div>
          <h3 className="text-lg font-medium">Team Collaboration</h3>
          <p className="text-muted-foreground">
            Invite team members and manage roles and permissions.
          </p>
        </div>
      </div>
      
      <div className="border rounded-lg p-4 bg-muted/20">
        <div className="flex justify-between items-center mb-3">
          <div className="text-sm font-medium">Team Members</div>
          <button type="button" className="flex items-center gap-1 text-xs text-primary">
            <UserPlus className="h-3 w-3" />
            <span>Invite</span>
          </button>
        </div>
        
        <div className="space-y-3">
          <div className="bg-background rounded-md p-3 border">
            <div className="flex items-center gap-3">
              <div className="flex-shrink-0 w-8 h-8 rounded-full bg-blue-100 flex items-center justify-center">
                <span className="text-xs font-medium text-blue-700">JD</span>
              </div>
              <div className="flex-grow min-w-0">
                <div className="font-medium text-sm">John Doe</div>
                <div className="flex items-center gap-1 text-xs text-muted-foreground mt-0.5">
                  <Mail className="h-3 w-3" />
                  <span><EMAIL></span>
                </div>
              </div>
              <div className="flex-shrink-0 text-xs px-1.5 py-0.5 rounded bg-blue-100 text-blue-700 flex items-center gap-1">
                <Shield className="h-3 w-3" />
                <span>Admin</span>
              </div>
            </div>
          </div>
          
          <div className="bg-background rounded-md p-3 border">
            <div className="flex items-center gap-3">
              <div className="flex-shrink-0 w-8 h-8 rounded-full bg-purple-100 flex items-center justify-center">
                <span className="text-xs font-medium text-purple-700">JS</span>
              </div>
              <div className="flex-grow min-w-0">
                <div className="font-medium text-sm">Jane Smith</div>
                <div className="flex items-center gap-1 text-xs text-muted-foreground mt-0.5">
                  <Mail className="h-3 w-3" />
                  <span><EMAIL></span>
                </div>
              </div>
              <div className="flex-shrink-0 text-xs px-1.5 py-0.5 rounded bg-purple-100 text-purple-700 flex items-center gap-1">
                <Shield className="h-3 w-3" />
                <span>Editor</span>
              </div>
            </div>
          </div>
          
          <div className="bg-background rounded-md p-3 border">
            <div className="flex items-center gap-3">
              <div className="flex-shrink-0 w-8 h-8 rounded-full bg-green-100 flex items-center justify-center">
                <span className="text-xs font-medium text-green-700">RJ</span>
              </div>
              <div className="flex-grow min-w-0">
                <div className="font-medium text-sm">Robert Johnson</div>
                <div className="flex items-center gap-1 text-xs text-muted-foreground mt-0.5">
                  <Mail className="h-3 w-3" />
                  <span><EMAIL></span>
                </div>
              </div>
              <div className="flex-shrink-0 text-xs px-1.5 py-0.5 rounded bg-green-100 text-green-700 flex items-center gap-1">
                <Shield className="h-3 w-3" />
                <span>Viewer</span>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <div className="flex items-start gap-3 mt-2">
        <div className="flex h-6 w-6 items-center justify-center rounded-full bg-primary/10">
          <span className="text-xs font-medium text-primary">?</span>
        </div>
        <div className="text-sm text-muted-foreground">
          <p>
            <span className="font-medium text-foreground">Pro Tip:</span> Assign different roles to team members 
            based on their responsibilities. Admins have full access, Editors can modify content, 
            and Viewers can only view.
          </p>
        </div>
      </div>
      
      <div className="flex flex-col gap-1 mt-2">
        <div className="text-sm font-medium">Team features:</div>
        <ul className="text-sm text-muted-foreground space-y-1 list-disc pl-5">
          <li>Invite team members via email</li>
          <li>Assign roles and permissions</li>
          <li>Collaborate on projects and tasks</li>
          <li>Track team member activity</li>
          <li>Communicate through comments and notifications</li>
          <li>Manage access to sensitive information</li>
        </ul>
      </div>
    </div>
  )
}
