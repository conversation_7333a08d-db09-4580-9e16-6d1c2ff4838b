"use client"

import { Sparkles } from "lucide-react"

export function OnboardingWelcome() {
  return (
    <div className="flex flex-col items-center text-center space-y-4">
      <div className="flex items-center justify-center w-16 h-16 rounded-full bg-primary/10 mb-2">
        <Sparkles className="h-8 w-8 text-primary" />
      </div>
      
      <h3 className="text-xl font-medium">Welcome to Scene-o-matic!</h3>
      
      <p className="text-muted-foreground max-w-md">
        Your all-in-one platform for managing film and media production locations.
        Let&apos;s take a quick tour to help you get started.
      </p>
      
      <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 w-full mt-4">
        <div className="flex items-center p-4 rounded-lg border bg-background/50">
          <div className="flex-shrink-0 mr-4 flex items-center justify-center w-10 h-10 rounded-full bg-blue-100">
            <span className="text-blue-700 font-medium">1</span>
          </div>
          <div>
            <h4 className="font-medium">Organize Projects</h4>
            <p className="text-sm text-muted-foreground">
              Create and manage all your production projects
            </p>
          </div>
        </div>
        
        <div className="flex items-center p-4 rounded-lg border bg-background/50">
          <div className="flex-shrink-0 mr-4 flex items-center justify-center w-10 h-10 rounded-full bg-green-100">
            <span className="text-green-700 font-medium">2</span>
          </div>
          <div>
            <h4 className="font-medium">Scout Locations</h4>
            <p className="text-sm text-muted-foreground">
              Find and evaluate potential filming locations
            </p>
          </div>
        </div>
        
        <div className="flex items-center p-4 rounded-lg border bg-background/50">
          <div className="flex-shrink-0 mr-4 flex items-center justify-center w-10 h-10 rounded-full bg-purple-100">
            <span className="text-purple-700 font-medium">3</span>
          </div>
          <div>
            <h4 className="font-medium">Collaborate</h4>
            <p className="text-sm text-muted-foreground">
              Work with your team in real-time
            </p>
          </div>
        </div>
        
        <div className="flex items-center p-4 rounded-lg border bg-background/50">
          <div className="flex-shrink-0 mr-4 flex items-center justify-center w-10 h-10 rounded-full bg-amber-100">
            <span className="text-amber-700 font-medium">4</span>
          </div>
          <div>
            <h4 className="font-medium">Track Progress</h4>
            <p className="text-sm text-muted-foreground">
              Monitor production timelines and tasks
            </p>
          </div>
        </div>
      </div>
      
      <p className="text-sm text-muted-foreground mt-4">
        Click &quot;Next&quot; to continue the tour and learn more about each feature.
      </p>
    </div>
  )
}
