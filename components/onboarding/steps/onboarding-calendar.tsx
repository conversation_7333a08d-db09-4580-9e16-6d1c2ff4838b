"use client"

import { Calendar as CalendarIcon, Clock, Users } from "lucide-react"

export function OnboardingCalendar() {
  return (
    <div className="space-y-4">
      <div className="flex items-start gap-3">
        <div className="flex items-center justify-center w-10 h-10 rounded-full bg-primary/10 mt-1">
          <CalendarIcon className="h-5 w-5 text-primary" />
        </div>
        <div>
          <h3 className="text-lg font-medium">Calendar & Scheduling</h3>
          <p className="text-muted-foreground">
            Plan and coordinate your production schedule with an integrated calendar.
          </p>
        </div>
      </div>
      
      <div className="border rounded-lg overflow-hidden">
        {/* Calendar preview */}
        <div className="bg-background p-3">
          <div className="flex justify-between items-center mb-3">
            <div className="text-sm font-medium">May 2025</div>
            <div className="flex items-center gap-1">
              <button type="button" className="h-6 w-6 flex items-center justify-center rounded hover:bg-muted">
                <span className="text-xs">←</span>
              </button>
              <button type="button" className="h-6 w-6 flex items-center justify-center rounded hover:bg-muted">
                <span className="text-xs">→</span>
              </button>
            </div>
          </div>
          
          {/* Calendar grid */}
          <div className="grid grid-cols-7 gap-1 text-center text-xs mb-2">
            <div className="text-muted-foreground">Su</div>
            <div className="text-muted-foreground">Mo</div>
            <div className="text-muted-foreground">Tu</div>
            <div className="text-muted-foreground">We</div>
            <div className="text-muted-foreground">Th</div>
            <div className="text-muted-foreground">Fr</div>
            <div className="text-muted-foreground">Sa</div>
          </div>
          
          <div className="grid grid-cols-7 gap-1 text-center">
            <div className="h-8 flex items-center justify-center text-xs text-muted-foreground">28</div>
            <div className="h-8 flex items-center justify-center text-xs text-muted-foreground">29</div>
            <div className="h-8 flex items-center justify-center text-xs text-muted-foreground">30</div>
            <div className="h-8 flex items-center justify-center text-xs">1</div>
            <div className="h-8 flex items-center justify-center text-xs">2</div>
            <div className="h-8 flex items-center justify-center text-xs">3</div>
            <div className="h-8 flex items-center justify-center text-xs">4</div>
            
            <div className="h-8 flex items-center justify-center text-xs">5</div>
            <div className="h-8 flex items-center justify-center text-xs">6</div>
            <div className="h-8 flex items-center justify-center text-xs">7</div>
            <div className="h-8 flex items-center justify-center text-xs">8</div>
            <div className="h-8 flex items-center justify-center text-xs">9</div>
            <div className="h-8 flex items-center justify-center text-xs relative">
              10
              <div className="absolute bottom-1 left-1/2 transform -translate-x-1/2 w-1 h-1 bg-blue-500 rounded-full" />
            </div>
            <div className="h-8 flex items-center justify-center text-xs">11</div>
            
            <div className="h-8 flex items-center justify-center text-xs">12</div>
            <div className="h-8 flex items-center justify-center text-xs">13</div>
            <div className="h-8 flex items-center justify-center text-xs">14</div>
            <div className="h-8 flex items-center justify-center text-xs relative">
              15
              <div className="absolute bottom-1 left-1/2 transform -translate-x-1/2 w-1 h-1 bg-red-500 rounded-full" />
            </div>
            <div className="h-8 flex items-center justify-center text-xs">16</div>
            <div className="h-8 flex items-center justify-center text-xs">17</div>
            <div className="h-8 flex items-center justify-center text-xs">18</div>
          </div>
        </div>
        
        {/* Calendar events */}
        <div className="border-t p-3">
          <div className="text-sm font-medium mb-2">Upcoming Events</div>
          <div className="space-y-2">
            <div className="bg-blue-50 p-2 rounded-md border border-blue-100">
              <div className="flex justify-between items-start">
                <div>
                  <div className="font-medium text-sm">Location Scouting</div>
                  <div className="flex items-center gap-2 mt-1">
                    <div className="flex items-center gap-1 text-xs">
                      <Clock className="h-3 w-3 text-muted-foreground" />
                      <span className="text-muted-foreground">9:00 AM - 2:00 PM</span>
                    </div>
                  </div>
                </div>
                <div className="text-xs px-1.5 py-0.5 rounded bg-blue-100 text-blue-700">May 10</div>
              </div>
            </div>
            
            <div className="bg-red-50 p-2 rounded-md border border-red-100">
              <div className="flex justify-between items-start">
                <div>
                  <div className="font-medium text-sm">Production Meeting</div>
                  <div className="flex items-center gap-2 mt-1">
                    <div className="flex items-center gap-1 text-xs">
                      <Clock className="h-3 w-3 text-muted-foreground" />
                      <span className="text-muted-foreground">10:00 AM - 11:30 AM</span>
                    </div>
                    <div className="flex items-center gap-1 text-xs">
                      <Users className="h-3 w-3 text-muted-foreground" />
                      <span className="text-muted-foreground">8 attendees</span>
                    </div>
                  </div>
                </div>
                <div className="text-xs px-1.5 py-0.5 rounded bg-red-100 text-red-700">May 15</div>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <div className="flex items-start gap-3 mt-2">
        <div className="flex h-6 w-6 items-center justify-center rounded-full bg-primary/10">
          <span className="text-xs font-medium text-primary">?</span>
        </div>
        <div className="text-sm text-muted-foreground">
          <p>
            <span className="font-medium text-foreground">Pro Tip:</span> Use the calendar to schedule 
            shoots, meetings, and deadlines. You can also sync with external calendars.
          </p>
        </div>
      </div>
      
      <div className="flex flex-col gap-1 mt-2">
        <div className="text-sm font-medium">Calendar features:</div>
        <ul className="text-sm text-muted-foreground space-y-1 list-disc pl-5">
          <li>Schedule production events and meetings</li>
          <li>View availability of team members and locations</li>
          <li>Set reminders for important deadlines</li>
          <li>Color-code events by project or type</li>
          <li>Export and share schedules with your team</li>
        </ul>
      </div>
    </div>
  )
}
