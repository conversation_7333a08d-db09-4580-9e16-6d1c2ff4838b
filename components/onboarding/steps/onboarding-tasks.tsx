"use client"

import { CheckSquare, Clock, Calendar, Tag } from "lucide-react"

export function OnboardingTasks() {
  return (
    <div className="space-y-4">
      <div className="flex items-start gap-3">
        <div className="flex items-center justify-center w-10 h-10 rounded-full bg-primary/10 mt-1">
          <CheckSquare className="h-5 w-5 text-primary" />
        </div>
        <div>
          <h3 className="text-lg font-medium">Task Management</h3>
          <p className="text-muted-foreground">
            Keep track of all your production tasks and deadlines.
          </p>
        </div>
      </div>
      
      <div className="border rounded-lg p-4 bg-muted/20">
        <div className="flex justify-between items-center mb-3">
          <div className="text-sm font-medium">Current Tasks</div>
          <div className="flex items-center gap-2">
            <div className="text-xs px-2 py-0.5 rounded-full bg-green-100 text-green-700">Active</div>
            <div className="text-xs px-2 py-0.5 rounded-full bg-muted text-muted-foreground">Completed</div>
          </div>
        </div>
        
        <div className="space-y-3">
          <div className="bg-background rounded-md p-3 border">
            <div className="flex justify-between items-start">
              <div className="flex items-start gap-2">
                <div className="mt-0.5">
                  <div className="h-4 w-4 border rounded-sm" />
                </div>
                <div>
                  <div className="font-medium">Location scouting - Downtown</div>
                  <div className="flex items-center gap-2 mt-1">
                    <div className="flex items-center gap-1 text-xs">
                      <Calendar className="h-3 w-3 text-muted-foreground" />
                      <span className="text-muted-foreground">May 10</span>
                    </div>
                    <div className="flex items-center gap-1 text-xs">
                      <Tag className="h-3 w-3 text-blue-500" />
                      <span className="text-blue-500">Location</span>
                    </div>
                  </div>
                </div>
              </div>
              <div className="flex items-center gap-1 text-xs text-amber-500">
                <Clock className="h-3 w-3" />
                <span>Due soon</span>
              </div>
            </div>
          </div>
          
          <div className="bg-background rounded-md p-3 border">
            <div className="flex justify-between items-start">
              <div className="flex items-start gap-2">
                <div className="mt-0.5">
                  <div className="h-4 w-4 border rounded-sm" />
                </div>
                <div>
                  <div className="font-medium">Finalize equipment list</div>
                  <div className="flex items-center gap-2 mt-1">
                    <div className="flex items-center gap-1 text-xs">
                      <Calendar className="h-3 w-3 text-muted-foreground" />
                      <span className="text-muted-foreground">May 15</span>
                    </div>
                    <div className="flex items-center gap-1 text-xs">
                      <Tag className="h-3 w-3 text-purple-500" />
                      <span className="text-purple-500">Equipment</span>
                    </div>
                  </div>
                </div>
              </div>
              <div className="flex items-center gap-1 text-xs text-green-500">
                <Clock className="h-3 w-3" />
                <span>On track</span>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <div className="flex items-start gap-3 mt-2">
        <div className="flex h-6 w-6 items-center justify-center rounded-full bg-primary/10">
          <span className="text-xs font-medium text-primary">?</span>
        </div>
        <div className="text-sm text-muted-foreground">
          <p>
            <span className="font-medium text-foreground">Pro Tip:</span> Tasks can be assigned to team members, 
            categorized by type, and linked to specific projects or locations.
          </p>
        </div>
      </div>
      
      <div className="flex flex-col gap-1 mt-2">
        <div className="text-sm font-medium">Task features:</div>
        <ul className="text-sm text-muted-foreground space-y-1 list-disc pl-5">
          <li>Create and assign tasks to team members</li>
          <li>Set due dates and priorities</li>
          <li>Categorize tasks with custom tags</li>
          <li>Track task progress and completion</li>
          <li>Receive notifications for upcoming deadlines</li>
        </ul>
      </div>
    </div>
  )
}
