"use client"

import { useEffect, useState } from "react"
import { useOnboarding } from "@/providers/onboarding-provider"

interface OnboardingTriggerProps {
  organizationSlug: string
}

export function OnboardingTrigger({ organizationSlug }: OnboardingTriggerProps) {
  const { startOnboarding } = useOnboarding()
  const [hasTriggered, setHasTriggered] = useState(false)
  
  useEffect(() => {
    // Check if the user has completed onboarding before
    const hasCompletedOnboarding = localStorage.getItem(`onboarding-completed-${organizationSlug}`)
    
    // If the user hasn't completed onboarding and we haven't triggered it yet in this session
    if (!hasCompletedOnboarding && !hasTriggered) {
      // Add a small delay to ensure the UI is fully loaded
      const timer = setTimeout(() => {
        startOnboarding()
        setHasTriggered(true)
      }, 1500)
      
      return () => clearTimeout(timer)
    }
  }, [organizationSlug, startOnboarding, hasTriggered])
  
  // Mark onboarding as completed when the component unmounts
  useEffect(() => {
    return () => {
      if (hasTriggered) {
        localStorage.setItem(`onboarding-completed-${organizationSlug}`, "true")
      }
    }
  }, [organizationSlug, hasTriggered])
  
  // This component doesn't render anything
  return null
}
