"use client"

import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, DialogTitle } from "@/components/ui/dialog"
import { But<PERSON> } from "@/components/ui/button"
import { useOnboarding, ONBOARDING_STEPS } from "@/providers/onboarding-provider"
import { ChevronLeft, ChevronRight, X } from "lucide-react"

export function OnboardingModal() {
  const {
    isOpen,
    currentStep,
    currentStepIndex,
    totalSteps,
    closeOnboarding,
    nextStep,
    prevStep,
    goToStep
  } = useOnboarding()

  // Get the current step component
  const StepComponent = currentStep.component

  // Calculate progress percentage
  const progress = ((currentStepIndex + 1) / totalSteps) * 100

  return (
    <Dialog open={isOpen} onOpenChange={(open) => {
      if (!open) closeOnboarding()
    }}>
      <DialogContent className="sm:max-w-[600px] p-0 gap-0">
        {/* Header */}
        <DialogHeader className="flex flex-row items-center justify-between p-4 border-b">
          <DialogTitle className="text-lg font-semibold">{currentStep.title}</DialogTitle>
          <Button
            variant="ghost"
            size="icon"
            onClick={closeOnboarding}
            className="h-8 w-8"
          >
            <X className="h-4 w-4" />
          </Button>
        </DialogHeader>

        {/* Progress bar */}
        <div className="w-full h-1 bg-muted">
          <div
            className="h-full bg-primary transition-all duration-300"
            style={{ width: `${progress}%` }}
          />
        </div>

        {/* Step content */}
        <div className="p-6">
          <StepComponent />
        </div>

        {/* Navigation */}
        <div className="flex items-center justify-between p-4 border-t">
          <div className="flex items-center gap-2">
            {/* Step indicators */}
            <div className="flex items-center gap-1.5">
              {ONBOARDING_STEPS.map((step, index) => (
                <button
                  key={step.id}
                  type="button"
                  onClick={() => goToStep(index)}
                  className={`h-2 w-2 rounded-full transition-all ${
                    index === currentStepIndex
                      ? "bg-primary w-4"
                      : "bg-muted-foreground/30"
                  }`}
                  aria-label={`Go to step ${index + 1}: ${step.title}`}
                />
              ))}
            </div>
          </div>

          <div className="flex items-center gap-2">
            {/* Back button (hidden on first step) */}
            {currentStepIndex > 0 && (
              <Button variant="outline" size="sm" onClick={prevStep}>
                <ChevronLeft className="mr-1 h-4 w-4" />
                Back
              </Button>
            )}

            {/* Next/Finish button */}
            <Button size="sm" onClick={nextStep}>
              {currentStepIndex === totalSteps - 1 ? (
                "Finish"
              ) : (
                <>
                  Next
                  <ChevronRight className="ml-1 h-4 w-4" />
                </>
              )}
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
}
