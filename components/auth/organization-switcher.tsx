"use client"

import { useState } from "react"
import { Check, Chev<PERSON>UpDown, PlusCircle } from "lucide-react"
import { useRouter } from "next/navigation"
import { cn } from "@/lib/utils"
import { But<PERSON> } from "@/components/ui/button"
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
  CommandSeparator,
} from "@/components/ui/command"
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover"
import { useOrganization } from "@/hooks/use-organization"

export function OrganizationSwitcher() {
  const router = useRouter()
  const { organization, organizations, setOrganization } = useOrganization()
  const [open, setOpen] = useState(false)

  if (!organization || organizations.length === 0) {
    return null
  }

  const handleSelect = async (orgId: string) => {
    // Find the selected organization
    const selectedOrg = organizations.find((org) => org.id === orgId)
    
    if (!selectedOrg) return
    
    try {
      // Call the API to switch organizations
      const response = await fetch(`/api/organizations/${orgId}/switch`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
      })
      
      if (!response.ok) {
        throw new Error("Failed to switch organization")
      }
      
      // Update the local state
      setOrganization(selectedOrg)
      
      // Close the popover
      setOpen(false)
      
      // Navigate to the organization dashboard
      router.push(`/organizations/${selectedOrg.slug}`)
    } catch (error) {
      console.error("Error switching organization:", error)
    }
  }

  const handleCreateOrg = () => {
    // Navigate to the organization creation page
    router.push("/auth/organization/create")
    setOpen(false)
  }

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <Button
          variant="outline"
          aria-expanded={open}
          aria-label="Select organization"
          className="w-[200px] justify-between"
        >
          {organization.name}
          <ChevronsUpDown className="ml-auto h-4 w-4 shrink-0 opacity-50" />
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-[200px] p-0">
        <Command>
          <CommandList>
            <CommandInput placeholder="Search organization..." />
            <CommandEmpty>No organization found.</CommandEmpty>
            <CommandGroup heading="Organizations">
              {organizations.map((org) => (
                <CommandItem
                  key={org.id}
                  onSelect={() => handleSelect(org.id)}
                  className="text-sm"
                >
                  {org.name}
                  <Check
                    className={cn(
                      "ml-auto h-4 w-4",
                      organization.id === org.id ? "opacity-100" : "opacity-0"
                    )}
                  />
                </CommandItem>
              ))}
            </CommandGroup>
          </CommandList>
          <CommandSeparator />
          <CommandList>
            <CommandGroup>
              <CommandItem onSelect={handleCreateOrg}>
                <PlusCircle className="mr-2 h-4 w-4" />
                Create Organization
              </CommandItem>
            </CommandGroup>
          </CommandList>
        </Command>
      </PopoverContent>
    </Popover>
  )
}
