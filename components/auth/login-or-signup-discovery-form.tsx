"use client"

import { StytchB2B } from '@stytch/nextjs/b2b'
import { AuthFlowType, B2BProducts } from '@stytch/vanilla-js/b2b'
import type { StytchEvent, StytchError } from '@stytch/vanilla-js'
import { authConfig } from '@/lib/config/auth'

export const LoginOrSignupDiscoveryForm = () => {
  // Get the base URL from the environment or use the current origin
  const baseUrl = process.env.NEXT_PUBLIC_APP_URL || 
    (typeof window !== 'undefined' ? window.location.origin : 'http://localhost:3000')
  
  // Configure the StytchB2B component
  const config = {
    products: [B2BProducts.emailMagicLinks],
    sessionOptions: { sessionDurationMinutes: 60 * 24 }, // 1 day (1440 minutes) - reduced to comply with live project limits
    authFlowType: AuthFlowType.Discovery,
    emailMagicLinksOptions: {
      loginRedirectURL: `${baseUrl}${authConfig.callbackUrl}`,
      loginExpirationMinutes: 30,
      signupRedirectURL: `${baseUrl}${authConfig.callbackUrl}`,
      signupExpirationMinutes: 30,
      discoveryRedirectURL: `${baseUrl}${authConfig.callbackUrl}`,
    },
    style: {
      fontFamily: 'inherit',
      width: '100%',
      borderRadius: '0.375rem',
      primaryColor: 'hsl(var(--primary))',
      primaryTextColor: 'hsl(var(--primary-foreground))',
    }
  }
  
  // Define callbacks for the StytchB2B component
  const callbacks = {
    onEvent: (event: StytchEvent) => {
      console.log("Stytch event:", event)
    },
    onError: (error: StytchError) => {
      console.error("Stytch error:", error)
    },
    onSuccess: (data: { sessionToken?: string; sessionJwt?: string }) => {
      console.log("Stytch success:", data)
    }
  }

  return <StytchB2B config={config} callbacks={callbacks} />
}
