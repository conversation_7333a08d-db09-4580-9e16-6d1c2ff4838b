"use client"

import { useEffect, useState } from "react"
import { useRouter, useSearchParams } from "next/navigation"
import { Card, CardContent } from "@/components/ui/card"
import { Loader2 } from "lucide-react"
import { useAuth } from "@/hooks/use-auth"

export function AuthenticateForm() {
  const router = useRouter()
  const searchParams = useSearchParams()
  const { user, isLoading } = useAuth()
  const [error, setError] = useState<string | null>(null)
  const [isAuthenticating, setIsAuthenticating] = useState(true)

  useEffect(() => {
    // If user is already logged in, redirect to dashboard
    if (!isLoading && user) {
      router.replace("/")
      return
    }

    // Check for token in URL - Stytch uses different parameter names depending on the flow
    const token = searchParams.get("token") || 
                  searchParams.get("stytch_token") || 
                  searchParams.get("stytch_token_magic_links")
    const tokenType = searchParams.get("stytch_token_type") || 
                      (searchParams.get("stytch_token_magic_links") ? "magic_links" : 
                       searchParams.get("stytch_token") ? "discovery" : undefined)
    
    if (!token) {
      setIsAuthenticating(false)
      return
    }

    // Authenticate the token
    async function authenticateToken() {
      try {
        // Check if this is a discovery token
        const isDiscoveryToken = tokenType === "discovery"
        
        console.log("Authenticating token:", { token, tokenType, isDiscoveryToken })
        
        // Call the API to authenticate the token
        const response = await fetch("/api/auth/authenticate", {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({ 
            token,
            token_type: tokenType
          }),
        })

        const data = await response.json()
        console.log("Authentication response:", data)

        if (!response.ok) {
          throw new Error(data.error || "Authentication failed")
        }

        // Handle discovery flow
        if (isDiscoveryToken && data.intermediateSessionToken) {
          console.log("Handling discovery flow")
          
          // Store the intermediate session token in localStorage for use in organization selection/creation
          localStorage.setItem("stytch_intermediate_session_token", data.intermediateSessionToken)
          
          // If there are discovered organizations, redirect to organization selection
          if (data.discoveredOrganizations && data.discoveredOrganizations.length > 0) {
            console.log("Organizations found, redirecting to organization selection")
            
            // Store the discovered organizations in localStorage
            localStorage.setItem(
              "stytch_discovered_organizations", 
              JSON.stringify(data.discoveredOrganizations)
            )
            
            router.replace("/auth/organization/select")
          } else {
            // Otherwise redirect to organization creation
            console.log("No organizations found, redirecting to organization creation")
            router.replace("/auth/organization/create")
          }
          return
        }

        // Redirect to dashboard on success for regular authentication
        console.log("Authentication successful, redirecting to dashboard")
        router.replace("/")
      } catch (err) {
        console.error("Authentication error:", err)
        setError("Failed to authenticate. Please try again.")
        setIsAuthenticating(false)
      }
    }

    // Always authenticate the token in all environments
    authenticateToken()
  }, [isLoading, user, router, searchParams])

  return (
    <Card>
      <CardContent className="pt-6">
        <div className="flex flex-col items-center justify-center py-8">
          {isAuthenticating ? (
            <>
              <Loader2 className="h-8 w-8 animate-spin text-primary mb-4" />
              <p className="text-center font-medium">Authenticating...</p>
              <p className="text-center text-sm text-muted-foreground mt-2">
                Please wait while we verify your identity
              </p>
            </>
          ) : error ? (
            <div className="text-center">
              <p className="text-red-500 font-medium">{error}</p>
              <p className="text-sm text-muted-foreground mt-2">
                Please try logging in again
              </p>
            </div>
          ) : (
            <div className="text-center">
              <p className="font-medium">No authentication token found</p>
              <p className="text-sm text-muted-foreground mt-2">
                Please try logging in again
              </p>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  )
}
