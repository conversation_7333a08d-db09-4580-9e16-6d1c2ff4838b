{"name": "location-management-saas", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "db:generate": "tsx scripts/generate-migrations.ts", "db:migrate": "tsx scripts/run-migrations.ts", "db:seed": "tsx scripts/seed-database.ts", "db:test": "tsx scripts/test-connection.ts", "db:test-connection": "tsx scripts/test-db-connection.ts"}, "dependencies": {"@mapbox/mapbox-gl-geocoder": "^5.0.3", "@neondatabase/serverless": "^1.0.0", "@polar-sh/nextjs": "0.4.0", "@polar-sh/sdk": "0.32.11", "@radix-ui/react-alert-dialog": "^1.1.11", "@radix-ui/react-avatar": "^1.0.3", "@radix-ui/react-checkbox": "^1.2.3", "@radix-ui/react-dialog": "^1.0.5", "@radix-ui/react-dropdown-menu": "^2.0.6", "@radix-ui/react-label": "^2.1.4", "@radix-ui/react-popover": "^1.1.11", "@radix-ui/react-radio-group": "^1.3.4", "@radix-ui/react-scroll-area": "^1.0.5", "@radix-ui/react-select": "^2.2.2", "@radix-ui/react-separator": "^1.1.4", "@radix-ui/react-slider": "^1.3.2", "@radix-ui/react-slot": "^1.0.2", "@radix-ui/react-tabs": "^1.0.4", "@stytch/nextjs": "^21.4.4", "@stytch/vanilla-js": "^5.22.6", "@tanstack/react-query": "^5.74.7", "@tanstack/react-table": "^8.21.3", "@uploadthing/react": "^7.3.0", "axios": "^1.9.0", "class-variance-authority": "^0.7.0", "clsx": "^2.1.0", "cmdk": "^0.2.1", "date-fns": "^3.3.1", "dotenv": "^16.4.1", "drizzle-orm": "^0.29.3", "drizzle-zod": "^0.5.1", "lucide-react": "^0.323.0", "mapbox-gl": "^3.1.2", "next": "15.3.1", "next-themes": "^0.4.6", "postgres": "^3.4.3", "react": "^19.1.0", "react-day-picker": "^8.9.1", "react-dom": "^19.1.0", "sonner": "^2.0.3", "stytch": "^10.5.0", "tailwind-merge": "^2.2.1", "tailwindcss-animate": "^1.0.7", "uploadthing": "^7.6.0", "zod": "^3.24.3", "zustand": "^5.0.3"}, "devDependencies": {"@tanstack/react-query-devtools": "^5.74.7", "@types/node": "^20", "@types/react": "^19.1.2", "@types/react-dom": "^19.1.2", "@typescript-eslint/parser": "^8.31.0", "autoprefixer": "^10.0.1", "drizzle-kit": "^0.20.14", "eslint": "^9.25.1", "eslint-config-next": "15.3.1", "postcss": "^8", "tailwindcss": "^3.3.0", "tsx": "^4.7.0", "typescript": "^5"}}