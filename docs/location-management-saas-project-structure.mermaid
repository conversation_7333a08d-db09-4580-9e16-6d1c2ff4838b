graph TD
    Root["Project Root"]
    
    %% Main directories
    App["app/"]
    Components["components/"]
    Modules["modules/"]
    Lib["lib/"]
    Public["public/"]
    Hooks["hooks/"]
    Providers["providers/"]
    Scripts["scripts/"]
    Drizzle["drizzle/"]
    Docs["docs/"]

    %% App subdirectories
    AppDashboard["app/(dashboard)/"]
    AppOrganizations["app/organizations/"]
    AppOrgSlug["app/organizations/[organizationSlug]/"]
    AppAuthPages["app/auth/"]
    AppAPI["app/api/"]
    AppAPIOrgs["app/api/organizations/"]
    AppAPIOrgId["app/api/organizations/[organizationId]/"]
    AppProviders["app/providers/"]
    
    %% Component subdirectories
    CompDashboard["components/dashboard/"]
    CompProjects["components/projects/"]
    CompLocations["components/locations/"]
    CompDocuments["components/documents/"]
    CompMaps["components/maps/"]
    CompCalendar["components/calendar/"]
    CompTeam["components/team/"]
    CompTasks["components/tasks/"]
    CompSettings["components/settings/"]
    CompUI["components/ui/"]
    CompShared["components/shared/"]
    
    %% Module subdirectories
    ModAuth["modules/auth/"]
    ModProject["modules/project/"]
    ModLocation["modules/location/"]
    ModOrganization["modules/organization/"]
    ModMap["modules/map/"]
    ModScene["modules/scene/"]
    ModTask["modules/task/"]
    ModDocument["modules/document/"]
    ModUser["modules/user/"]
    ModShared["modules/shared/"]
    
    %% Connect main directories to root
    Root --> App
    Root --> Components
    Root --> Modules
    Root --> Lib
    Root --> Public
    Root --> Hooks
    Root --> Providers
    Root --> Scripts
    Root --> Drizzle
    Root --> Docs
    
    %% Connect app subdirectories
    App --> AppDashboard["(dashboard) Layout"]
    App --> AppOrganizations
    App --> AppAuthPages["Auth Pages"]
    App --> AppAPI
    App --> AppProviders["Client Providers"]
    AppOrganizations --> AppOrgSlug["Org-Specific Pages"]
    AppAPI --> AppAPIOrgs
    AppAPIOrgs --> AppAPIOrgId["Org-Specific APIs"]
    
    %% Connect component subdirectories
    Components --> CompDashboard
    Components --> CompProjects
    Components --> CompLocations
    Components --> CompDocuments
    Components --> CompMaps
    Components --> CompCalendar
    Components --> CompTeam
    Components --> CompTasks
    Components --> CompSettings
    Components --> CompUI
    Components --> CompShared
    
    %% Connect module subdirectories
    Modules --> ModAuth
    Modules --> ModProject
    Modules --> ModLocation
    Modules --> ModOrganization
    Modules --> ModMap
    Modules --> ModScene
    Modules --> ModTask
    Modules --> ModDocument
    Modules --> ModUser
    Modules --> ModShared
    
    %% Organization Pages (under app/organizations/[organizationSlug]/)
    OrgDashboardPage["page.tsx (Org Dashboard)"]
    OrgProjectsPage["projects/page.tsx"]
    OrgProjectDetailPage["projects/[projectId]/page.tsx"]
    OrgLocationsPage["locations/page.tsx"]
    OrgLocationDetailPage["locations/[locationId]/page.tsx"]
    OrgMapPage["map/page.tsx"]
    OrgDocumentsPage["documents/page.tsx"]
    OrgCalendarPage["calendar/page.tsx"]
    OrgTeamPage["team/page.tsx"]
    OrgSettingsPage["settings/page.tsx"]

    AppOrgSlug --> OrgDashboardPage
    AppOrgSlug --> OrgProjectsPage
    AppOrgSlug --> OrgProjectDetailPage
    AppOrgSlug --> OrgLocationsPage
    AppOrgSlug --> OrgLocationDetailPage
    AppOrgSlug --> OrgMapPage
    AppOrgSlug --> OrgDocumentsPage
    AppOrgSlug --> OrgCalendarPage
    AppOrgSlug --> OrgTeamPage
    AppOrgSlug --> OrgSettingsPage
    
    %% Project components
    ProjectGrid["project-grid.tsx"]
    ProjectList["project-list.tsx"]
    ProjectCalendar["project-calendar.tsx"]
    ProjectLocations["project-locations.tsx"]
    ProjectTeam["project-team.tsx"]
    ProjectDocuments["project-documents.tsx"]
    ProjectTimeline["project-timeline.tsx"]
    ProjectBudget["project-budget.tsx"]
    ProjectTasks["project-tasks.tsx"]
    ProjectNotes["project-notes.tsx"]
    
    CompProjects --> ProjectGrid
    CompProjects --> ProjectList
    CompProjects --> ProjectCalendar
    CompProjects --> ProjectLocations
    CompProjects --> ProjectTeam
    CompProjects --> ProjectDocuments
    CompProjects --> ProjectTimeline
    CompProjects --> ProjectBudget
    CompProjects --> ProjectTasks
    CompProjects --> ProjectNotes
    
    %% Location components
    LocationGrid["location-grid.tsx"]
    LocationList["location-list.tsx"]
    LocationMap["location-map.tsx"]
    LocationGallery["location-gallery.tsx"]
    LocationDocuments["location-documents.tsx"]
    LocationNotes["location-notes.tsx"]
    
    CompLocations --> LocationGrid
    CompLocations --> LocationList
    CompLocations --> LocationMap
    CompLocations --> LocationGallery
    CompLocations --> LocationDocuments
    CompLocations --> LocationNotes
    
    %% Dashboard components
    DashboardShell["dashboard-shell.tsx"]
    DashboardHeader["dashboard-header.tsx"]
    DashboardNav["dashboard-nav.tsx"]
    StatsCards["stats-cards.tsx"]
    ActivityFeed["activity-feed.tsx"]
    
    CompDashboard --> DashboardShell
    CompDashboard --> DashboardHeader
    CompDashboard --> DashboardNav
    CompDashboard --> StatsCards
    CompDashboard --> ActivityFeed
    
    %% UI components
    Button["button.tsx"]
    Card["card.tsx"]
    Tabs["tabs.tsx"]
    Dialog["dialog.tsx"]
    Sidebar["sidebar.tsx"]
    
    CompUI --> Button
    CompUI --> Card
    CompUI --> Tabs
    CompUI --> Dialog
    CompUI --> Sidebar
    
    %% Module files
    ProjectModel["model.ts"]
    ProjectSchema["schema.ts"]
    ProjectService["service.ts"]
    ProjectActions["actions.ts"]
    
    ModProject --> ProjectModel
    ModProject --> ProjectSchema
    ModProject --> ProjectService
    ModProject --> ProjectActions
    
    LocationModel["model.ts"]
    LocationSchema["schema.ts"]
    LocationService["service.ts"]
    LocationActions["actions.ts"]
    
    ModLocation --> LocationModel
    ModLocation --> LocationSchema
    ModLocation --> LocationService
    ModLocation --> LocationActions
    
    %% API routes (under app/api/organizations/[organizationId]/)
    OrgProjectsAPI["projects/route.ts"]
    OrgProjectDetailAPI["projects/[projectId]/route.ts"]
    OrgLocationsAPI["locations/route.ts"]
    OrgLocationDetailAPI["locations/[locationId]/route.ts"]
    OrgMapAPI["map/route.ts"]
    OrgMembersAPI["members/route.ts"]

    AppAPIOrgId --> OrgProjectsAPI
    AppAPIOrgId --> OrgProjectDetailAPI
    AppAPIOrgId --> OrgLocationsAPI
    AppAPIOrgId --> OrgLocationDetailAPI
    AppAPIOrgId --> OrgMapAPI
    AppAPIOrgId --> OrgMembersAPI
    
    %% Key relationships (Page <--> Component)
    OrgProjectDetailPage --> CompProjects
    OrgLocationDetailPage --> CompLocations
    OrgDashboardPage --> CompDashboard
    OrgProjectsPage --> CompProjects
    OrgLocationsPage --> CompLocations
    OrgMapPage --> CompMaps
    OrgDocumentsPage --> CompDocuments
    OrgCalendarPage --> CompCalendar
    OrgTeamPage --> CompTeam
    OrgSettingsPage --> CompSettings

    %% Component relationships (Example)
    CompDashboard --> DashboardShell["dashboard-shell.tsx"]
    CompDashboard --> DashboardHeader["dashboard-header.tsx"]
    CompDashboard --> DashboardNav["dashboard-nav.tsx"]
    CompDashboard --> StatsCards["stats-cards.tsx"]
    CompDashboard --> ActivityFeed["activity-feed.tsx"]

    CompProjects --> ProjectGrid["project-grid.tsx"]
    CompProjects --> ProjectList["project-list.tsx"]
    CompProjects --> ProjectCalendar["project-calendar.tsx"]
    CompProjects --> ProjectLocations["project-locations.tsx"]
    CompProjects --> ProjectTeam["project-team.tsx"]
    CompProjects --> ProjectDocuments["project-documents.tsx"]
    CompProjects --> ProjectTimeline["project-timeline.tsx"]
    CompProjects --> ProjectBudget["project-budget.tsx"]
    CompProjects --> ProjectTasks["project-tasks.tsx"]
    CompProjects --> ProjectNotes["project-notes.tsx"]

    CompLocations --> LocationGrid["location-grid.tsx"]
    CompLocations --> LocationList["location-list.tsx"]
    CompLocations --> LocationMap["location-map.tsx"]
    CompLocations --> LocationGallery["location-gallery.tsx"]
    CompLocations --> LocationDocuments["location-documents.tsx"]
    CompLocations --> LocationNotes["location-notes.tsx"]

    CompUI --> Button["button.tsx"]
    CompUI --> Card["card.tsx"]
    CompUI --> Tabs["tabs.tsx"]
    CompUI --> Dialog["dialog.tsx"]
    CompUI --> Sidebar["sidebar.tsx"]

    %% Module relationships (Example)
    ModProject --> ProjectModel["model.ts"]
    ModProject --> ProjectSchema["schema.ts"]
    ModProject --> ProjectService["service.ts"]
    ModProject --> ProjectActions["actions.ts"]

    ModLocation --> LocationModel["model.ts"]
    ModLocation --> LocationSchema["schema.ts"]
    ModLocation --> LocationService["service.ts"]
    ModLocation --> LocationActions["actions.ts"]
