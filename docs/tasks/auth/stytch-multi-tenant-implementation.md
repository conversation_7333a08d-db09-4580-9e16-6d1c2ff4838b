# Stytch Multi-Tenant Authentication Implementation Checklist

## Overview

This document provides a detailed, atomic-level checklist for implementing multi-tenant authentication using Stytch B2B in the Scene-o-matic application. It's designed for AI coding assistants to follow when implementing the authentication system.

## Prerequisites

- Stytch B2B account with API credentials
- Next.js application with existing authentication structure
- Database with user and organization schemas

## AI Assistant Guidelines

When implementing this authentication system, follow these best practices:

1. **Incremental Implementation**: Implement one step at a time, testing each component before moving to the next.
2. **Error Handling**: Add comprehensive error handling for all API calls and user interactions.
3. **Type Safety**: Use TypeScript types for all components and functions.
4. **Documentation**: Add comments to explain complex logic or integration points.
5. **Security First**: Prioritize security best practices in all authentication-related code.
6. **Backward Compatibility**: Maintain backward compatibility where possible during the transition.
7. **Testing**: After implementing each major component, suggest appropriate tests.

## Implementation Checklist

### Phase 1: Setup and Configuration

#### 1.1 Create Stytch B2B Client

- [x] Create a new file `lib/stytch-b2b.ts` for the B2B client
- [x] Implement the B2B client initialization function:
  ```typescript
  import { B2BClient } from "stytch"

  let stytchB2BClient: B2BClient | null = null

  export function getStytchB2BClient() {
    if (!stytchB2BClient) {
      const projectId = process.env.STYTCH_B2B_PROJECT_ID
      const secret = process.env.STYTCH_B2B_SECRET

      if (!projectId || !secret) {
        throw new Error(
          "Missing Stytch B2B credentials. Please set STYTCH_B2B_PROJECT_ID and STYTCH_B2B_SECRET environment variables.",
        )
      }

      stytchB2BClient = new B2BClient({
        project_id: projectId,
        secret,
      })
    }

    return stytchB2BClient
  }
  ```
- [x] Update environment variables in `.env.local` and `.env.example`

#### 1.2 Update Database Schema

- [x] Update the user schema to include Stytch user ID:
  ```typescript
  // modules/user/schema.ts
  stytchUserId: varchar("stytch_user_id", { length: 255 }).unique(),
  ```
- [x] Update the organization schema to include Stytch organization ID:
  ```typescript
  // modules/organization/schema.ts
  stytchOrganizationId: varchar("stytch_organization_id", { length: 255 }).unique(),
  ```
- [x] Create a migration for these schema changes
- [x] Run the migration to update the database

### Phase 2: Organization Management

#### 2.1 Create Organization Management Functions

- [x] Create a new file `modules/organization/stytch.ts`
- [x] Implement the following functions:
  - `createStytchOrganization(name: string, slug: string, allowedDomains?: string[])`
  - `getStytchOrganization(organizationId: string)`
  - `updateStytchOrganization(organizationId: string, data: object)`
  - `deleteStytchOrganization(organizationId: string)`
  - `getStytchOrganizations()`
  - `inviteUserToOrganization(organizationId: string, email: string, role: string)`
  - `getStytchOrganizationMembers(organizationId: string)`
  - `removeUserFromOrganization(organizationId: string, userId: string)`

#### 2.2 Update Organization Service

- [x] Update `modules/organization/service.ts` to integrate with Stytch B2B
- [x] Modify `createOrganization` function to create an organization in Stytch
- [x] Modify `updateOrganization` function to update the organization in Stytch
- [x] Modify `deleteOrganization` function to delete the organization in Stytch
- [x] Add `getOrganizationByStytchId` function
- [x] Update `inviteToOrganization` function to use Stytch for invitations

### Phase 3: Authentication Functions

#### 3.1 Update Authentication Functions

- [x] Create a new file `modules/auth/stytch-b2b.ts`
- [x] Implement the following functions:
  - `createMagicLink(email: string, redirectUrl: string, organizationId?: string)`
  - `authenticateMagicLink(token: string)`
  - `revokeSession(sessionToken: string)`
  - `verifySession(sessionToken: string)`

#### 3.2 Update Auth Actions

- [x] Update `modules/auth/actions.ts` to use the new B2B authentication functions
- [x] Modify the `login` function to accept an optional `organizationId` parameter
- [x] Update the `logout` function to revoke the Stytch session

### Phase 4: API Routes

#### 4.1 Create Organization API Routes

- [ ] Create `app/api/organizations/route.ts` for listing and creating organizations
- [ ] Create `app/api/organizations/[id]/route.ts` for getting, updating, and deleting organizations
- [ ] Create `app/api/organizations/[id]/members/route.ts` for managing organization members
- [ ] Create `app/api/organizations/[id]/switch/route.ts` for switching between organizations

#### 4.2 Update Auth API Routes

- [ ] Update `app/api/auth/route.ts` to handle organization-specific login
- [ ] Update `app/api/auth/callback/route.ts` to handle Stytch B2B authentication
- [ ] Update `app/api/auth/me/route.ts` to include organization information
- [ ] Update `app/api/auth/logout/route.ts` to revoke the Stytch session

### Phase 5: Context Providers

#### 5.1 Update Auth Provider

- [ ] Update `providers/auth-provider.tsx` to include organization information
- [ ] Add `currentOrganization` to the auth context
- [ ] Update the `login` function to accept an optional `organizationId` parameter
- [ ] Update the `useEffect` hook to fetch the current organization

#### 5.2 Update Organization Provider

- [ ] Update `providers/organization-provider.tsx` to integrate with the auth provider
- [ ] Add `switchOrganization` function to the organization context
- [ ] Update the `useEffect` hook to fetch organizations from the API

### Phase 6: UI Components

#### 6.1 Create Organization Selection Page

- [ ] Create `app/auth/organization/select/page.tsx` for selecting an organization
- [ ] Implement UI for displaying available organizations
- [ ] Add functionality to select an organization or create a new one

#### 6.2 Create Organization Creation Page

- [ ] Create `app/auth/organization/create/page.tsx` for creating a new organization
- [ ] Implement form for organization name, slug, and other details
- [ ] Add validation and submission functionality

#### 6.3 Update Organization Switcher Component

- [ ] Update `components/auth/organization-switcher.tsx` to use the new organization switching functionality
- [ ] Implement UI for displaying available organizations
- [ ] Add functionality to switch between organizations

#### 6.4 Update Login Page

- [ ] Update `app/auth/login/page.tsx` to support organization-specific login
- [ ] Add support for login with organization ID from URL parameter

### Phase 7: Middleware and Access Control

#### 7.1 Update Middleware

- [ ] Update `middleware.ts` to check for organization access
- [ ] Add logic to redirect to organization selection if no organization is selected
- [ ] Add logic to verify organization membership

#### 7.2 Implement Role-Based Access Control

- [ ] Create a new file `lib/utils/rbac.ts` for role-based access control functions
- [ ] Implement functions to check user roles and permissions
- [ ] Create UI components for conditional rendering based on user roles

### Phase 8: Testing and Validation

#### 8.1 Test Authentication Flow

- [ ] Test user registration and login
- [ ] Test organization creation
- [ ] Test organization invitation and acceptance
- [ ] Test organization switching
- [ ] Test session persistence and expiration

#### 8.2 Test Access Control

- [ ] Test role-based access control
- [ ] Test organization-specific access
- [ ] Test unauthorized access handling

## Implementation Notes

### Environment Variables

Required environment variables:
```
STYTCH_B2B_PROJECT_ID=your_project_id
STYTCH_B2B_SECRET=your_secret
NEXT_PUBLIC_APP_URL=your_app_url
```

### API Response Formats

Standardize API responses with the following format:
```typescript
// Success response
{
  success: true,
  data: { ... }
}

// Error response
{
  success: false,
  error: "Error message"
}
```

### Session Management

Store session information in HTTP-only cookies:
- `session_token`: The Stytch session token
- `current_organization`: The current organization ID

### Error Handling Strategy

1. Client-side errors: Display user-friendly error messages
2. API errors: Log detailed errors server-side, return simplified messages to the client
3. Authentication errors: Redirect to login page with appropriate error message

## Conclusion

This checklist provides a comprehensive guide for implementing multi-tenant authentication using Stytch B2B. By following these steps in order, you'll create a secure, scalable authentication system that supports multiple organizations and role-based access control.

Remember to test each component thoroughly before moving to the next step, and maintain comprehensive error handling throughout the implementation.

## Updates

### 2025-04-23
- Completed Phase 1.1: Created Stytch B2B client in `lib/stytch-b2b.ts`
- Created `.env.example` and `.env.local` files with required environment variables for Stytch B2B
- Completed Phase 1.2: Updated database schema to include Stytch user ID and organization ID
- Created and ran migration to update the database
- Note: Using the same Stytch API credentials for both regular and B2B functionality since separate B2B credentials were not available
- Completed Phase 2.1: Created organization management functions in `modules/organization/stytch.ts`
- Completed Phase 2.2: Updated organization service to integrate with Stytch B2B
- Completed Phase 3.1: Created authentication functions in `modules/auth/stytch-b2b.ts`
- Completed Phase 3.2: Updated auth actions to use the new B2B authentication functions
- Note: Due to TypeScript errors with the cookies() function in Next.js, we've simplified the logout implementation for now
