# Data Fetching and Caching Strategy

## Overview

This document outlines a comprehensive strategy for optimizing data fetching and implementing effective caching mechanisms in the Scene-o-matic application. The current implementation makes redundant API calls and lacks proper caching, leading to performance issues, especially on the map page.

## Current Issues

1. **Redundant API Calls**: Multiple identical requests to the same endpoints
2. **Sequential Loading**: Resources loaded one after another instead of in parallel
3. **No Client-Side Caching**: Data is fetched from scratch on each component mount
4. **Inefficient Data Fetching Patterns**: Components fetch their own data independently
5. **No Stale-While-Revalidate Strategy**: No mechanism to show stale data while fetching fresh data

## Implementation Strategy

We'll implement a robust data fetching and caching strategy using React Query and SWR, along with browser caching mechanisms and localStorage for persistent data.

### 1. React Query Implementation

[React Query](https://tanstack.com/query/latest) provides a powerful data fetching and caching solution that will help eliminate redundant API calls and implement proper caching.

#### Installation and Setup

```bash
pnpm add @tanstack/react-query
```

#### Query Client Provider Setup

```typescript
// app/providers/query-provider.tsx
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { ReactQueryDevtools } from '@tanstack/react-query-devtools';
import { useState } from 'react';

export function QueryProvider({ children }: { children: React.ReactNode }) {
  const [queryClient] = useState(
    () =>
      new QueryClient({
        defaultOptions: {
          queries: {
            staleTime: 60 * 1000, // 1 minute
            cacheTime: 5 * 60 * 1000, // 5 minutes
            refetchOnWindowFocus: false,
            retry: 1,
          },
        },
      })
  );

  return (
    <QueryClientProvider client={queryClient}>
      {children}
      {process.env.NODE_ENV === 'development' && <ReactQueryDevtools />}
    </QueryClientProvider>
  );
}
```

#### Integration with AllClientProviders

```typescript
// app/providers/AllClientProviders.tsx
import { QueryProvider } from './query-provider';

export function AllClientProviders({ children }: { children: React.ReactNode }) {
  return (
    <QueryProvider>
      {/* Other providers */}
      {children}
    </QueryProvider>
  );
}
```

### 2. Custom Hooks for Data Fetching

Create reusable hooks for common data fetching operations to ensure consistency and reduce duplication.

#### User and Authentication Data

```typescript
// hooks/use-auth-data.ts
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';

// Query keys for better cache management
export const authKeys = {
  all: ['auth'] as const,
  user: () => [...authKeys.all, 'user'] as const,
  session: () => [...authKeys.all, 'session'] as const,
};

export function useUser() {
  return useQuery({
    queryKey: authKeys.user(),
    queryFn: async () => {
      const res = await fetch('/api/auth/me');
      if (!res.ok) throw new Error('Failed to fetch user');
      return res.json();
    },
    // Don't refetch on window focus for user data
    refetchOnWindowFocus: false,
    // Keep cached for 10 minutes
    staleTime: 10 * 60 * 1000,
  });
}

export function useLogout() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: async () => {
      const res = await fetch('/api/auth/logout', { method: 'POST' });
      if (!res.ok) throw new Error('Failed to logout');
      return res.json();
    },
    onSuccess: () => {
      // Invalidate user and session queries on logout
      queryClient.invalidateQueries({ queryKey: authKeys.all });
    },
  });
}
```

#### Organization Data

```typescript
// hooks/use-organization-data.ts
import { useQuery } from '@tanstack/react-query';

export const organizationKeys = {
  all: ['organizations'] as const,
  lists: () => [...organizationKeys.all, 'list'] as const,
  detail: (slug: string) => [...organizationKeys.all, 'detail', slug] as const,
  members: (slug: string) => [...organizationKeys.all, 'members', slug] as const,
};

export function useOrganizations() {
  return useQuery({
    queryKey: organizationKeys.lists(),
    queryFn: async () => {
      const res = await fetch('/api/organizations');
      if (!res.ok) throw new Error('Failed to fetch organizations');
      return res.json();
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

export function useOrganization(slug: string) {
  return useQuery({
    queryKey: organizationKeys.detail(slug),
    queryFn: async () => {
      const res = await fetch(`/api/organizations/${slug}`);
      if (!res.ok) throw new Error(`Failed to fetch organization: ${slug}`);
      return res.json();
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
    enabled: !!slug, // Only run if slug is provided
  });
}

export function useOrganizationMembers(slug: string) {
  return useQuery({
    queryKey: organizationKeys.members(slug),
    queryFn: async () => {
      const res = await fetch(`/api/organizations/${slug}/members`);
      if (!res.ok) throw new Error(`Failed to fetch organization members: ${slug}`);
      return res.json();
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
    enabled: !!slug, // Only run if slug is provided
  });
}
```

#### Map Data

```typescript
// hooks/use-map-data.ts
import { useQuery, useInfiniteQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { useCallback } from 'react';

export const mapKeys = {
  all: ['map'] as const,
  locations: (orgSlug: string, filters?: any) => 
    [...mapKeys.all, 'locations', orgSlug, ...(filters ? [JSON.stringify(filters)] : [])] as const,
  views: (orgSlug: string) => [...mapKeys.all, 'views', orgSlug] as const,
  view: (viewId: string) => [...mapKeys.all, 'view', viewId] as const,
};

export function useMapLocations(orgSlug: string, filters?: any, pageSize = 50) {
  return useInfiniteQuery({
    queryKey: mapKeys.locations(orgSlug, filters),
    queryFn: async ({ pageParam = 1 }) => {
      const queryParams = new URLSearchParams({
        page: pageParam.toString(),
        pageSize: pageSize.toString(),
        ...(filters && Object.fromEntries(
          Object.entries(filters).filter(([_, v]) => v !== undefined && v !== null)
        )),
      });
      
      const res = await fetch(`/api/map?${queryParams}`);
      if (!res.ok) throw new Error('Failed to fetch map locations');
      return res.json();
    },
    getNextPageParam: (lastPage) => {
      const { currentPage, totalPages } = lastPage.pagination;
      return currentPage < totalPages ? currentPage + 1 : undefined;
    },
    staleTime: 2 * 60 * 1000, // 2 minutes
    enabled: !!orgSlug,
  });
}

export function useMapViews(orgSlug: string) {
  return useQuery({
    queryKey: mapKeys.views(orgSlug),
    queryFn: async () => {
      const res = await fetch(`/api/map/views`);
      if (!res.ok) throw new Error('Failed to fetch map views');
      return res.json();
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
    enabled: !!orgSlug,
  });
}

export function useSaveMapView() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: async (viewData: any) => {
      const res = await fetch('/api/map/views', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(viewData),
      });
      
      if (!res.ok) throw new Error('Failed to save map view');
      return res.json();
    },
    onSuccess: (data, variables) => {
      // Invalidate map views query to refresh the list
      queryClient.invalidateQueries({ 
        queryKey: mapKeys.views(variables.organizationSlug) 
      });
    },
  });
}
```

#### Notification Data

```typescript
// hooks/use-notification-data.ts
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';

export const notificationKeys = {
  all: ['notifications'] as const,
  list: (page?: number) => 
    [...notificationKeys.all, 'list', ...(page ? [page.toString()] : [])] as const,
  count: () => [...notificationKeys.all, 'count'] as const,
  preferences: () => [...notificationKeys.all, 'preferences'] as const,
};

export function useNotifications(page = 1, pageSize = 10) {
  return useQuery({
    queryKey: notificationKeys.list(page),
    queryFn: async () => {
      const res = await fetch(`/api/notifications?page=${page}&pageSize=${pageSize}`);
      if (!res.ok) throw new Error('Failed to fetch notifications');
      return res.json();
    },
    staleTime: 1 * 60 * 1000, // 1 minute
  });
}

export function useNotificationCount() {
  return useQuery({
    queryKey: notificationKeys.count(),
    queryFn: async () => {
      const res = await fetch('/api/notifications/count');
      if (!res.ok) throw new Error('Failed to fetch notification count');
      return res.json();
    },
    // Shorter stale time for count as it changes frequently
    staleTime: 30 * 1000, // 30 seconds
    refetchInterval: 2 * 60 * 1000, // Refetch every 2 minutes
  });
}

export function useNotificationPreferences() {
  return useQuery({
    queryKey: notificationKeys.preferences(),
    queryFn: async () => {
      const res = await fetch('/api/notifications/preferences');
      if (!res.ok) throw new Error('Failed to fetch notification preferences');
      return res.json();
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

export function useMarkNotificationAsRead() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: async (notificationId: string) => {
      const res = await fetch(`/api/notifications/${notificationId}/read`, {
        method: 'POST',
      });
      
      if (!res.ok) throw new Error('Failed to mark notification as read');
      return res.json();
    },
    onSuccess: () => {
      // Invalidate notifications and count queries
      queryClient.invalidateQueries({ queryKey: notificationKeys.list() });
      queryClient.invalidateQueries({ queryKey: notificationKeys.count() });
    },
  });
}

export function useMarkAllNotificationsAsRead() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: async () => {
      const res = await fetch('/api/notifications/read-all', {
        method: 'POST',
      });
      
      if (!res.ok) throw new Error('Failed to mark all notifications as read');
      return res.json();
    },
    onSuccess: () => {
      // Invalidate notifications and count queries
      queryClient.invalidateQueries({ queryKey: notificationKeys.list() });
      // Optimistically update count to zero
      queryClient.setQueryData(notificationKeys.count(), { count: 0 });
    },
  });
}
```

### 3. Implementing SWR for Simpler Data Fetching

For some components, SWR might be a simpler alternative to React Query. Here's how to implement it:

```typescript
// hooks/use-swr-data.ts
import useSWR from 'swr';

const fetcher = (url: string) => fetch(url).then((res) => res.json());

export function useMapboxToken() {
  return useSWR('/api/mapbox-token', fetcher, {
    revalidateOnFocus: false,
    revalidateIfStale: false,
    revalidateOnReconnect: false,
    // Cache token for 24 hours
    dedupingInterval: 24 * 60 * 60 * 1000,
  });
}

export function useFavorites() {
  return useSWR('/api/favorites', fetcher, {
    revalidateOnFocus: false,
    // Cache for 5 minutes
    dedupingInterval: 5 * 60 * 1000,
  });
}
```

### 4. Browser Cache Headers

Update API routes to include proper cache headers for browser-level caching:

```typescript
// Example API route with cache headers
// app/api/mapbox-token/route.ts
export async function GET() {
  const token = process.env.MAPBOX_TOKEN;
  
  return new Response(
    JSON.stringify({ token }),
    {
      status: 200,
      headers: {
        'Content-Type': 'application/json',
        // Cache for 24 hours
        'Cache-Control': 'public, max-age=86400, s-maxage=86400',
      },
    }
  );
}

// app/api/map/views/route.ts
export async function GET() {
  const userId = await getUserIdFromSession();
  if (!userId) {
    return new Response(JSON.stringify({ error: 'Unauthorized' }), {
      status: 401,
    });
  }

  const views = await getMapViewsForUser(userId);
  
  return new Response(
    JSON.stringify({ views }),
    {
      status: 200,
      headers: {
        'Content-Type': 'application/json',
        // Private cache for 5 minutes
        'Cache-Control': 'private, max-age=300',
      },
    }
  );
}
```

### 5. LocalStorage for Persistent Data

Implement localStorage for data that should persist between sessions:

```typescript
// lib/utils/storage.ts
export const storage = {
  get: <T>(key: string, defaultValue: T): T => {
    if (typeof window === 'undefined') return defaultValue;
    
    try {
      const item = window.localStorage.getItem(key);
      return item ? (JSON.parse(item) as T) : defaultValue;
    } catch (error) {
      console.error(`Error getting item ${key} from localStorage:`, error);
      return defaultValue;
    }
  },
  
  set: <T>(key: string, value: T): void => {
    if (typeof window === 'undefined') return;
    
    try {
      window.localStorage.setItem(key, JSON.stringify(value));
    } catch (error) {
      console.error(`Error setting item ${key} in localStorage:`, error);
    }
  },
  
  remove: (key: string): void => {
    if (typeof window === 'undefined') return;
    
    try {
      window.localStorage.removeItem(key);
    } catch (error) {
      console.error(`Error removing item ${key} from localStorage:`, error);
    }
  },
};

// Example usage for map preferences
export const mapStorage = {
  getLastView: (orgSlug: string) => 
    storage.get(`map_last_view_${orgSlug}`, null),
  
  setLastView: (orgSlug: string, view: any) => 
    storage.set(`map_last_view_${orgSlug}`, view),
  
  getFilters: (orgSlug: string) => 
    storage.get(`map_filters_${orgSlug}`, {}),
  
  setFilters: (orgSlug: string, filters: any) => 
    storage.set(`map_filters_${orgSlug}`, filters),
};
```

### 6. Parallel Data Loading

Implement parallel data loading for the map page:

```typescript
// app/(dashboard)/organizations/[organizationSlug]/map/page.tsx
import { Suspense } from 'react';
import { dehydrate, HydrationBoundary, QueryClient } from '@tanstack/react-query';
import { organizationKeys } from 'hooks/use-organization-data';
import { mapKeys } from 'hooks/use-map-data';
import { notificationKeys } from 'hooks/use-notification-data';

// Server component with parallel data loading
export default async function MapPage({ params }: { params: { organizationSlug: string } }) {
  const { organizationSlug } = params;
  
  // Create a new QueryClient for server
  const queryClient = new QueryClient();
  
  // Prefetch data in parallel
  await Promise.all([
    // Prefetch organization data
    queryClient.prefetchQuery({
      queryKey: organizationKeys.detail(organizationSlug),
      queryFn: async () => {
        const res = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/api/organizations/${organizationSlug}`);
        if (!res.ok) throw new Error(`Failed to fetch organization: ${organizationSlug}`);
        return res.json();
      },
    }),
    
    // Prefetch initial map locations
    queryClient.prefetchQuery({
      queryKey: mapKeys.locations(organizationSlug),
      queryFn: async () => {
        const res = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/api/map?page=1&pageSize=50`);
        if (!res.ok) throw new Error('Failed to fetch map locations');
        return res.json();
      },
    }),
    
    // Prefetch map views
    queryClient.prefetchQuery({
      queryKey: mapKeys.views(organizationSlug),
      queryFn: async () => {
        const res = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/api/map/views`);
        if (!res.ok) throw new Error('Failed to fetch map views');
        return res.json();
      },
    }),
  ]);
  
  return (
    <HydrationBoundary state={dehydrate(queryClient)}>
      <Suspense fallback={<MapPageSkeleton />}>
        <MapPageClient organizationSlug={organizationSlug} />
      </Suspense>
    </HydrationBoundary>
  );
}

// Client component that uses the prefetched data
'use client';

import { useOrganization } from 'hooks/use-organization-data';
import { useMapLocations, useMapViews } from 'hooks/use-map-data';

function MapPageClient({ organizationSlug }: { organizationSlug: string }) {
  // These queries will use the prefetched data
  const { data: organization } = useOrganization(organizationSlug);
  const { data: locationsData } = useMapLocations(organizationSlug);
  const { data: viewsData } = useMapViews(organizationSlug);
  
  // Render with prefetched data
  return (
    // JSX implementation
  );
}
```

### 7. Optimistic Updates

Implement optimistic updates for better user experience:

```typescript
// Example for favorites
export function useFavoriteToggle() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: async ({ itemId, isFavorite }: { itemId: string; isFavorite: boolean }) => {
      const res = await fetch('/api/favorites', {
        method: isFavorite ? 'DELETE' : 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ itemId }),
      });
      
      if (!res.ok) throw new Error('Failed to toggle favorite');
      return res.json();
    },
    onMutate: async ({ itemId, isFavorite }) => {
      // Cancel any outgoing refetches
      await queryClient.cancelQueries({ queryKey: ['favorites'] });
      
      // Snapshot the previous value
      const previousFavorites = queryClient.getQueryData(['favorites']);
      
      // Optimistically update to the new value
      queryClient.setQueryData(['favorites'], (old: any) => {
        if (isFavorite) {
          // Remove from favorites
          return {
            ...old,
            favorites: old.favorites.filter((fav: any) => fav.itemId !== itemId),
          };
        } else {
          // Add to favorites
          return {
            ...old,
            favorites: [...old.favorites, { itemId, createdAt: new Date().toISOString() }],
          };
        }
      });
      
      // Return a context object with the snapshotted value
      return { previousFavorites };
    },
    onError: (err, variables, context) => {
      // If the mutation fails, use the context returned from onMutate to roll back
      if (context?.previousFavorites) {
        queryClient.setQueryData(['favorites'], context.previousFavorites);
      }
    },
    onSettled: () => {
      // Always refetch after error or success to ensure data consistency
      queryClient.invalidateQueries({ queryKey: ['favorites'] });
    },
  });
}
```

## Component Integration Examples

### Map Component with React Query

```typescript
// components/maps/map-view.tsx
'use client';

import { useMapLocations, useMapViews } from 'hooks/use-map-data';
import { useMapboxToken } from 'hooks/use-swr-data';
import { useState, useEffect } from 'react';
import { mapStorage } from 'lib/utils/storage';

export function MapView({ organizationSlug }: { organizationSlug: string }) {
  const [filters, setFilters] = useState(() => 
    mapStorage.getFilters(organizationSlug) || {}
  );
  
  // Fetch data using React Query
  const { data: mapboxData } = useMapboxToken();
  const { 
    data: locationsData,
    fetchNextPage,
    hasNextPage,
    isFetchingNextPage,
    isLoading: isLoadingLocations
  } = useMapLocations(organizationSlug, filters);
  const { data: viewsData, isLoading: isLoadingViews } = useMapViews(organizationSlug);
  
  // Save filters to localStorage when they change
  useEffect(() => {
    mapStorage.setFilters(organizationSlug, filters);
  }, [filters, organizationSlug]);
  
  // Handle filter changes
  const handleFilterChange = (newFilters: any) => {
    setFilters(newFilters);
  };
  
  // Load more locations when scrolling
  const handleLoadMore = () => {
    if (hasNextPage && !isFetchingNextPage) {
      fetchNextPage();
    }
  };
  
  // Show loading state
  if (isLoadingLocations || isLoadingViews) {
    return <MapSkeleton />;
  }
  
  // Render map with data
  return (
    <div>
      {/* Map implementation */}
      <MapControls 
        filters={filters} 
        onFilterChange={handleFilterChange} 
        views={viewsData?.views || []}
      />
      
      <MapLocations 
        locations={locationsData?.pages.flatMap(page => page.locations) || []}
        onLoadMore={handleLoadMore}
        hasMore={hasNextPage}
        isLoading={isFetchingNextPage}
      />
    </div>
  );
}
```

### Notification Component with React Query

```typescript
// components/notifications/notification-dropdown.tsx
'use client';

import { useNotificationCount, useMarkAllNotificationsAsRead } from 'hooks/use-notification-data';
import { memo } from 'react';

export const NotificationDropdown = memo(function NotificationDropdown() {
  const { data, isLoading } = useNotificationCount();
  const markAllAsRead = useMarkAllNotificationsAsRead();
  
  const handleMarkAllAsRead = () => {
    markAllAsRead.mutate();
  };
  
  return (
    <div>
      {isLoading ? (
        <NotificationCountSkeleton />
      ) : (
        <span>{data?.count || 0}</span>
      )}
      
      <button onClick={handleMarkAllAsRead} disabled={markAllAsRead.isPending}>
        Mark all as read
      </button>
    </div>
  );
});
```

## API Route Optimization

### Combined Initial Data Endpoint

Create a combined endpoint for initial map data to reduce multiple requests:

```typescript
// app/api/map/initial-data/route.ts
export async function GET(req: Request) {
  const userId = await getUserIdFromSession();
  if (!userId) {
    return new Response(JSON.stringify({ error: 'Unauthorized' }), {
      status: 401,
    });
  }
  
  const url = new URL(req.url);
  const organizationSlug = url.searchParams.get('organizationSlug');
  
  if (!organizationSlug) {
    return new Response(JSON.stringify({ error: 'Organization slug is required' }), {
      status: 400,
    });
  }
  
  // Fetch all required data in parallel
  const [organization, locations, views, favorites] = await Promise.all([
    getOrganizationBySlug(organizationSlug),
    getMapLocations({ organizationSlug, page: 1, pageSize: 50 }),
    getMapViewsForUser(userId),
    getFavoritesForUser(userId),
  ]);
  
  return new Response(
    JSON.stringify({
      organization,
      locations,
      views,
      favorites,
    }),
    {
      status: 200,
      headers: {
        'Content-Type': 'application/json',
        'Cache-Control': 'private, max-age=60', // Cache for 1 minute
      },
    }
  );
}
```

## Testing and Validation

### Performance Monitoring

```typescript
// lib/utils/performance.ts
export function trackDataFetching() {
  if (typeof window === 'undefined') return;
  
  const fetchStats = {
    totalRequests: 0,
    endpoints: {} as Record<string, number>,
    duplicateRequests: 0,
  };
  
  const seenUrls = new Set<string>();
  const originalFetch = window.fetch;
  
  window.fetch = async function(input, init) {
    const url = typeof input === 'string' ? input : input.url;
    
    // Track total requests
    fetchStats.totalRequests++;
    
    // Track by endpoint
    const endpoint = url.split('?')[0]; // Remove query params
    fetchStats.endpoints[endpoint] = (fetchStats.endpoints[endpoint] || 0) + 1;
    
    // Track duplicate requests in the same session
    if (seenUrls.has(url)) {
      fetchStats.duplicateRequests++;
    } else {
      seenUrls.add(url);
    }
    
    // Log stats every 10 requests
    if (fetchStats.totalRequests % 10 === 0) {
      console.log('Fetch Stats:', fetchStats);
    }
    
    return originalFetch.apply(this, [input, init]);
  };
  
  return fetchStats;
}
```

### Validation Script

```typescript
// scripts/test-data-fetching.ts
import fetch from 'node-fetch';

async function testDataFetching() {
  console.log('Testing data fetching performance...');
  
  // Test combined endpoint
  console.time('Combined endpoint');
  await fetch('http://localhost:3000/api/map/initial-data?organizationSlug=test-org', {
    headers: {
      Cookie: 'session=your-test-session-cookie',
    },
  });
  console.timeEnd('Combined endpoint');
  
  // Test individual endpoints
  console.time('Individual endpoints');
  await Promise.all([
    fetch('http://localhost:3000/api/organizations/test-org', {
      headers: { Cookie: 'session=your-test-session-cookie' },
    }),
    fetch('http://localhost:3000/api/map?page=1&pageSize=50', {
      headers: { Cookie: 'session=your-test-session-cookie' },
    }),
    fetch('http://localhost:3000/api/map/views', {
      headers: { Cookie: 'session=your-test-session-cookie' },
    }),
    fetch('http://localhost:3000/api/favorites', {
      headers: { Cookie: 'session=your-test-session-cookie' },
    }),
  ]);
  console.timeEnd('Individual endpoints');
}

testDataFetching().catch(console.error);
```

## Expected Outcomes

After implementing this data fetching and caching strategy, we expect:

1. **Reduced API Calls**: Elimination of redundant API calls through proper caching
2. **Faster Initial Load**: Improved initial page load through parallel data fetching
3. **Better User Experience**: Smoother UI through optimistic updates and stale-while-revalidate
4. **Reduced Server Load**: Less strain on backend services through client-side caching
5. **Improved Offline Support**: Better handling of network issues through cached data

## Implementation Checklist

- [ ] Set up React Query provider
- [ ] Create custom hooks for data fetching
- [ ] Implement SWR for simpler data fetching cases
- [ ] Add cache headers to API routes
- [ ] Implement localStorage for persistent data
- [ ] Set up parallel data loading
- [ ] Implement optimistic updates
- [ ] Create combined initial data endpoint
- [ ] Add performance monitoring
- [ ] Test and validate the implementation

This data fetching and caching strategy addresses the core performance issues related to redundant API calls and inefficient data loading patterns. By implementing these changes, we can significantly improve the application's performance and user experience.
