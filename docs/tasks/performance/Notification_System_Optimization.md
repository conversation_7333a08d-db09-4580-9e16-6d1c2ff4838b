# Notification System Optimization

## Overview

This document provides a detailed plan for optimizing the notification system in the Scene-o-matic application. The current implementation makes excessive API calls, initializes preferences multiple times, and lacks proper caching mechanisms. These issues contribute significantly to the overall performance problems identified in the Map Page Performance Optimization Plan.

## Current Issues

1. **Multiple Initialization Calls**: The system repeatedly calls `/api/notifications/preferences/initialize`
2. **Redundant Preference Checks**: Multiple components check notification preferences independently
3. **Frequent Count Updates**: Notification count is fetched too often without debouncing
4. **No Caching**: Notification data is not cached, leading to repeated API calls
5. **Inefficient Provider Implementation**: The notification provider causes unnecessary re-renders

## Affected Components

- `providers/notification-provider.tsx`
- `components/notifications/notification-dropdown.tsx`
- `components/notifications/notification-modal.tsx`
- `components/notifications/notifications-page-client.tsx`
- `app/api/notifications/preferences/route.ts`
- `app/api/notifications/count/route.ts`
- `modules/notification/service.ts`

## Optimization Plan

### 1. Fix Initialization Logic

#### Current Implementation Issues

The notification system currently initializes preferences multiple times because:

- The initialization check is not properly conditional
- Multiple components trigger initialization independently
- There's no state tracking for whether initialization has occurred

#### Implementation Steps

1. **Update Notification Provider**:

```typescript
// providers/notification-provider.tsx
// BEFORE:
useEffect(() => {
  const initializePreferences = async () => {
    await fetch('/api/notifications/preferences/initialize', {
      method: 'POST',
    });
    fetchPreferences();
  };
  
  initializePreferences();
}, []);

// AFTER:
useEffect(() => {
  const initializePreferences = async () => {
    // Check if preferences already exist first
    const existingPrefs = await fetch('/api/notifications/preferences');
    const data = await existingPrefs.json();
    
    // Only initialize if no preferences exist
    if (!data || !data.preferences || data.preferences.length === 0) {
      await fetch('/api/notifications/preferences/initialize', {
        method: 'POST',
      });
    }
    
    fetchPreferences();
  };
  
  initializePreferences();
  
  // Set initialization flag in localStorage to prevent duplicate calls
  localStorage.setItem('notificationPreferencesInitialized', 'true');
}, []);
```

2. **Update API Route**:

```typescript
// app/api/notifications/preferences/initialize/route.ts
// Add check to prevent duplicate initialization
export async function POST(req: Request) {
  const userId = await getUserIdFromSession();
  if (!userId) {
    return new Response(JSON.stringify({ error: 'Unauthorized' }), {
      status: 401,
    });
  }

  // Check if preferences already exist
  const existingPreferences = await getNotificationPreferencesForUser(userId);
  if (existingPreferences && existingPreferences.length > 0) {
    return new Response(
      JSON.stringify({ message: 'Preferences already initialized' }),
      { status: 200 }
    );
  }

  // Initialize preferences only if they don't exist
  const result = await initializeNotificationPreferences(userId);
  return new Response(JSON.stringify(result), { status: 200 });
}
```

### 2. Implement Debouncing for Notification Count

#### Current Implementation Issues

- Notification count is fetched too frequently
- Each UI update triggers a new count fetch
- No throttling or debouncing mechanism

#### Implementation Steps

1. **Create a Debounced Hook**:

```typescript
// hooks/use-notification-count.ts
import { useState, useEffect } from 'react';
import { useDebounce } from 'hooks/use-debounce';

export function useNotificationCount() {
  const [count, setCount] = useState(0);
  const [loading, setLoading] = useState(true);
  const [lastUpdated, setLastUpdated] = useState<Date | null>(null);
  
  // Debounce the fetch trigger
  const [shouldFetch, setShouldFetch] = useState(true);
  const debouncedShouldFetch = useDebounce(shouldFetch, 5000); // 5 second debounce
  
  const fetchCount = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/notifications/count');
      const data = await response.json();
      setCount(data.count);
      setLastUpdated(new Date());
    } catch (error) {
      console.error('Error fetching notification count:', error);
    } finally {
      setLoading(false);
    }
  };
  
  // Trigger fetch when debounced value changes
  useEffect(() => {
    if (debouncedShouldFetch) {
      fetchCount();
      setShouldFetch(false);
    }
  }, [debouncedShouldFetch]);
  
  // Function to manually trigger a refresh
  const refreshCount = () => {
    setShouldFetch(true);
  };
  
  return { count, loading, lastUpdated, refreshCount };
}
```

2. **Update Notification Components**:

```typescript
// components/notifications/notification-dropdown.tsx
import { useNotificationCount } from 'hooks/use-notification-count';

export function NotificationDropdown() {
  const { count, loading, refreshCount } = useNotificationCount();
  
  // Use the count and only refresh when needed
  // ...
  
  // Refresh count after marking notifications as read
  const handleMarkAsRead = async () => {
    await fetch('/api/notifications/read-all', { method: 'POST' });
    refreshCount(); // Only refresh count when needed
  };
  
  // ...
}
```

### 3. Implement Client-Side Caching

#### Current Implementation Issues

- Notification data is fetched from scratch on each page load
- No use of browser caching capabilities
- No local storage for frequently accessed data

#### Implementation Steps

1. **Add Cache Headers to API Responses**:

```typescript
// app/api/notifications/preferences/route.ts
export async function GET(req: Request) {
  const userId = await getUserIdFromSession();
  if (!userId) {
    return new Response(JSON.stringify({ error: 'Unauthorized' }), {
      status: 401,
    });
  }

  const preferences = await getNotificationPreferencesForUser(userId);
  
  // Add cache headers
  return new Response(JSON.stringify({ preferences }), {
    status: 200,
    headers: {
      'Content-Type': 'application/json',
      'Cache-Control': 'private, max-age=300', // Cache for 5 minutes
    },
  });
}
```

2. **Implement SWR for Data Fetching**:

```typescript
// hooks/use-notification-preferences.ts
import useSWR from 'swr';

const fetcher = (url: string) => fetch(url).then((res) => res.json());

export function useNotificationPreferences() {
  const { data, error, mutate } = useSWR(
    '/api/notifications/preferences',
    fetcher,
    {
      revalidateOnFocus: false,
      revalidateOnReconnect: true,
      refreshInterval: 300000, // Refresh every 5 minutes
    }
  );
  
  const updatePreference = async (preferenceId: string, enabled: boolean) => {
    // Optimistic update
    const originalData = data;
    
    // Update local data immediately
    mutate(
      {
        preferences: data.preferences.map((pref: any) =>
          pref.id === preferenceId ? { ...pref, enabled } : pref
        ),
      },
      false
    );
    
    try {
      // Send update to server
      const response = await fetch(`/api/notifications/preferences`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ preferenceId, enabled }),
      });
      
      if (!response.ok) throw new Error('Failed to update preference');
      
      // Revalidate data
      mutate();
    } catch (error) {
      // Rollback on error
      mutate(originalData, false);
      console.error('Error updating notification preference:', error);
    }
  };
  
  return {
    preferences: data?.preferences || [],
    isLoading: !error && !data,
    isError: error,
    updatePreference,
  };
}
```

3. **Store Critical Data in LocalStorage**:

```typescript
// providers/notification-provider.tsx
// Add local storage caching
useEffect(() => {
  const fetchPreferences = async () => {
    try {
      // Check localStorage first
      const cachedPrefs = localStorage.getItem('notificationPreferences');
      const cachedTimestamp = localStorage.getItem('notificationPreferencesTimestamp');
      
      // Use cached data if it's less than 5 minutes old
      if (cachedPrefs && cachedTimestamp) {
        const timestamp = parseInt(cachedTimestamp, 10);
        const now = Date.now();
        
        if (now - timestamp < 5 * 60 * 1000) { // 5 minutes
          setPreferences(JSON.parse(cachedPrefs));
          return;
        }
      }
      
      // Fetch fresh data if cache is stale or missing
      const response = await fetch('/api/notifications/preferences');
      const data = await response.json();
      
      setPreferences(data.preferences);
      
      // Update cache
      localStorage.setItem('notificationPreferences', JSON.stringify(data.preferences));
      localStorage.setItem('notificationPreferencesTimestamp', Date.now().toString());
    } catch (error) {
      console.error('Error fetching notification preferences:', error);
    }
  };
  
  fetchPreferences();
}, []);
```

### 4. Optimize State Management

#### Current Implementation Issues

- Notification provider causes unnecessary re-renders
- State is not properly memoized
- Context values are recreated on each render

#### Implementation Steps

1. **Optimize Notification Provider**:

```typescript
// providers/notification-provider.tsx
import { createContext, useContext, useState, useEffect, useMemo, useCallback } from 'react';

// Create context with proper typing
type NotificationContextType = {
  preferences: NotificationPreference[];
  updatePreference: (preferenceId: string, enabled: boolean) => Promise<void>;
  unreadCount: number;
  refreshCount: () => void;
  markAllAsRead: () => Promise<void>;
};

const NotificationContext = createContext<NotificationContextType | undefined>(undefined);

export function NotificationProvider({ children }: { children: React.ReactNode }) {
  const [preferences, setPreferences] = useState<NotificationPreference[]>([]);
  const [unreadCount, setUnreadCount] = useState(0);
  
  // Fetch preferences (implementation from previous sections)
  // ...
  
  // Fetch notification count (implementation from previous sections)
  // ...
  
  // Memoize functions to prevent unnecessary re-renders
  const updatePreference = useCallback(async (preferenceId: string, enabled: boolean) => {
    try {
      // Optimistic update
      setPreferences((prev) =>
        prev.map((pref) =>
          pref.id === preferenceId ? { ...pref, enabled } : pref
        )
      );
      
      // Update server
      const response = await fetch('/api/notifications/preferences', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ preferenceId, enabled }),
      });
      
      if (!response.ok) {
        throw new Error('Failed to update preference');
      }
    } catch (error) {
      // Rollback on error
      console.error('Error updating notification preference:', error);
      // Refetch to ensure consistency
      fetchPreferences();
    }
  }, []);
  
  const refreshCount = useCallback(() => {
    fetchNotificationCount();
  }, []);
  
  const markAllAsRead = useCallback(async () => {
    try {
      await fetch('/api/notifications/read-all', { method: 'POST' });
      setUnreadCount(0);
    } catch (error) {
      console.error('Error marking notifications as read:', error);
    }
  }, []);
  
  // Memoize context value to prevent unnecessary re-renders
  const contextValue = useMemo(
    () => ({
      preferences,
      updatePreference,
      unreadCount,
      refreshCount,
      markAllAsRead,
    }),
    [preferences, updatePreference, unreadCount, refreshCount, markAllAsRead]
  );
  
  return (
    <NotificationContext.Provider value={contextValue}>
      {children}
    </NotificationContext.Provider>
  );
}

// Custom hook for using the notification context
export function useNotification() {
  const context = useContext(NotificationContext);
  if (context === undefined) {
    throw new Error('useNotification must be used within a NotificationProvider');
  }
  return context;
}
```

2. **Optimize Component Re-renders**:

```typescript
// components/notifications/notification-dropdown.tsx
import { memo } from 'react';
import { useNotification } from 'providers/notification-provider';

// Use memo to prevent unnecessary re-renders
export const NotificationDropdown = memo(function NotificationDropdown() {
  const { unreadCount, markAllAsRead } = useNotification();
  
  // Component implementation
  // ...
  
  return (
    // JSX implementation
  );
});
```

## Backend Optimizations

### 1. Optimize Database Queries

#### Current Implementation Issues

- Inefficient queries in notification service
- Lack of proper indexing
- Potential N+1 query problems

#### Implementation Steps

1. **Review and Optimize Service Methods**:

```typescript
// modules/notification/service.ts
// Optimize getNotificationsForUser method
export async function getNotificationsForUser(
  userId: string,
  page = 1,
  pageSize = 10
): Promise<{ notifications: Notification[]; total: number }> {
  const offset = (page - 1) * pageSize;
  
  // Use a single query with COUNT() OVER() to get total in one query
  const result = await db.execute(sql`
    SELECT n.*, 
           COUNT(*) OVER() as total_count
    FROM notifications n
    WHERE n.user_id = ${userId}
    ORDER BY n.created_at DESC
    LIMIT ${pageSize} OFFSET ${offset}
  `);
  
  const notifications = result.rows as Notification[];
  const total = notifications.length > 0 ? Number(notifications[0].total_count) : 0;
  
  return { notifications, total };
}
```

2. **Add Proper Indexes**:

```sql
-- Add to migration file or create a new one
CREATE INDEX IF NOT EXISTS idx_notifications_user_id ON notifications(user_id);
CREATE INDEX IF NOT EXISTS idx_notifications_created_at ON notifications(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_notification_preferences_user_id ON notification_preferences(user_id);
```

### 2. Implement API Response Caching

#### Implementation Steps

1. **Add Redis Caching (Optional)**:

```typescript
// lib/redis.ts
import { Redis } from '@upstash/redis';

export const redis = new Redis({
  url: process.env.UPSTASH_REDIS_URL || '',
  token: process.env.UPSTASH_REDIS_TOKEN || '',
});

// modules/notification/service.ts
import { redis } from 'lib/redis';

export async function getNotificationCountForUser(userId: string): Promise<number> {
  // Try to get from cache first
  const cacheKey = `notification_count:${userId}`;
  const cachedCount = await redis.get(cacheKey);
  
  if (cachedCount !== null) {
    return Number(cachedCount);
  }
  
  // If not in cache, query database
  const result = await db.execute(sql`
    SELECT COUNT(*) as count
    FROM notifications
    WHERE user_id = ${userId} AND read = false
  `);
  
  const count = Number(result.rows[0].count);
  
  // Cache the result for 5 minutes
  await redis.set(cacheKey, count, { ex: 300 });
  
  return count;
}
```

2. **Implement Memory Caching for Server**:

```typescript
// lib/cache.ts
type CacheEntry<T> = {
  value: T;
  expiry: number;
};

class MemoryCache {
  private cache: Map<string, CacheEntry<any>> = new Map();
  
  set<T>(key: string, value: T, ttlSeconds: number): void {
    const expiry = Date.now() + ttlSeconds * 1000;
    this.cache.set(key, { value, expiry });
  }
  
  get<T>(key: string): T | null {
    const entry = this.cache.get(key);
    
    if (!entry) return null;
    
    if (Date.now() > entry.expiry) {
      this.cache.delete(key);
      return null;
    }
    
    return entry.value as T;
  }
  
  invalidate(key: string): void {
    this.cache.delete(key);
  }
  
  invalidateByPrefix(prefix: string): void {
    for (const key of this.cache.keys()) {
      if (key.startsWith(prefix)) {
        this.cache.delete(key);
      }
    }
  }
}

export const memoryCache = new MemoryCache();
```

## Testing and Validation

### 1. Performance Metrics

Implement performance tracking to measure the impact of these optimizations:

```typescript
// app/providers/AllClientProviders.tsx
import { useEffect } from 'react';

export function AllClientProviders({ children }: { children: React.ReactNode }) {
  useEffect(() => {
    // Track API calls for notifications
    let notificationApiCalls = 0;
    
    const originalFetch = window.fetch;
    window.fetch = async function(input, init) {
      const url = typeof input === 'string' ? input : input.url;
      
      if (url.includes('/api/notifications')) {
        notificationApiCalls++;
        console.log(`Notification API calls: ${notificationApiCalls}`);
      }
      
      return originalFetch.apply(this, [input, init]);
    };
    
    return () => {
      window.fetch = originalFetch;
    };
  }, []);
  
  // Rest of the provider implementation
  // ...
}
```

### 2. Validation Script

Create a script to validate the notification system performance:

```typescript
// scripts/test-notification-performance.ts
import fetch from 'node-fetch';

async function testNotificationPerformance() {
  console.log('Testing notification system performance...');
  
  // Test initialization
  console.time('Initialize preferences');
  await fetch('http://localhost:3000/api/notifications/preferences/initialize', {
    method: 'POST',
    headers: {
      Cookie: 'session=your-test-session-cookie',
    },
  });
  console.timeEnd('Initialize preferences');
  
  // Test preference fetching
  console.time('Fetch preferences');
  await fetch('http://localhost:3000/api/notifications/preferences', {
    headers: {
      Cookie: 'session=your-test-session-cookie',
    },
  });
  console.timeEnd('Fetch preferences');
  
  // Test notification count
  console.time('Fetch notification count');
  await fetch('http://localhost:3000/api/notifications/count', {
    headers: {
      Cookie: 'session=your-test-session-cookie',
    },
  });
  console.timeEnd('Fetch notification count');
  
  // Test multiple sequential calls to simulate redundant calls
  console.time('Multiple sequential calls');
  await Promise.all([
    fetch('http://localhost:3000/api/notifications/count', {
      headers: { Cookie: 'session=your-test-session-cookie' },
    }),
    fetch('http://localhost:3000/api/notifications/preferences', {
      headers: { Cookie: 'session=your-test-session-cookie' },
    }),
    fetch('http://localhost:3000/api/notifications/count', {
      headers: { Cookie: 'session=your-test-session-cookie' },
    }),
  ]);
  console.timeEnd('Multiple sequential calls');
}

testNotificationPerformance().catch(console.error);
```

## Expected Outcomes

After implementing these optimizations, we expect:

1. **Reduced API Calls**: Notification-related API calls should decrease by at least 70%
2. **Faster Loading**: Notification components should load significantly faster
3. **Improved Responsiveness**: UI should remain responsive during notification operations
4. **Reduced Server Load**: Backend servers should experience less load from notification queries
5. **Better User Experience**: Users should notice smoother interactions with notification features

## Implementation Checklist

- [ ] Fix initialization logic in notification provider
- [ ] Update API routes to prevent duplicate initialization
- [ ] Implement debouncing for notification count updates
- [ ] Add client-side caching with SWR
- [ ] Optimize state management in notification provider
- [ ] Implement localStorage caching for preferences
- [ ] Optimize database queries and add indexes
- [ ] Add server-side caching (memory or Redis)
- [ ] Implement performance metrics tracking
- [ ] Test and validate optimizations

This optimization plan addresses the specific issues with the notification system that contribute to the overall performance problems. By implementing these changes, we can significantly improve the application's performance and user experience.
