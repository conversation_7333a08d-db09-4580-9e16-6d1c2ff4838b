# Map Page Performance Optimization Plan

## Overview

This document outlines a comprehensive plan to optimize the performance of the map page in the Scene-o-matic application. After analyzing server logs, we've identified that the current map page takes approximately 12.5 seconds to load initially, with numerous redundant API calls occurring afterward.

## Current Loading Sequence Analysis

```
Server                                  Client
-----------------------------------------------------------------
                                       GET /organizations/[slug]/map
12.5s to process
Return map page HTML/JS
                                       GET /api/mapbox-token
                                       GET /api/auth/me (multiple times)
                                       GET /api/organizations (multiple times)
                                       GET /api/map/views
                                       GET /api/favorites
                                       GET /api/notifications/preferences (multiple times)
                                       GET /api/notifications/count (multiple times)
                                       GET /api/map (multiple times)
                                       POST /api/notifications/preferences/initialize (multiple times)
```

## Key Issues Identified

1. **Redundant API Calls**: Multiple identical requests to the same endpoints
2. **Notification System Overhead**: Excessive API calls for notification data
3. **Sequential Loading**: Resources loaded one after another instead of in parallel
4. **Inefficient Data Fetching**: Map component making multiple data requests
5. **Server-Side Stytch Initialization**: Repeated B2B client initialization
6. **Lack of Caching**: No client-side caching for frequently accessed data
7. **Database Query Inefficiencies**: Potential for query optimization

## Optimization Plan

### Phase 1: Eliminate Redundant API Calls (High Priority)

#### Consolidate Authentication Checks

- Implement a single auth check at the page level instead of in each component
- Cache user and organization data in context to prevent repeated `/api/auth/me` calls
- Update affected components:
  - `app/providers/AllClientProviders.tsx`
  - `providers/auth-provider.tsx`
  - `providers/organization-provider.tsx`

#### Fix Notification System

- Prevent multiple initialization calls to `/api/notifications/preferences/initialize`
- Add conditional logic to check if preferences exist before initialization
- Implement debouncing for notification count updates
- Update affected components:
  - `providers/notification-provider.tsx`
  - `components/notifications/notification-dropdown.tsx`
  - `app/api/notifications/preferences/route.ts`

#### Optimize Provider Initialization

- Ensure Stytch B2B client is initialized only once
- Add proper dependency arrays to useEffect hooks to prevent unnecessary re-renders
- Update affected files:
  - `lib/stytch-b2b-client.ts`
  - `lib/loadStytch.ts`
  - `providers/auth-provider.tsx`

### Phase 2: Implement Caching and State Management (High Priority)

#### Client-Side Caching

- Implement React Query for API calls with appropriate caching strategies
- Set up SWR for data fetching with stale-while-revalidate pattern
- Add cache headers to API responses for browser caching
- Implementation steps:
  1. Install required packages: `pnpm add @tanstack/react-query swr`
  2. Create query client provider in `app/providers/AllClientProviders.tsx`
  3. Update API routes to include cache headers
  4. Refactor data fetching in map components

#### State Management Optimization

- Consolidate overlapping state in providers
- Use React Context more efficiently with memoization
- Implement selective re-rendering with useMemo and useCallback
- Update affected components:
  - `providers/map-provider.tsx`
  - `providers/notification-provider.tsx`
  - `providers/organization-provider.tsx`

#### Local Storage for Preferences

- Cache user preferences in localStorage to reduce API calls on page reload
- Implement optimistic UI updates for preference changes
- Update affected components:
  - `providers/notification-provider.tsx`
  - `hooks/use-map-context.ts`

### Phase 3: Optimize Data Loading Patterns (Medium Priority)

#### Implement Parallel Data Loading

- Use Promise.all for concurrent API requests where possible
- Prioritize critical data loading before non-essential data
- Update affected components:
  - `components/maps/full-map.tsx`
  - `components/maps/map-view.tsx`
  - `app/(dashboard)/organizations/[organizationSlug]/map/page.tsx`

#### Lazy Loading Components

- Implement code splitting for map components
- Defer loading of non-critical UI elements
- Implementation steps:
  1. Use dynamic imports for non-critical components
  2. Add Suspense boundaries around lazy-loaded components
  3. Update affected components:
     - `components/maps/full-screen-map.tsx`
     - `components/maps/map-controls.tsx`
     - `components/maps/view-selector.tsx`

#### Progressive Data Loading

- Implement pagination for location data
- Load initial viewport data first, then load additional data as needed
- Update affected components:
  - `components/maps/map-locations.tsx`
  - `components/maps/map-pagination.tsx`
  - `app/api/map/route.ts`

### Phase 4: Backend Optimizations (Medium Priority)

#### API Endpoint Consolidation

- Create a combined endpoint for initial map data to reduce multiple requests
- Implement GraphQL for more efficient data fetching
- Implementation steps:
  1. Create new endpoint at `app/api/map/initial-data/route.ts`
  2. Consolidate data from multiple endpoints
  3. Update client components to use the new endpoint

#### Database Query Optimization

- Add appropriate indexes for frequently queried fields
- Optimize JOIN operations in location queries
- Implement query caching at the database level
- Update affected files:
  - `modules/map/service.ts`
  - `modules/location/service.ts`
  - `lib/db/schema.ts`

#### Server-Side Rendering Improvements

- Pre-render critical map data during SSR
- Implement streaming SSR for faster initial page load
- Update affected files:
  - `app/(dashboard)/organizations/[organizationSlug]/map/page.tsx`
  - `app/shared/map/[token]/page.tsx`

### Phase 5: Infrastructure and Monitoring (Lower Priority)

#### Performance Monitoring

- Implement detailed performance metrics collection
- Set up alerts for performance degradation
- Implementation steps:
  1. Add performance monitoring library (e.g., `web-vitals`)
  2. Create custom events for tracking map page performance
  3. Set up dashboard for monitoring performance metrics

#### CDN and Edge Caching

- Utilize CDN for static assets
- Implement edge caching for API responses where appropriate
- Update `next.config.mjs` to configure CDN settings

#### Resource Optimization

- Optimize Mapbox usage and initialization
- Reduce JavaScript bundle size through tree shaking and code splitting
- Update affected files:
  - `components/maps/map-view.tsx`
  - `hooks/use-map.ts`

## Implementation Prioritization

```mermaid
gantt
    title Map Page Performance Optimization Plan
    dateFormat  YYYY-MM-DD
    section Phase 1
    Eliminate Redundant API Calls      :a1, 2025-05-05, 5d
    Fix Notification System            :a2, after a1, 3d
    Optimize Provider Init             :a3, after a2, 2d
    section Phase 2
    Client-Side Caching                :b1, after a3, 4d
    State Management                   :b2, after b1, 3d
    Local Storage                      :b3, after b2, 2d
    section Phase 3
    Parallel Loading                   :c1, after b3, 3d
    Lazy Loading                       :c2, after c1, 3d
    Progressive Data                   :c3, after c2, 4d
    section Phase 4
    API Consolidation                  :d1, after c3, 5d
    DB Optimization                    :d2, after d1, 4d
    SSR Improvements                   :d3, after d2, 3d
    section Phase 5
    Monitoring                         :e1, after d3, 3d
    CDN/Edge                           :e2, after e1, 2d
    Resource Opt                       :e3, after e2, 3d
```

## Expected Outcomes

- **Initial Page Load**: Reduce from 12.5s to under 3s
- **API Request Count**: Reduce by at least 60%
- **Redundant Calls**: Eliminate completely
- **Perceived Performance**: Significantly improved through progressive loading
- **Stability**: Reduced error rates and improved error handling

## Implementation Approach

1. Begin with Phase 1 to address the most critical issues causing redundant API calls
2. Move to Phase 2 to implement proper caching and state management
3. Continue with Phase 3 to optimize data loading patterns
4. Implement backend optimizations in Phase 4
5. Finish with infrastructure improvements in Phase 5

## Key Files to Modify

### Client Components
- `app/providers/AllClientProviders.tsx`
- `providers/auth-provider.tsx`
- `providers/map-provider.tsx`
- `providers/notification-provider.tsx`
- `components/maps/full-map.tsx`
- `components/maps/map-view.tsx`
- `components/maps/map-locations.tsx`
- `components/notifications/notification-dropdown.tsx`

### API Routes
- `app/api/auth/me/route.ts`
- `app/api/map/route.ts`
- `app/api/notifications/preferences/route.ts`
- `app/api/notifications/count/route.ts`

### Services and Utilities
- `lib/stytch-b2b-client.ts`
- `modules/map/service.ts`
- `modules/notification/service.ts`
- `hooks/use-map-context.ts`
- `hooks/use-map.ts`

## Testing Strategy

1. Establish performance baselines before making changes
2. Implement changes in isolated branches
3. Use Lighthouse and Chrome DevTools to measure performance improvements
4. Test on various devices and network conditions
5. Monitor error rates during implementation

This optimization plan addresses both immediate performance bottlenecks and longer-term architectural improvements. By implementing these changes in the suggested order, we can achieve significant performance gains while maintaining system stability.
