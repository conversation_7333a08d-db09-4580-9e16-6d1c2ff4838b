# Stytch B2B Client Optimization

## Overview

This document outlines a comprehensive plan for optimizing the Stytch B2B client implementation in the Scene-o-matic application. The current implementation initializes the Stytch client multiple times on the server side, leading to performance issues and potential race conditions.

## Current Issues

1. **Multiple Initializations**: The Stytch B2B client is initialized multiple times on the server side
2. **Inefficient Loading**: The client is loaded even when not needed
3. **No Caching Mechanism**: Each initialization creates a new instance
4. **Potential Race Conditions**: Multiple initializations can lead to race conditions
5. **Redundant Authentication Checks**: Authentication is verified multiple times unnecessarily

## Affected Files

- `lib/stytch-b2b-client.ts`
- `lib/loadStytch.ts`
- `lib/stytch-b2b.ts`
- `providers/auth-provider.tsx`
- `app/api/auth/me/route.ts`
- `middleware.ts`

## Optimization Plan

### 1. Implement Singleton Pattern for Stytch Client

The first step is to ensure that the Stytch B2B client is initialized only once and reused across the application.

#### Current Implementation

```typescript
// lib/stytch-b2b-client.ts
import * as stytch from 'stytch';

let client: stytch.B2BClient | null = null;

export function getStytchB2BClient(): stytch.B2BClient {
  if (!client) {
    client = new stytch.B2BClient({
      project_id: process.env.STYTCH_PROJECT_ID as string,
      secret: process.env.STYTCH_SECRET as string,
    });
  }
  return client;
}
```

#### Optimized Implementation

```typescript
// lib/stytch-b2b-client.ts
import * as stytch from 'stytch';

// Use a more robust singleton pattern with proper typing
let client: stytch.B2BClient | null = null;
let clientPromise: Promise<stytch.B2BClient> | null = null;

export function getStytchB2BClient(): stytch.B2BClient {
  if (!client) {
    if (!process.env.STYTCH_PROJECT_ID || !process.env.STYTCH_SECRET) {
      throw new Error('Stytch environment variables are not set');
    }
    
    client = new stytch.B2BClient({
      project_id: process.env.STYTCH_PROJECT_ID,
      secret: process.env.STYTCH_SECRET,
    });
  }
  return client;
}

// Async version for use in async contexts
export async function getStytchB2BClientAsync(): Promise<stytch.B2BClient> {
  if (client) return client;
  
  if (!clientPromise) {
    clientPromise = Promise.resolve().then(() => {
      if (!client) {
        if (!process.env.STYTCH_PROJECT_ID || !process.env.STYTCH_SECRET) {
          throw new Error('Stytch environment variables are not set');
        }
        
        client = new stytch.B2BClient({
          project_id: process.env.STYTCH_PROJECT_ID,
          secret: process.env.STYTCH_SECRET,
        });
      }
      return client;
    });
  }
  
  return clientPromise;
}

// Reset function for testing purposes
export function resetStytchB2BClient(): void {
  client = null;
  clientPromise = null;
}
```

### 2. Optimize Client Loading

Ensure that the Stytch client is only loaded when needed and properly cached.

#### Current Implementation

```typescript
// lib/loadStytch.ts
import { StytchB2BClient } from '@stytch/vanilla-js/b2b';

let stytchClient: StytchB2BClient | null = null;

export function loadStytch(): StytchB2BClient {
  if (stytchClient) {
    return stytchClient;
  }

  stytchClient = new StytchB2BClient(process.env.NEXT_PUBLIC_STYTCH_PUBLIC_TOKEN as string);
  return stytchClient;
}
```

#### Optimized Implementation

```typescript
// lib/loadStytch.ts
import { StytchB2BClient } from '@stytch/vanilla-js/b2b';

// Use a more robust singleton pattern with proper typing
let stytchClient: StytchB2BClient | null = null;
let stytchClientPromise: Promise<StytchB2BClient> | null = null;

// For client-side usage
export function loadStytch(): StytchB2BClient {
  if (typeof window === 'undefined') {
    throw new Error('loadStytch should only be called on the client side');
  }
  
  if (stytchClient) {
    return stytchClient;
  }

  if (!process.env.NEXT_PUBLIC_STYTCH_PUBLIC_TOKEN) {
    throw new Error('NEXT_PUBLIC_STYTCH_PUBLIC_TOKEN is not set');
  }

  stytchClient = new StytchB2BClient(process.env.NEXT_PUBLIC_STYTCH_PUBLIC_TOKEN);
  return stytchClient;
}

// Async version for use in async contexts
export async function loadStytchAsync(): Promise<StytchB2BClient> {
  if (typeof window === 'undefined') {
    throw new Error('loadStytchAsync should only be called on the client side');
  }
  
  if (stytchClient) return stytchClient;
  
  if (!stytchClientPromise) {
    stytchClientPromise = Promise.resolve().then(() => {
      if (!stytchClient) {
        if (!process.env.NEXT_PUBLIC_STYTCH_PUBLIC_TOKEN) {
          throw new Error('NEXT_PUBLIC_STYTCH_PUBLIC_TOKEN is not set');
        }
        
        stytchClient = new StytchB2BClient(process.env.NEXT_PUBLIC_STYTCH_PUBLIC_TOKEN);
      }
      return stytchClient;
    });
  }
  
  return stytchClientPromise;
}

// Reset function for testing purposes
export function resetStytchClient(): void {
  stytchClient = null;
  stytchClientPromise = null;
}
```

### 3. Implement Server-Side Caching for Authentication

Implement a caching mechanism for authentication checks to prevent redundant API calls.

```typescript
// lib/utils/auth-cache.ts
import { LRUCache } from 'lru-cache';

type AuthCacheData = {
  userId: string;
  organizationId: string;
  expiresAt: number;
};

// Create a cache with a maximum of 1000 items that expire after 5 minutes
const authCache = new LRUCache<string, AuthCacheData>({
  max: 1000,
  ttl: 5 * 60 * 1000, // 5 minutes
});

export function cacheAuthData(sessionToken: string, userId: string, organizationId: string): void {
  authCache.set(sessionToken, {
    userId,
    organizationId,
    expiresAt: Date.now() + 5 * 60 * 1000, // 5 minutes from now
  });
}

export function getCachedAuthData(sessionToken: string): AuthCacheData | undefined {
  return authCache.get(sessionToken);
}

export function invalidateAuthCache(sessionToken: string): void {
  authCache.delete(sessionToken);
}
```

### 4. Optimize Auth Provider

Update the auth provider to use the optimized Stytch client and implement proper caching.

#### Current Implementation

```typescript
// providers/auth-provider.tsx
import { createContext, useContext, useEffect, useState } from 'react';
import { loadStytch } from 'lib/loadStytch';

export const AuthContext = createContext<any>(null);

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [user, setUser] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  
  useEffect(() => {
    const fetchUser = async () => {
      try {
        const response = await fetch('/api/auth/me');
        const data = await response.json();
        setUser(data.user);
      } catch (error) {
        console.error('Error fetching user:', error);
      } finally {
        setLoading(false);
      }
    };
    
    fetchUser();
    
    // Initialize Stytch client
    loadStytch();
  }, []);
  
  // Rest of the provider implementation
}
```

#### Optimized Implementation

```typescript
// providers/auth-provider.tsx
import { createContext, useContext, useEffect, useState, useMemo } from 'react';
import { loadStytchAsync } from 'lib/loadStytch';

type AuthContextType = {
  user: any | null;
  loading: boolean;
  error: Error | null;
  refreshUser: () => Promise<void>;
  logout: () => Promise<void>;
};

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [user, setUser] = useState<any | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);
  
  // Fetch user data only once on initial load
  useEffect(() => {
    let isMounted = true;
    
    const fetchUser = async () => {
      try {
        setLoading(true);
        
        // Use browser cache for subsequent requests
        const response = await fetch('/api/auth/me', {
          headers: {
            'Cache-Control': 'max-age=300', // Cache for 5 minutes
          },
        });
        
        if (!response.ok) {
          throw new Error('Failed to fetch user data');
        }
        
        const data = await response.json();
        
        if (isMounted) {
          setUser(data.user);
          setError(null);
        }
      } catch (error) {
        if (isMounted) {
          console.error('Error fetching user:', error);
          setError(error instanceof Error ? error : new Error(String(error)));
        }
      } finally {
        if (isMounted) {
          setLoading(false);
        }
      }
    };
    
    fetchUser();
    
    // Initialize Stytch client lazily only when needed
    const initStytch = async () => {
      try {
        await loadStytchAsync();
      } catch (error) {
        console.error('Error initializing Stytch client:', error);
      }
    };
    
    // Only initialize Stytch if we're in a browser environment
    if (typeof window !== 'undefined') {
      initStytch();
    }
    
    return () => {
      isMounted = false;
    };
  }, []);
  
  // Function to refresh user data
  const refreshUser = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/auth/me', {
        headers: {
          'Cache-Control': 'no-cache', // Skip cache for refresh
        },
      });
      
      if (!response.ok) {
        throw new Error('Failed to refresh user data');
      }
      
      const data = await response.json();
      setUser(data.user);
      setError(null);
    } catch (error) {
      console.error('Error refreshing user:', error);
      setError(error instanceof Error ? error : new Error(String(error)));
    } finally {
      setLoading(false);
    }
  };
  
  // Function to handle logout
  const logout = async () => {
    try {
      const response = await fetch('/api/auth/logout', {
        method: 'POST',
      });
      
      if (!response.ok) {
        throw new Error('Failed to logout');
      }
      
      setUser(null);
    } catch (error) {
      console.error('Error logging out:', error);
      setError(error instanceof Error ? error : new Error(String(error)));
    }
  };
  
  // Memoize context value to prevent unnecessary re-renders
  const contextValue = useMemo(
    () => ({
      user,
      loading,
      error,
      refreshUser,
      logout,
    }),
    [user, loading, error]
  );
  
  return (
    <AuthContext.Provider value={contextValue}>
      {children}
    </AuthContext.Provider>
  );
}

// Custom hook for using the auth context
export function useAuth() {
  const context = useContext(AuthContext);
  
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  
  return context;
}
```

### 5. Optimize API Routes

Update API routes to use the optimized Stytch client and implement proper caching.

#### Current Implementation

```typescript
// app/api/auth/me/route.ts
import { cookies } from 'next/headers';
import { getStytchB2BClient } from 'lib/stytch-b2b-client';
import { getUserByStytchId } from 'modules/user/service';

export async function GET() {
  const cookieStore = cookies();
  const sessionToken = cookieStore.get('stytch_session_jwt')?.value;
  
  if (!sessionToken) {
    return new Response(JSON.stringify({ error: 'Unauthorized' }), {
      status: 401,
    });
  }
  
  try {
    const stytchClient = getStytchB2BClient();
    const session = await stytchClient.sessions.authenticate({ session_jwt: sessionToken });
    
    const user = await getUserByStytchId(session.session.member.member_id);
    
    return new Response(JSON.stringify({ user }), {
      status: 200,
    });
  } catch (error) {
    console.error('Error authenticating session:', error);
    return new Response(JSON.stringify({ error: 'Unauthorized' }), {
      status: 401,
    });
  }
}
```

#### Optimized Implementation

```typescript
// app/api/auth/me/route.ts
import { cookies } from 'next/headers';
import { getStytchB2BClientAsync } from 'lib/stytch-b2b-client';
import { getUserByStytchId } from 'modules/user/service';
import { getCachedAuthData, cacheAuthData } from 'lib/utils/auth-cache';

export async function GET() {
  const cookieStore = cookies();
  const sessionToken = cookieStore.get('stytch_session_jwt')?.value;
  
  if (!sessionToken) {
    return new Response(JSON.stringify({ error: 'Unauthorized' }), {
      status: 401,
    });
  }
  
  try {
    // Check cache first
    const cachedData = getCachedAuthData(sessionToken);
    
    if (cachedData) {
      // Use cached user data
      const user = await getUserByStytchId(cachedData.userId);
      
      return new Response(JSON.stringify({ user }), {
        status: 200,
        headers: {
          'Content-Type': 'application/json',
          'Cache-Control': 'private, max-age=300', // Cache for 5 minutes
        },
      });
    }
    
    // If not in cache, authenticate with Stytch
    const stytchClient = await getStytchB2BClientAsync();
    const session = await stytchClient.sessions.authenticate({ session_jwt: sessionToken });
    
    const memberId = session.session.member.member_id;
    const organizationId = session.session.organization.organization_id;
    
    // Cache the authentication data
    cacheAuthData(sessionToken, memberId, organizationId);
    
    const user = await getUserByStytchId(memberId);
    
    return new Response(JSON.stringify({ user }), {
      status: 200,
      headers: {
        'Content-Type': 'application/json',
        'Cache-Control': 'private, max-age=300', // Cache for 5 minutes
      },
    });
  } catch (error) {
    console.error('Error authenticating session:', error);
    return new Response(JSON.stringify({ error: 'Unauthorized' }), {
      status: 401,
    });
  }
}
```

### 6. Optimize Middleware

Update the middleware to use the optimized Stytch client and implement proper caching.

#### Current Implementation

```typescript
// middleware.ts
import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';
import { getStytchB2BClient } from 'lib/stytch-b2b-client';

export async function middleware(request: NextRequest) {
  const sessionToken = request.cookies.get('stytch_session_jwt')?.value;
  
  // Public routes that don't require authentication
  const publicRoutes = ['/auth/login', '/auth/register', '/auth/authenticate'];
  
  if (publicRoutes.some(route => request.nextUrl.pathname.startsWith(route))) {
    return NextResponse.next();
  }
  
  if (!sessionToken) {
    return NextResponse.redirect(new URL('/auth/login', request.url));
  }
  
  try {
    const stytchClient = getStytchB2BClient();
    await stytchClient.sessions.authenticate({ session_jwt: sessionToken });
    
    return NextResponse.next();
  } catch (error) {
    console.error('Error authenticating session:', error);
    return NextResponse.redirect(new URL('/auth/login', request.url));
  }
}

export const config = {
  matcher: [
    '/((?!api|_next/static|_next/image|favicon.ico).*)',
  ],
};
```

#### Optimized Implementation

```typescript
// middleware.ts
import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';
import { getStytchB2BClientAsync } from 'lib/stytch-b2b-client';
import { getCachedAuthData, cacheAuthData } from 'lib/utils/auth-cache';

export async function middleware(request: NextRequest) {
  const sessionToken = request.cookies.get('stytch_session_jwt')?.value;
  
  // Public routes that don't require authentication
  const publicRoutes = [
    '/auth/login',
    '/auth/register',
    '/auth/authenticate',
    '/api/auth/login',
    '/api/auth/register',
    '/_next',
    '/favicon.ico',
  ];
  
  // Check if the current path is a public route
  if (publicRoutes.some(route => request.nextUrl.pathname.startsWith(route))) {
    return NextResponse.next();
  }
  
  // If no session token, redirect to login
  if (!sessionToken) {
    return NextResponse.redirect(new URL('/auth/login', request.url));
  }
  
  try {
    // Check cache first
    const cachedData = getCachedAuthData(sessionToken);
    
    if (cachedData) {
      // Use cached authentication data
      return NextResponse.next();
    }
    
    // If not in cache, authenticate with Stytch
    const stytchClient = await getStytchB2BClientAsync();
    const session = await stytchClient.sessions.authenticate({ session_jwt: sessionToken });
    
    const memberId = session.session.member.member_id;
    const organizationId = session.session.organization.organization_id;
    
    // Cache the authentication data
    cacheAuthData(sessionToken, memberId, organizationId);
    
    return NextResponse.next();
  } catch (error) {
    console.error('Error authenticating session:', error);
    return NextResponse.redirect(new URL('/auth/login', request.url));
  }
}

export const config = {
  matcher: [
    '/((?!_next/static|_next/image|favicon.ico).*)',
  ],
};
```

### 7. Create a Utility Function for Authentication

Create a utility function to handle authentication checks consistently across the application.

```typescript
// lib/utils/auth-utils.ts
import { cookies } from 'next/headers';
import { getStytchB2BClientAsync } from 'lib/stytch-b2b-client';
import { getCachedAuthData, cacheAuthData } from 'lib/utils/auth-cache';

export async function authenticateSession(): Promise<{
  authenticated: boolean;
  memberId?: string;
  organizationId?: string;
  error?: string;
}> {
  const cookieStore = cookies();
  const sessionToken = cookieStore.get('stytch_session_jwt')?.value;
  
  if (!sessionToken) {
    return { authenticated: false, error: 'No session token found' };
  }
  
  try {
    // Check cache first
    const cachedData = getCachedAuthData(sessionToken);
    
    if (cachedData) {
      // Use cached authentication data
      return {
        authenticated: true,
        memberId: cachedData.userId,
        organizationId: cachedData.organizationId,
      };
    }
    
    // If not in cache, authenticate with Stytch
    const stytchClient = await getStytchB2BClientAsync();
    const session = await stytchClient.sessions.authenticate({ session_jwt: sessionToken });
    
    const memberId = session.session.member.member_id;
    const organizationId = session.session.organization.organization_id;
    
    // Cache the authentication data
    cacheAuthData(sessionToken, memberId, organizationId);
    
    return {
      authenticated: true,
      memberId,
      organizationId,
    };
  } catch (error) {
    console.error('Error authenticating session:', error);
    return {
      authenticated: false,
      error: error instanceof Error ? error.message : 'Authentication failed',
    };
  }
}

// Helper function to get the current user ID
export async function getCurrentUserId(): Promise<string | null> {
  const auth = await authenticateSession();
  
  if (!auth.authenticated || !auth.memberId) {
    return null;
  }
  
  return auth.memberId;
}

// Helper function to get the current organization ID
export async function getCurrentOrganizationId(): Promise<string | null> {
  const auth = await authenticateSession();
  
  if (!auth.authenticated || !auth.organizationId) {
    return null;
  }
  
  return auth.organizationId;
}
```

### 8. Update API Routes to Use the Authentication Utility

Update all API routes to use the authentication utility function.

```typescript
// Example API route using the authentication utility
// app/api/organizations/route.ts
import { authenticateSession, getCurrentUserId } from 'lib/utils/auth-utils';
import { getOrganizationsForUser } from 'modules/organization/service';

export async function GET() {
  const auth = await authenticateSession();
  
  if (!auth.authenticated) {
    return new Response(JSON.stringify({ error: 'Unauthorized' }), {
      status: 401,
    });
  }
  
  try {
    const userId = auth.memberId;
    const organizations = await getOrganizationsForUser(userId!);
    
    return new Response(JSON.stringify({ organizations }), {
      status: 200,
      headers: {
        'Content-Type': 'application/json',
        'Cache-Control': 'private, max-age=300', // Cache for 5 minutes
      },
    });
  } catch (error) {
    console.error('Error fetching organizations:', error);
    return new Response(JSON.stringify({ error: 'Failed to fetch organizations' }), {
      status: 500,
    });
  }
}
```

## Testing and Validation

### 1. Performance Monitoring

Implement performance monitoring to track Stytch client initializations and authentication checks.

```typescript
// lib/utils/performance.ts
export function trackStytchPerformance() {
  if (typeof window === 'undefined') return;
  
  const stytchStats = {
    clientInitializations: 0,
    authenticationChecks: 0,
    cachedAuthChecks: 0,
    authErrors: 0,
  };
  
  // Monkey patch the Stytch client initialization
  const originalGetStytchB2BClient = require('lib/stytch-b2b-client').getStytchB2BClient;
  require('lib/stytch-b2b-client').getStytchB2BClient = function() {
    stytchStats.clientInitializations++;
    console.log('Stytch client initializations:', stytchStats.clientInitializations);
    return originalGetStytchB2BClient();
  };
  
  // Monkey patch the authentication function
  const originalAuthenticateSession = require('lib/utils/auth-utils').authenticateSession;
  require('lib/utils/auth-utils').authenticateSession = async function() {
    stytchStats.authenticationChecks++;
    
    const result = await originalAuthenticateSession();
    
    if (result.authenticated) {
      stytchStats.cachedAuthChecks++;
    } else {
      stytchStats.authErrors++;
    }
    
    console.log('Stytch authentication stats:', {
      checks: stytchStats.authenticationChecks,
      cached: stytchStats.cachedAuthChecks,
      errors: stytchStats.authErrors,
    });
    
    return result;
  };
  
  return stytchStats;
}
```

### 2. Validation Script

Create a script to validate the Stytch client optimization.

```typescript
// scripts/test-stytch-optimization.ts
import { getStytchB2BClient, getStytchB2BClientAsync, resetStytchB2BClient } from 'lib/stytch-b2b-client';
import { authenticateSession } from 'lib/utils/auth-utils';
import { cacheAuthData, getCachedAuthData, invalidateAuthCache } from 'lib/utils/auth-cache';

async function testStytchOptimization() {
  console.log('Testing Stytch client optimization...');
  
  // Test singleton pattern
  console.log('Testing singleton pattern...');
  const client1 = getStytchB2BClient();
  const client2 = getStytchB2BClient();
  console.log('Clients are the same instance:', client1 === client2);
  
  // Test async singleton pattern
  console.log('Testing async singleton pattern...');
  const asyncClient1 = await getStytchB2BClientAsync();
  const asyncClient2 = await getStytchB2BClientAsync();
  console.log('Async clients are the same instance:', asyncClient1 === asyncClient2);
  
  // Test auth cache
  console.log('Testing auth cache...');
  const testSessionToken = 'test-session-token';
  const testUserId = 'test-user-id';
  const testOrgId = 'test-org-id';
  
  cacheAuthData(testSessionToken, testUserId, testOrgId);
  const cachedData = getCachedAuthData(testSessionToken);
  
  console.log('Cache working correctly:', 
    cachedData?.userId === testUserId && 
    cachedData?.organizationId === testOrgId
  );
  
  // Test cache invalidation
  invalidateAuthCache(testSessionToken);
  const invalidatedCache = getCachedAuthData(testSessionToken);
  
  console.log('Cache invalidation working correctly:', invalidatedCache === undefined);
  
  // Reset for next tests
  resetStytchB2BClient();
}

testStytchOptimization().catch(console.error);
```

## Expected Outcomes

After implementing these optimizations, we expect:

1. **Single Initialization**: The Stytch B2B client will be initialized only once
2. **Reduced API Calls**: Authentication checks will be cached, reducing API calls
3. **Improved Performance**: Server-side performance will improve due to reduced initialization overhead
4. **Eliminated Race Conditions**: Race conditions from multiple initializations will be eliminated
5. **Better Error Handling**: More robust error handling for Stytch client initialization

## Implementation Checklist

- [ ] Implement singleton pattern for Stytch client
- [ ] Optimize client loading
- [ ] Implement server-side caching for authentication
- [ ] Optimize auth provider
- [ ] Update API routes to use the optimized client
- [ ] Optimize middleware
- [ ] Create authentication utility functions
- [ ] Update API routes to use the authentication utility
- [ ] Implement performance monitoring
- [ ] Test and validate the implementation

This optimization plan addresses the specific issues with the Stytch B2B client that contribute to the overall performance problems. By implementing these changes, we can significantly improve the application's performance and stability.
