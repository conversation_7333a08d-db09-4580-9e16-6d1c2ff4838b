# Location Management SaaS RBAC User Roles & Access Levels

This document defines the Role-Based Access Control (RBAC) system for the Location Management SaaS platform, detailing the various user roles and their specific access levels across all system features and resources.

## Table of Contents

- [Role Overview](#role-overview)
- [Role Definitions](#role-definitions)
  - [Admin](#admin)
  - [Manager](#manager)
  - [Scout](#scout)
  - [Viewer](#viewer)
- [Access Matrix](#access-matrix)
- [Resource-Specific Permissions](#resource-specific-permissions)
  - [Organization Resources](#organization-resources)
  - [Project Resources](#project-resources)
  - [Location Resources](#location-resources)
  - [Document Resources](#document-resources)
  - [Map Resources](#map-resources)
  - [Task Resources](#task-resources)
  - [Billing Resources](#billing-resources)
- [Special Permissions](#special-permissions)
- [Permission Implementation](#permission-implementation)

## Role Overview

The Location Management SaaS platform implements a four-tier role hierarchy within each organization:

1. **Admin**: Organization administrators with full system access
2. **Manager**: Project and team managers with extensive operational permissions
3. **Scout**: Location scouts with focused location management capabilities
4. **Viewer**: Read-only access for clients, crew, and stakeholders

Every user must have at least one role within their organization. These roles operate exclusively within the organization context, and a user's role in one organization has no impact on their permissions in another organization.

## Role Definitions

### Admin

**Description:**  
Organization administrators with full system access and control over all aspects of the platform.

**Typical Users:**  
Production executives, company owners, senior location managers

**Key Capabilities:**
- Manage organization settings and billing
- Control user access and roles
- Full access to all features
- Configure SSO and security settings
- Delete/archive any resource

### Manager

**Description:**  
Project and team managers with extensive permissions to manage productions, but without organization-wide administrative capabilities.

**Typical Users:**  
Location managers, production managers, team leads

**Key Capabilities:**
- Create and manage projects
- Approve locations and documents
- Manage team members within projects
- Assign and track tasks
- Create custom maps and reports

### Scout

**Description:**  
Location scouts with focused permissions on finding, documenting, and recommending locations.

**Typical Users:**  
Location scouts, assistant location managers, field researchers

**Key Capabilities:**
- Add and edit locations
- Upload location media
- Create basic tasks
- Tag and categorize locations
- Submit locations for approval

### Viewer

**Description:**  
Read-only access for stakeholders who need to view but not modify data.

**Typical Users:**  
Clients, directors, producers, crew members, external stakeholders

**Key Capabilities:**
- View approved locations, documents, and maps
- Access shared reports
- View project timelines
- Comment on locations (if enabled)
- Basic search and filtering

## Access Matrix

| Action                      | Admin | Manager | Scout | Viewer |
|-----------------------------|-------|---------|-------|--------|
| **Organization Management** |       |         |       |        |
| Manage organization settings| ✓     | ✗       | ✗     | ✗      |
| Configure SSO               | ✓     | ✗       | ✗     | ✗      |
| Manage billing/subscription | ✓     | ✗       | ✗     | ✗      |
| Add/remove users            | ✓     | ✗       | ✗     | ✗      |
| Modify user roles           | ✓     | ✗       | ✗     | ✗      |
| **Project Management**      |       |         |       |        |
| Create projects             | ✓     | ✓       | ✗     | ✗      |
| Edit projects               | ✓     | ✓       | ✗     | ✗      |
| Delete projects             | ✓     | ✗       | ✗     | ✗      |
| View projects               | ✓     | ✓       | ✓     | ✓      |
| Manage project team         | ✓     | ✓       | ✗     | ✗      |
| **Location Management**     |       |         |       |        |
| Create locations            | ✓     | ✓       | ✓     | ✗      |
| Edit locations              | ✓     | ✓       | ✓     | ✗      |
| Delete locations            | ✓     | ✓       | ✗     | ✗      |
| Approve locations           | ✓     | ✓       | ✗     | ✗      |
| View locations              | ✓     | ✓       | ✓     | ✓      |
| **Document Management**     |       |         |       |        |
| Upload documents            | ✓     | ✓       | ✓     | ✗      |
| Edit documents              | ✓     | ✓       | ✓     | ✗      |
| Delete documents            | ✓     | ✓       | ✗     | ✗      |
| Approve documents           | ✓     | ✓       | ✗     | ✗      |
| View documents              | ✓     | ✓       | ✓     | ✓      |
| **Map Management**          |       |         |       |        |
| Create custom maps          | ✓     | ✓       | ✗     | ✗      |
| Edit map layers             | ✓     | ✓       | ✗     | ✗      |
| Delete maps                 | ✓     | ✓       | ✗     | ✗      |
| View maps                   | ✓     | ✓       | ✓     | ✓      |
| Add markers to maps         | ✓     | ✓       | ✓     | ✗      |
| **Task Management**         |       |         |       |        |
| Create tasks                | ✓     | ✓       | ✓     | ✗      |
| Assign tasks                | ✓     | ✓       | ✗     | ✗      |
| Complete own tasks          | ✓     | ✓       | ✓     | ✗      |
| Delete tasks                | ✓     | ✓       | ✗     | ✗      |
| View tasks                  | ✓     | ✓       | ✓     | ✓      |
| **API Access**              |       |         |       |        |
| Generate API keys           | ✓     | ✗       | ✗     | ✗      |
| Use API                     | ✓     | ✓       | ✗     | ✗      |
| **Advanced Features**       |       |         |       |        |
| Access AI tools             | ✓     | ✓       | ✓     | ✗      |
| Create reports              | ✓     | ✓       | ✗     | ✗      |
| Export data                 | ✓     | ✓       | ✓     | ✗      |

## Resource-Specific Permissions

### Organization Resources

**Admin:**
- Create/delete organizations
- Manage organization settings
- Configure branding and defaults
- Manage billing and subscriptions
- Add/remove members
- Configure SSO and security settings
- Manage role permissions
- Access and export audit logs
- Delete or archive organization

**Manager:**
- View organization details
- Access organization-wide data
- View member list

**Scout:**
- View organization details
- Limited view of organization-wide data

**Viewer:**
- View organization name and basic info

### Project Resources

**Admin:**
- Create, edit, delete projects
- Archive projects
- Manage project settings
- Add/remove team members
- Set project budgets
- Export all project data
- Configure project templates

**Manager:**
- Create, edit projects
- Manage project settings
- Add/remove team members
- Track project budgets
- Set project timelines
- Generate project reports

**Scout:**
- View project details
- Access assigned projects
- View project timelines
- Basic project reporting

**Viewer:**
- View project details (read-only)
- View project timelines
- View shared reports

### Location Resources

**Admin:**
- Create, edit, delete locations
- Approve/reject locations
- Set location status
- Manage location contracts
- Configure location metadata fields
- Bulk operations on locations
- Delete location media

**Manager:**
- Create, edit, delete locations
- Approve/reject locations
- Set location status
- Manage location permits
- Add/edit location media
- Edit location metadata

**Scout:**
- Create, edit locations
- Upload location media
- Tag and categorize locations
- Submit locations for approval
- Add basic location metadata
- Record location measurements

**Viewer:**
- View approved locations
- View location media
- Access location details
- View location maps
- View permit status (no editing)

### Document Resources

**Admin:**
- Upload, edit, delete documents
- Approve/reject documents
- Manage document templates
- Configure document workflows
- Manage document categories
- Delete document versions

**Manager:**
- Upload, edit, delete documents
- Approve/reject documents
- Use document templates
- Organize documents
- Manage document versions

**Scout:**
- Upload, edit documents
- Use document templates
- Submit documents for approval
- Add document metadata

**Viewer:**
- View approved documents
- Download shared documents
- View document metadata

### Map Resources

**Admin:**
- Create, edit, delete maps
- Configure map defaults
- Manage custom layers
- Create/edit saved views
- Set map permissions
- Export map data

**Manager:**
- Create, edit, delete maps
- Use custom layers
- Create/edit saved views
- Add features to maps
- Draw on maps

**Scout:**
- View all maps
- Use predefined layers
- Add location markers
- Create basic route plans
- Use saved views

**Viewer:**
- View approved maps
- Access shared saved views
- View location markers
- View route information

### Task Resources

**Admin:**
- Create, edit, delete all tasks
- Assign tasks to any user
- Set task priorities
- Configure task templates
- Track all task progress
- Manage task categories

**Manager:**
- Create, edit, delete tasks
- Assign tasks to team members
- Set task priorities
- Track team task progress
- Use task templates

**Scout:**
- Create personal tasks
- Create location-related tasks
- Complete assigned tasks
- Track own task progress
- View team tasks

**Viewer:**
- View task summaries
- View project timelines
- No task creation/editing

### Billing Resources

**Admin:**
- Manage subscription plans
- Update payment methods
- View/download invoices
- Configure billing details
- Add/remove add-on features
- Set billing notifications

**Manager:**
- View subscription details (if allowed by admin)
- Track feature usage

**Scout:**
- No billing access

**Viewer:**
- No billing access

## Special Permissions

### Content Ownership

Users who create content (locations, documents, tasks) have special permissions:

1. **Creators can always edit their own content**, even if their role typically wouldn't allow editing that type of content.
2. **Creators cannot delete their content** if it has been approved by a Manager or Admin.
3. **Content ownership transfers** when a user is removed from an organization.

### Project-Level Role Overrides

The system supports project-specific role assignments that can override organization-level permissions:

1. **Project Manager Assignment**: An organization Manager or Admin can assign a Scout as a Project Manager for a specific project, granting them Manager-level permissions only within that project.
2. **Restricted Viewer**: An organization Admin can create a "Restricted Viewer" for specific projects only.

### Time-Limited Access

For temporary collaborators:

1. **Temporary Access**: Admins can grant time-limited access to any role.
2. **Guest Access**: Special limited-time access for external stakeholders.

## Permission Implementation

### Technical Implementation

Permissions are implemented through:

1. **Middleware Checks**: Server-side verification on all API routes
2. **Database Queries**: Filtered by organization and permission level
3. **UI Rendering**: Conditional display of actions based on permissions
4. **Token-Based Verification**: JWT tokens with role and organization claims

### Permission Evaluation Order

When evaluating permissions, the system follows this order:

1. **Authentication Check**: Verify user is authenticated
2. **Organization Membership**: Verify user belongs to the requested organization
3. **Role Check**: Verify user has a role that can perform the action
4. **Resource Ownership**: Check if user owns the resource (for edit permissions)
5. **Special Overrides**: Apply any project-specific or time-based overrides

### Default Deny

The system implements a "default deny" security posture:

1. All actions are denied by default
2. Permissions must be explicitly granted
3. If multiple permission rules apply, the most restrictive takes precedence

### Audit Logging

All permission-sensitive actions are logged:

1. **Action Logs**: Record who performed what action
2. **Access Logs**: Track resource access
3. **Permission Changes**: Log all role and permission modifications
4. **Login Activity**: Record all authentication events

## Customization Options

Organization Admins can customize certain aspects of the RBAC system:

1. **Custom Roles**: Create organization-specific roles (Enterprise plan only)
2. **Permission Adjustments**: Fine-tune specific permissions for standard roles
3. **Approval Workflows**: Configure which actions require approval
4. **Default Project Permissions**: Set organization-wide defaults
5. **Resource Visibility Rules**: Configure which resources are visible to Viewers