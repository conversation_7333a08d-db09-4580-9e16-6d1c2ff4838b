# Stytch RBAC Configuration Reference

This document provides a human-readable summary of the custom Roles and their permissions, intended as a reference for manually configuring the RBAC policy in the Stytch Dashboard. It is based on the JSON structure defined in the `RBAC_Implementation_Plan.md`.

**Note:** `*` indicates all actions defined for that resource. Application-specific logic is still required for certain checks (e.g., content ownership, approved status filtering).

---

## Custom Resources & Actions

First, ensure these Resources and their Actions are defined in the Stytch Dashboard:

*   **Resource:** `organization`
    *   **Description:** Application-specific organization settings and management beyond Stytch defaults.
    *   **Actions:** `manage_settings`, `manage_billing`, `view_details`, `view_members`, `manage_custom_roles`, `adjust_role_permissions`, `configure_approval_workflows`, `set_default_project_permissions`, `set_resource_visibility_rules`, `generate_api_keys`, `access_audit_logs`, `delete_organization`
*   **Resource:** `project`
    *   **Description:** Represents a production or project.
    *   **Actions:** `create`, `edit`, `delete`, `view`, `manage_team`, `manage_settings`, `manage_budget`, `manage_timeline`, `generate_reports`, `archive`, `export_data`, `configure_templates`, `assign_project_manager_role`
*   **Resource:** `location`
    *   **Description:** Represents a physical location.
    *   **Actions:** `create`, `edit`, `delete`, `approve`, `view`, `set_status`, `manage_contracts`, `manage_permits`, `manage_media`, `tag`, `categorize`, `submit_for_approval`, `bulk_operations`, `record_measurements`
*   **Resource:** `document`
    *   **Description:** Represents documents associated with projects or locations.
    *   **Actions:** `create`, `edit`, `delete`, `approve`, `view`, `manage_templates`, `manage_workflows`, `manage_categories`, `manage_versions`, `download`, `submit_for_approval`
*   **Resource:** `map`
    *   **Description:** Represents custom maps and map features.
    *   **Actions:** `create`, `edit`, `delete`, `view`, `manage_layers`, `manage_views`, `add_markers`, `set_permissions`, `export_data`, `configure_defaults`, `create_route_plans`, `add_features`, `draw`
*   **Resource:** `task`
    *   **Description:** Represents tasks within projects.
    *   **Actions:** `create`, `edit`, `delete`, `view`, `assign`, `complete_own`, `set_priority`, `manage_templates`, `track_progress`, `track_own_progress`, `manage_categories`
*   **Resource:** `billing`
    *   **Description:** Permissions related to viewing/managing billing.
    *   **Actions:** `view_subscription`, `track_usage`
*   **Resource:** `api`
    *   **Description:** Permissions for using the application API.
    *   **Actions:** `use`
*   **Resource:** `ai_tools`
    *   **Description:** Permissions for accessing AI-powered features.
    *   **Actions:** `access`
*   **Resource:** `reports`
    *   **Description:** Permissions for creating and viewing reports.
    *   **Actions:** `create`, `view_shared`

*(Remember to also consider the necessary actions for the default Stytch resources: `stytch.organization`, `stytch.member`, `stytch.sso`, `stytch.self`)*

---

## Custom Role Definitions

Define the following Roles and assign the specified permissions:

### 1. Role: `admin`

*   **Description:** Organization administrators with full system access.
*   **Permissions:**
    *   `organization`: `*` (All Actions)
    *   `project`: `*` (All Actions)
    *   `location`: `*` (All Actions)
    *   `document`: `*` (All Actions)
    *   `map`: `*` (All Actions)
    *   `task`: `*` (All Actions)
    *   `billing`: `*` (All Actions)
    *   `api`: `*` (All Actions)
    *   `ai_tools`: `*` (All Actions)
    *   `reports`: `*` (All Actions)
    *   `stytch.organization`: `*` (Recommended: All Actions, review if needed)
    *   `stytch.member`: `*` (Recommended: All Actions, review if needed)
    *   `stytch.sso`: `*` (Recommended: All Actions, review if needed)

### 2. Role: `manager`

*   **Description:** Project and team managers with extensive operational permissions.
*   **Permissions:**
    *   `organization`: `view_details`, `view_members`
    *   `project`: `create`, `edit`, `view`, `manage_team`, `manage_settings`, `manage_budget`, `manage_timeline`, `generate_reports`, `assign_project_manager_role`
    *   `location`: `create`, `edit`, `delete`, `approve`, `view`, `set_status`, `manage_permits`, `manage_media`, `tag`, `categorize`, `submit_for_approval`
    *   `document`: `create`, `edit`, `delete`, `approve`, `view`, `manage_templates`, `manage_versions`, `download`, `submit_for_approval`
    *   `map`: `create`, `edit`, `delete`, `view`, `manage_layers`, `manage_views`, `add_markers`, `add_features`, `draw`, `create_route_plans`
    *   `task`: `create`, `edit`, `delete`, `view`, `assign`, `complete_own`, `set_priority`, `track_progress`, `manage_templates`
    *   `billing`: `view_subscription`, `track_usage`
    *   `api`: `use`
    *   `ai_tools`: `access`
    *   `reports`: `create`, `view_shared`
    *   `stytch.member`: `search` (Allows viewing/searching other members)

### 3. Role: `scout`

*   **Description:** Location scouts with focused location management capabilities.
*   **Permissions:**
    *   `organization`: `view_details`
    *   `project`: `view`
    *   `location`: `create`, `edit`, `view`, `manage_media`, `tag`, `categorize`, `submit_for_approval`, `record_measurements` (Note: Edit permission might be further restricted by app logic based on ownership)
    *   `document`: `create`, `edit`, `view`, `manage_templates`, `submit_for_approval`, `download` (Note: Edit permission might be further restricted by app logic based on ownership)
    *   `map`: `view`, `manage_views`, `add_markers`, `create_route_plans`
    *   `task`: `create`, `view`, `complete_own`, `track_own_progress`
    *   `ai_tools`: `access`
    *   `reports`: `view_shared`

### 4. Role: `viewer`

*   **Description:** Read-only access for clients, crew, and stakeholders.
*   **Permissions:**
    *   `organization`: `view_details`
    *   `project`: `view`
    *   `location`: `view` (Note: App logic needed to filter for 'approved' status)
    *   `document`: `view`, `download` (Note: App logic needed to filter for 'approved' status)
    *   `map`: `view` (Note: App logic needed to filter for 'approved' status)
    *   `task`: `view` (Note: App logic needed to show summaries/timeline)
    *   `reports`: `view_shared`

---

## Default Stytch Roles (Review & Adjust if Needed)

### Role: `stytch_admin`

*   **Description:** Default Stytch admin role, assigned on Org creation.
*   **Default Permissions (Review):**
    *   `stytch.organization`: `*`
    *   `stytch.member`: `*`
    *   `stytch.sso`: `*`
*   **Consider:** Should this role *also* get the permissions of the custom `admin` role defined above? Or should they be kept separate?

### Role: `stytch_member`

*   **Description:** Default Stytch member role, assigned to all members. Cannot be removed.
*   **Default Permissions (Review):**
    *   `stytch.self`: `*` (Allows users to manage their own profile info like name, MFA, password)
*   **Consider:** Should all members automatically get any baseline app permissions? E.g., add `organization: view_details`?

---

Use this guide to populate the Roles & Permissions section in your Stytch Dashboard accurately.
