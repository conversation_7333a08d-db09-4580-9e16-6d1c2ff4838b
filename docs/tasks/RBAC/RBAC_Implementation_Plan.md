# RBAC Implementation Plan for Location Management SaaS

## 1. Goal

To implement the Role-Based Access Control (RBAC) system using Stytch, based on the defined roles (<PERSON><PERSON>, Manager, Scout, Viewer) and resources outlined in `docs/tasks/RBAC/User Roles and Access`. This plan details the atomic steps required for implementation, including backend API protection, frontend UI adaptation, and handling special permission logic.

## 2. Prerequisites

*   Stytch B2B project configured.
*   Defined RBAC roles and resources documented in `docs/tasks/RBAC/User Roles and Access`.
*   Agreed-upon JSON structure for the Stytch RBAC Policy (Resources & Roles definitions).
*   Stytch B2B SDK installed and configured in the project (`lib/stytch-b2b.ts`).

## 3. Documentation Protocol

*   **Build Log:** After completing each step, add an entry to `docs/Build Log.md` detailing the step completed, the date, and any brief notes.
*   **RBAC Task Folder:** For significant findings, complex logic decisions, or troubleshooting notes related to a specific step, create or append to relevant markdown files within `docs/tasks/RBAC/` (e.g., `Middleware_Notes.md`, `Special_Permissions_Notes.md`).
*   **Completion:** Upon finishing the entire plan, add a final entry to the Build Log summarizing the completion of RBAC implementation.

## 4. Atomic Implementation Steps

**Phase 1: Stytch Configuration & Backend Setup**

1.  **Configure Stytch RBAC Policy:**
    *   **Action:** Manually create all custom Resources (organization, project, location, etc.) and custom Roles (admin, manager, scout, viewer) with their associated permissions in the Stytch Dashboard under "Roles & Permissions", precisely matching the agreed-upon JSON structure. Also, review and adjust permissions for default Stytch roles (`stytch_admin`, `stytch_member`) if necessary.
    *   **Verification:** Double-check that the configured policy in the Stytch Dashboard exactly matches the intended JSON structure and the definitions in `docs/tasks/RBAC/User Roles and Access`.
    *   **Documentation:** Update Build Log.

2.  **Implement/Update RBAC Middleware (`middleware.ts`):**
    *   **Action:** Ensure the existing `middleware.ts` correctly authenticates the Stytch session token (`stytch.sessions.authenticate`). If not already done, modify it to potentially fetch and attach the member's roles or the full session object to the request context for easier access in API routes. Handle potential authentication errors gracefully.
    *   **Verification:** Test that protected routes require a valid Stytch session and that session data (including roles) is accessible downstream. Test unauthenticated access rejection.
    *   **Documentation:** Update Build Log. Create/update `docs/tasks/RBAC/Middleware_Notes.md` if there are significant implementation details or challenges.

3.  **Create RBAC Helper Function:**
    *   **Action:** Create a reusable server-side function (e.g., `lib/rbac/checkPermission.ts` or similar). This function should accept the required `action`, `resource_id`, and the authenticated Stytch `MemberSession` object (obtained from middleware/context). Internally, it will use `stytchClient.rbac.authenticate({ session_token, action, resource_id })` to perform the check against the Stytch policy. It should return a boolean (`true` if authorized, `false` otherwise). Handle potential errors from the Stytch API call.
    *   **Verification:** Write unit tests for this helper function, covering authorized, unauthorized, and error scenarios.
    *   **Documentation:** Update Build Log.

**Phase 2: API Endpoint Protection**

4.  **Secure API Endpoints (Start with Projects):**
    *   **Action:** Go to the API route handlers for project operations (e.g., `app/api/organizations/[organizationSlug]/projects/route.ts` for creating/listing, `app/api/projects/[projectId]/route.ts` for get/update/delete). Import and use the `checkPermission` helper function at the beginning of each relevant handler (e.g., `POST` for create, `PUT` for edit, `DELETE` for delete). Pass the required action (e.g., 'create'), resource ('project'), and the session token/object. If the check returns `false`, return a 403 Forbidden response immediately.
    *   **Verification:** Use tools like Postman or write integration tests to call these API endpoints using session tokens from users with different roles (Admin, Manager, Scout, Viewer). Verify that only authorized roles can perform actions and others receive a 403 error.
    *   **Documentation:** Update Build Log.

5.  **Secure API Endpoints (Remaining Resources):**
    *   **Action:** Systematically repeat Step 4 for all other resource types: Locations, Documents, Maps, Tasks, Organization settings, etc. Apply the `checkPermission` helper in their respective API route handlers.
    *   **Verification:** Test API endpoints for each resource type with different user roles.
    *   **Documentation:** Update Build Log after securing each major resource type.

**Phase 3: Frontend Integration**

6.  **Implement Frontend Permission Hook:**
    *   **Action:** Create a client-side hook (e.g., `hooks/usePermissions.ts`). This hook should access the current Stytch session/user data (likely from a context provider like `StytchProvider`). It should expose a function, e.g., `hasPermission(action: string, resource: string): boolean`, which checks if the current user's roles (available in the session data) grant the specified permission based on the *client-side understanding* of the RBAC policy (this might involve fetching the policy or having a simplified version client-side, or making calls to a dedicated backend endpoint - choose the appropriate strategy). *Alternatively, and often simpler, this hook might just expose the user's roles, and components decide based on roles.* A more robust approach involves a backend check if complex logic is needed client-side. *Initial approach recommendation: Expose roles via the hook.*
    *   **Verification:** Test the hook returns the correct roles or permission status for users logged in with different roles.
    *   **Documentation:** Update Build Log.

7.  **Adapt UI Components (Start with Project Creation):**
    *   **Action:** In UI components related to project actions (e.g., the button to create a new project, edit/delete buttons in a project list), use the `usePermissions` hook (or directly access roles from context). Conditionally render the UI element based on the required permission/role. For example, wrap the "Create Project" button: `{hasPermission('create', 'project') && <Button>Create Project</Button>}` or `{userRoles.includes('admin') || userRoles.includes('manager') ? <Button>Create Project</Button> : null}`.
    *   **Verification:** Log in as users with different roles (Admin, Manager, Scout, Viewer). Verify that buttons/actions are correctly shown or hidden based on their permissions.
    *   **Documentation:** Update Build Log.

8.  **Adapt UI Components (Remaining Features):**
    *   **Action:** Systematically repeat Step 7 throughout the application for all features controlled by RBAC (location editing, document uploading, map creation, task assignment, organization settings access, etc.). Apply conditional rendering based on permissions/roles.
    *   **Verification:** Perform UI walkthroughs with each role, ensuring the interface correctly reflects their allowed actions.
    *   **Documentation:** Update Build Log as major sections of the UI are adapted.

**Phase 4: Role Assignment & Special Logic**

9.  **Implement Explicit Role Assignment UI/API:**
    *   **Action:** Modify the UI components for inviting new members and editing existing members. Add controls (e.g., dropdowns) allowing Admins to select one or more roles (`admin`, `manager`, `scout`, `viewer`) for the user. Update the corresponding backend API calls (`stytchClient.organizations.members.create`, `stytchClient.organizations.members.update`) to include the `roles` array in the request body. Ensure the API endpoint performing the update checks if the *calling* user has permission to modify roles (`stytch.member:update.settings.roles`).
    *   **Verification:** As an Admin, invite a new user and assign the 'Scout' role. Verify the user is created with that role in Stytch. As an Admin, edit an existing user and change their role to 'Manager'. Verify the change in Stytch. Test that a non-Admin cannot change roles.
    *   **Documentation:** Update Build Log. Create/update `docs/tasks/RBAC/Role_Assignment_Notes.md` for any complex UI/API interactions.

10. **Implement Special Permission Logic (Content Ownership):**
    *   **Action:** In the backend API route handlers for *editing* and *deleting* resources (e.g., Locations, Documents), *after* the standard Stytch RBAC check (`checkPermission`), add application-level logic.
        *   For **Edit:** If the standard check fails, query the database to check if the `userId` from the session matches the `creatorId` of the resource. If they match, allow the edit operation to proceed.
        *   For **Delete:** If the standard check passes, query the database to check if the resource has an 'approved' status. If it is approved, *prevent* deletion even if the role allows it (as per the spec: "Creators cannot delete their content if it has been approved"). Return a 403 or appropriate error.
    *   **Verification:** Test that a Scout can edit a location they created, even if it's approved. Test that a Scout *cannot* delete an approved location they created. Test that a Manager *can* delete an approved location (assuming their role permits delete).
    *   **Documentation:** Update Build Log. Create/update `docs/tasks/RBAC/Special_Permissions_Notes.md`.

11. **Implement Special Permission Logic (Project Role Overrides):**
    *   **Action:**
        *   **Schema:** Add a mechanism to store project-specific roles (e.g., modify the `project_members` table to include an optional `project_role` column).
        *   **UI/API:** Create UI and API endpoints for Admins/Managers to assign a project-specific role (e.g., assign a 'Scout' as 'Project Manager' for Project X).
        *   **RBAC Check Update:** Modify the `checkPermission` helper or create a new one for project-context actions. This check should first look for a project-specific role for the user on that project. If found, evaluate permissions based on that role. If not found, fall back to the user's organization-level roles.
    *   **Verification:** Assign a Scout as Project Manager for Project A. Verify they can perform Manager actions *only* within Project A, but retain Scout permissions elsewhere.
    *   **Documentation:** Update Build Log. Update `docs/tasks/RBAC/Special_Permissions_Notes.md`.

**Phase 5: Testing & Finalization**

12. **Comprehensive End-to-End Testing:**
    *   **Action:** Create test user accounts for each role (Admin, Manager, Scout, Viewer). Log in as each user and systematically test their access to all features, API endpoints, and UI elements. Verify they can perform allowed actions and are blocked from disallowed actions. Test edge cases and the special permission logic.
    *   **Verification:** All test cases pass. Create a checklist based on the Access Matrix in `User Roles and Access` doc and verify each item.
    *   **Documentation:** Update Build Log. Document any bugs found and fixed in `docs/tasks/RBAC/Testing_Notes.md`.

13. **Final Documentation Update:**
    *   **Action:** Ensure the `docs/Build Log.md` accurately reflects all completed steps. Review all notes created in `docs/tasks/RBAC/`. Consolidate important findings or create a summary document (`docs/tasks/RBAC/RBAC_Implementation_Summary.md`) if necessary. Ensure the main `README.md` or relevant architecture documents briefly mention the RBAC implementation.
    *   **Verification:** All documentation is consistent and up-to-date.
    *   **Documentation:** Update Build Log with the final completion entry.

## 5. Completion Criteria

*   All steps in this plan are completed and verified.
*   Stytch RBAC policy is correctly configured.
*   Backend APIs are protected according to the defined roles and permissions.
*   Frontend UI correctly reflects user permissions.
*   Special permission logic (ownership, overrides) is implemented and functional.
*   Role assignment is working correctly.
*   Comprehensive testing confirms the system behaves as expected for all roles.
*   All required documentation (Build Log, RBAC task notes) is complete.
