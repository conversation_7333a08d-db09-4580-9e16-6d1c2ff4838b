# Stytch RBAC Implementation Notes & Troubleshooting Guide

This document provides context on the Role-Based Access Control (RBAC) implementation using St<PERSON>ch within the Sceneomatic v2 application, focusing on the flow, key components, and troubleshooting insights gained during development.

## RBAC Implementation Overview

The system leverages Stytch B2B for authentication and authorization. The core flow for checking permissions on protected API routes is as follows:

1.  **Middleware (`middleware.ts`):**
    *   Intercepts incoming requests.
    *   Checks for public vs. protected routes.
    *   Authenticates the user's session using either `stytch_session_token` or `stytch_session_jwt` cookies via `stytchClient.sessions.authenticate` or `stytchClient.sessions.authenticateJwt`.
    *   Upon successful authentication, extracts the `member_id`, `organization_id`, and `roles` from the Stytch session.
    *   Attaches this information, along with the original token/JWT used for authentication, to the request headers (`X-Stytch-Member-Id`, `X-Stytch-Org-Id`, `X-Stytch-Roles`, `X-Stytch-Session-Token`/`X-Stytch-Session-JWT`).
    *   Forwards the request to the appropriate API route handler.

2.  **API Route Handlers (e.g., `app/api/.../route.ts`):**
    *   Receive the request with headers populated by the middleware.
    *   Extract the necessary authentication context (Stytch Org ID, session token/JWT) from the headers.
    *   Call the `checkPermission` helper function before executing protected logic.

3.  **Permission Helper (`lib/rbac/checkPermission.ts`):**
    *   Accepts authentication parameters (either `session_token` or `session_jwt`), the Stytch Organization ID, the `action` being performed (e.g., "view", "create"), and the `resourceId` being accessed (e.g., "project", "location").
    *   Uses the Stytch SDK's `stytchClient.sessions.authenticate` method, passing the correct token type (`session_token` or `session_jwt`) along with an `authorization_check` object containing the `organization_id`, `action`, and `resource_id`.
    *   Returns `true` if the Stytch API call succeeds (indicating the user is authorized according to the policy defined in Stytch).
    *   Returns `false` if the Stytch API call throws an error (including permission denied errors or invalid token errors). Includes detailed logging of `StytchError` for debugging.

## Troubleshooting: 403 Forbidden on `GET /api/.../projects`

During the implementation of RBAC checks (Phase 2, Step 4 of the plan), a persistent 403 Forbidden error was encountered when accessing the projects list page, even for users expected to have access (e.g., 'admin' role).

**Troubleshooting Steps & Findings:**

1.  **Initial Error:** Server logs showed `RBAC check failed: User ... does not have 'read' permission on 'project' in org ...`.
2.  **Verify User Role:** Confirmed the test user (`member-live-...`) was assigned the necessary roles (`admin`, `stytch_member`, `stytch_admin`) within the correct organization (`organization-live-...`) in the Stytch dashboard. Initially, the 'admin' role was missing and was added.
3.  **Refresh Session:** Logged out and back in to ensure a fresh session token with the updated roles was being used. The error persisted.
4.  **Verify Middleware:** Checked `middleware.ts` to confirm it correctly authenticated the session and passed the Stytch Org ID, Member ID, Roles, and the original session token/JWT via request headers. Added logging to the API route to confirm headers were received. Log `X-Stytch-Roles header: ["stytch_member","admin","stytch_admin"]` confirmed roles were passed correctly.
5.  **Verify `checkPermission` Call:** Examined the API route (`app/api/organizations/[organizationId]/projects/route.ts`) to ensure `checkPermission` was called with the correct parameters.
6.  **Check Action Name:** Investigated potential mismatch between the action checked in code (`'read'`) and the action defined in Stytch policy. Changed the code to check for `'view'` based on Stytch configuration details provided. The error persisted.
7.  **Check Stytch Policy Configuration:**
    *   Verified the `project` resource existed with the correct ID.
    *   Verified the `admin` role existed.
    *   Verified the `admin` role had wildcard (`"*"`) permission for the `project` resource.
    *   Identified and corrected formatting issues (extra backticks/commas) in the action names listed within the `project` resource definition in Stytch. The error persisted.
    *   Explicitly listed all actions (including `"view"`) for the `admin` role on the `project` resource, removing the wildcard. The error persisted.
8.  **Add Detailed Error Logging:** Modified `checkPermission` to log the specific `StytchError` details (`error_type`, `error_message`, `status_code`).
9.  **Identify Root Cause:** The detailed logs revealed the actual error from Stytch: `Error Type: invalid_session_token, Message: Session token format is invalid., Code: 400`. This indicated the token being passed to `stytchClient.sessions.authenticate` was invalid *for that specific SDK method parameter*.
10. **Pinpoint Code Issue:** Realized the `checkPermission` function was always passing the token/JWT retrieved from headers to the `session_token` parameter of `stytchClient.sessions.authenticate`. If the user authenticated with a JWT, this would cause the "invalid format" error.
11. **Implement Fix:**
    *   Refactored `checkPermission` to accept an `AuthParams` object (`{ session_token?: string | null; session_jwt?: string | null; }`).
    *   Updated `checkPermission` to dynamically construct the arguments for `stytchClient.sessions.authenticate`, using the `session_token` parameter if `authParams.session_token` is present, or the `session_jwt` parameter if `authParams.session_jwt` is present.
    *   Updated the calling API route (`projects/route.ts`) to pass the `AuthParams` object correctly: `checkPermission({ session_token: sessionToken, session_jwt: sessionJwt }, ...)`.
12. **Resolution:** After applying the fix, the `invalid_session_token` error was resolved, and the RBAC check passed successfully, eliminating the 403 error.

## Key Takeaways & Best Practices

*   **Stytch Policy is King:** Always double-check the exact resource IDs, action names (including formatting), role IDs, and permission assignments directly in the Stytch dashboard. Documentation can become outdated.
*   **Token vs. JWT:** Be mindful of the different session credential types (opaque tokens vs. JWTs) used by Stytch. Ensure the correct SDK method parameter (`session_token` or `session_jwt`) is used when authenticating or performing checks.
*   **Detailed Error Logging:** When troubleshooting API errors (especially 4xx codes), logging the specific error details (type, message, code) returned by the external service (like Stytch) is crucial for diagnosis.
*   **Internal vs. External IDs:** Use internal database primary keys (UUIDs) for internal foreign key relationships. Store external service IDs (like Stytch Org/Member IDs) in dedicated fields. Use the appropriate ID depending on whether you are interacting with your database or the external service. This is the current setup and is correct.
*   **RBAC Flow:** Understand the data flow: Middleware authenticates and passes context via headers -> API route extracts context -> Helper function performs the check using context against Stytch. A failure can occur at any step.
