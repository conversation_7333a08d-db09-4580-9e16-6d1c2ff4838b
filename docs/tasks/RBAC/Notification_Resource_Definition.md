# Notification Resource Definition for RBAC

This document defines the notification resource and its permissions for the Role-Based Access Control (RBAC) system. This resource should be added to the Stytch RBAC policy.

## Resource Definition

**Resource:** `notification`
**Description:** Represents user notifications and notification preferences.

### Actions:
- `read`: View notifications
- `mark_as_read`: Mark notifications as read
- `delete`: Delete notifications
- `manage_preferences`: Manage notification preferences

## Permission Matrix

| Action               | Admin | Manager | Scout | Viewer |
|----------------------|-------|---------|-------|--------|
| `read`               | ✓     | ✓       | ✓     | ✓      |
| `mark_as_read`       | ✓     | ✓       | ✓     | ✓      |
| `delete`             | ✓     | ✓       | ✗     | ✗      |
| `manage_preferences` | ✓     | ✓       | ✓     | ✓      |

## Implementation Steps

1. Add the `notification` resource to the Stytch RBAC policy with the actions defined above.
2. Update the role definitions to include the notification permissions as follows:

### Role: `admin`
- `notification`: `*` (All Actions)

### Role: `manager`
- `notification`: `read`, `mark_as_read`, `delete`, `manage_preferences`

### Role: `scout`
- `notification`: `read`, `mark_as_read`, `manage_preferences`

### Role: `viewer`
- `notification`: `read`, `mark_as_read`, `manage_preferences`

## API Route Protection

The following API routes should be protected with the appropriate RBAC checks:

- `GET /api/notifications` - Requires `notification:read` permission
- `GET /api/notifications/count` - Requires `notification:read` permission
- `PUT /api/notifications/[id]/read` - Requires `notification:mark_as_read` permission
- `PUT /api/notifications/read-all` - Requires `notification:mark_as_read` permission
- `GET /api/notifications/preferences` - Requires `notification:read` permission
- `PUT /api/notifications/preferences` - Requires `notification:manage_preferences` permission
- `POST /api/notifications/preferences` - Requires `notification:manage_preferences` permission

## Special Considerations

1. Users should only be able to view and manage their own notifications.
2. Organization-wide notifications should be visible to all members of the organization.
3. Project-specific notifications should be visible only to members of that project.
