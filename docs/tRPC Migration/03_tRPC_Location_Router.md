# tRPC Migration: Location Router Implementation

This document provides detailed instructions for implementing the location router in the tRPC migration. This is the second phase of the migration process and focuses on creating the tRPC procedures for the locations feature.

## Prerequisites

Before proceeding, ensure you have:

1. Completed the tRPC infrastructure setup as described in [02_tRPC_Setup_Infrastructure.md](./02_tRPC_Setup_Infrastructure.md)
2. Familiarized yourself with the existing locations feature implementation
3. Reviewed the location service and schema files

## Step 1: Understand the Existing Location Schema

First, let's examine the existing location schema to understand the data structure we'll be working with. The schema is defined in `modules/location/schema.ts`.

The location schema typically includes fields such as:
- id
- name
- description
- address
- coordinates (latitude, longitude)
- organizationId
- projectId (optional)
- status
- createdAt
- updatedAt
- createdBy
- media
- boundary
- contact information

## Step 2: Create the Location Router

Create a new file at `lib/trpc/routers/location.ts` to define the location router:

```typescript
import { z } from 'zod';
import { TRPCError } from '@trpc/server';
import { router, organizationProcedure } from '../server';
import * as locationService from '../../../modules/location/service';
import { locationSchema, createLocationSchema, updateLocationSchema } from '../../../modules/location/schema';

/**
 * Location router for tRPC
 * This defines all procedures related to locations
 */
export const locationRouter = router({
  /**
   * Get all locations for an organization
   */
  getAll: organizationProcedure
    .input(
      z.object({
        organizationId: z.string(),
        page: z.number().optional().default(1),
        limit: z.number().optional().default(10),
        search: z.string().optional(),
        status: z.string().optional(),
        projectId: z.string().optional(),
        sortBy: z.string().optional(),
        sortOrder: z.enum(['asc', 'desc']).optional(),
      })
    )
    .query(async ({ ctx, input }) => {
      try {
        const { organizationId } = input;
        
        // Verify organization access
        if (ctx.auth.organizationId !== organizationId) {
          throw new TRPCError({
            code: 'FORBIDDEN',
            message: 'You do not have access to this organization',
          });
        }
        
        const locations = await locationService.getLocations({
          ...input,
          organizationId,
        });
        
        return locations;
      } catch (error) {
        console.error('Error fetching locations:', error);
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: 'Failed to fetch locations',
          cause: error,
        });
      }
    }),
    
  /**
   * Get a single location by ID
   */
  getById: organizationProcedure
    .input(
      z.object({
        id: z.string(),
        organizationId: z.string(),
      })
    )
    .query(async ({ ctx, input }) => {
      try {
        const { id, organizationId } = input;
        
        // Verify organization access
        if (ctx.auth.organizationId !== organizationId) {
          throw new TRPCError({
            code: 'FORBIDDEN',
            message: 'You do not have access to this organization',
          });
        }
        
        const location = await locationService.getLocationById(id, organizationId);
        
        if (!location) {
          throw new TRPCError({
            code: 'NOT_FOUND',
            message: 'Location not found',
          });
        }
        
        return location;
      } catch (error) {
        if (error instanceof TRPCError) {
          throw error;
        }
        
        console.error('Error fetching location:', error);
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: 'Failed to fetch location',
          cause: error,
        });
      }
    }),
    
  /**
   * Create a new location
   */
  create: organizationProcedure
    .input(
      z.object({
        organizationId: z.string(),
        data: createLocationSchema,
      })
    )
    .mutation(async ({ ctx, input }) => {
      try {
        const { organizationId, data } = input;
        
        // Verify organization access
        if (ctx.auth.organizationId !== organizationId) {
          throw new TRPCError({
            code: 'FORBIDDEN',
            message: 'You do not have access to this organization',
          });
        }
        
        const location = await locationService.createLocation({
          ...data,
          organizationId,
          createdBy: ctx.auth.userId,
        });
        
        return location;
      } catch (error) {
        console.error('Error creating location:', error);
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: 'Failed to create location',
          cause: error,
        });
      }
    }),
    
  /**
   * Update an existing location
   */
  update: organizationProcedure
    .input(
      z.object({
        id: z.string(),
        organizationId: z.string(),
        data: updateLocationSchema,
      })
    )
    .mutation(async ({ ctx, input }) => {
      try {
        const { id, organizationId, data } = input;
        
        // Verify organization access
        if (ctx.auth.organizationId !== organizationId) {
          throw new TRPCError({
            code: 'FORBIDDEN',
            message: 'You do not have access to this organization',
          });
        }
        
        // Check if location exists
        const existingLocation = await locationService.getLocationById(id, organizationId);
        
        if (!existingLocation) {
          throw new TRPCError({
            code: 'NOT_FOUND',
            message: 'Location not found',
          });
        }
        
        const updatedLocation = await locationService.updateLocation(id, data);
        
        return updatedLocation;
      } catch (error) {
        if (error instanceof TRPCError) {
          throw error;
        }
        
        console.error('Error updating location:', error);
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: 'Failed to update location',
          cause: error,
        });
      }
    }),
    
  /**
   * Delete a location
   */
  delete: organizationProcedure
    .input(
      z.object({
        id: z.string(),
        organizationId: z.string(),
      })
    )
    .mutation(async ({ ctx, input }) => {
      try {
        const { id, organizationId } = input;
        
        // Verify organization access
        if (ctx.auth.organizationId !== organizationId) {
          throw new TRPCError({
            code: 'FORBIDDEN',
            message: 'You do not have access to this organization',
          });
        }
        
        // Check if location exists
        const existingLocation = await locationService.getLocationById(id, organizationId);
        
        if (!existingLocation) {
          throw new TRPCError({
            code: 'NOT_FOUND',
            message: 'Location not found',
          });
        }
        
        await locationService.deleteLocation(id);
        
        return { success: true };
      } catch (error) {
        if (error instanceof TRPCError) {
          throw error;
        }
        
        console.error('Error deleting location:', error);
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: 'Failed to delete location',
          cause: error,
        });
      }
    }),
    
  /**
   * Add media to a location
   */
  addMedia: organizationProcedure
    .input(
      z.object({
        locationId: z.string(),
        organizationId: z.string(),
        media: z.array(
          z.object({
            url: z.string(),
            type: z.string(),
            name: z.string().optional(),
            size: z.number().optional(),
          })
        ),
      })
    )
    .mutation(async ({ ctx, input }) => {
      try {
        const { locationId, organizationId, media } = input;
        
        // Verify organization access
        if (ctx.auth.organizationId !== organizationId) {
          throw new TRPCError({
            code: 'FORBIDDEN',
            message: 'You do not have access to this organization',
          });
        }
        
        // Check if location exists
        const existingLocation = await locationService.getLocationById(locationId, organizationId);
        
        if (!existingLocation) {
          throw new TRPCError({
            code: 'NOT_FOUND',
            message: 'Location not found',
          });
        }
        
        const updatedMedia = await locationService.addLocationMedia(locationId, media);
        
        return updatedMedia;
      } catch (error) {
        if (error instanceof TRPCError) {
          throw error;
        }
        
        console.error('Error adding media to location:', error);
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: 'Failed to add media to location',
          cause: error,
        });
      }
    }),
    
  /**
   * Remove media from a location
   */
  removeMedia: organizationProcedure
    .input(
      z.object({
        locationId: z.string(),
        organizationId: z.string(),
        mediaId: z.string(),
      })
    )
    .mutation(async ({ ctx, input }) => {
      try {
        const { locationId, organizationId, mediaId } = input;
        
        // Verify organization access
        if (ctx.auth.organizationId !== organizationId) {
          throw new TRPCError({
            code: 'FORBIDDEN',
            message: 'You do not have access to this organization',
          });
        }
        
        // Check if location exists
        const existingLocation = await locationService.getLocationById(locationId, organizationId);
        
        if (!existingLocation) {
          throw new TRPCError({
            code: 'NOT_FOUND',
            message: 'Location not found',
          });
        }
        
        await locationService.removeLocationMedia(locationId, mediaId);
        
        return { success: true };
      } catch (error) {
        if (error instanceof TRPCError) {
          throw error;
        }
        
        console.error('Error removing media from location:', error);
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: 'Failed to remove media from location',
          cause: error,
        });
      }
    }),
});
```

## Step 3: Update the Root Router

Update the root router in `lib/trpc/routers/_app.ts` to include the location router:

```typescript
import { router } from '../server';
import { locationRouter } from './location';

/**
 * Root router for the application
 * This combines all feature routers
 */
export const appRouter = router({
  location: locationRouter,
});

/**
 * Export type definition of the API
 * This is used for client-side type inference
 */
export type AppRouter = typeof appRouter;
```

## Step 4: Implement Additional Location Procedures (Optional)

Depending on your application's requirements, you may need to implement additional procedures for the location router. Here are some examples:

### Share Location Procedure

```typescript
/**
 * Share a location with a token
 */
shareLocation: organizationProcedure
  .input(
    z.object({
      locationId: z.string(),
      organizationId: z.string(),
      expiresIn: z.number().optional(),
    })
  )
  .mutation(async ({ ctx, input }) => {
    try {
      const { locationId, organizationId, expiresIn } = input;
      
      // Verify organization access
      if (ctx.auth.organizationId !== organizationId) {
        throw new TRPCError({
          code: 'FORBIDDEN',
          message: 'You do not have access to this organization',
        });
      }
      
      // Check if location exists
      const existingLocation = await locationService.getLocationById(locationId, organizationId);
      
      if (!existingLocation) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: 'Location not found',
        });
      }
      
      const shareToken = await locationService.createShareToken(locationId, expiresIn);
      
      return { token: shareToken };
    } catch (error) {
      if (error instanceof TRPCError) {
        throw error;
      }
      
      console.error('Error sharing location:', error);
      throw new TRPCError({
        code: 'INTERNAL_SERVER_ERROR',
        message: 'Failed to share location',
        cause: error,
      });
    }
  }),
```

### Get Shared Location Procedure

```typescript
/**
 * Get a shared location by token
 */
getSharedLocation: publicProcedure
  .input(
    z.object({
      token: z.string(),
    })
  )
  .query(async ({ input }) => {
    try {
      const { token } = input;
      
      const location = await locationService.getLocationByShareToken(token);
      
      if (!location) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: 'Shared location not found or link expired',
        });
      }
      
      return location;
    } catch (error) {
      if (error instanceof TRPCError) {
        throw error;
      }
      
      console.error('Error fetching shared location:', error);
      throw new TRPCError({
        code: 'INTERNAL_SERVER_ERROR',
        message: 'Failed to fetch shared location',
        cause: error,
      });
    }
  }),
```

## Step 5: Testing the Location Router

To test the location router, you can create a simple test script:

```typescript
// scripts/test-location-router.ts
import { appRouter } from '../lib/trpc/routers/_app';
import { createContext } from '../lib/trpc/server';

async function main() {
  // Mock request
  const req = {
    headers: {
      // Add your authentication headers here
    },
  } as any;
  
  // Create context
  const ctx = await createContext({ req });
  
  // Create caller
  const caller = appRouter.createCaller(ctx);
  
  try {
    // Test getAll procedure
    const locations = await caller.location.getAll({
      organizationId: 'your-organization-id',
      page: 1,
      limit: 10,
    });
    
    console.log('Locations:', locations);
  } catch (error) {
    console.error('Error testing location router:', error);
  }
}

main().catch(console.error);
```

## Verification

To verify that the location router is implemented correctly:

1. Ensure all files are created as specified
2. Check for any TypeScript errors
3. Run the test script to verify that the procedures work as expected
4. Start the development server with `pnpm dev` and check for any errors

## Next Steps

Once the location router is implemented, proceed to the next document in this guide: [04_tRPC_Frontend_Hooks.md](./04_tRPC_Frontend_Hooks.md) to update the frontend hooks to use tRPC.

## Troubleshooting

### Common Issues

1. **TypeScript Errors**: Ensure that all imports are correct and that the types are properly defined.
2. **Module Not Found Errors**: Check that all paths in import statements are correct.
3. **Authentication Errors**: Verify that the authentication middleware is correctly implemented and that it's properly extracting authentication information from the request.
4. **Service Function Errors**: Ensure that the location service functions are correctly implemented and that they're properly handling errors.

### Debugging Tips

1. Add console logs to the tRPC procedures to verify that they're being called with the correct parameters.
2. Use the React Query Devtools to inspect queries and mutations.
3. Check the browser console for any client-side errors.
