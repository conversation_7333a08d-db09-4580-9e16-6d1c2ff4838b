# tRPC Migration: Maintaining Backward Compatibility

This document provides detailed instructions for maintaining backward compatibility with the existing REST API during the tRPC migration. This is the fourth phase of the migration process and ensures that existing code continues to work while new code uses tRPC.

## Prerequisites

Before proceeding, ensure you have:

1. Completed the tRPC infrastructure setup as described in [02_tRPC_Setup_Infrastructure.md](./02_tRPC_Setup_Infrastructure.md)
2. Implemented the location router as described in [03_tRPC_Location_Router.md](./03_tRPC_Location_Router.md)
3. Implemented the frontend hooks as described in [04_tRPC_Frontend_Hooks.md](./04_tRPC_Frontend_Hooks.md)

## Step 1: Update Existing API Routes to Use tRPC Router Logic

To maintain backward compatibility, we'll update the existing REST API routes to use the same logic as the tRPC router. This ensures that both the REST API and tRPC API behave consistently.

### Update the Locations API Route

Update the `app/api/locations/route.ts` file:

```typescript
import { NextResponse } from 'next/server';
import { type NextRequest } from 'next/server';
import { z } from 'zod';
import { getAuthFromRequest } from '../../../lib/utils/auth-utils';
import { appRouter } from '../../../lib/trpc/routers/_app';
import { createContext } from '../../../lib/trpc/server';

/**
 * GET handler for fetching locations
 */
export async function GET(request: NextRequest) {
  try {
    // Parse query parameters
    const url = new URL(request.url);
    const page = parseInt(url.searchParams.get('page') || '1');
    const limit = parseInt(url.searchParams.get('limit') || '10');
    const search = url.searchParams.get('search') || undefined;
    const status = url.searchParams.get('status') || undefined;
    const projectId = url.searchParams.get('projectId') || undefined;
    const sortBy = url.searchParams.get('sortBy') || undefined;
    const sortOrder = url.searchParams.get('sortOrder') as 'asc' | 'desc' | undefined;
    const organizationId = url.searchParams.get('organizationId') || '';
    
    // Create context
    const ctx = await createContext({ req: request });
    
    // Create caller
    const caller = appRouter.createCaller(ctx);
    
    // Call tRPC procedure
    const result = await caller.location.getAll({
      organizationId,
      page,
      limit,
      search,
      status,
      projectId,
      sortBy,
      sortOrder,
    });
    
    return NextResponse.json(result);
  } catch (error: any) {
    console.error('Error fetching locations:', error);
    
    return NextResponse.json(
      { error: error.message || 'Failed to fetch locations' },
      { status: error.code === 'UNAUTHORIZED' ? 401 : error.code === 'FORBIDDEN' ? 403 : 500 }
    );
  }
}

/**
 * POST handler for creating a location
 */
export async function POST(request: NextRequest) {
  try {
    // Parse request body
    const body = await request.json();
    
    // Validate input
    const inputSchema = z.object({
      organizationId: z.string(),
      data: z.any(),
    });
    
    const { organizationId, data } = inputSchema.parse(body);
    
    // Create context
    const ctx = await createContext({ req: request });
    
    // Create caller
    const caller = appRouter.createCaller(ctx);
    
    // Call tRPC procedure
    const result = await caller.location.create({
      organizationId,
      data,
    });
    
    return NextResponse.json(result);
  } catch (error: any) {
    console.error('Error creating location:', error);
    
    if (error.name === 'ZodError') {
      return NextResponse.json(
        { error: 'Invalid input', details: error.format() },
        { status: 400 }
      );
    }
    
    return NextResponse.json(
      { error: error.message || 'Failed to create location' },
      { status: error.code === 'UNAUTHORIZED' ? 401 : error.code === 'FORBIDDEN' ? 403 : 500 }
    );
  }
}
```

### Update the Location by ID API Route

Update the `app/api/locations/[id]/route.ts` file:

```typescript
import { NextResponse } from 'next/server';
import { type NextRequest } from 'next/server';
import { z } from 'zod';
import { getAuthFromRequest } from '../../../../lib/utils/auth-utils';
import { appRouter } from '../../../../lib/trpc/routers/_app';
import { createContext } from '../../../../lib/trpc/server';

/**
 * GET handler for fetching a location by ID
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { id } = params;
    
    // Parse query parameters
    const url = new URL(request.url);
    const organizationId = url.searchParams.get('organizationId') || '';
    
    // Create context
    const ctx = await createContext({ req: request });
    
    // Create caller
    const caller = appRouter.createCaller(ctx);
    
    // Call tRPC procedure
    const result = await caller.location.getById({
      id,
      organizationId,
    });
    
    return NextResponse.json(result);
  } catch (error: any) {
    console.error('Error fetching location:', error);
    
    return NextResponse.json(
      { error: error.message || 'Failed to fetch location' },
      { 
        status: error.code === 'NOT_FOUND' ? 404 : 
                error.code === 'UNAUTHORIZED' ? 401 : 
                error.code === 'FORBIDDEN' ? 403 : 500 
      }
    );
  }
}

/**
 * PATCH handler for updating a location
 */
export async function PATCH(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { id } = params;
    
    // Parse request body
    const body = await request.json();
    
    // Validate input
    const inputSchema = z.object({
      organizationId: z.string(),
      data: z.any(),
    });
    
    const { organizationId, data } = inputSchema.parse(body);
    
    // Create context
    const ctx = await createContext({ req: request });
    
    // Create caller
    const caller = appRouter.createCaller(ctx);
    
    // Call tRPC procedure
    const result = await caller.location.update({
      id,
      organizationId,
      data,
    });
    
    return NextResponse.json(result);
  } catch (error: any) {
    console.error('Error updating location:', error);
    
    if (error.name === 'ZodError') {
      return NextResponse.json(
        { error: 'Invalid input', details: error.format() },
        { status: 400 }
      );
    }
    
    return NextResponse.json(
      { error: error.message || 'Failed to update location' },
      { 
        status: error.code === 'NOT_FOUND' ? 404 : 
                error.code === 'UNAUTHORIZED' ? 401 : 
                error.code === 'FORBIDDEN' ? 403 : 500 
      }
    );
  }
}

/**
 * DELETE handler for deleting a location
 */
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { id } = params;
    
    // Parse query parameters
    const url = new URL(request.url);
    const organizationId = url.searchParams.get('organizationId') || '';
    
    // Create context
    const ctx = await createContext({ req: request });
    
    // Create caller
    const caller = appRouter.createCaller(ctx);
    
    // Call tRPC procedure
    const result = await caller.location.delete({
      id,
      organizationId,
    });
    
    return NextResponse.json(result);
  } catch (error: any) {
    console.error('Error deleting location:', error);
    
    return NextResponse.json(
      { error: error.message || 'Failed to delete location' },
      { 
        status: error.code === 'NOT_FOUND' ? 404 : 
                error.code === 'UNAUTHORIZED' ? 401 : 
                error.code === 'FORBIDDEN' ? 403 : 500 
      }
    );
  }
}
```

### Update the Location Media API Route

Update the `app/api/locations/media/route.ts` file:

```typescript
import { NextResponse } from 'next/server';
import { type NextRequest } from 'next/server';
import { z } from 'zod';
import { getAuthFromRequest } from '../../../../lib/utils/auth-utils';
import { appRouter } from '../../../../lib/trpc/routers/_app';
import { createContext } from '../../../../lib/trpc/server';

/**
 * POST handler for adding media to a location
 */
export async function POST(request: NextRequest) {
  try {
    // Parse request body
    const body = await request.json();
    
    // Validate input
    const inputSchema = z.object({
      locationId: z.string(),
      organizationId: z.string(),
      media: z.array(
        z.object({
          url: z.string(),
          type: z.string(),
          name: z.string().optional(),
          size: z.number().optional(),
        })
      ),
    });
    
    const { locationId, organizationId, media } = inputSchema.parse(body);
    
    // Create context
    const ctx = await createContext({ req: request });
    
    // Create caller
    const caller = appRouter.createCaller(ctx);
    
    // Call tRPC procedure
    const result = await caller.location.addMedia({
      locationId,
      organizationId,
      media,
    });
    
    return NextResponse.json(result);
  } catch (error: any) {
    console.error('Error adding media to location:', error);
    
    if (error.name === 'ZodError') {
      return NextResponse.json(
        { error: 'Invalid input', details: error.format() },
        { status: 400 }
      );
    }
    
    return NextResponse.json(
      { error: error.message || 'Failed to add media to location' },
      { 
        status: error.code === 'NOT_FOUND' ? 404 : 
                error.code === 'UNAUTHORIZED' ? 401 : 
                error.code === 'FORBIDDEN' ? 403 : 500 
      }
    );
  }
}

/**
 * DELETE handler for removing media from a location
 */
export async function DELETE(request: NextRequest) {
  try {
    // Parse query parameters
    const url = new URL(request.url);
    const locationId = url.searchParams.get('locationId') || '';
    const organizationId = url.searchParams.get('organizationId') || '';
    const mediaId = url.searchParams.get('mediaId') || '';
    
    // Create context
    const ctx = await createContext({ req: request });
    
    // Create caller
    const caller = appRouter.createCaller(ctx);
    
    // Call tRPC procedure
    const result = await caller.location.removeMedia({
      locationId,
      organizationId,
      mediaId,
    });
    
    return NextResponse.json(result);
  } catch (error: any) {
    console.error('Error removing media from location:', error);
    
    return NextResponse.json(
      { error: error.message || 'Failed to remove media from location' },
      { 
        status: error.code === 'NOT_FOUND' ? 404 : 
                error.code === 'UNAUTHORIZED' ? 401 : 
                error.code === 'FORBIDDEN' ? 403 : 500 
      }
    );
  }
}
```

### Update the Location Share API Route

Update the `app/api/locations/share/route.ts` file:

```typescript
import { NextResponse } from 'next/server';
import { type NextRequest } from 'next/server';
import { z } from 'zod';
import { getAuthFromRequest } from '../../../../lib/utils/auth-utils';
import { appRouter } from '../../../../lib/trpc/routers/_app';
import { createContext } from '../../../../lib/trpc/server';

/**
 * POST handler for sharing a location
 */
export async function POST(request: NextRequest) {
  try {
    // Parse request body
    const body = await request.json();
    
    // Validate input
    const inputSchema = z.object({
      locationId: z.string(),
      organizationId: z.string(),
      expiresIn: z.number().optional(),
    });
    
    const { locationId, organizationId, expiresIn } = inputSchema.parse(body);
    
    // Create context
    const ctx = await createContext({ req: request });
    
    // Create caller
    const caller = appRouter.createCaller(ctx);
    
    // Call tRPC procedure
    const result = await caller.location.shareLocation({
      locationId,
      organizationId,
      expiresIn,
    });
    
    return NextResponse.json(result);
  } catch (error: any) {
    console.error('Error sharing location:', error);
    
    if (error.name === 'ZodError') {
      return NextResponse.json(
        { error: 'Invalid input', details: error.format() },
        { status: 400 }
      );
    }
    
    return NextResponse.json(
      { error: error.message || 'Failed to share location' },
      { 
        status: error.code === 'NOT_FOUND' ? 404 : 
                error.code === 'UNAUTHORIZED' ? 401 : 
                error.code === 'FORBIDDEN' ? 403 : 500 
      }
    );
  }
}
```

### Update the Shared Location API Route

Update the `app/api/locations/shared/[token]/route.ts` file:

```typescript
import { NextResponse } from 'next/server';
import { type NextRequest } from 'next/server';
import { appRouter } from '../../../../../lib/trpc/routers/_app';
import { createContext } from '../../../../../lib/trpc/server';

/**
 * GET handler for fetching a shared location by token
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { token: string } }
) {
  try {
    const { token } = params;
    
    // Create context
    const ctx = await createContext({ req: request });
    
    // Create caller
    const caller = appRouter.createCaller(ctx);
    
    // Call tRPC procedure
    const result = await caller.location.getSharedLocation({
      token,
    });
    
    return NextResponse.json(result);
  } catch (error: any) {
    console.error('Error fetching shared location:', error);
    
    return NextResponse.json(
      { error: error.message || 'Failed to fetch shared location' },
      { 
        status: error.code === 'NOT_FOUND' ? 404 : 500 
      }
    );
  }
}
```

## Step 2: Create a Helper Function for Error Handling

To standardize error handling between the REST API and tRPC, create a helper function in `lib/utils/api-utils.ts`:

```typescript
import { TRPCError } from '@trpc/server';

/**
 * Maps tRPC error codes to HTTP status codes
 */
export function mapTRPCErrorToHTTPStatus(error: TRPCError): number {
  switch (error.code) {
    case 'BAD_REQUEST':
      return 400;
    case 'UNAUTHORIZED':
      return 401;
    case 'FORBIDDEN':
      return 403;
    case 'NOT_FOUND':
      return 404;
    case 'TIMEOUT':
      return 408;
    case 'CONFLICT':
      return 409;
    case 'PRECONDITION_FAILED':
      return 412;
    case 'PAYLOAD_TOO_LARGE':
      return 413;
    case 'METHOD_NOT_SUPPORTED':
      return 405;
    case 'UNPROCESSABLE_CONTENT':
      return 422;
    case 'TOO_MANY_REQUESTS':
      return 429;
    case 'CLIENT_CLOSED_REQUEST':
      return 499;
    case 'INTERNAL_SERVER_ERROR':
    default:
      return 500;
  }
}

/**
 * Formats an error response for REST API
 */
export function formatErrorResponse(error: unknown) {
  if (error instanceof TRPCError) {
    return {
      error: error.message,
      code: error.code,
      status: mapTRPCErrorToHTTPStatus(error),
    };
  }
  
  if (error instanceof Error) {
    return {
      error: error.message,
      status: 500,
    };
  }
  
  return {
    error: 'An unknown error occurred',
    status: 500,
  };
}
```

## Step 3: Update the API Routes to Use the Helper Function

Now, update the API routes to use the helper function for error handling:

```typescript
import { formatErrorResponse } from '../../../../lib/utils/api-utils';

// In the catch block
catch (error: any) {
  console.error('Error fetching location:', error);
  
  const { error: errorMessage, status } = formatErrorResponse(error);
  
  return NextResponse.json(
    { error: errorMessage },
    { status }
  );
}
```

## Step 4: Maintain Existing Frontend Hooks

To ensure a smooth transition, maintain the existing frontend hooks that use the REST API. This allows components to gradually migrate to tRPC without breaking existing functionality.

For example, keep the existing `useLocations` hook in `hooks/use-locations.ts`:

```typescript
'use client';

import useSWR from 'swr';
import { useState } from 'react';
import { useOrganization } from './use-organization';
import { fetcher } from '../lib/utils/fetcher';

/**
 * Hook for fetching locations using the REST API
 * This is maintained for backward compatibility
 */
export function useLocations(options?: {
  page?: number;
  limit?: number;
  search?: string;
  status?: string;
  projectId?: string;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}) {
  const { organization } = useOrganization();
  const organizationId = organization?.id;
  
  const [page, setPage] = useState(options?.page || 1);
  const [limit, setLimit] = useState(options?.limit || 10);
  
  const queryParams = new URLSearchParams({
    page: page.toString(),
    limit: limit.toString(),
  });
  
  if (options?.search) queryParams.append('search', options.search);
  if (options?.status) queryParams.append('status', options.status);
  if (options?.projectId) queryParams.append('projectId', options.projectId);
  if (options?.sortBy) queryParams.append('sortBy', options.sortBy);
  if (options?.sortOrder) queryParams.append('sortOrder', options.sortOrder);
  if (organizationId) queryParams.append('organizationId', organizationId);
  
  const { data, error, isLoading, mutate } = useSWR(
    organizationId ? `/api/locations?${queryParams.toString()}` : null,
    fetcher
  );
  
  const handlePageChange = (newPage: number) => {
    setPage(newPage);
  };
  
  const handleLimitChange = (newLimit: number) => {
    setLimit(newLimit);
    setPage(1); // Reset to first page when changing limit
  };
  
  return {
    locations: data?.locations || [],
    totalLocations: data?.totalCount || 0,
    totalPages: data?.totalPages || 0,
    currentPage: page,
    pageSize: limit,
    isLoading,
    isError: !!error,
    error,
    refetch: mutate,
    handlePageChange,
    handleLimitChange,
  };
}
```

## Step 5: Create a Migration Guide for Components

Create a guide for migrating components from the REST API to tRPC. This will help developers understand how to update their components to use the new tRPC hooks.

```markdown
# Component Migration Guide

This guide explains how to migrate components from using the REST API to using tRPC.

## Migrating a Component

1. Import the tRPC hooks instead of the REST API hooks:

   ```typescript
   // Before
   import { useLocations } from '../../hooks/use-locations';
   
   // After
   import { useLocations } from '../../hooks/use-locations-trpc';
   ```

2. Update the component to use the tRPC hooks:

   ```typescript
   // Before
   const {
     locations,
     totalLocations,
     totalPages,
     currentPage,
     isLoading,
     isError,
     handlePageChange,
   } = useLocations({
     page: 1,
     limit: 10,
     search,
     status,
     projectId,
   });
   
   // After
   const {
     locations,
     totalLocations,
     totalPages,
     currentPage,
     isLoading,
     isError,
     handlePageChange,
   } = useLocations({
     page: 1,
     limit: 10,
     search,
     status,
     projectId,
   });
   ```

   Note that the API is designed to be compatible, so the component code often doesn't need to change.

3. Update mutation calls:

   ```typescript
   // Before
   const { createLocation, updateLocation, deleteLocation } = useLocationMutations();
   
   // After
   const { createLocation, updateLocation, deleteLocation } = useLocationMutations();
   
   // Usage remains the same
   await createLocation.mutate(data);
   await updateLocation.mutate(id, data);
   await deleteLocation.mutate(id);
   ```

## Testing the Migration

After migrating a component, test it thoroughly to ensure that it works correctly with tRPC:

1. Test all data fetching functionality
2. Test all mutation functionality
3. Test error handling
4. Test loading states
```

## Verification

To verify that backward compatibility is maintained:

1. Ensure all API routes are updated to use tRPC router logic
2. Test the API routes with tools like Postman or curl
3. Verify that existing components continue to work with the REST API
4. Test migrated components with tRPC hooks

## Next Steps

Once backward compatibility is established, proceed to the next document in this guide: [06_tRPC_Testing_Validation.md](./06_tRPC_Testing_Validation.md) to test and validate the tRPC implementation.

## Troubleshooting

### Common Issues

1. **Inconsistent Behavior**: Ensure that the REST API and tRPC API behave consistently by using the same underlying logic.
2. **Error Handling**: Make sure that error handling is consistent between the REST API and tRPC.
3. **Authentication**: Verify that authentication works correctly in both APIs.
4. **Type Errors**: Check for type errors when updating API routes to use tRPC router logic.

### Debugging Tips

1. Add console logs to the API routes to verify that they're being called with the correct parameters.
2. Use the Network tab in the browser's developer tools to inspect the API requests and responses.
3. Compare the behavior of the REST API and tRPC API to ensure consistency.
