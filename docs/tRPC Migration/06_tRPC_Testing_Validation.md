# tRPC Migration: Testing and Validation

This document provides detailed instructions for testing and validating the tRPC implementation in the Scene-o-matic application. This is the fifth phase of the migration process and ensures that the tRPC implementation works correctly.

## Prerequisites

Before proceeding, ensure you have:

1. Completed the tRPC infrastructure setup as described in [02_tRPC_Setup_Infrastructure.md](./02_tRPC_Setup_Infrastructure.md)
2. Implemented the location router as described in [03_tRPC_Location_Router.md](./03_tRPC_Location_Router.md)
3. Implemented the frontend hooks as described in [04_tRPC_Frontend_Hooks.md](./04_tRPC_Frontend_Hooks.md)
4. Maintained backward compatibility as described in [05_tRPC_Backward_Compatibility.md](./05_tRPC_Backward_Compatibility.md)

## Step 1: Create Test Scripts for tRPC Endpoints

Create test scripts to verify that the tRPC endpoints work correctly. These scripts will help you identify any issues with the implementation.

### Create a Test Script for the Location Router

Create a new file at `scripts/test-trpc-location-router.ts`:

```typescript
import { appRouter } from '../lib/trpc/routers/_app';
import { createContext } from '../lib/trpc/server';
import { loadStytch } from '../lib/stytch-b2b';
import { cookies } from 'next/headers';

/**
 * Test script for the location router
 * This script tests the tRPC procedures for locations
 */
async function main() {
  try {
    console.log('Testing tRPC location router...');
    
    // Get session token from cookies
    const cookieStore = cookies();
    const sessionToken = cookieStore.get('stytch_session')?.value;
    
    if (!sessionToken) {
      console.error('No session token found. Please log in first.');
      return;
    }
    
    // Get user and organization IDs from Stytch
    const stytch = loadStytch();
    const session = await stytch.sessions.authenticate({ session_token: sessionToken });
    
    const userId = session.session.user_id;
    const organizationId = session.session.organization_id;
    
    if (!organizationId) {
      console.error('No organization ID found in session.');
      return;
    }
    
    console.log(`User ID: ${userId}`);
    console.log(`Organization ID: ${organizationId}`);
    
    // Create mock request
    const req = {
      headers: {
        cookie: `stytch_session=${sessionToken}`,
      },
    } as any;
    
    // Create context
    const ctx = await createContext({ req });
    
    // Create caller
    const caller = appRouter.createCaller(ctx);
    
    // Test getAll procedure
    console.log('\nTesting getAll procedure...');
    const locations = await caller.location.getAll({
      organizationId,
      page: 1,
      limit: 10,
    });
    
    console.log(`Found ${locations.locations.length} locations`);
    console.log(`Total count: ${locations.totalCount}`);
    console.log(`Total pages: ${locations.totalPages}`);
    
    if (locations.locations.length > 0) {
      const locationId = locations.locations[0].id;
      
      // Test getById procedure
      console.log('\nTesting getById procedure...');
      const location = await caller.location.getById({
        id: locationId,
        organizationId,
      });
      
      console.log(`Location name: ${location.name}`);
      console.log(`Location address: ${location.address}`);
      
      // Test create procedure
      console.log('\nTesting create procedure...');
      const newLocation = await caller.location.create({
        organizationId,
        data: {
          name: `Test Location ${Date.now()}`,
          address: '123 Test Street',
          description: 'A test location created by the tRPC test script',
          status: 'active',
          coordinates: {
            latitude: 37.7749,
            longitude: -122.4194,
          },
        },
      });
      
      console.log(`Created location with ID: ${newLocation.id}`);
      
      // Test update procedure
      console.log('\nTesting update procedure...');
      const updatedLocation = await caller.location.update({
        id: newLocation.id,
        organizationId,
        data: {
          name: `${newLocation.name} (Updated)`,
          description: 'An updated test location',
        },
      });
      
      console.log(`Updated location name: ${updatedLocation.name}`);
      
      // Test delete procedure
      console.log('\nTesting delete procedure...');
      const deleteResult = await caller.location.delete({
        id: newLocation.id,
        organizationId,
      });
      
      console.log(`Delete result: ${JSON.stringify(deleteResult)}`);
    }
    
    console.log('\nAll tests completed successfully!');
  } catch (error) {
    console.error('Error testing tRPC location router:', error);
  }
}

main().catch(console.error);
```

### Create a Test Script for the tRPC Client

Create a new file at `scripts/test-trpc-client.ts`:

```typescript
import { createTRPCProxyClient, httpBatchLink } from '@trpc/client';
import { type AppRouter } from '../lib/trpc/routers/_app';
import fetch from 'node-fetch';

/**
 * Test script for the tRPC client
 * This script tests the tRPC client by making requests to the tRPC API
 */
async function main() {
  try {
    console.log('Testing tRPC client...');
    
    // Get session token from environment variable or command line argument
    const sessionToken = process.env.SESSION_TOKEN || process.argv[2];
    
    if (!sessionToken) {
      console.error('No session token provided. Please set SESSION_TOKEN environment variable or provide it as a command line argument.');
      return;
    }
    
    // Create tRPC client
    const client = createTRPCProxyClient<AppRouter>({
      links: [
        httpBatchLink({
          url: 'http://localhost:3000/api/trpc',
          fetch: fetch as any,
          headers: {
            cookie: `stytch_session=${sessionToken}`,
          },
        }),
      ],
    });
    
    // Get organization ID from command line argument
    const organizationId = process.env.ORGANIZATION_ID || process.argv[3];
    
    if (!organizationId) {
      console.error('No organization ID provided. Please set ORGANIZATION_ID environment variable or provide it as a command line argument.');
      return;
    }
    
    console.log(`Organization ID: ${organizationId}`);
    
    // Test getAll procedure
    console.log('\nTesting getAll procedure...');
    const locations = await client.location.getAll.query({
      organizationId,
      page: 1,
      limit: 10,
    });
    
    console.log(`Found ${locations.locations.length} locations`);
    console.log(`Total count: ${locations.totalCount}`);
    console.log(`Total pages: ${locations.totalPages}`);
    
    if (locations.locations.length > 0) {
      const locationId = locations.locations[0].id;
      
      // Test getById procedure
      console.log('\nTesting getById procedure...');
      const location = await client.location.getById.query({
        id: locationId,
        organizationId,
      });
      
      console.log(`Location name: ${location.name}`);
      console.log(`Location address: ${location.address}`);
      
      // Test create procedure
      console.log('\nTesting create procedure...');
      const newLocation = await client.location.create.mutate({
        organizationId,
        data: {
          name: `Test Location ${Date.now()}`,
          address: '123 Test Street',
          description: 'A test location created by the tRPC client test script',
          status: 'active',
          coordinates: {
            latitude: 37.7749,
            longitude: -122.4194,
          },
        },
      });
      
      console.log(`Created location with ID: ${newLocation.id}`);
      
      // Test update procedure
      console.log('\nTesting update procedure...');
      const updatedLocation = await client.location.update.mutate({
        id: newLocation.id,
        organizationId,
        data: {
          name: `${newLocation.name} (Updated)`,
          description: 'An updated test location',
        },
      });
      
      console.log(`Updated location name: ${updatedLocation.name}`);
      
      // Test delete procedure
      console.log('\nTesting delete procedure...');
      const deleteResult = await client.location.delete.mutate({
        id: newLocation.id,
        organizationId,
      });
      
      console.log(`Delete result: ${JSON.stringify(deleteResult)}`);
    }
    
    console.log('\nAll tests completed successfully!');
  } catch (error) {
    console.error('Error testing tRPC client:', error);
  }
}

main().catch(console.error);
```

### Update Package.json Scripts

Update the `package.json` file to add scripts for running the test scripts:

```json
{
  "scripts": {
    "test:trpc:router": "ts-node scripts/test-trpc-location-router.ts",
    "test:trpc:client": "ts-node scripts/test-trpc-client.ts"
  }
}
```

## Step 2: Create a Component Test Page

Create a test page to verify that the tRPC hooks work correctly in a real component.

### Create a Test Page Component

Create a new file at `app/test/trpc/page.tsx`:

```tsx
'use client';

import { useState } from 'react';
import { useLocations, useLocationMutations } from '../../../hooks/use-locations-trpc';
import { useOrganization } from '../../../hooks/use-organization';

/**
 * Test page for tRPC hooks
 * This page tests the tRPC hooks for locations
 */
export default function TRPCTestPage() {
  const { organization } = useOrganization();
  const [page, setPage] = useState(1);
  const [limit, setLimit] = useState(10);
  const [search, setSearch] = useState('');
  const [status, setStatus] = useState('');
  const [newLocationName, setNewLocationName] = useState('');
  const [newLocationAddress, setNewLocationAddress] = useState('');
  const [selectedLocationId, setSelectedLocationId] = useState('');
  
  const {
    locations,
    totalLocations,
    totalPages,
    currentPage,
    isLoading,
    isError,
    error,
    refetch,
    handlePageChange,
  } = useLocations({
    page,
    limit,
    search,
    status,
  });
  
  const {
    createLocation,
    updateLocation,
    deleteLocation,
  } = useLocationMutations();
  
  const handleCreateLocation = async () => {
    if (!newLocationName || !newLocationAddress) {
      alert('Please enter a name and address for the new location');
      return;
    }
    
    try {
      await createLocation.mutate({
        name: newLocationName,
        address: newLocationAddress,
        status: 'active',
        coordinates: {
          latitude: 0,
          longitude: 0,
        },
      });
      
      setNewLocationName('');
      setNewLocationAddress('');
      refetch();
    } catch (error) {
      console.error('Error creating location:', error);
      alert(`Error creating location: ${error}`);
    }
  };
  
  const handleUpdateLocation = async () => {
    if (!selectedLocationId) {
      alert('Please select a location to update');
      return;
    }
    
    try {
      await updateLocation.mutate(selectedLocationId, {
        name: `${newLocationName || 'Updated Location'} (${Date.now()})`,
      });
      
      setSelectedLocationId('');
      refetch();
    } catch (error) {
      console.error('Error updating location:', error);
      alert(`Error updating location: ${error}`);
    }
  };
  
  const handleDeleteLocation = async () => {
    if (!selectedLocationId) {
      alert('Please select a location to delete');
      return;
    }
    
    if (!confirm('Are you sure you want to delete this location?')) {
      return;
    }
    
    try {
      await deleteLocation.mutate(selectedLocationId);
      
      setSelectedLocationId('');
      refetch();
    } catch (error) {
      console.error('Error deleting location:', error);
      alert(`Error deleting location: ${error}`);
    }
  };
  
  if (!organization) {
    return <div>Please select an organization</div>;
  }
  
  return (
    <div className="container mx-auto py-8">
      <h1 className="text-2xl font-bold mb-4">tRPC Test Page</h1>
      
      <div className="mb-8">
        <h2 className="text-xl font-semibold mb-2">Create Location</h2>
        <div className="flex gap-2 mb-2">
          <input
            type="text"
            placeholder="Name"
            value={newLocationName}
            onChange={(e) => setNewLocationName(e.target.value)}
            className="border p-2 rounded"
          />
          <input
            type="text"
            placeholder="Address"
            value={newLocationAddress}
            onChange={(e) => setNewLocationAddress(e.target.value)}
            className="border p-2 rounded"
          />
          <button
            onClick={handleCreateLocation}
            disabled={createLocation.isLoading}
            className="bg-blue-500 text-white px-4 py-2 rounded"
          >
            {createLocation.isLoading ? 'Creating...' : 'Create'}
          </button>
        </div>
      </div>
      
      <div className="mb-8">
        <h2 className="text-xl font-semibold mb-2">Update/Delete Location</h2>
        <div className="flex gap-2 mb-2">
          <select
            value={selectedLocationId}
            onChange={(e) => setSelectedLocationId(e.target.value)}
            className="border p-2 rounded"
          >
            <option value="">Select a location</option>
            {locations.map((location) => (
              <option key={location.id} value={location.id}>
                {location.name}
              </option>
            ))}
          </select>
          <button
            onClick={handleUpdateLocation}
            disabled={updateLocation.isLoading || !selectedLocationId}
            className="bg-green-500 text-white px-4 py-2 rounded"
          >
            {updateLocation.isLoading ? 'Updating...' : 'Update'}
          </button>
          <button
            onClick={handleDeleteLocation}
            disabled={deleteLocation.isLoading || !selectedLocationId}
            className="bg-red-500 text-white px-4 py-2 rounded"
          >
            {deleteLocation.isLoading ? 'Deleting...' : 'Delete'}
          </button>
        </div>
      </div>
      
      <div className="mb-4">
        <h2 className="text-xl font-semibold mb-2">Locations</h2>
        <div className="flex gap-2 mb-2">
          <input
            type="text"
            placeholder="Search"
            value={search}
            onChange={(e) => setSearch(e.target.value)}
            className="border p-2 rounded"
          />
          <select
            value={status}
            onChange={(e) => setStatus(e.target.value)}
            className="border p-2 rounded"
          >
            <option value="">All statuses</option>
            <option value="active">Active</option>
            <option value="inactive">Inactive</option>
          </select>
          <button
            onClick={() => refetch()}
            className="bg-gray-500 text-white px-4 py-2 rounded"
          >
            Refresh
          </button>
        </div>
      </div>
      
      {isLoading ? (
        <div>Loading locations...</div>
      ) : isError ? (
        <div>Error loading locations: {error?.message}</div>
      ) : locations.length === 0 ? (
        <div>No locations found</div>
      ) : (
        <div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {locations.map((location) => (
              <div
                key={location.id}
                className="border p-4 rounded"
                onClick={() => setSelectedLocationId(location.id)}
              >
                <h3 className="font-semibold">{location.name}</h3>
                <p>{location.address}</p>
                <p>Status: {location.status}</p>
              </div>
            ))}
          </div>
          
          <div className="mt-4 flex justify-between items-center">
            <div>
              Showing {locations.length} of {totalLocations} locations
            </div>
            <div className="flex gap-2">
              <button
                onClick={() => handlePageChange(currentPage - 1)}
                disabled={currentPage === 1}
                className="bg-gray-200 px-3 py-1 rounded"
              >
                Previous
              </button>
              <span className="px-3 py-1">
                Page {currentPage} of {totalPages}
              </span>
              <button
                onClick={() => handlePageChange(currentPage + 1)}
                disabled={currentPage === totalPages}
                className="bg-gray-200 px-3 py-1 rounded"
              >
                Next
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
```

## Step 3: Create a Test Plan

Create a test plan to systematically verify that the tRPC implementation works correctly.

```markdown
# tRPC Implementation Test Plan

## Server-Side Tests

1. **Router Tests**
   - Run the router test script: `pnpm test:trpc:router`
   - Verify that all procedures work correctly
   - Check error handling for invalid inputs
   - Verify authentication and authorization checks

2. **Client Tests**
   - Run the client test script: `pnpm test:trpc:client`
   - Verify that the client can connect to the server
   - Verify that all procedures work correctly
   - Check error handling for invalid inputs

## Frontend Tests

1. **Component Tests**
   - Navigate to the test page: `/test/trpc`
   - Test creating a location
   - Test updating a location
   - Test deleting a location
   - Test pagination
   - Test filtering
   - Verify that loading states are displayed correctly
   - Verify that error states are displayed correctly

2. **Hook Tests**
   - Verify that the `useLocations` hook works correctly
   - Verify that the `useLocationDetails` hook works correctly
   - Verify that the `useLocationMutations` hook works correctly
   - Verify that the `useSharedLocation` hook works correctly

## Backward Compatibility Tests

1. **REST API Tests**
   - Test the REST API endpoints with Postman or curl
   - Verify that the endpoints return the expected responses
   - Check error handling for invalid inputs

2. **Existing Component Tests**
   - Verify that existing components that use the REST API continue to work
   - Test creating, updating, and deleting locations using the existing components
   - Verify that the components display the correct data

## Performance Tests

1. **Response Time Tests**
   - Measure the response time for tRPC queries and mutations
   - Compare with the response time for REST API requests
   - Verify that tRPC is at least as fast as the REST API

2. **Payload Size Tests**
   - Measure the payload size for tRPC queries and mutations
   - Compare with the payload size for REST API requests
   - Verify that tRPC payloads are smaller than REST API payloads

## Security Tests

1. **Authentication Tests**
   - Verify that unauthenticated users cannot access protected procedures
   - Verify that users cannot access data from other organizations

2. **Authorization Tests**
   - Verify that users cannot perform actions they are not authorized to perform
   - Verify that users cannot access data they are not authorized to access
```

## Step 4: Document Test Results

Create a document to record the test results.

```markdown
# tRPC Implementation Test Results

## Server-Side Tests

### Router Tests
- [ ] All procedures work correctly
- [ ] Error handling for invalid inputs works correctly
- [ ] Authentication and authorization checks work correctly

### Client Tests
- [ ] Client can connect to the server
- [ ] All procedures work correctly
- [ ] Error handling for invalid inputs works correctly

## Frontend Tests

### Component Tests
- [ ] Creating a location works correctly
- [ ] Updating a location works correctly
- [ ] Deleting a location works correctly
- [ ] Pagination works correctly
- [ ] Filtering works correctly
- [ ] Loading states are displayed correctly
- [ ] Error states are displayed correctly

### Hook Tests
- [ ] `useLocations` hook works correctly
- [ ] `useLocationDetails` hook works correctly
- [ ] `useLocationMutations` hook works correctly
- [ ] `useSharedLocation` hook works correctly

## Backward Compatibility Tests

### REST API Tests
- [ ] Endpoints return the expected responses
- [ ] Error handling for invalid inputs works correctly

### Existing Component Tests
- [ ] Existing components continue to work
- [ ] Creating, updating, and deleting locations works correctly
- [ ] Components display the correct data

## Performance Tests

### Response Time Tests
- [ ] tRPC is at least as fast as the REST API

### Payload Size Tests
- [ ] tRPC payloads are smaller than REST API payloads

## Security Tests

### Authentication Tests
- [ ] Unauthenticated users cannot access protected procedures
- [ ] Users cannot access data from other organizations

### Authorization Tests
- [ ] Users cannot perform actions they are not authorized to perform
- [ ] Users cannot access data they are not authorized to access

## Issues and Resolutions

| Issue | Resolution |
| ----- | ---------- |
|       |            |
|       |            |
|       |            |
```

## Step 5: Create a Build Log

Create a build log to track the progress of the tRPC migration.

```markdown
# tRPC Migration Build Log

## Phase 1: Setup tRPC Infrastructure

- [x] Install required dependencies
- [x] Create tRPC server configuration
- [x] Create API route handler
- [x] Create root router
- [x] Create tRPC client configuration
- [x] Create tRPC provider
- [x] Update root layout to include tRPC provider

## Phase 2: Implement Location Router

- [x] Create location router
- [x] Implement input validation
- [x] Implement authentication and authorization checks
- [x] Update root router to include location router

## Phase 3: Update Frontend Hooks

- [x] Create tRPC hooks for locations
- [x] Update location components to use tRPC hooks
- [x] Create shared location component
- [x] Update shared location page

## Phase 4: Maintain Backward Compatibility

- [x] Update existing API routes to use tRPC router logic
- [x] Create helper function for error handling
- [x] Update API routes to use the helper function
- [x] Maintain existing frontend hooks
- [x] Create migration guide for components

## Phase 5: Testing and Validation

- [x] Create test scripts for tRPC endpoints
- [x] Create component test page
- [x] Create test plan
- [x] Document test results
- [x] Create build log

## Phase 6: Documentation

- [x] Document the tRPC implementation
- [x] Create guidelines for future feature migrations

## Phase 7: Migrate Other Features

- [ ] Apply the same pattern to other features
- [ ] Ensure consistent implementation across the application

## Phase 8: Optimizations

- [ ] Implement query invalidation strategies
- [ ] Implement request batching
- [ ] Implement optimistic updates
```

## Verification

To verify that the tRPC implementation works correctly:

1. Run the test scripts: `pnpm test:trpc:router` and `pnpm test:trpc:client`
2. Navigate to the test page: `/test/trpc`
3. Follow the test plan to systematically verify the implementation
4. Document the test results
5. Update the build log

## Next Steps

Once the tRPC implementation is tested and validated, proceed to the next document in this guide: [07_tRPC_Documentation.md](./07_tRPC_Documentation.md) to document the implementation.

## Troubleshooting

### Common Issues

1. **Authentication Errors**: Verify that the authentication middleware is correctly implemented and that it's properly extracting authentication information from the request.
2. **Authorization Errors**: Verify that the authorization checks are correctly implemented and that they're properly checking the user's permissions.
3. **Type Errors**: Check for type errors in the tRPC implementation.
4. **Network Errors**: Verify that the tRPC client is correctly configured to connect to the server.

### Debugging Tips

1. Add console logs to the tRPC procedures to verify that they're being called with the correct parameters.
2. Use the React Query Devtools to inspect queries and mutations.
3. Check the browser console for any client-side errors.
4. Use the Network tab in the browser's developer tools to inspect the tRPC requests and responses.
