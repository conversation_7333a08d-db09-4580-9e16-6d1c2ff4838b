# tRPC Migration: Setup Infrastructure

This document provides detailed instructions for setting up the tRPC infrastructure in the Scene-o-matic application. This is the first phase of the migration process and lays the foundation for implementing tRPC across the application.

## Prerequisites

Before proceeding, ensure you have:

- A working Next.js App Router project
- TypeScript configured
- Zod for schema validation
- TanStack Query (React Query) for data fetching

## Step 1: Install Required Dependencies

Install the necessary tRPC packages:

```bash
pnpm add @trpc/server @trpc/client @trpc/react-query @trpc/next zod
```

## Step 2: Create tRPC Server Configuration

Create a new file at `lib/trpc/server.ts` to set up the tRPC server:

```typescript
import { initTRPC, TRPCError } from '@trpc/server';
import { type NextRequest } from 'next/server';
import { ZodError } from 'zod';
import { getAuthFromRequest } from '../utils/auth-utils';

/**
 * Context type for tRPC procedures
 * This defines what will be available in the context parameter of procedures
 */
export interface Context {
  req: NextRequest;
  auth: {
    userId: string | null;
    organizationId: string | null;
    isAuthenticated: boolean;
    roles: string[];
    stytchMemberId: string | null;
    stytchSessionJwt?: string;
    stytchSessionToken?: string;
  };
}

/**
 * Creates context for tRPC procedures
 * This extracts authentication information from the request
 */
export async function createContext({ req }: { req: NextRequest }): Promise<Context> {
  const auth = await getAuthFromRequest(req);
  
  return {
    req,
    auth,
  };
}

/**
 * Initialize tRPC
 */
const t = initTRPC.context<Context>().create({
  errorFormatter({ shape, error }) {
    return {
      ...shape,
      data: {
        ...shape.data,
        zodError:
          error.cause instanceof ZodError ? error.cause.flatten() : null,
      },
    };
  },
});

/**
 * Create a router
 */
export const router = t.router;

/**
 * Create a public procedure (no authentication required)
 */
export const publicProcedure = t.procedure;

/**
 * Create a protected procedure (authentication required)
 */
export const protectedProcedure = t.procedure.use(({ ctx, next }) => {
  if (!ctx.auth.isAuthenticated) {
    throw new TRPCError({ code: 'UNAUTHORIZED' });
  }
  return next({
    ctx: {
      ...ctx,
      // Infers that auth is non-null
      auth: {
        ...ctx.auth,
        userId: ctx.auth.userId as string,
      },
    },
  });
});

/**
 * Create an organization procedure (organization membership required)
 */
export const organizationProcedure = t.procedure.use(({ ctx, next }) => {
  if (!ctx.auth.isAuthenticated) {
    throw new TRPCError({ code: 'UNAUTHORIZED' });
  }
  
  if (!ctx.auth.organizationId) {
    throw new TRPCError({ 
      code: 'FORBIDDEN',
      message: 'Organization access required'
    });
  }
  
  return next({
    ctx: {
      ...ctx,
      auth: {
        ...ctx.auth,
        userId: ctx.auth.userId as string,
        organizationId: ctx.auth.organizationId as string,
      },
    },
  });
});
```

## Step 3: Create Auth Utility Function

Ensure the `getAuthFromRequest` function is available in `lib/utils/auth-utils.ts`. If it doesn't exist, create it:

```typescript
import { type NextRequest } from 'next/server';
import { cookies } from 'next/headers';
import { loadStytch } from '../stytch-b2b';

/**
 * Extracts authentication information from a request
 * This function uses the Stytch headers added by the middleware
 */
export async function getAuthFromRequest(req: NextRequest) {
  try {
    // Extract auth headers added by middleware
    const stytchMemberId = req.headers.get('X-Stytch-Member-Id');
    const stytchOrgId = req.headers.get('X-Stytch-Org-Id');
    const stytchRoles = req.headers.get('X-Stytch-Roles');
    const stytchSessionJwt = req.headers.get('X-Stytch-Session-JWT');
    const stytchSessionToken = req.headers.get('X-Stytch-Session-Token');
    
    // Parse roles from JSON string
    let roles: string[] = [];
    try {
      if (stytchRoles) {
        roles = JSON.parse(stytchRoles);
      }
    } catch (e) {
      console.error('Error parsing roles:', e);
    }
    
    if (!stytchMemberId || !stytchOrgId) {
      return {
        userId: null,
        organizationId: null,
        isAuthenticated: false,
        roles: [],
        stytchMemberId: null,
        stytchSessionJwt: undefined,
        stytchSessionToken: undefined,
      };
    }
    
    return {
      userId: stytchMemberId,
      organizationId: stytchOrgId,
      isAuthenticated: true,
      roles,
      stytchMemberId,
      stytchSessionJwt: stytchSessionJwt || undefined,
      stytchSessionToken: stytchSessionToken || undefined,
    };
  } catch (error) {
    console.error('Auth error:', error);
    return {
      userId: null,
      organizationId: null,
      isAuthenticated: false,
      roles: [],
      stytchMemberId: null,
      stytchSessionJwt: undefined,
      stytchSessionToken: undefined,
    };
  }
}
```

## Step 4: Create API Route Handler for tRPC

Create a new file at `app/api/trpc/[trpc]/route.ts` to handle tRPC API requests:

```typescript
import { fetchRequestHandler } from '@trpc/server/adapters/fetch';
import { type NextRequest } from 'next/server';
import { appRouter } from '../../../../lib/trpc/routers/_app';
import { createContext } from '../../../../lib/trpc/server';

/**
 * Handle tRPC requests
 */
const handler = async (req: NextRequest) => {
  return fetchRequestHandler({
    endpoint: '/api/trpc',
    req,
    router: appRouter,
    createContext: () => createContext({ req }),
    onError:
      process.env.NODE_ENV === 'development'
        ? ({ path, error }) => {
            console.error(`❌ tRPC error on ${path ?? '<no-path>'}: ${error.message}`);
          }
        : undefined,
  });
};

export { handler as GET, handler as POST };
```

## Step 5: Create Root Router

Create a new file at `lib/trpc/routers/_app.ts` to define the root router:

```typescript
import { router } from '../server';

/**
 * Root router for the application
 * This will combine all feature routers
 */
export const appRouter = router({
  // Feature routers will be added here
});

/**
 * Export type definition of the API
 * This is used for client-side type inference
 */
export type AppRouter = typeof appRouter;
```

## Step 6: Create tRPC Client Configuration

Create a new file at `lib/trpc/client.ts` to set up the tRPC client:

```typescript
import { createTRPCReact } from '@trpc/react-query';
import { type AppRouter } from './routers/_app';

/**
 * Create a tRPC client for use in React components
 */
export const trpc = createTRPCReact<AppRouter>();
```

## Step 7: Create tRPC Provider

Create a new file at `providers/trpc-provider.tsx` to provide tRPC to the application:

```typescript
'use client';

import { useState } from 'react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { ReactQueryDevtools } from '@tanstack/react-query-devtools';
import { httpBatchLink } from '@trpc/client';
import { trpc } from '../lib/trpc/client';

/**
 * Props for the TRPCProvider component
 */
interface TRPCProviderProps {
  children: React.ReactNode;
}

/**
 * Provider component for tRPC
 * This sets up the tRPC client and provides it to the application
 */
export function TRPCProvider({ children }: TRPCProviderProps) {
  const [queryClient] = useState(() => new QueryClient({
    defaultOptions: {
      queries: {
        staleTime: 5 * 1000, // 5 seconds
        refetchOnWindowFocus: false,
      },
    },
  }));
  
  const [trpcClient] = useState(() =>
    trpc.createClient({
      links: [
        httpBatchLink({
          url: '/api/trpc',
        }),
      ],
    }),
  );
  
  return (
    <trpc.Provider client={trpcClient} queryClient={queryClient}>
      <QueryClientProvider client={queryClient}>
        {children}
        {process.env.NODE_ENV === 'development' && <ReactQueryDevtools />}
      </QueryClientProvider>
    </trpc.Provider>
  );
}
```

## Step 8: Update Root Layout to Include tRPC Provider

Update the `app/providers/AllClientProviders.tsx` file to include the TRPCProvider:

```typescript
'use client';

import { ThemeProvider } from '../../components/theme-provider';
import { AuthProvider } from '../../providers/auth-provider';
import { OrganizationProvider } from '../../providers/organization-provider';
import { NotificationProvider } from '../../providers/notification-provider';
import { MapProvider } from '../../providers/map-provider';
import { TRPCProvider } from '../../providers/trpc-provider';

/**
 * Props for the AllClientProviders component
 */
interface AllClientProvidersProps {
  children: React.ReactNode;
}

/**
 * Component that wraps all client-side providers
 */
export function AllClientProviders({ children }: AllClientProvidersProps) {
  return (
    <TRPCProvider>
      <ThemeProvider
        attribute="class"
        defaultTheme="system"
        enableSystem
        disableTransitionOnChange
      >
        <AuthProvider>
          <OrganizationProvider>
            <NotificationProvider>
              <MapProvider>
                {children}
              </MapProvider>
            </NotificationProvider>
          </OrganizationProvider>
        </AuthProvider>
      </ThemeProvider>
    </TRPCProvider>
  );
}
```

## Verification

To verify that the tRPC infrastructure is set up correctly:

1. Ensure all files are created as specified
2. Check for any TypeScript errors
3. Start the development server with `pnpm dev`
4. Verify that the application loads without errors

## Next Steps

Once the tRPC infrastructure is set up, proceed to the next document in this guide: [03_tRPC_Location_Router.md](./03_tRPC_Location_Router.md) to implement the location router.

## Troubleshooting

### Common Issues

1. **TypeScript Errors**: Ensure that all imports are correct and that the types are properly defined.
2. **Module Not Found Errors**: Check that all paths in import statements are correct.
3. **Authentication Errors**: Verify that the `getAuthFromRequest` function is correctly implemented and that it's properly extracting authentication information from the request.

### Debugging Tips

1. Add console logs to the tRPC handler to verify that requests are being processed.
2. Use the React Query Devtools to inspect queries and mutations.
3. Check the browser console for any client-side errors.
