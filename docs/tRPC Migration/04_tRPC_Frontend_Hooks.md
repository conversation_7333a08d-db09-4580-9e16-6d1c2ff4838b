# tRPC Migration: Frontend Hooks Implementation

This document provides detailed instructions for implementing frontend hooks that use tRPC in the Scene-o-matic application. This is the third phase of the migration process and focuses on creating React hooks that leverage tRPC for data fetching and mutations.

## Prerequisites

Before proceeding, ensure you have:

1. Completed the tRPC infrastructure setup as described in [02_tRPC_Setup_Infrastructure.md](./02_tRPC_Setup_Infrastructure.md)
2. Implemented the location router as described in [03_tRPC_Location_Router.md](./03_tRPC_Location_Router.md)
3. Familiarized yourself with the existing frontend hooks for locations

## Step 1: Create tRPC Hooks for Locations

Create a new file at `hooks/use-locations-trpc.ts` to define the tRPC hooks for locations:

```typescript
'use client';

import { useState } from 'react';
import { trpc } from '../lib/trpc/client';
import { useOrganization } from './use-organization';
import { type RouterOutputs } from '../lib/trpc/types';

/**
 * Type definition for location data from tRPC
 */
type Location = RouterOutputs['location']['getById'];
type LocationList = RouterOutputs['location']['getAll'];

/**
 * Hook for fetching locations using tRPC
 */
export function useLocations(options?: {
  page?: number;
  limit?: number;
  search?: string;
  status?: string;
  projectId?: string;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}) {
  const { organization } = useOrganization();
  const organizationId = organization?.id;
  
  const [page, setPage] = useState(options?.page || 1);
  const [limit, setLimit] = useState(options?.limit || 10);
  
  const {
    data,
    isLoading,
    isError,
    error,
    refetch,
  } = trpc.location.getAll.useQuery(
    {
      organizationId: organizationId || '',
      page,
      limit,
      search: options?.search,
      status: options?.status,
      projectId: options?.projectId,
      sortBy: options?.sortBy,
      sortOrder: options?.sortOrder,
    },
    {
      enabled: !!organizationId,
      keepPreviousData: true,
    }
  );
  
  const handlePageChange = (newPage: number) => {
    setPage(newPage);
  };
  
  const handleLimitChange = (newLimit: number) => {
    setLimit(newLimit);
    setPage(1); // Reset to first page when changing limit
  };
  
  return {
    locations: data?.locations || [],
    totalLocations: data?.totalCount || 0,
    totalPages: data?.totalPages || 0,
    currentPage: page,
    pageSize: limit,
    isLoading,
    isError,
    error,
    refetch,
    handlePageChange,
    handleLimitChange,
  };
}

/**
 * Hook for fetching a single location by ID using tRPC
 */
export function useLocationDetails(locationId: string) {
  const { organization } = useOrganization();
  const organizationId = organization?.id;
  
  const {
    data: location,
    isLoading,
    isError,
    error,
    refetch,
  } = trpc.location.getById.useQuery(
    {
      id: locationId,
      organizationId: organizationId || '',
    },
    {
      enabled: !!organizationId && !!locationId,
    }
  );
  
  return {
    location,
    isLoading,
    isError,
    error,
    refetch,
  };
}

/**
 * Hook for location mutations using tRPC
 */
export function useLocationMutations() {
  const { organization } = useOrganization();
  const organizationId = organization?.id;
  
  const utils = trpc.useContext();
  
  // Create location mutation
  const createLocation = trpc.location.create.useMutation({
    onSuccess: () => {
      // Invalidate the locations query to refetch the data
      utils.location.getAll.invalidate();
    },
  });
  
  // Update location mutation
  const updateLocation = trpc.location.update.useMutation({
    onSuccess: (updatedLocation) => {
      // Invalidate the specific location query
      if (updatedLocation.id) {
        utils.location.getById.invalidate({
          id: updatedLocation.id,
          organizationId: organizationId || '',
        });
      }
      
      // Invalidate the locations list query
      utils.location.getAll.invalidate();
    },
  });
  
  // Delete location mutation
  const deleteLocation = trpc.location.delete.useMutation({
    onSuccess: () => {
      // Invalidate the locations query to refetch the data
      utils.location.getAll.invalidate();
    },
  });
  
  // Add media mutation
  const addMedia = trpc.location.addMedia.useMutation({
    onSuccess: (_, variables) => {
      // Invalidate the specific location query
      utils.location.getById.invalidate({
        id: variables.locationId,
        organizationId: variables.organizationId,
      });
    },
  });
  
  // Remove media mutation
  const removeMedia = trpc.location.removeMedia.useMutation({
    onSuccess: (_, variables) => {
      // Invalidate the specific location query
      utils.location.getById.invalidate({
        id: variables.locationId,
        organizationId: variables.organizationId,
      });
    },
  });
  
  // Share location mutation (if implemented)
  const shareLocation = trpc.location.shareLocation?.useMutation();
  
  return {
    createLocation: {
      mutate: (data: any) => createLocation.mutate({ organizationId: organizationId || '', data }),
      isLoading: createLocation.isLoading,
      error: createLocation.error,
    },
    updateLocation: {
      mutate: (id: string, data: any) => updateLocation.mutate({ id, organizationId: organizationId || '', data }),
      isLoading: updateLocation.isLoading,
      error: updateLocation.error,
    },
    deleteLocation: {
      mutate: (id: string) => deleteLocation.mutate({ id, organizationId: organizationId || '' }),
      isLoading: deleteLocation.isLoading,
      error: deleteLocation.error,
    },
    addMedia: {
      mutate: (locationId: string, media: any[]) => addMedia.mutate({ locationId, organizationId: organizationId || '', media }),
      isLoading: addMedia.isLoading,
      error: addMedia.error,
    },
    removeMedia: {
      mutate: (locationId: string, mediaId: string) => removeMedia.mutate({ locationId, organizationId: organizationId || '', mediaId }),
      isLoading: removeMedia.isLoading,
      error: removeMedia.error,
    },
    shareLocation: shareLocation ? {
      mutate: (locationId: string, expiresIn?: number) => shareLocation.mutate({ locationId, organizationId: organizationId || '', expiresIn }),
      isLoading: shareLocation.isLoading,
      error: shareLocation.error,
    } : undefined,
  };
}

/**
 * Hook for fetching a shared location by token using tRPC
 */
export function useSharedLocation(token: string) {
  const {
    data: location,
    isLoading,
    isError,
    error,
  } = trpc.location.getSharedLocation.useQuery(
    { token },
    {
      enabled: !!token,
    }
  );
  
  return {
    location,
    isLoading,
    isError,
    error,
  };
}
```

## Step 2: Create Types for tRPC Router Outputs

Create a new file at `lib/trpc/types.ts` to define types for tRPC router outputs:

```typescript
import { type inferRouterOutputs } from '@trpc/server';
import { type AppRouter } from './routers/_app';

/**
 * Infer the router output types
 */
export type RouterOutputs = inferRouterOutputs<AppRouter>;
```

## Step 3: Update Location Components to Use tRPC Hooks

Now, let's update the location components to use the new tRPC hooks. Here are examples of how to update key components:

### Location List Component

Update the `components/locations/location-list.tsx` component:

```typescript
'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { useLocations } from '../../hooks/use-locations-trpc';
import { useOrganization } from '../../hooks/use-organization';
import { Pagination } from '../ui/pagination';
// Import other UI components as needed

export function LocationList({
  search,
  status,
  projectId,
  sortBy,
  sortOrder,
}: {
  search?: string;
  status?: string;
  projectId?: string;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}) {
  const router = useRouter();
  const { organization } = useOrganization();
  const [limit, setLimit] = useState(10);
  
  const {
    locations,
    totalLocations,
    totalPages,
    currentPage,
    isLoading,
    isError,
    handlePageChange,
    handleLimitChange,
  } = useLocations({
    page: 1,
    limit,
    search,
    status,
    projectId,
    sortBy,
    sortOrder,
  });
  
  const handleLocationClick = (id: string) => {
    router.push(`/organizations/${organization?.slug}/locations/${id}`);
  };
  
  if (isLoading) {
    return <div>Loading locations...</div>;
  }
  
  if (isError) {
    return <div>Error loading locations</div>;
  }
  
  if (locations.length === 0) {
    return <div>No locations found</div>;
  }
  
  return (
    <div>
      <div className="space-y-4">
        {locations.map((location) => (
          <div
            key={location.id}
            className="cursor-pointer p-4 border rounded-lg hover:bg-gray-50"
            onClick={() => handleLocationClick(location.id)}
          >
            <h3 className="font-medium">{location.name}</h3>
            <p className="text-sm text-gray-500">{location.address}</p>
            {/* Add more location details as needed */}
          </div>
        ))}
      </div>
      
      {totalPages > 1 && (
        <div className="mt-4">
          <Pagination
            currentPage={currentPage}
            totalPages={totalPages}
            onPageChange={handlePageChange}
          />
        </div>
      )}
    </div>
  );
}
```

### Location Details Component

Update the `components/locations/location-details-client.tsx` component:

```typescript
'use client';

import { useRouter } from 'next/navigation';
import { useLocationDetails, useLocationMutations } from '../../hooks/use-locations-trpc';
import { useOrganization } from '../../hooks/use-organization';
import { Button } from '../ui/button';
import { LocationMap } from './location-map';
// Import other UI components as needed

export function LocationDetailsClient({ locationId }: { locationId: string }) {
  const router = useRouter();
  const { organization } = useOrganization();
  
  const {
    location,
    isLoading,
    isError,
  } = useLocationDetails(locationId);
  
  const {
    deleteLocation,
    shareLocation,
  } = useLocationMutations();
  
  const handleEdit = () => {
    router.push(`/organizations/${organization?.slug}/locations/edit/${locationId}`);
  };
  
  const handleDelete = async () => {
    if (confirm('Are you sure you want to delete this location?')) {
      try {
        await deleteLocation.mutate(locationId);
        router.push(`/organizations/${organization?.slug}/locations`);
      } catch (error) {
        console.error('Error deleting location:', error);
      }
    }
  };
  
  const handleShare = async () => {
    if (shareLocation) {
      try {
        const result = await shareLocation.mutate(locationId);
        // Handle share result, e.g., show a modal with the share link
        console.log('Share token:', result?.token);
      } catch (error) {
        console.error('Error sharing location:', error);
      }
    }
  };
  
  if (isLoading) {
    return <div>Loading location details...</div>;
  }
  
  if (isError || !location) {
    return <div>Error loading location details</div>;
  }
  
  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold">{location.name}</h1>
        <div className="space-x-2">
          <Button onClick={handleEdit}>Edit</Button>
          <Button variant="outline" onClick={handleShare}>Share</Button>
          <Button variant="destructive" onClick={handleDelete}>Delete</Button>
        </div>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div>
          <h2 className="text-lg font-medium mb-2">Details</h2>
          <div className="space-y-2">
            <p><strong>Address:</strong> {location.address}</p>
            <p><strong>Status:</strong> {location.status}</p>
            {location.description && (
              <p><strong>Description:</strong> {location.description}</p>
            )}
            {/* Add more location details as needed */}
          </div>
        </div>
        
        <div>
          <h2 className="text-lg font-medium mb-2">Map</h2>
          <LocationMap location={location} />
        </div>
      </div>
      
      {location.media && location.media.length > 0 && (
        <div>
          <h2 className="text-lg font-medium mb-2">Media</h2>
          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
            {location.media.map((item) => (
              <div key={item.id} className="aspect-square relative">
                <img
                  src={item.url}
                  alt={item.name || 'Location media'}
                  className="object-cover w-full h-full rounded-lg"
                />
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
}
```

### Location Create Form Component

Update the `components/locations/location-create-form.tsx` component:

```typescript
'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { useLocationMutations } from '../../hooks/use-locations-trpc';
import { useOrganization } from '../../hooks/use-organization';
import { createLocationSchema } from '../../modules/location/schema';
import { Button } from '../ui/button';
import { Input } from '../ui/input';
import { Textarea } from '../ui/textarea';
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '../ui/form';
// Import other UI components as needed

export function LocationCreateForm() {
  const router = useRouter();
  const { organization } = useOrganization();
  const [isSubmitting, setIsSubmitting] = useState(false);
  
  const { createLocation } = useLocationMutations();
  
  const form = useForm({
    resolver: zodResolver(createLocationSchema),
    defaultValues: {
      name: '',
      address: '',
      description: '',
      status: 'active',
      coordinates: {
        latitude: 0,
        longitude: 0,
      },
    },
  });
  
  const onSubmit = async (data: any) => {
    if (!organization?.id) return;
    
    setIsSubmitting(true);
    
    try {
      const newLocation = await createLocation.mutate(data);
      router.push(`/organizations/${organization.slug}/locations/${newLocation.id}`);
    } catch (error) {
      console.error('Error creating location:', error);
      // Handle error, e.g., show an error message
    } finally {
      setIsSubmitting(false);
    }
  };
  
  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
        <FormField
          control={form.control}
          name="name"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Name</FormLabel>
              <FormControl>
                <Input {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        
        <FormField
          control={form.control}
          name="address"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Address</FormLabel>
              <FormControl>
                <Input {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        
        <FormField
          control={form.control}
          name="description"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Description</FormLabel>
              <FormControl>
                <Textarea {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        
        {/* Add more form fields as needed */}
        
        <Button type="submit" disabled={isSubmitting}>
          {isSubmitting ? 'Creating...' : 'Create Location'}
        </Button>
      </form>
    </Form>
  );
}
```

## Step 4: Create a Shared Location Component

Create a new component for viewing shared locations at `components/locations/shared-location-view.tsx`:

```typescript
'use client';

import { useSharedLocation } from '../../hooks/use-locations-trpc';
import { LocationMap } from './location-map';
// Import other UI components as needed

export function SharedLocationView({ token }: { token: string }) {
  const {
    location,
    isLoading,
    isError,
  } = useSharedLocation(token);
  
  if (isLoading) {
    return <div>Loading shared location...</div>;
  }
  
  if (isError || !location) {
    return <div>Error loading shared location. The link may be invalid or expired.</div>;
  }
  
  return (
    <div className="space-y-6">
      <h1 className="text-2xl font-bold">{location.name}</h1>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div>
          <h2 className="text-lg font-medium mb-2">Details</h2>
          <div className="space-y-2">
            <p><strong>Address:</strong> {location.address}</p>
            {location.description && (
              <p><strong>Description:</strong> {location.description}</p>
            )}
            {/* Add more location details as needed */}
          </div>
        </div>
        
        <div>
          <h2 className="text-lg font-medium mb-2">Map</h2>
          <LocationMap location={location} />
        </div>
      </div>
      
      {location.media && location.media.length > 0 && (
        <div>
          <h2 className="text-lg font-medium mb-2">Media</h2>
          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
            {location.media.map((item) => (
              <div key={item.id} className="aspect-square relative">
                <img
                  src={item.url}
                  alt={item.name || 'Location media'}
                  className="object-cover w-full h-full rounded-lg"
                />
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
}
```

## Step 5: Update the Shared Location Page

Update the shared location page at `app/shared/location/[token]/page.tsx`:

```typescript
import { SharedLocationView } from '../../../../components/locations/shared-location-view';

export default function SharedLocationPage({
  params,
}: {
  params: { token: string };
}) {
  return (
    <div className="container mx-auto py-8">
      <SharedLocationView token={params.token} />
    </div>
  );
}
```

## Verification

To verify that the frontend hooks are implemented correctly:

1. Ensure all files are created as specified
2. Check for any TypeScript errors
3. Start the development server with `pnpm dev`
4. Navigate to the locations page and verify that the locations are displayed correctly
5. Test creating, updating, and deleting locations
6. Test sharing a location and accessing the shared location page

## Next Steps

Once the frontend hooks are implemented, proceed to the next document in this guide: [05_tRPC_Backward_Compatibility.md](./05_tRPC_Backward_Compatibility.md) to maintain backward compatibility with the existing REST API.

## Troubleshooting

### Common Issues

1. **TypeScript Errors**: Ensure that all imports are correct and that the types are properly defined.
2. **Module Not Found Errors**: Check that all paths in import statements are correct.
3. **React Hook Errors**: Ensure that hooks are used according to the rules of hooks (only called at the top level of components, not inside conditionals, etc.).
4. **tRPC Query Errors**: Verify that the tRPC queries are properly enabled and that they're receiving the correct parameters.

### Debugging Tips

1. Use the React Query Devtools to inspect queries and mutations.
2. Add console logs to the hooks to verify that they're being called with the correct parameters.
3. Check the browser console for any client-side errors.
4. Use the Network tab in the browser's developer tools to inspect the tRPC requests and responses.
