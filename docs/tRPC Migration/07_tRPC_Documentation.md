# tRPC Migration: Documentation

This document provides comprehensive documentation for the tRPC implementation in the Scene-o-matic application. This is the sixth phase of the migration process and ensures that the implementation is well-documented for future reference.

## Overview

tRPC (TypeScript Remote Procedure Call) is a library for building end-to-end typesafe APIs. It allows you to define your API endpoints as TypeScript functions and automatically infer the types for your client, providing a seamless development experience with full type safety.

In the Scene-o-matic application, tRPC is used to:

1. Define API endpoints with full type safety
2. Simplify data fetching and mutations
3. Reduce boilerplate code
4. Improve developer experience with automatic type inference
5. Optimize network requests with request batching

## Project Structure

The tRPC implementation follows a specific structure to organize the code and make it maintainable:

```
lib/
├── trpc/
│   ├── server.ts             # tRPC server configuration
│   ├── client.ts             # tRPC client configuration
│   ├── types.ts              # Type definitions for tRPC
│   └── routers/
│       ├── _app.ts           # Root router that combines all feature routers
│       ├── location.ts       # Location feature router
│       └── [feature].ts      # Other feature routers
app/
└── api/
    └── trpc/
        └── [trpc]/
            └── route.ts      # API route handler for tRPC
providers/
└── trpc-provider.tsx         # tRPC provider component
hooks/
└── use-[feature]-trpc.ts     # Feature-specific hooks using tRPC
```

## Core Components

### tRPC Server Configuration

The tRPC server configuration is defined in `lib/trpc/server.ts`. This file sets up the tRPC server, defines the context type, and creates the procedures.

```typescript
// lib/trpc/server.ts
import { initTRPC, TRPCError } from '@trpc/server';
import { type NextRequest } from 'next/server';
import { ZodError } from 'zod';
import { getAuthFromRequest } from '../utils/auth-utils';

// Context type
export interface Context {
  req: NextRequest;
  auth: {
    userId: string | null;
    organizationId: string | null;
    isAuthenticated: boolean;
  };
}

// Context creator
export async function createContext({ req }: { req: NextRequest }): Promise<Context> {
  const auth = await getAuthFromRequest(req);
  
  return {
    req,
    auth,
  };
}

// Initialize tRPC
const t = initTRPC.context<Context>().create({
  errorFormatter({ shape, error }) {
    return {
      ...shape,
      data: {
        ...shape.data,
        zodError:
          error.cause instanceof ZodError ? error.cause.flatten() : null,
      },
    };
  },
});

// Create a router
export const router = t.router;

// Create a public procedure (no authentication required)
export const publicProcedure = t.procedure;

// Create a protected procedure (authentication required)
export const protectedProcedure = t.procedure.use(({ ctx, next }) => {
  if (!ctx.auth.isAuthenticated) {
    throw new TRPCError({ code: 'UNAUTHORIZED' });
  }
  return next({
    ctx: {
      ...ctx,
      auth: {
        ...ctx.auth,
        userId: ctx.auth.userId as string,
      },
    },
  });
});

// Create an organization procedure (organization membership required)
export const organizationProcedure = t.procedure.use(({ ctx, next }) => {
  if (!ctx.auth.isAuthenticated) {
    throw new TRPCError({ code: 'UNAUTHORIZED' });
  }
  
  if (!ctx.auth.organizationId) {
    throw new TRPCError({ 
      code: 'FORBIDDEN',
      message: 'Organization access required'
    });
  }
  
  return next({
    ctx: {
      ...ctx,
      auth: {
        ...ctx.auth,
        userId: ctx.auth.userId as string,
        organizationId: ctx.auth.organizationId as string,
      },
    },
  });
});
```

### API Route Handler

The API route handler is defined in `app/api/trpc/[trpc]/route.ts`. This file handles the tRPC API requests and forwards them to the appropriate router.

```typescript
// app/api/trpc/[trpc]/route.ts
import { fetchRequestHandler } from '@trpc/server/adapters/fetch';
import { type NextRequest } from 'next/server';
import { appRouter } from '../../../../lib/trpc/routers/_app';
import { createContext } from '../../../../lib/trpc/server';

const handler = async (req: NextRequest) => {
  return fetchRequestHandler({
    endpoint: '/api/trpc',
    req,
    router: appRouter,
    createContext: () => createContext({ req }),
    onError:
      process.env.NODE_ENV === 'development'
        ? ({ path, error }) => {
            console.error(`❌ tRPC error on ${path ?? '<no-path>'}: ${error.message}`);
          }
        : undefined,
  });
};

export { handler as GET, handler as POST };
```

### Root Router

The root router is defined in `lib/trpc/routers/_app.ts`. This file combines all feature routers into a single router.

```typescript
// lib/trpc/routers/_app.ts
import { router } from '../server';
import { locationRouter } from './location';
// Import other feature routers as needed

export const appRouter = router({
  location: locationRouter,
  // Add other feature routers here
});

export type AppRouter = typeof appRouter;
```

### tRPC Client Configuration

The tRPC client configuration is defined in `lib/trpc/client.ts`. This file sets up the tRPC client for use in React components.

```typescript
// lib/trpc/client.ts
import { createTRPCReact } from '@trpc/react-query';
import { type AppRouter } from './routers/_app';

export const trpc = createTRPCReact<AppRouter>();
```

### tRPC Provider

The tRPC provider is defined in `providers/trpc-provider.tsx`. This file provides the tRPC client to the application.

```typescript
// providers/trpc-provider.tsx
'use client';

import { useState } from 'react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { ReactQueryDevtools } from '@tanstack/react-query-devtools';
import { httpBatchLink } from '@trpc/client';
import { trpc } from '../lib/trpc/client';

interface TRPCProviderProps {
  children: React.ReactNode;
}

export function TRPCProvider({ children }: TRPCProviderProps) {
  const [queryClient] = useState(() => new QueryClient({
    defaultOptions: {
      queries: {
        staleTime: 5 * 1000, // 5 seconds
        refetchOnWindowFocus: false,
      },
    },
  }));
  
  const [trpcClient] = useState(() =>
    trpc.createClient({
      links: [
        httpBatchLink({
          url: '/api/trpc',
        }),
      ],
    }),
  );
  
  return (
    <trpc.Provider client={trpcClient} queryClient={queryClient}>
      <QueryClientProvider client={queryClient}>
        {children}
        {process.env.NODE_ENV === 'development' && <ReactQueryDevtools />}
      </QueryClientProvider>
    </trpc.Provider>
  );
}
```

## Feature Implementation: Locations

### Location Router

The location router is defined in `lib/trpc/routers/location.ts`. This file defines the procedures for the locations feature.

```typescript
// lib/trpc/routers/location.ts
import { z } from 'zod';
import { TRPCError } from '@trpc/server';
import { router, organizationProcedure } from '../server';
import * as locationService from '../../../modules/location/service';
import { locationSchema, createLocationSchema, updateLocationSchema } from '../../../modules/location/schema';

export const locationRouter = router({
  // Get all locations
  getAll: organizationProcedure
    .input(
      z.object({
        organizationId: z.string(),
        page: z.number().optional().default(1),
        limit: z.number().optional().default(10),
        search: z.string().optional(),
        status: z.string().optional(),
        projectId: z.string().optional(),
        sortBy: z.string().optional(),
        sortOrder: z.enum(['asc', 'desc']).optional(),
      })
    )
    .query(async ({ ctx, input }) => {
      // Implementation...
    }),
    
  // Get a single location by ID
  getById: organizationProcedure
    .input(
      z.object({
        id: z.string(),
        organizationId: z.string(),
      })
    )
    .query(async ({ ctx, input }) => {
      // Implementation...
    }),
    
  // Create a new location
  create: organizationProcedure
    .input(
      z.object({
        organizationId: z.string(),
        data: createLocationSchema,
      })
    )
    .mutation(async ({ ctx, input }) => {
      // Implementation...
    }),
    
  // Update an existing location
  update: organizationProcedure
    .input(
      z.object({
        id: z.string(),
        organizationId: z.string(),
        data: updateLocationSchema,
      })
    )
    .mutation(async ({ ctx, input }) => {
      // Implementation...
    }),
    
  // Delete a location
  delete: organizationProcedure
    .input(
      z.object({
        id: z.string(),
        organizationId: z.string(),
      })
    )
    .mutation(async ({ ctx, input }) => {
      // Implementation...
    }),
    
  // Add media to a location
  addMedia: organizationProcedure
    .input(
      z.object({
        locationId: z.string(),
        organizationId: z.string(),
        media: z.array(
          z.object({
            url: z.string(),
            type: z.string(),
            name: z.string().optional(),
            size: z.number().optional(),
          })
        ),
      })
    )
    .mutation(async ({ ctx, input }) => {
      // Implementation...
    }),
    
  // Remove media from a location
  removeMedia: organizationProcedure
    .input(
      z.object({
        locationId: z.string(),
        organizationId: z.string(),
        mediaId: z.string(),
      })
    )
    .mutation(async ({ ctx, input }) => {
      // Implementation...
    }),
    
  // Share a location
  shareLocation: organizationProcedure
    .input(
      z.object({
        locationId: z.string(),
        organizationId: z.string(),
        expiresIn: z.number().optional(),
      })
    )
    .mutation(async ({ ctx, input }) => {
      // Implementation...
    }),
    
  // Get a shared location by token
  getSharedLocation: publicProcedure
    .input(
      z.object({
        token: z.string(),
      })
    )
    .query(async ({ input }) => {
      // Implementation...
    }),
});
```

### Location Hooks

The location hooks are defined in `hooks/use-locations-trpc.ts`. This file defines the hooks for the locations feature.

```typescript
// hooks/use-locations-trpc.ts
'use client';

import { useState } from 'react';
import { trpc } from '../lib/trpc/client';
import { useOrganization } from './use-organization';
import { type RouterOutputs } from '../lib/trpc/types';

// Type definitions
type Location = RouterOutputs['location']['getById'];
type LocationList = RouterOutputs['location']['getAll'];

// Hook for fetching locations
export function useLocations(options?: {
  page?: number;
  limit?: number;
  search?: string;
  status?: string;
  projectId?: string;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}) {
  // Implementation...
}

// Hook for fetching a single location by ID
export function useLocationDetails(locationId: string) {
  // Implementation...
}

// Hook for location mutations
export function useLocationMutations() {
  // Implementation...
}

// Hook for fetching a shared location by token
export function useSharedLocation(token: string) {
  // Implementation...
}
```

## Backward Compatibility

To maintain backward compatibility with the existing REST API, the API routes are updated to use the tRPC router logic. This ensures that both the REST API and tRPC API behave consistently.

### API Routes

The API routes are updated to use the tRPC router logic:

```typescript
// app/api/locations/route.ts
import { NextResponse } from 'next/server';
import { type NextRequest } from 'next/server';
import { z } from 'zod';
import { getAuthFromRequest } from '../../../lib/utils/auth-utils';
import { appRouter } from '../../../lib/trpc/routers/_app';
import { createContext } from '../../../lib/trpc/server';

export async function GET(request: NextRequest) {
  try {
    // Parse query parameters
    const url = new URL(request.url);
    const page = parseInt(url.searchParams.get('page') || '1');
    const limit = parseInt(url.searchParams.get('limit') || '10');
    const search = url.searchParams.get('search') || undefined;
    const status = url.searchParams.get('status') || undefined;
    const projectId = url.searchParams.get('projectId') || undefined;
    const sortBy = url.searchParams.get('sortBy') || undefined;
    const sortOrder = url.searchParams.get('sortOrder') as 'asc' | 'desc' | undefined;
    const organizationId = url.searchParams.get('organizationId') || '';
    
    // Create context
    const ctx = await createContext({ req: request });
    
    // Create caller
    const caller = appRouter.createCaller(ctx);
    
    // Call tRPC procedure
    const result = await caller.location.getAll({
      organizationId,
      page,
      limit,
      search,
      status,
      projectId,
      sortBy,
      sortOrder,
    });
    
    return NextResponse.json(result);
  } catch (error: any) {
    // Error handling...
  }
}

export async function POST(request: NextRequest) {
  // Implementation...
}
```

### Error Handling

A helper function is created to standardize error handling between the REST API and tRPC:

```typescript
// lib/utils/api-utils.ts
import { TRPCError } from '@trpc/server';

export function mapTRPCErrorToHTTPStatus(error: TRPCError): number {
  // Implementation...
}

export function formatErrorResponse(error: unknown) {
  // Implementation...
}
```

## Best Practices

### Input Validation

Use Zod schemas for input validation:

```typescript
import { z } from 'zod';

const inputSchema = z.object({
  id: z.string(),
  organizationId: z.string(),
});

const { id, organizationId } = inputSchema.parse(input);
```

### Error Handling

Use TRPCError for consistent error handling:

```typescript
import { TRPCError } from '@trpc/server';

if (!ctx.auth.isAuthenticated) {
  throw new TRPCError({ code: 'UNAUTHORIZED' });
}

if (!location) {
  throw new TRPCError({
    code: 'NOT_FOUND',
    message: 'Location not found',
  });
}
```

### Query Invalidation

Implement proper cache invalidation strategies:

```typescript
const utils = trpc.useContext();

const createLocation = trpc.location.create.useMutation({
  onSuccess: () => {
    // Invalidate the locations query to refetch the data
    utils.location.getAll.invalidate();
  },
});
```

### Optimistic Updates

Use optimistic updates for better user experience:

```typescript
const updateLocation = trpc.location.update.useMutation({
  onMutate: async (variables) => {
    // Cancel any outgoing refetches
    await utils.location.getById.cancel({ id: variables.id, organizationId });
    
    // Snapshot the previous value
    const previousLocation = utils.location.getById.getData({ id: variables.id, organizationId });
    
    // Optimistically update to the new value
    utils.location.getById.setData({ id: variables.id, organizationId }, (old) => {
      return {
        ...old,
        ...variables.data,
      };
    });
    
    return { previousLocation };
  },
  onError: (err, variables, context) => {
    // If the mutation fails, use the context returned from onMutate to roll back
    if (context?.previousLocation) {
      utils.location.getById.setData({ id: variables.id, organizationId }, context.previousLocation);
    }
  },
  onSettled: (data, error, variables) => {
    // Always refetch after error or success to make sure the server state is correct
    utils.location.getById.invalidate({ id: variables.id, organizationId });
    utils.location.getAll.invalidate();
  },
});
```

## Migration Guide

### Migrating a Component

1. Import the tRPC hooks instead of the REST API hooks:

   ```typescript
   // Before
   import { useLocations } from '../../hooks/use-locations';
   
   // After
   import { useLocations } from '../../hooks/use-locations-trpc';
   ```

2. Update the component to use the tRPC hooks:

   ```typescript
   // Before
   const {
     locations,
     totalLocations,
     totalPages,
     currentPage,
     isLoading,
     isError,
     handlePageChange,
   } = useLocations({
     page: 1,
     limit: 10,
     search,
     status,
     projectId,
   });
   
   // After
   const {
     locations,
     totalLocations,
     totalPages,
     currentPage,
     isLoading,
     isError,
     handlePageChange,
   } = useLocations({
     page: 1,
     limit: 10,
     search,
     status,
     projectId,
   });
   ```

   Note that the API is designed to be compatible, so the component code often doesn't need to change.

3. Update mutation calls:

   ```typescript
   // Before
   const { createLocation, updateLocation, deleteLocation } = useLocationMutations();
   
   // After
   const { createLocation, updateLocation, deleteLocation } = useLocationMutations();
   
   // Usage remains the same
   await createLocation.mutate(data);
   await updateLocation.mutate(id, data);
   await deleteLocation.mutate(id);
   ```

### Testing the Migration

After migrating a component, test it thoroughly to ensure that it works correctly with tRPC:

1. Test all data fetching functionality
2. Test all mutation functionality
3. Test error handling
4. Test loading states

## Extending the Implementation

### Adding a New Feature Router

To add a new feature router:

1. Create a new file at `lib/trpc/routers/[feature].ts`:

   ```typescript
   import { z } from 'zod';
   import { TRPCError } from '@trpc/server';
   import { router, organizationProcedure } from '../server';
   import * as featureService from '../../../modules/[feature]/service';
   
   export const featureRouter = router({
     // Define procedures here
   });
   ```

2. Update the root router in `lib/trpc/routers/_app.ts`:

   ```typescript
   import { router } from '../server';
   import { locationRouter } from './location';
   import { featureRouter } from './[feature]';
   
   export const appRouter = router({
     location: locationRouter,
     feature: featureRouter,
   });
   
   export type AppRouter = typeof appRouter;
   ```

3. Create hooks for the new feature in `hooks/use-[feature]-trpc.ts`:

   ```typescript
   'use client';
   
   import { useState } from 'react';
   import { trpc } from '../lib/trpc/client';
   import { useOrganization } from './use-organization';
   import { type RouterOutputs } from '../lib/trpc/types';
   
   // Define hooks here
   ```

### Adding a New Procedure

To add a new procedure to an existing router:

1. Update the router file:

   ```typescript
   export const featureRouter = router({
     // Existing procedures...
     
     // New procedure
     newProcedure: organizationProcedure
       .input(
         z.object({
           // Define input schema
         })
       )
       .query(async ({ ctx, input }) => {
         // Implementation...
       }),
   });
   ```

2. Update the hooks file:

   ```typescript
   export function useFeature() {
     // Existing hook implementation...
     
     // Add new procedure
     const newProcedureResult = trpc.feature.newProcedure.useQuery(
       {
         // Input parameters
       },
       {
         // Options
       }
     );
     
     return {
       // Existing return values...
       newProcedureResult,
     };
   }
   ```

## Conclusion

This documentation provides a comprehensive overview of the tRPC implementation in the Scene-o-matic application. It covers the core components, feature implementation, backward compatibility, best practices, migration guide, and how to extend the implementation.

By following this documentation, developers can understand how tRPC is implemented in the application and how to use it effectively.

## References

- [tRPC Documentation](https://trpc.io/docs)
- [Next.js Documentation](https://nextjs.org/docs)
- [React Query Documentation](https://tanstack.com/query/latest/docs/react/overview)
- [Zod Documentation](https://zod.dev/)
