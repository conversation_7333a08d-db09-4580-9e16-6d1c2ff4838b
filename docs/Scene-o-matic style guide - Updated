# Scene-o-matic Style Guide - Futuristic Edition

## 1. Overall Look & Feel

The application presents a **modern, futuristic, and high-tech** user interface. It's data-centric with a sleek, cutting-edge aesthetic that utilizes neon accents, dark backgrounds, and glowing elements for a sci-fi inspired look. Key characteristics include:

* **High Contrast:** Charcoal backgrounds with vibrant neon accents create a striking visual experience.
* **Glowing Elements:** Subtle shadows and glows around interactive elements create a futuristic feel.
* **Rounded Corners:** Consistent use of rounded corners on cards, buttons, and UI elements.
* **Neon Accents:** Vibrant neon colors are used for highlights, active states, and important UI elements.
* **Clean Typography:** Sharp, legible text with clear hierarchy and generous spacing.
* **Data Visualization:** Modern charts, interactive maps, and well-structured tables present information effectively.

## 2. Color Palette

| Name | Hex Code | CSS Variable | Usage |
|:-----|:---------|:-------------|:------|
| **Neon Blue** | `#2323FF` | `--neon-blue` | Primary accent color, active states, primary buttons, highlights |
| **Neon Green** | `#16FF00` | `--neon-green` | Success states, positive indicators, action buttons, available status |
| **Neon Pink** | `#F61981` | `--neon-pink` | Alerts, destructive actions, error states, sold/cancelled status |
| **Neon Yellow** | `#FFED00` | `--neon-yellow` | Warnings, pending states, secondary highlights |
| **Deep Black** | `#000000` | `--deep-black` | Dark mode backgrounds |
| **Charcoal** | `#1F2937` | `--sidebar-bg` (light mode) | Light mode sidebar background, dark UI elements |
| **Deep Black** | `#000000` | `--sidebar-bg` (dark mode) | Dark mode sidebar background |
| **Pure White** | `#FFFFFF` | `--white` | Text on dark backgrounds, light mode backgrounds |
| **Light Gray** | `#F9FAFB` | `--gray-50` | Subtle backgrounds, dividers in light mode |
| **Medium Gray** | `#9CA3AF` | `--gray-400` | Secondary text, disabled states |
| **Dark Gray** | `#1F2937` | `--gray-800` | Text on light backgrounds |

## 3. Typography

* **Font Family:** System UI stack with fallbacks for cross-platform consistency
* **Hierarchy:**
  * **Page Titles:** `text-2xl font-bold text-foreground`
  * **Section Titles:** `text-xl font-semibold text-foreground`
  * **Card Titles:** `text-lg font-medium text-foreground`
  * **Body Text:** `text-sm text-foreground`
  * **Small Text/Labels:** `text-xs text-muted-foreground`
  * **Stats/KPIs:** `text-3xl font-bold text-foreground`

## 4. Layout & Spacing

* **Main Layout:** Fixed charcoal sidebar (`w-64`), main content area takes remaining space
* **Padding:**
  * Cards: `p-4` or `p-6`
  * Buttons: `px-4 py-2` (adjust based on size)
  * Inputs: `px-3 py-2`
  * Modals: `p-6`
  * Sidebar items: `px-3 py-2.5`
* **Margins/Gaps:** Consistent spacing using Tailwind's scale (`gap-2`, `gap-4`, `space-y-3`, etc.)
* **Rounding:**
  * Buttons: `rounded-md` or `rounded-full` for icon buttons
  * Cards: `rounded-lg`
  * Badges: `rounded-full`
  * Inputs: `rounded-md` or `rounded-full` for search inputs
* **Shadows & Glows:**
  * Neon Blue glow: `shadow-[0_0_10px_rgba(35,35,255,0.7)]`
  * Neon Green glow: `shadow-[0_0_10px_rgba(22,255,0,0.7)]`
  * Neon Pink glow: `shadow-[0_0_10px_rgba(246,25,129,0.7)]`
  * Neon Yellow glow: `shadow-[0_0_10px_rgba(255,237,0,0.7)]`
  * Subtle card shadow: `shadow-md`
  * **Note on Neon Glows:** The `shadow-[...]` examples below illustrate the desired neon glow effect. These specific arbitrary shadow classes are *not* pre-defined in the Tailwind configuration or global CSS. They need to be implemented either via a custom Tailwind plugin, added to global CSS, or applied directly where needed.

## 5. Component Styles

### Sidebar

```html
<div class="flex h-full flex-col border-r bg-[#1F2937] dark:bg-black text-white z-30">
  <!-- Logo area -->
  <div class="p-4 border-b border-white/20">
    <div class="flex flex-col">
      <div class="flex items-center gap-2">
        <div class="flex h-8 w-8 items-center justify-center rounded-md bg-neon-blue text-white shadow-[0_0_10px_rgba(35,35,255,0.7)]">
          <span class="text-sm font-bold">SM</span>
        </div>
        <span class="text-lg font-bold text-white">Scene-o-matic</span>
      </div>
      <span class="text-xs text-white/80 mt-1 ml-10 italic">Your Locations Start Here</span>
    </div>
  </div>
  
  <!-- Navigation -->
  <nav class="flex flex-col gap-2 p-4">
    <!-- Active item -->
    <a class="group flex items-center gap-3 rounded-md px-3 py-2.5 text-sm font-medium bg-neon-green text-deep-black shadow-[0_0_10px_rgba(22,255,0,0.7)] hover:shadow-[0_0_15px_rgba(22,255,0,0.9)]">
      <svg class="h-5 w-5 text-deep-black transition-all"></svg>
      <span>Active Item</span>
    </a>
    
    <!-- Inactive item -->
    <a class="group flex items-center gap-3 rounded-md px-3 py-2.5 text-sm font-medium text-white/70 hover:bg-neon-green/10 hover:text-neon-green hover:shadow-[0_0_5px_rgba(22,255,0,0.5)]">
      <svg class="h-5 w-5 text-white/70 transition-all group-hover:text-neon-green group-hover:shadow-[0_0_8.75px_rgba(22,255,0,1.0)]"></svg>
      <span>Inactive Item</span>
    </a>
  </nav>
  
  <!-- User profile -->
  <div class="border-t border-white/10 p-4 mt-auto">
    <div class="flex items-center gap-3">
      <div class="relative h-10 w-10 overflow-hidden rounded-full bg-neon-blue/20">
        <img src="avatar.jpg" alt="User avatar" class="h-full w-full object-cover" />
      </div>
      <div class="flex flex-col">
        <span class="text-sm font-medium">User Name</span>
        <span class="text-xs text-white/60"><EMAIL></span>
      </div>
    </div>
  </div>
</div>
```

### Buttons

```html
<!-- Primary (Neon Blue) -->
<button class="bg-neon-blue text-white hover:bg-neon-blue/90 shadow-[0_0_10px_rgba(35,35,255,0.7)] px-4 py-2 rounded-md text-sm font-medium">
  Primary Button
</button>

<!-- Success (Neon Green) -->
<button class="bg-neon-green text-deep-black hover:bg-neon-green/90 shadow-[0_0_10px_rgba(22,255,0,0.7)] px-4 py-2 rounded-md text-sm font-medium">
  Success Button
</button>

<!-- Danger (Neon Pink) -->
<button class="bg-neon-pink text-white hover:bg-neon-pink/90 shadow-[0_0_10px_rgba(246,25,129,0.7)] px-4 py-2 rounded-md text-sm font-medium">
  Danger Button
</button>

<!-- Warning (Neon Yellow) -->
<button class="bg-neon-yellow text-deep-black hover:bg-neon-yellow/90 shadow-[0_0_10px_rgba(255,237,0,0.7)] px-4 py-2 rounded-md text-sm font-medium">
  Warning Button
</button>

<!-- Secondary/Outline -->
<button class="border border-border-medium bg-background text-foreground hover:bg-accent hover:text-accent-foreground px-4 py-2 rounded-md text-sm font-medium">
  Secondary Button
</button>

<!-- Ghost -->
<button class="text-foreground hover:bg-accent hover:text-accent-foreground px-4 py-2 rounded-md text-sm font-medium">
  Ghost Button
</button>

<!-- Icon Button -->
<button class="h-10 w-10 rounded-full flex items-center justify-center bg-neon-blue text-white shadow-[0_0_10px_rgba(35,35,255,0.7)]">
  <svg class="h-5 w-5"></svg>
</button>
```

### Cards

```html
<!-- Default Card -->
<div class="rounded-lg border border-border bg-card text-card-foreground shadow-md p-6">
  <h3 class="text-lg font-semibold mb-2">Card Title</h3>
  <p class="text-sm text-muted-foreground">Card content goes here.</p>
</div>

<!-- Neon Blue Card -->
<div class="rounded-lg border border-neon-blue/30 bg-card text-card-foreground shadow-[0_0_15px_rgba(35,35,255,0.3)] p-6">
  <h3 class="text-lg font-semibold mb-2">Neon Blue Card</h3>
  <p class="text-sm text-muted-foreground">Card content goes here.</p>
</div>

<!-- Neon Green Card -->
<div class="rounded-lg border border-neon-green/30 bg-card text-card-foreground shadow-[0_0_15px_rgba(22,255,0,0.3)] p-6">
  <h3 class="text-lg font-semibold mb-2">Neon Green Card</h3>
  <p class="text-sm text-muted-foreground">Card content goes here.</p>
</div>
```

### Badges/Status Tags

Status badges should directly use the defined neon colors for visual consistency across themes, as shown in the examples below. (Note: While CSS variables like `--status-red-*` exist, they map differently in light/dark modes, whereas these examples use direct neon colors).

```html
<!-- Neon Blue Badge (e.g., Info) -->
<span class="inline-flex items-center rounded-full border-transparent bg-neon-blue/10 text-neon-blue shadow-[0_0_5px_rgba(35,35,255,0.5)] px-2.5 py-0.5 text-xs font-medium">
  Info
</span>

<!-- Neon Green Badge (e.g., Available, Success) -->
<span class="inline-flex items-center rounded-full border-transparent bg-neon-green/10 text-neon-green shadow-[0_0_5px_rgba(22,255,0,0.5)] px-2.5 py-0.5 text-xs font-medium">
  Available
</span>

<!-- Neon Yellow Badge (e.g., Pending, Warning) -->
<span class="inline-flex items-center rounded-full border-transparent bg-neon-yellow/10 text-deep-black shadow-[0_0_5px_rgba(255,237,0,0.5)] px-2.5 py-0.5 text-xs font-medium">
  Pending
</span>

<!-- Neon Pink Badge (e.g., Sold, Error, Danger) -->
<span class="inline-flex items-center rounded-full border-transparent bg-neon-pink/10 text-neon-pink shadow-[0_0_5px_rgba(246,25,129,0.5)] px-2.5 py-0.5 text-xs font-medium">
  Sold
</span>
```

### Tables

```html
<div class="relative w-full overflow-auto rounded-lg">
  <table class="w-full caption-bottom text-sm">
    <thead class="[&_tr]:border-b bg-muted/30">
      <tr>
        <th class="h-12 px-4 text-left align-middle font-medium text-muted-foreground uppercase text-xs tracking-wider">
          Column 1
        </th>
        <th class="h-12 px-4 text-left align-middle font-medium text-muted-foreground uppercase text-xs tracking-wider">
          Column 2
        </th>
      </tr>
    </thead>
    <tbody class="[&_tr:last-child]:border-0">
      <tr class="border-b transition-colors hover:bg-muted/30 data-[state=selected]:bg-muted/50">
        <td class="p-4 align-middle">Cell 1</td>
        <td class="p-4 align-middle">Cell 2</td>
      </tr>
    </tbody>
  </table>
</div>
```

### Maps

```html
<div class="relative h-[600px] w-full rounded-lg overflow-hidden border border-border">
  <!-- Map container -->
  <div class="h-full w-full bg-muted"></div>
  
  <!-- Search bar -->
  <div class="absolute left-4 top-4 w-64">
    <div class="relative">
      <svg class="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground"></svg>
      <input 
        type="text" 
        placeholder="Search address..." 
        class="h-10 w-full rounded-full border border-border bg-background pl-10 pr-4 text-sm focus:outline-none focus:ring-2 focus:ring-neon-blue"
      />
    </div>
  </div>
  
  <!-- Map controls -->
  <div class="absolute right-4 top-4 flex flex-col space-y-2">
    <button class="h-10 w-10 rounded-full bg-neon-blue text-white shadow-[0_0_10px_rgba(35,35,255,0.7)] flex items-center justify-center">
      <svg class="h-5 w-5"></svg>
    </button>
    <button class="h-10 w-10 rounded-full bg-white/90 backdrop-blur-sm flex items-center justify-center">
      <svg class="h-5 w-5"></svg>
    </button>
  </div>
  
  <!-- Location marker -->
  <div class="absolute" style="top: 30%; left: 40%;">
    <div class="flex h-10 w-10 items-center justify-center rounded-full bg-neon-blue text-white shadow-[0_0_10px_rgba(35,35,255,0.7)]">
      <svg class="h-5 w-5"></svg>
    </div>
  </div>
</div>
```

## 6. Dark Mode & Light Mode

The application supports both dark and light modes, with the dark mode being the primary design focus for the futuristic aesthetic.

### Dark Mode (Default)
- Background: Deep black (`#000000`)
- Text: White (`#FFFFFF`)
- Sidebar: Black (`#000000`)
- Cards: Near black (`#0A0A0A`) with subtle borders
- Accents: Vibrant neon colors with glowing effects

### Light Mode
- Background: White (`#FFFFFF`)
- Text: Deep black (`#000000`)
- Sidebar: Charcoal (`#1F2937`) with white text
  - **Important**: The sidebar text is white to ensure readability against the dark background
- Cards: White with subtle borders
- Accents: Same neon colors but with reduced opacity and glow effects

### Theme Toggle

The application includes a theme toggle in the top navigation bar, allowing users to switch between dark and light modes:

```html
<div class="flex items-center gap-2">
  <DropdownMenu>
    <DropdownMenuTrigger asChild>
      <Button variant="ghost" size="icon" className="h-9 w-9 rounded-full">
        <Sun className="h-[1.2rem] w-[1.2rem] rotate-0 scale-100 transition-all dark:-rotate-90 dark:scale-0" />
        <Moon className="absolute h-[1.2rem] w-[1.2rem] rotate-90 scale-0 transition-all dark:rotate-0 dark:scale-100" />
        <span className="sr-only">Toggle theme</span>
      </Button>
    </DropdownMenuTrigger>
    <DropdownMenuContent align="end">
      <DropdownMenuItem onClick={() => setTheme("light")}>
        <Sun className="mr-2 h-4 w-4" />
        <span>Light</span>
      </DropdownMenuItem>
      <DropdownMenuItem onClick={() => setTheme("dark")}>
        <Moon className="mr-2 h-4 w-4" />
        <span>Dark</span>
      </DropdownMenuItem>
      <DropdownMenuItem onClick={() => setTheme("system")}>
        <span className="mr-2">💻</span>
        <span>System</span>
      </DropdownMenuItem>
    </DropdownMenuContent>
  </DropdownMenu>
</div>
```

Key features:
- Animated icon transition between sun and moon
- Dropdown menu with light, dark, and system options
- Positioned in the top navigation bar for easy access
- Dark mode is set as the default theme

## 7. Accessibility Considerations

- Maintain sufficient contrast ratios between text and backgrounds
- Ensure interactive elements have clear focus states
- Provide alternative text for images and icons
- Use semantic HTML elements for proper screen reader support
- Ensure color is not the only means of conveying information
- The dark sidebar (charcoal in light mode, black in dark mode) with white text ensures readability in both themes

## 8. Animation & Transitions

- **Hover Effects**: All interactive elements have subtle hover animations:
  - Cards: Slight elevation (translate-y) and increased shadow on hover
  - Buttons: Increased glow effect and slight elevation on hover
  - Badges: Increased glow and slightly higher opacity background on hover
- **Transition Properties**: 
  - Use `transition-all duration-300` for smooth animations
  - Apply subtle transforms like `hover:translate-y-[-2px]` for elevation effects
  - Increase shadow intensity on hover with values like `hover:shadow-[0_0_15px_rgba(35,35,255,0.9)]`
- **Glow Effects**: Neon elements have subtle glow effects that intensify on hover
  - Normal state: `shadow-[0_0_10px_rgba(35,35,255,0.7)]`
  - Hover state: `hover:shadow-[0_0_15px_rgba(35,35,255,0.9)]`
- **Animation Principles**:
  - Keep animations subtle and purposeful
  - Ensure animations provide visual feedback for user interactions
  - Maintain consistent animation timing across similar elements

This style guide provides a foundation for creating a modern, futuristic interface for the Scene-o-matic application. The design emphasizes high contrast, neon accents, and a sleek, professional aesthetic while maintaining usability and accessibility.
