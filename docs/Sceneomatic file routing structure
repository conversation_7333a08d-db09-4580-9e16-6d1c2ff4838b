# Scene-o-matic: Understanding the Next.js App Router Structure

This document explains the file-based routing structure used in the Scene-o-matic application, built with the Next.js App Router. 
It focuses on the multi-tenant architecture centered around organizations and how layouts are shared.

## Core Concept: Route Groups for Shared Layouts

Next.js App Router uses parentheses `()` to create **route groups**.

-   **Purpose:** Route groups organize files and apply a shared layout (`layout.tsx` within the group) to all routes nested inside them.
-   **URL Impact:** Route groups **do not** add segments to the URL path. `app/(dashboard)/settings/page.tsx` maps to `/settings`.
-   **Scene-o-matic Usage:** The `app/(dashboard)` group primarily houses the shared layout definition (`layout.tsx`) and associated components (`_components`) used across authenticated, post-organization-selection views. The actual organization-specific routes reside elsewhere but utilize this shared layout structure.

## Core Routing Pattern: Organization-Scoped Routes within Dashboard Layout

The application employs a strict multi-tenant architecture. After authentication and organization selection, all user activity occurs
within the context of that organization, utilizing a common dashboard layout structure defined within `app/(dashboard)/`.

**The fundamental pattern for organization-specific routes is:** `app/organizations/[organizationSlug]/...`

-   **`app/organizations/`**: This directory contains all routes specific to an organization context.
-   **`[organizationSlug]`**: This is a **dynamic route segment**. It captures the unique identifier (slug) of the organization the
    user is currently accessing (e.g., `acme-corp`, `cinematics`, `default-org`). This slug is crucial for data fetching and context.
-   **`...`**: Represents further nested routes for specific features within that organization
    (e.g., `projects`, `settings`, `locations/[locationId]`).
-   **Shared Layout Application:** While these routes are *not* directly inside the `app/(dashboard)` route group, they inherit the shared dashboard UI. This is typically achieved through a layout file within `app/organizations/` (e.g., `app/organizations/layout.tsx` or `app/organizations/[organizationSlug]/layout.tsx`) that imports and utilizes the layout structure defined in `app/(dashboard)/layout.tsx` or its associated components (`app/(dashboard)/_components`). The `(dashboard)` route group itself doesn't directly render these organization pages but provides the reusable layout definition.

**Example URLs:**

-   `/organizations/acme-corp/projects` (Maps to `app/organizations/[organizationSlug]/projects/page.tsx`)
-   `/organizations/default-org/settings/members` (Maps to `app/organizations/[organizationSlug]/settings/members/page.tsx`)
-   `/organizations/acme-corp/projects/proj_123/locations/loc_456` (Maps to `app/organizations/[organizationSlug]/projects/[projectId]/locations/[locationId]/page.tsx`)

## How Dynamic Routing (`[organizationSlug]`) Works

1.  **URL Matching:** When a user navigates to `/organizations/acme-corp/projects`, Next.js matches this pattern to the 
    file structure `app/(dashboard)/organizations/[organizationSlug]/projects/page.tsx`.
2.  **Parameter Extraction:** The value `acme-corp` is extracted.
3.  **Parameter Access in Pages:** Page components receive the extracted parameters via the `params` prop.

```typescriptreact
// Example: app/organizations/[organizationSlug]/projects/page.tsx

interface OrgProjectsPageProps {
  params: {
    organizationSlug: string; // Will contain 'acme-corp', 'cinematics', etc.
  };
}

export default function OrgProjectsPage({ params }: OrgProjectsPageProps) {
  const { organizationSlug } = params;
  // Use organizationSlug for data fetching specific to this organization
  return <div>Projects for {organizationSlug}</div>;
}
```

4.  **Layout Access:** Crucially, the shared layout structure (defined in `app/(dashboard)/layout.tsx` and applied via layouts in `app/organizations/`) also has access to these parameters, allowing it to display organization-specific information (like the org name) or adjust navigation based on user roles within that organization.

## File Structure Overview (Actual Structure)

The `app/` directory separates the definition of the shared dashboard layout (`app/(dashboard)/`) from the actual organization-specific routes (`app/organizations/`).

```plaintext
app/
├── (dashboard)/                     # Route Group containing shared layout definition
│   ├── _components/                 # Components specific to the dashboard layout (header, sidebar)
│   │   └── ...
│   └── layout.tsx                   # SHARED LAYOUT DEFINITION (used by organization routes)
├── organizations/                   # Organization-specific features ARE HERE
│   ├── [organizationSlug]/          # Dynamic segment for organization
│   │   ├── page.tsx                 # Org Dashboard: /organizations/[slug]
│   │   ├── activity/                # /organizations/[slug]/activity
│   │   ├── billing/                 # /organizations/[slug]/billing/**
│   │   ├── calendar/                # /organizations/[slug]/calendar/**
│   │   ├── documents/               # /organizations/[slug]/documents/**
│   │   ├── locations/               # /organizations/[slug]/locations/**
│   │   ├── map/                     # /organizations/[slug]/map/**
│   │   ├── members/                 # /organizations/[slug]/members/**
│   │   ├── projects/                # /organizations/[slug]/projects/**
│   │   ├── scenes/                  # /organizations/[slug]/scenes/**
│   │   ├── settings/                # /organizations/[slug]/settings/**
│   │   ├── tasks/                   # /organizations/[slug]/tasks/**
│   │   └── team/                    # /organizations/[slug]/team/**
│   │       # ... further nesting for projects/[id], settings/*, etc.
│   └── layout.tsx                   # Optional: Layout for all org routes, likely applies the (dashboard) layout
├── auth/                            # Authentication routes (outside dashboard layout)
│   └── ...
├── api/                             # API routes
│   └── organizations/
│       └── [organizationId]/        # API endpoints must also respect org context
│           └── ...
├── components/                      # Global UI Components
│   └── ...
├── lib/                             # Libraries, utilities, config
│   └── ...
├── modules/                         # Business logic modules
│   └── ...
└── page.tsx                         # Landing page: /
└── layout.tsx                       # Root layout
```

*(Note: The file structure example above is illustrative)*

## Multi-Tenant Architecture Summary

1.  **Organization is the Tenant Unit:** All application data and functionality post-authentication is tied to an organization.
2.  **URL-Based Context:** `[organizationSlug]` in the URL identifies the current organization.
3.  **Shared Dashboard Layout:** The layout defined in `app/(dashboard)/layout.tsx` (and its `_components`) provides the consistent UI shell (nav, header). This layout is applied to organization-specific views located in `app/organizations/[organizationSlug]/...`, likely via a layout file within `app/organizations/`.
4.  **Data Isolation:** Backend APIs and data fetching logic **must** use the `organizationSlug` (or corresponding ID) to filter data.
5.  **Middleware:** `middleware.ts` handles authentication checks and potentially initial organization selection/redirection.

## Guidelines for Development

### Adding New Features (Post-Authentication):

1.  **Determine Placement:** Since nearly all features operate within an organization context and should share the dashboard layout,
    new feature routes should almost always go inside `app/organizations/[organizationSlug]/...`.
2.  **Define URL/File Path:** Follow the established patterns:
    -   List: `app/organizations/[organizationSlug]/feature/page.tsx` -> `/organizations/[slug]/feature`
    -   Detail: `app/organizations/[organizationSlug]/feature/[featureId]/page.tsx` -> `/organizations/[slug]/feature/[id]`
3.  **Leverage Shared Layout:** Utilize the context or data provided by the shared dashboard layout structure (defined in `app/(dashboard)/` and applied via `app/organizations/` layouts).
4.  **Component Placement:** Place reusable UI components in `components/`.
5.  **Data Fetching & APIs:** Ensure organization context (`organizationSlug` or ID) is passed to and used by data fetching functions 
    and API endpoints (`app/api/organizations/[organizationId]/...`).

### Troubleshooting Routing:

1.  **404 Errors:** Check the file path inside `app/organizations/[organizationSlug]/...`. Ensure dynamic segments `[]` are correct and the corresponding `page.tsx` or `route.ts` exists.
2.  **Layout Not Applied:** Verify the layout structure in `app/organizations/layout.tsx` (or similar) correctly imports and applies the shared layout from `app/(dashboard)/layout.tsx`. Check for errors in both layout files.
3.  **Incorrect Org Data:** Confirm `organizationSlug` is correctly accessed from `params` in the page component and any relevant layout files. Verify API filtering.

This documentation reflects the current file structure and routing patterns, ensuring layout consistency and proper context handling for the multi-tenant application.
