# Build Log

**May 1, 2025**
*   **Notification System Implementation:** Implemented a comprehensive notification system with the following components:
    *   **Database Schema:** Created database tables and enums for notifications and notification preferences:
        *   **Tables:** `notifications` and `notification_preferences`
        *   **Enums:** `notification_type`, `notification_status`, and `notification_priority`
        *   **Migration:** Added migration file `0002_notification_tables.sql` with schema definitions
    *   **API Endpoints:** Implemented RESTful API endpoints for notification management:
        *   `GET /api/notifications/count`: Returns total and unread notification counts
        *   `GET /api/notifications`: Returns paginated list of notifications with filtering options
        *   `PUT /api/notifications/[id]/read`: Marks a specific notification as read
        *   `PUT /api/notifications/read-all`: Marks all notifications as read
        *   `GET /api/notifications/preferences`: Gets notification preferences
        *   `PUT /api/notifications/preferences`: Updates notification preferences
    *   **Service Layer:** Created notification service (`modules/notification/service.ts`) with methods for:
        *   Creating, retrieving, updating, and deleting notifications
        *   Managing notification preferences
        *   Handling notification status changes
    *   **UI Components:** Implemented frontend components for notification display and interaction:
        *   **Notification Provider:** Created a notification provider (`providers/notification-provider.tsx`) that manages notification state and operations
        *   **Notification Bell:** Implemented a notification bell component (`components/notifications/notification-bell.tsx`) with a badge showing unread notification count
        *   **Notification Dropdown:** Created a dropdown menu (`components/notifications/notification-dropdown.tsx`) that displays recent notifications
        *   **Notification Modal:** Added a modal component (`components/notifications/notification-modal.tsx`) for detailed notification viewing
        *   **Notifications Page:** Implemented a dedicated notifications page (`app/notifications/page.tsx`) with client component (`components/notifications/notifications-page-client.tsx`)
    *   **Stytch Integration:** Added utility functions for Stytch authentication integration:
        *   `extractUuidFromStytchId`: Extracts UUID from Stytch ID format
        *   `getUserIdFromStytchId`: Converts Stytch user ID to database user ID
        *   `getOrgIdFromStytchId`: Converts Stytch organization ID to database organization ID
    *   **RBAC Integration:** Created a notification resource definition for the RBAC system (`docs/tasks/RBAC/Notification_Resource_Definition.md`) with permissions:
        *   `read`: For viewing notifications
        *   `mark_as_read`: For marking notifications as read
        *   `manage_preferences`: For updating notification preferences
    *   **Documentation:** Added comprehensive documentation (`docs/Notification System.md`) covering:
        *   System overview and database schema
        *   API endpoints and service methods
        *   Frontend components and integration details
        *   Common use cases and troubleshooting

**April 30, 2025**
*   **RBAC Fix:** Resolved 403 Forbidden error on `GET /api/organizations/[organizationId]/projects`. The issue stemmed from the `checkPermission` helper incorrectly passing the session JWT to the `session_token` parameter of the Stytch `sessions.authenticate` SDK method.
    *   **Investigation:** Verified user role assignment in Stytch, checked middleware token/role propagation via headers, confirmed API route parameters, and analyzed Stytch policy configuration. Added detailed error logging to `checkPermission` which revealed the `invalid_session_token` error.
    *   **Solution:** Modified `lib/rbac/checkPermission.ts` to accept an `AuthParams` object containing either `session_token` or `session_jwt`. Updated the function to dynamically pass the correct parameter (`session_token` or `session_jwt`) to `stytchClient.sessions.authenticate`. Updated the calling API route (`app/api/organizations/[organizationId]/projects/route.ts`) to pass the `AuthParams` object instead of just the token string.
*   **RBAC Phase 2, Step 5:** Secured remaining core API endpoints (Organizations, Members, Auth Settings, Locations, Map, Debug, etc.) using `checkPermission` helper or appropriate membership checks. Refactored `/api/auth/me` to use middleware headers. Verified webhook signature checks.
*   **RBAC Phase 2, Step 4:** Secured project API endpoints (`app/api/organizations/[organizationId]/projects/route.ts`) for `GET` (read - later changed to 'view') and `POST` (create) actions using `checkPermission` helper. Updated `middleware.ts` to pass session token/JWT via headers (`X-Stytch-Session-Token` / `X-Stytch-Session-JWT`).


## Database Schema Update: Stripe/Kinde to Polar/Stytch Migration
**Date:** April 29, 2025
**Developer:** AI Assistant

### Overview
This update migrates the payment and authentication systems from Stripe/Kinde to Polar/Stytch. The changes include database schema updates, new modules for subscription management, and webhook handlers for Polar events.

### Changes Made

#### 1. Database Schema Updates

##### Organizations Table
Added the following fields to the `organizations` table:
- `polar_customer_id` (varchar(255), unique): Links organizations to Polar customers
- `subscription_tier` (varchar(50), default 'basic'): Stores the organization's subscription tier
- `subscription_status` (varchar(50)): Tracks the status of the organization's subscription
- `subscription_valid_until` (timestamp): Records when the subscription expires
- `max_projects` (integer, default 5): Limit based on subscription tier
- `max_locations_per_project` (integer, default 50): Limit based on subscription tier
- `max_team_members` (integer, default 5): Limit based on subscription tier
- `max_storage` (integer, default 10): Storage limit in GB based on subscription tier

##### New Tables
Created two new tables for subscription management:

**subscriptions**
- `id` (uuid, primary key)
- `organization_id` (uuid, foreign key to organizations)
- `polar_subscription_id` (varchar(255), unique)
- `tier` (varchar(50))
- `status` (varchar(50))
- `current_period_start` (timestamp)
- `current_period_end` (timestamp)
- `cancel_at_period_end` (boolean, default false)
- `created_at` (timestamp)
- `updated_at` (timestamp)
- `metadata` (jsonb)
- `quantity` (integer, default 1)

**subscription_invoices**
- `id` (uuid, primary key)
- `organization_id` (uuid, foreign key to organizations)
- `subscription_id` (uuid, foreign key to subscriptions)
- `polar_invoice_id` (varchar(255), unique)
- `amount` (integer)
- `currency` (varchar(10), default 'usd')
- `status` (varchar(50))
- `invoice_url` (varchar(255))
- `invoice_number` (varchar(100))
- `invoice_date` (timestamp)
- `paid_at` (timestamp)

#### 2. New Module: Subscription

Created a new module for subscription management with the following files:

**modules/subscription/schema.ts**
- Defines the database schema for subscriptions and invoices
- Includes enums for subscription tiers, statuses, and payment statuses

**modules/subscription/model.ts**
- Defines TypeScript types for subscriptions and invoices
- Includes Zod validation schemas for data validation

**modules/subscription/service.ts**
- Provides CRUD operations for subscriptions and invoices
- Includes functions to get, create, update, and delete subscriptions and invoices

**modules/subscription/polar.ts**
- Implements integration with the Polar API
- Provides functions for customer management, subscription management, and checkout

#### 3. Organization Module Updates

**modules/organization/schema.ts**
- Updated to include Polar-related fields

**modules/organization/model.ts**
- Updated to include Polar-related fields in the Organization type
- Updated validation schemas

**modules/organization/service.ts**
- Added `getOrganizationByPolarCustomerId` function to retrieve organizations by Polar customer ID

#### 4. Webhook Handler

**app/api/webhooks/polar/route.ts**
- Implements a webhook handler for Polar events
- Includes signature verification for security
- Handles subscription and invoice events
- Updates organization subscription details based on webhook events

#### 5. Database Migration

**drizzle/migrations/0001_polar_integration.sql**
- SQL migration file to add Polar-related fields to the organizations table
- Creates the subscriptions and subscription_invoices tables

**drizzle/migrations/meta/0001_snapshot.json**
- Metadata file for the migration

### Implementation Notes

1. **Subscription Tiers**
   - Basic: 5 projects, 50 locations per project, 5 team members, 10GB storage
   - Professional: 20 projects, 200 locations per project, 15 team members, 50GB storage
   - Enterprise: 100 projects, 1000 locations per project, 50 team members, 250GB storage

2. **Webhook Events**
   - subscription.created: Creates a new subscription record
   - subscription.updated: Updates an existing subscription record
   - invoice.created: Creates a new invoice record
   - invoice.paid: Updates an invoice's status to paid

3. **Environment Variables**
   The following environment variables need to be set for Polar integration:
   - POLAR_ACCESS_TOKEN
   - POLAR_SERVER
   - POLAR_WEBHOOK_SECRET

### Testing

The migration was tested by:
1. Running the SQL migration script
2. Verifying the database schema changes using a custom check-schema script
3. Confirming that the organizations table has the new fields
4. Confirming that the subscriptions and subscription_invoices tables were created

### Next Steps

1. Update environment variables to include Polar-related settings
2. Test the webhook endpoint with Polar events
3. Implement UI components for subscription management and checkout
4. Update the organization creation flow to include Polar customer creation
