# SceneoMatic v2 System Architecture

This document provides a comprehensive overview of the SceneoMatic v2 system architecture, illustrating how different components interact across the application.

## Table of Contents

1. [Architecture Diagram](#architecture-diagram)
2. [Architecture Layers](#architecture-layers)
3. [Key Relationships](#key-relationships)
4. [Authentication Flow](#authentication-flow)
5. [Data Flow](#data-flow)
   - [General Data Flow](#general-data-flow)
   - [Request Flow Diagram](#request-flow-diagram)
   - [Example Flows](#example-flows)
6. [Technology Stack](#technology-stack)
   - [Frontend](#frontend)
   - [Backend](#backend)
   - [Development Tools](#development-tools)
7. [Deployment Architecture](#deployment-architecture)
   - [Deployment Options](#deployment-options)
8. [Data Model](#data-model)
   - [Key Database Tables](#key-database-tables)
   - [Database Features](#database-features)

## Architecture Diagram

```mermaid
graph TB
    %% Client Layer
    subgraph "Client Layer"
        NextJS["Next.js App Router"]
        UI["UI Components"]
        Providers["Context Providers"]
        Hooks["Custom Hooks"]
    end

    %% API Layer
    subgraph "API Layer"
        APIRoutes["API Routes"]
        ServerActions["Server Actions"]
        Middleware["Next.js Middleware"]
    end

    %% Business Logic Layer
    subgraph "Business Logic Layer"
        AuthModule["Auth Module"]
        ProjectModule["Project Module"]
        LocationModule["Location Module"]
        MapModule["Map Module"]
        OrganizationModule["Organization Module"]
        UserModule["User Module"]
        SceneModule["Scene Module"]
        TaskModule["Task Module"]
        DocumentModule["Document Module"]
    end

    %% Data Access Layer
    subgraph "Data Access Layer"
        DrizzleORM["Drizzle ORM"]
        Schemas["Database Schemas"]
        Migrations["Database Migrations"]
    end

    %% External Services
    subgraph "External Services"
        StytchAuth["Stytch Authentication"]
        MapboxAPI["Mapbox API"]
        PostgreSQL["PostgreSQL + PostGIS"]
    end

    %% Connections between layers
    NextJS --> UI
    NextJS --> Providers
    NextJS --> APIRoutes
    NextJS --> ServerActions
    
    UI --> Hooks
    Providers --> Hooks
    
    Hooks --> APIRoutes
    Hooks --> ServerActions
    
    APIRoutes --> AuthModule
    APIRoutes --> ProjectModule
    APIRoutes --> LocationModule
    APIRoutes --> MapModule
    APIRoutes --> OrganizationModule
    
    ServerActions --> AuthModule
    ServerActions --> ProjectModule
    ServerActions --> LocationModule
    ServerActions --> MapModule
    ServerActions --> OrganizationModule
    
    Middleware --> AuthModule
    
    AuthModule --> DrizzleORM
    ProjectModule --> DrizzleORM
    LocationModule --> DrizzleORM
    MapModule --> DrizzleORM
    OrganizationModule --> DrizzleORM
    UserModule --> DrizzleORM
    SceneModule --> DrizzleORM
    TaskModule --> DrizzleORM
    DocumentModule --> DrizzleORM
    
    DrizzleORM --> Schemas
    DrizzleORM --> Migrations
    
    AuthModule --> StytchAuth
    MapModule --> MapboxAPI
    DrizzleORM --> PostgreSQL
    
    %% Module relationships
    ProjectModule <--> LocationModule
    ProjectModule <--> SceneModule
    OrganizationModule <--> ProjectModule
    OrganizationModule <--> UserModule
    LocationModule <--> MapModule
    TaskModule <--> ProjectModule
    DocumentModule <--> LocationModule
```

## Architecture Layers

### 1. Client Layer

- **Next.js App Router**: The application uses Next.js with the App Router for routing and rendering.
- **UI Components**: Reusable UI components built with React and styled with Tailwind CSS.
- **State Management**:
    - **TanStack Query (React Query)**: Used for managing server state, caching, and data fetching.
    - **Zustand**: Used for managing global client state.
    - **Context Providers**: React context providers may be used for more localized state or dependency injection (e.g., theme, map instance).
- **Custom Hooks**: Custom React hooks encapsulate data fetching logic (often using TanStack Query), interact with client state (Zustand), and contain UI-related business logic.

### 2. API Layer

- **API Routes**: Next.js API routes for handling HTTP requests.
- **Server Actions**: Next.js server actions for handling form submissions and server-side mutations.
- **Middleware**: Next.js middleware for authentication and request processing.

#### Key API Endpoints (v1)

- **Projects:**
  - `GET /api/organizations/{organizationId}/projects`: Lists all projects for the specified organization. Requires authentication and organization membership.
  - `POST /api/organizations/{organizationId}/projects`: Creates a new project for the specified organization. Requires authentication, organization membership, and appropriate permissions (RBAC to be added).

### 3. Business Logic Layer

- **Auth Module**: Handles user authentication and authorization using Stytch.
- **Project Module**: Manages projects, including CRUD operations and project-related business logic.
- **Location Module**: Manages locations, including CRUD operations and location-related business logic.
- **Map Module**: Handles map-related functionality, including location filtering and map settings.
- **Organization Module**: Manages organizations, including CRUD operations and organization-related business logic.
- **User Module**: Manages users, including CRUD operations and user-related business logic.
- **Scene Module**: Manages scenes, which are related to projects and locations.
- **Task Module**: Manages tasks, which can be associated with projects, locations, or scenes.
- **Document Module**: Manages documents, which can be associated with locations or projects.

### 4. Data Access Layer

- **Drizzle ORM**: Object-Relational Mapping library for database access.
- **Database Schemas**: Defines the structure of the database tables and relationships.
- **Database Migrations**: Handles database schema changes and versioning.

### 5. External Services

- **Stytch Authentication**: External service for user authentication.
- **Mapbox API**: External service for maps and geospatial functionality.
- **PostgreSQL + PostGIS**: Database for storing application data with geospatial capabilities.

## Key Relationships

- **Organizations** contain **Projects** and **Users** (through organization roles)
- **Projects** contain **Locations** and **Scenes**
- **Locations** have geospatial data that is displayed on **Maps**
- **Users** belong to **Organizations** and can be assigned to **Projects**
- **Tasks** can be associated with **Projects**, **Locations**, or **Scenes**
- **Documents** can be associated with **Locations** or **Projects**

## Authentication Flow

1. User enters email on login page
2. System sends magic link via Stytch
3. User clicks magic link and is redirected to callback URL
4. System verifies token with Stytch and creates session
5. User is redirected to dashboard

## Data Flow

### General Data Flow

1. UI components use custom hooks to fetch data
2. Hooks call API routes or server actions
3. API routes and server actions use business logic modules
4. Business logic modules use Drizzle ORM to access the database
5. Data is returned to the UI components for rendering

### Request Flow Diagram

The following diagram illustrates how a typical request flows through the system:

```mermaid
sequenceDiagram
    participant User
    participant UI as UI Component
    participant Hook as Custom Hook
    participant API as API Route/Server Action
    participant BL as Business Logic Module
    participant ORM as Drizzle ORM
    participant DB as PostgreSQL Database
    participant External as External Service

    User->>UI: Interact (click, submit)
    UI->>Hook: Call hook function
    
    alt API Route
        Hook->>API: Fetch request
        API->>BL: Call business logic
    else Server Action
        Hook->>API: Form submission
        API->>BL: Call business logic
    end
    
    BL->>ORM: Database query
    ORM->>DB: SQL query
    DB->>ORM: Query results
    ORM->>BL: Processed results
    
    alt External Service Required
        BL->>External: API request
        External->>BL: API response
    end
    
    BL->>API: Return data
    API->>Hook: Response data
    Hook->>UI: Updated state
    UI->>User: Render updated UI
```

### Example Flows

#### User Authentication Flow

```mermaid
sequenceDiagram
    participant User
    participant Login as Login Page
    participant Auth as Auth API
    participant Stytch as Stytch Service
    participant DB as Database

    User->>Login: Enter email
    Login->>Auth: Submit login form
    Auth->>Stytch: Request magic link
    Stytch-->>User: Send email with magic link
    User->>Stytch: Click magic link
    Stytch->>Auth: Callback with token
    Auth->>Stytch: Verify token
    Stytch->>Auth: Token valid
    Auth->>DB: Create/update user session
    Auth->>User: Redirect to dashboard
```

#### Location Creation Flow

```mermaid
sequenceDiagram
    participant User
    participant UI as Location Form
    participant Action as Server Action
    participant Location as Location Module
    participant Map as Map Module
    participant DB as Database
    participant Mapbox as Mapbox API

    User->>UI: Fill location details
    User->>UI: Set location on map
    UI->>Mapbox: Get coordinates
    Mapbox->>UI: Return coordinates
    User->>UI: Submit form
    UI->>Action: Submit data
    Action->>Location: Create location
    Location->>Map: Process geospatial data
    Map->>DB: Store location with coordinates
    DB->>Location: Return created location
    Location->>Action: Return result
    Action->>UI: Show success message
    UI->>User: Display updated location list
```

## Technology Stack

The application is built using a modern technology stack:

### Frontend
- **Next.js**: React framework with App Router for server components and routing
- **React**: UI library for building component-based interfaces
- **TypeScript**: Typed JavaScript for improved developer experience and code quality
- **Tailwind CSS**: Utility-first CSS framework for styling
- **Shadcn UI**: Component library built on Radix UI primitives
- **TanStack Query (React Query)**: Server state management and data fetching
- **Zustand**: Client state management
- **Mapbox GL JS**: JavaScript library for interactive maps
- **Lucide React**: Icon library

### Backend
- **Next.js API Routes**: Backend API endpoints
- **Next.js Server Actions**: Server-side form handling and mutations
- **Drizzle ORM**: TypeScript ORM for database access
- **PostgreSQL**: Relational database
- **PostGIS**: Spatial database extension for PostgreSQL
- **Stytch**: Authentication service for passwordless login

### Development Tools
- **pnpm**: Fast, disk space efficient package manager
- **ESLint**: JavaScript/TypeScript linter
- **TypeScript**: Static type checking
- **Zod**: Schema validation library
- **Drizzle Kit**: Migration and schema management tools

## Deployment Architecture

The application is designed to be deployed as a Next.js application with:

- Frontend and API routes served by Next.js
- PostgreSQL database with PostGIS extension for geospatial data
- Integration with Stytch for authentication
- Integration with Mapbox for maps and geospatial visualization

### Deployment Options

1. **Vercel**: Optimized for Next.js applications
   - Automatic deployments from Git
   - Edge functions for API routes
   - Serverless functions for server actions
   - Integration with Vercel Postgres or external PostgreSQL database

2. **Self-hosted**:
   - Docker containers for application and database
   - Kubernetes for orchestration
   - PostgreSQL with PostGIS as a separate service
   - Nginx or similar for SSL termination and static file serving

## Data Model

The following diagram illustrates the database schema and relationships between entities:

```mermaid
erDiagram
    ORGANIZATION {
        uuid id PK
        varchar name
        varchar slug
        text description
        varchar logo
        jsonb metadata
        boolean isActive
        timestamp createdAt
        timestamp updatedAt
        timestamp deletedAt
    }
    
    USER {
        uuid id PK
        varchar email
        varchar name
        varchar avatar
        varchar stytchUserId
        jsonb metadata
        boolean isActive
        timestamp createdAt
        timestamp updatedAt
        timestamp deletedAt
    }
    
    ORGANIZATION_USER_ROLE {
        uuid id PK
        uuid organizationId FK
        uuid userId FK
        varchar role
        timestamp createdAt
        timestamp updatedAt
    }
    
    PROJECT {
        uuid id PK
        uuid organizationId FK
        varchar name
        text description
        varchar status
        date startDate
        date endDate
        boolean isArchived
        jsonb metadata
        timestamp createdAt
        timestamp updatedAt
        timestamp deletedAt
    }
    
    PROJECT_USER_ROLE {
        uuid id PK
        uuid projectId FK
        uuid userId FK
        varchar role
        timestamp createdAt
        timestamp updatedAt
    }
    
    LOCATION {
        uuid id PK
        uuid organizationId FK
        uuid projectId FK
        uuid createdBy FK
        uuid approvedBy FK
        varchar name
        text description
        varchar type
        varchar status
        point coordinates
        jsonb address
        jsonb metadata
        boolean isActive
        timestamp approvedAt
        timestamp createdAt
        timestamp updatedAt
        timestamp deletedAt
    }
    
    LOCATION_MEDIA {
        uuid id PK
        uuid locationId FK
        uuid uploadedBy FK
        varchar type
        varchar url
        varchar filename
        int filesize
        jsonb metadata
        timestamp createdAt
        timestamp updatedAt
    }
    
    SCENE {
        uuid id PK
        uuid projectId FK
        uuid locationId FK
        uuid createdBy FK
        varchar name
        text description
        varchar status
        date shootDate
        jsonb metadata
        timestamp createdAt
        timestamp updatedAt
        timestamp deletedAt
    }
    
    DOCUMENT {
        uuid id PK
        uuid locationId FK
        uuid projectId FK
        uuid uploadedBy FK
        varchar name
        varchar type
        varchar url
        varchar filename
        int filesize
        jsonb metadata
        timestamp createdAt
        timestamp updatedAt
        timestamp deletedAt
    }
    
    TASK {
        uuid id PK
        uuid projectId FK
        uuid locationId FK
        uuid sceneId FK
        uuid assignedTo FK
        uuid createdBy FK
        varchar title
        text description
        varchar status
        varchar priority
        date dueDate
        jsonb metadata
        timestamp completedAt
        timestamp createdAt
        timestamp updatedAt
        timestamp deletedAt
    }
    
    ORGANIZATION ||--o{ PROJECT : "has"
    ORGANIZATION ||--o{ LOCATION : "has"
    ORGANIZATION ||--o{ ORGANIZATION_USER_ROLE : "has"
    USER ||--o{ ORGANIZATION_USER_ROLE : "has"
    USER ||--o{ PROJECT_USER_ROLE : "has"
    USER ||--o{ LOCATION : "created by"
    USER ||--o{ LOCATION : "approved by"
    USER ||--o{ LOCATION_MEDIA : "uploaded by"
    USER ||--o{ DOCUMENT : "uploaded by"
    USER ||--o{ TASK : "assigned to"
    USER ||--o{ TASK : "created by"
    PROJECT ||--o{ LOCATION : "has"
    PROJECT ||--o{ SCENE : "has"
    PROJECT ||--o{ DOCUMENT : "has"
    PROJECT ||--o{ TASK : "has"
    PROJECT ||--o{ PROJECT_USER_ROLE : "has"
    LOCATION ||--o{ LOCATION_MEDIA : "has"
    LOCATION ||--o{ DOCUMENT : "has"
    LOCATION ||--o{ TASK : "has"
    LOCATION ||--o{ SCENE : "has"
    SCENE ||--o{ TASK : "has"
```

### Key Database Tables

1. **Organizations**: Represents companies or teams using the system
2. **Users**: System users who can belong to organizations
3. **Projects**: Groups of locations and scenes for specific production needs
4. **Locations**: Physical places with geospatial coordinates
5. **Scenes**: Specific filming or production events at locations
6. **Tasks**: Work items associated with projects, locations, or scenes
7. **Documents**: Files associated with locations or projects
8. **Media**: Images or videos associated with locations

### Database Features

- **Soft Deletion**: Most entities use a `deletedAt` timestamp for soft deletion
- **Timestamps**: All entities track creation and update times
- **Metadata**: Flexible JSONB fields for extensible data storage
- **Geospatial**: PostGIS integration for location coordinates and spatial queries
- **Relationships**: Well-defined foreign key relationships between entities
