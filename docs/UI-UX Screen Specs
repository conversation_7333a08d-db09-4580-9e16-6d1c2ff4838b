# Scene-o-matic SaaS UI/UX Screens Specification

This document outlines all the screens needed for the Scene-o-matic SaaS platform, organized by section based on the established project structure. Each screen includes key information for UI/UX development, with a strict focus on the multi-tenant model where users only ever see data from their own organization.

## Table of Contents

- [Marketing Screens](#marketing-screens)
- [Authentication Screens](#authentication-screens)
- [Organization Selection Screens](#organization-selection-screens)
- [Organization Dashboard Screens](#organization-dashboard-screens)
- [Project Screens](#project-screens)
- [Location Screens](#location-screens)
- [Map Screens](#map-screens)
- [Document Screens](#document-screens)
- [Task Screens](#task-screens)
- [Settings Screens](#settings-screens)
- [Billing Screens](#billing-screens)
- [User Screens](#user-screens)
- [Mobile-Specific Screens](#mobile-specific-screens)
- [Modals & Dialogs](#modals--dialogs)

## Marketing Screens

### 1. Landing Page
- **Path**: `/`
- **Purpose**: Showcase product features and value proposition
- **Key Elements**:
  - Hero section with product imagery
  - Feature highlights with location management examples
  - Customer testimonials
  - Pricing section with feature comparisons
  - Call-to-action for sign-up
- **User Type**: Prospective customers (unauthenticated)
- **Responsive Considerations**: Full design for desktop, tablet, mobile

### 2. Features Page
- **Path**: `/features`
- **Purpose**: Detailed breakdown of platform features
- **Key Elements**:
  - Interactive map demo
  - Feature categories (location management, collaboration, etc.)
  - Visual demonstrations of key workflows
  - Integration capabilities
- **User Type**: Prospective customers (unauthenticated)

### 3. Pricing Page
- **Path**: `/pricing`
- **Purpose**: Showcase subscription tiers
- **Key Elements**:
  - Tier comparison table (Basic, Professional, Enterprise)
  - Feature availability by tier
  - Pricing calculator for add-ons
  - FAQ section
- **User Type**: Prospective customers (unauthenticated)

### 4. About Page
- **Path**: `/about`
- **Purpose**: Company information and mission
- **Key Elements**:
  - Company story
  - Team information
  - Technology partners
  - Industry focus
- **User Type**: Prospective customers (unauthenticated)

### 5. Contact Page
- **Path**: `/contact`
- **Purpose**: Provide contact methods and support options
- **Key Elements**:
  - Contact form
  - Email/phone information
  - Office locations (if applicable)
  - Support hours
- **User Type**: Prospective and current customers (unauthenticated)

## Authentication Screens

### 6. Login Page
- **Path**: `/auth/login`
- **Purpose**: User authentication (primarily via Stytch passwordless or SSO)
- **Key Elements**:
  - Email input (for magic link/OTP or discovery)
  - SSO login options (if configured)
  - Link to registration/forgot password
- **User Type**: Unauthenticated users
- **States**: Default, Loading, Error

### 7. Registration Page
- **Path**: `/auth/register`
- **Purpose**: New user signup
- **Key Elements**:
  - Registration form (email, password, name)
  - Terms and conditions acceptance
  - Email verification message
- **User Type**: New users (unauthenticated)
- **States**: Default, Loading, Success, Error

### 8. Organization Creation Page
- **Path**: `/auth/organization/create`
- **Purpose**: Create new organization after signup
- **Key Elements**:
  - Organization name
  - Organization details
  - Industry selection
  - Initial team size
- **User Type**: New organization admins (authenticated but no organization)
- **States**: Default, Loading, Success, Error
- **Access**: Only for authenticated users without an organization

### 9. Forgot Password Page
- **Path**: `/auth/forgot-password`
- **Purpose**: Password recovery
- **Key Elements**:
  - Email input
  - Submit button
  - Success message
- **User Type**: Users who forgot passwords (unauthenticated)
- **States**: Default, Loading, Success, Error

### 10. Reset Password Page
- **Path**: `/auth/reset-password`
- **Purpose**: Set new password via token
- **Key Elements**:
  - New password input
  - Confirm password input
  - Password requirements
- **User Type**: Users resetting passwords (unauthenticated)
- **States**: Default, Loading, Success, Error, Invalid Token

## Organization Selection Screens

### 11. Organization Selection Page
- **Path**: `/auth/organization`
- **Purpose**: Select between multiple organizations (only shown if user is a member of multiple organizations)
- **Key Elements**:
  - List of organizations user belongs to
  - Organization roles
  - Create new organization option
- **User Type**: Users with multiple organization memberships
- **States**: Default, Loading
- **Access**: Only for authenticated users with multiple organizations

## Organization Dashboard Screens

### 12. Organization Dashboard
- **Path**: `/organizations/[organizationSlug]`
- **Purpose**: Overview of key activities and information within organization
- **Key Elements**:
  - Organization activity summary
  - Recent locations
  - Recent projects
  - Upcoming tasks
  - Quick action buttons
- **User Type**: All organization members
- **Responsive Considerations**: Dashboard layout changes for tablet/mobile
- **User Role Variations**: Different metrics shown based on role
- **Access Control**: Only shows data from current organization

### 13. Organization Activity Feed
- **Path**: `/organizations/[organizationSlug]/activity`
- **Purpose**: Chronological list of organization activities
- **Key Elements**:
  - Filterable activity list
  - User actions
  - Location updates
  - Project changes
  - Document updates
- **User Type**: All organization members
- **States**: Default, Loading, Empty, Filtered
- **Access Control**: Only shows activities from current organization

### 14. Organization Settings
- **Path**: `/organizations/[organizationSlug]/settings`
- **Purpose**: Manage organization settings
- **Key Elements**:
  - General settings
  - Branding settings
  - Default preferences
  - Delete organization option
- **User Type**: Organization admins
- **User Role Restrictions**: Admin only
- **Access Control**: Only affects current organization

### 15. Organization Members
- **Path**: `/organizations/[organizationSlug]/settings/members`
- **Purpose**: Manage organization members
- **Key Elements**:
  - Member list with roles
  - Invite new members
  - Edit member permissions
  - Remove members
  - Pending invitations
- **User Type**: Organization admins and managers
- **User Role Restrictions**: Admin/manager only
- **States**: Default, Loading, Empty
- **Access Control**: Only shows members from current organization

### 16. Organization Roles
- **Path**: `/organizations/[organizationSlug]/settings/roles`
- **Purpose**: Configure role permissions
- **Key Elements**:
  - Role list
  - Permission matrix
  - Custom role creation
- **User Type**: Organization admins
- **User Role Restrictions**: Admin only
- **Access Control**: Only affects current organization

### 17. Organization SSO Settings
- **Path**: `/organizations/[organizationSlug]/settings/sso`
- **Purpose**: Configure single sign-on
- **Key Elements**:
  - SSO provider setup
  - SAML configuration
  - OAuth settings
  - Identity provider linking
- **User Type**: Organization admins
- **User Role Restrictions**: Admin only
- **Plan Restrictions**: Enterprise plan only
- **Access Control**: Only affects current organization

## Project Screens

### 18. Organization Projects List
- **Path**: `/organizations/[organizationSlug]/projects`
- **Purpose**: View all projects within the organization
- **Key Elements**:
  - Filterable project grid/list
  - Project status indicators
  - Create new project button
  - Search functionality
  - Sort options
- **User Type**: All organization members
- **States**: Default, Loading, Empty, Filtered
- **Access Control**: Only shows projects from current organization

### 19. Project Creation
- **Path**: `/organizations/[organizationSlug]/projects/new`
- **Purpose**: Create new project in organization
- **Key Elements**:
  - Project details form
  - Production information
  - Date range picker
  - Team assignment
  - Budget allocation
- **User Type**: Organization admins and managers
- **User Role Restrictions**: Admin/manager only
- **States**: Default, Loading, Success, Error
- **Access Control**: Creates within current organization only

### 20. Project Dashboard
- **Path**: `/organizations/[organizationSlug]/projects/[projectId]`
- **Purpose**: Project-specific overview
- **Key Elements**:
  - Project status summary
  - Location counts
  - Team members
  - Recent activity
  - Timeline visualization
  - Quick action buttons
- **User Type**: Project team members
- **User Role Variations**: Different actions based on role
- **Access Control**: Only shows data from current organization's project

### 21. Project Edit
- **Path**: `/organizations/[organizationSlug]/projects/[projectId]/edit`
- **Purpose**: Modify project details
- **Key Elements**:
  - Edit project form
  - Update production information
  - Modify date ranges
  - Budget adjustments
- **User Type**: Project managers
- **User Role Restrictions**: Admin/manager only
- **States**: Default, Loading, Success, Error
- **Access Control**: Only modifies current organization's project

### 22. Project Team
- **Path**: `/organizations/[organizationSlug]/projects/[projectId]/team`
- **Purpose**: Manage project team members
- **Key Elements**:
  - Team member list
  - Role assignments
  - Add/remove members
  - Activity per member
- **User Type**: Project managers
- **User Role Restrictions**: Admin/manager only
- **Access Control**: Only shows members from current organization

### 23. Project Settings
- **Path**: `/organizations/[organizationSlug]/projects/[projectId]/settings`
- **Purpose**: Configure project settings
- **Key Elements**:
  - Project preferences
  - Notification settings
  - Archive/delete options
  - Export functionality
- **User Type**: Project managers
- **User Role Restrictions**: Admin/manager only
- **Access Control**: Only affects current organization's project

## Location Screens

### 24. Organization Locations List
- **Path**: `/organizations/[organizationSlug]/locations`
- **Purpose**: View all locations within the organization
- **Key Elements**:
  - Filterable location grid/list
  - Location status indicators
  - Location preview thumbnails
  - Advanced filtering options
  - Bulk actions
- **User Type**: All organization members
- **States**: Default, Loading, Empty, Filtered
- **Access Control**: Only shows locations from current organization

### 25. Project Locations List
- **Path**: `/organizations/[organizationSlug]/projects/[projectId]/locations`
- **Purpose**: View locations for specific project
- **Key Elements**:
  - Project-specific location list
  - Status indicators
  - Add new location button
  - Filter and sort options
  - View toggle (grid/list/table)
- **User Type**: Project team members
- **States**: Default, Loading, Empty, Filtered
- **Access Control**: Only shows locations from current organization's project

### 26. Location Creation
- **Path**: `/organizations/[organizationSlug]/projects/[projectId]/locations/new`
- **Purpose**: Add new location to project
- **Key Elements**:
  - Multi-step location form
  - Map location picker
  - Photo/video upload
  - Metadata fields
  - Contact information
  - Features and tags
  - Pricing information
- **User Type**: Location scouts and managers
- **User Role Restrictions**: Admin/manager/scout
- **States**: Default, Loading, Success, Error
- **Access Control**: Creates within current organization's project only

### 27. Location Details
- **Path**: `/organizations/[organizationSlug]/projects/[projectId]/locations/[locationId]`
- **Purpose**: View comprehensive location information
- **Key Elements**:
  - Photo gallery
  - Location details
  - Map view
  - Features list
  - Documents section
  - Comments/notes
  - Status controls
  - Weather info
  - Lighting conditions
- **User Type**: All project team members
- **User Role Variations**: Different actions based on role
- **Access Control**: Only shows location from current organization's project

### 28. Location Edit
- **Path**: `/organizations/[organizationSlug]/projects/[projectId]/locations/[locationId]/edit`
- **Purpose**: Modify location details
- **Key Elements**:
  - Edit location form
  - Update map location
  - Manage photos/videos
  - Update metadata
- **User Type**: Location scouts and managers
- **User Role Restrictions**: Admin/manager/scout
- **States**: Default, Loading, Success, Error
- **Access Control**: Only modifies location from current organization's project

## Map Screens

### 29. Organization Map View
- **Path**: `/organizations/[organizationSlug]/map`
- **Purpose**: Interactive map of all locations within organization
- **Key Elements**:
  - Full-screen map
  - Location markers
  - Custom filtering
  - Layer controls
  - Location clusters
  - Search functionality
  - Map style options
- **User Type**: All organization members
- **States**: Default, Loading, Empty, Filtered
- **Access Control**: Only shows locations from current organization

### 30. Project Map View
- **Path**: `/organizations/[organizationSlug]/projects/[projectId]/map`
- **Purpose**: Project-specific map view
- **Key Elements**:
  - Project locations on map
  - Status-based markers
  - Custom project layers
  - Distance calculations
  - Route planning
  - Save view functionality
- **User Type**: Project team members
- **States**: Default, Loading, Empty
- **Access Control**: Only shows locations from current organization's project

### 31. Custom Map Creation
- **Path**: `/organizations/[organizationSlug]/projects/[projectId]/map/create`
- **Purpose**: Create custom map views
- **Key Elements**:
  - Map configuration
  - Layer selection
  - Custom markers
  - Drawing tools
  - Label options
  - Sharing settings
- **User Type**: Project managers
- **User Role Restrictions**: Admin/manager only
- **States**: Default, Loading, Success, Error
- **Access Control**: Only creates within current organization's project

## Document Screens

### 32. Organization Documents List
- **Path**: `/organizations/[organizationSlug]/documents`
- **Purpose**: View all documents within the organization
- **Key Elements**:
  - Filterable document list
  - Document type indicators
  - Preview thumbnails
  - Search functionality
  - Sort options
- **User Type**: All organization members
- **States**: Default, Loading, Empty, Filtered
- **Access Control**: Only shows documents from current organization

### 33. Project Documents List
- **Path**: `/organizations/[organizationSlug]/projects/[projectId]/documents`
- **Purpose**: View documents for specific project
- **Key Elements**:
  - Project-specific document list
  - Status indicators
  - Upload new document button
  - Filter and sort options
  - Document categories
- **User Type**: Project team members
- **States**: Default, Loading, Empty, Filtered
- **Access Control**: Only shows documents from current organization's project

### 34. Document Upload
- **Path**: `/organizations/[organizationSlug]/projects/[projectId]/documents/new`
- **Purpose**: Add new document to project
- **Key Elements**:
  - File upload interface
  - Document metadata form
  - Document type selection
  - Location association
  - Permission settings
- **User Type**: Project team members
- **States**: Default, Loading, Success, Error
- **Access Control**: Creates within current organization's project only

### 35. Document Details
- **Path**: `/organizations/[organizationSlug]/projects/[projectId]/documents/[documentId]`
- **Purpose**: View document information and preview
- **Key Elements**:
  - Document preview
  - Metadata display
  - Version history
  - Comments/notes
  - Related locations
  - Download options
  - Sharing controls
- **User Type**: All project team members
- **User Role Variations**: Different actions based on role
- **Access Control**: Only shows document from current organization's project

### 36. Document Edit
- **Path**: `/organizations/[organizationSlug]/projects/[projectId]/documents/[documentId]/edit`
- **Purpose**: Modify document details
- **Key Elements**:
  - Edit document metadata
  - Update permissions
  - Replace document version
  - Change associations
- **User Type**: Document owners and managers
- **User Role Restrictions**: Admin/manager or document creator
- **States**: Default, Loading, Success, Error
- **Access Control**: Only modifies document from current organization's project

## Task Screens

### 37. Organization Tasks List
- **Path**: `/organizations/[organizationSlug]/tasks`
- **Purpose**: View all tasks within the organization
- **Key Elements**:
  - Filterable task list
  - Task status indicators
  - Priority indicators
  - Assignment information
  - Due dates
  - Quick actions
- **User Type**: All organization members
- **States**: Default, Loading, Empty, Filtered
- **Access Control**: Only shows tasks from current organization

### 38. Project Tasks List
- **Path**: `/organizations/[organizationSlug]/projects/[projectId]/tasks`
- **Purpose**: View tasks for specific project
- **Key Elements**:
  - Project-specific task list
  - Status filters
  - Create new task button
  - Board/list view toggle
  - Calendar view option
- **User Type**: Project team members
- **States**: Default, Loading, Empty, Filtered
- **Access Control**: Only shows tasks from current organization's project

### 39. Task Creation
- **Path**: `/organizations/[organizationSlug]/projects/[projectId]/tasks/new`
- **Purpose**: Create new project task
- **Key Elements**:
  - Task details form
  - Priority selection
  - Due date picker
  - Assignee selection
  - Location association
  - Task type selection
- **User Type**: Project managers
- **User Role Restrictions**: Admin/manager
- **States**: Default, Loading, Success, Error
- **Access Control**: Creates within current organization's project only

### 40. Task Details
- **Path**: `/organizations/[organizationSlug]/projects/[projectId]/tasks/[taskId]`
- **Purpose**: View comprehensive task information
- **Key Elements**:
  - Task details
  - Status controls
  - Comments thread
  - Activity log
  - Related locations/documents
  - Subtask list
- **User Type**: All project team members
- **User Role Variations**: Different actions based on role
- **Access Control**: Only shows task from current organization's project

### 41. Task Edit
- **Path**: `/organizations/[organizationSlug]/projects/[projectId]/tasks/[taskId]/edit`
- **Purpose**: Modify task details
- **Key Elements**:
  - Edit task form
  - Update status
  - Reassign task
  - Modify due date/priority
- **User Type**: Task creators and managers
- **User Role Restrictions**: Admin/manager or task creator
- **States**: Default, Loading, Success, Error
- **Access Control**: Only modifies task from current organization's project

## Settings Screens

### 42. Organization User Settings
- **Path**: `/organizations/[organizationSlug]/settings/profile`
- **Purpose**: Manage personal profile within organization context
- **Key Elements**:
  - Profile details form
  - Avatar upload
  - Name and contact info
  - Professional details
  - Bio/description
- **User Type**: All organization members
- **States**: Default, Loading, Success, Error
- **Access Control**: Only modifies current user's profile

### 43. Account Settings
- **Path**: `/organizations/[organizationSlug]/settings/account`
- **Purpose**: Manage account security
- **Key Elements**:
  - Password change
  - Two-factor authentication
  - Login history
  - Connected devices
  - Delete account option
- **User Type**: All organization members
- **States**: Default, Loading, Success, Error
- **Access Control**: Only affects current user's account

### 44. Notification Settings
- **Path**: `/organizations/[organizationSlug]/settings/notifications`
- **Purpose**: Configure notification preferences within organization
- **Key Elements**:
  - Email notification toggles
  - In-app notification toggles
  - Notification categories
  - Frequency settings
- **User Type**: All organization members
- **States**: Default, Loading, Success
- **Access Control**: Only affects current user's notifications for this organization

### 45. API Keys
- **Path**: `/organizations/[organizationSlug]/settings/api-keys`
- **Purpose**: Manage API access for the organization
- **Key Elements**:
  - API key list
  - Generate new key
  - Revoke keys
  - Usage statistics
  - Permission scopes
- **User Type**: Organization developers (admin role)
- **User Role Restrictions**: Admin only
- **Plan Restrictions**: Professional/Enterprise plans only
- **Access Control**: Keys only grant access to current organization's data

## Billing Screens

### 46. Organization Billing Overview
- **Path**: `/organizations/[organizationSlug]/billing`
- **Purpose**: Overview of organization billing information
- **Key Elements**:
  - Current plan
  - Usage statistics
  - Billing cycle information
  - Payment method summary
  - Recent invoices
- **User Type**: Organization admins
- **User Role Restrictions**: Admin only
- **Access Control**: Only shows billing for current organization

### 47. Subscription Plans
- **Path**: `/organizations/[organizationSlug]/billing/plans`
- **Purpose**: View and change organization subscription plans
- **Key Elements**:
  - Plan comparison
  - Current plan highlight
  - Upgrade/downgrade options
  - Feature limitations
  - Add-on options
- **User Type**: Organization admins
- **User Role Restrictions**: Admin only
- **States**: Default, Loading, Success, Error
- **Access Control**: Only affects current organization's subscription

### 48. Payment Methods
- **Path**: `/organizations/[organizationSlug]/billing/payment`
- **Purpose**: Manage organization payment information
- **Key Elements**:
  - Payment method list
  - Add new payment method
  - Set default method
  - Billing address
  - Tax information
- **User Type**: Organization admins
- **User Role Restrictions**: Admin only
- **States**: Default, Loading, Success, Error
- **Access Control**: Only affects current organization's payment methods

### 49. Invoices
- **Path**: `/organizations/[organizationSlug]/billing/invoices`
- **Purpose**: View organization billing history
- **Key Elements**:
  - Invoice list
  - Download/print options
  - Payment status
  - Detailed breakdown
  - Search and filter
- **User Type**: Organization admins
- **User Role Restrictions**: Admin only
- **States**: Default, Loading, Empty
- **Access Control**: Only shows invoices for current organization

## User Screens

### 50. Organization Members Management
- **Path**: `/organizations/[organizationSlug]/settings/members`
- **Purpose**: Manage organization users
- **Key Elements**:
  - User list with roles
  - Add/remove users
  - Edit permissions
  - Bulk actions
  - Filter and search
- **User Type**: Organization admins
- **User Role Restrictions**: Admin only
- **States**: Default, Loading, Empty, Filtered
- **Access Control**: Only shows/affects users within current organization

### 51. Organization Member Profile
- **Path**: `/organizations/[organizationSlug]/members/[userId]`
- **Purpose**: View organization member information
- **Key Elements**:
  - Profile information
  - Activity history
  - Assigned tasks
  - Projects involvement
  - Contact options
- **User Type**: All organization members
- **States**: Default, Loading, Not Found
- **Access Control**: Only shows member from current organization

## Mobile-Specific Screens

### 52. Mobile Organization Dashboard
- **Path**: `/organizations/[organizationSlug]` (mobile optimized)
- **Purpose**: Optimized dashboard for mobile within organization context
- **Key Elements**:
  - Simplified activity feed
  - Quick access to common functions
  - Touch-friendly controls
  - Responsive layouts
- **User Type**: All organization members
- **Device Target**: Smartphones and tablets
- **Access Control**: Only shows data from current organization

### 53. Mobile Location Capture
- **Path**: `/organizations/[organizationSlug]/projects/[projectId]/locations/capture`
- **Purpose**: Streamlined location scouting on mobile
- **Key Elements**:
  - Quick photo/video capture
  - GPS auto-tagging
  - Basic metadata entry
  - Offline capability
  - Upload queue
- **User Type**: Location scouts
- **User Role Restrictions**: Admin/manager/scout
- **Device Target**: Smartphones and tablets
- **States**: Default, Capturing, Uploading, Offline
- **Access Control**: Creates within current organization's project only

### 54. Mobile Organization Map View
- **Path**: `/organizations/[organizationSlug]/map` (mobile optimized)
- **Purpose**: Mobile-optimized maps for organization locations
- **Key Elements**:
  - Touch-friendly controls
  - Current location integration
  - Simplified markers
  - Direction features
  - Reduced data usage option
- **User Type**: All organization members
- **Device Target**: Smartphones and tablets
- **States**: Default, Loading, Navigating
- **Access Control**: Only shows locations from current organization

## Modals & Dialogs

### 55. Organization Switcher
- **Purpose**: Quick organization switching
- **Key Elements**:
  - Organization list
  - Current role display
  - Create new organization option
- **Trigger**: Header organization menu
- **User Type**: Users with multiple organizations
- **Access Control**: Only shows organizations user is a member of

### 56. Invite Organization Members
- **Purpose**: Send invitations to organization
- **Key Elements**:
  - Email input (single or bulk)
  - Role selection
  - Custom message
  - Copy invite link
- **Trigger**: "Invite" buttons throughout app
- **User Type**: Organization admins and managers
- **User Role Restrictions**: Admin/manager only
- **Access Control**: Only invites to current organization

### 57. Location Quick View
- **Purpose**: Preview location without full page load
- **Key Elements**:
  - Key location details
  - Primary image
  - Status indicator
  - Quick action buttons
  - Map thumbnail
- **Trigger**: Location cards throughout app
- **User Type**: All organization members
- **Access Control**: Only shows locations from current organization

### 58. Confirmation Dialogs
- **Purpose**: Confirm destructive actions
- **Key Elements**:
  - Clear warning message
  - Confirmation input (when appropriate)
  - Cancel/confirm buttons
- **Trigger**: Delete, archive, or other destructive actions
- **User Type**: All organization members
- **Access Control**: Only affects data user has permission to modify

### 59. Filter/Sort Panel
- **Purpose**: Advanced filtering options
- **Key Elements**:
  - Multiple filter criteria
  - Save filter preset
  - Clear all filters
  - Apply/cancel buttons
- **Trigger**: Filter buttons on list views
- **User Type**: All organization members
- **Access Control**: Filters only apply to data user has permission to access

## Additional Considerations

### Strict Multi-Tenant Security
- All API routes operating within an organization context must include the `organizationId` parameter (typically derived from the `organizationSlug` in the URL or user session).
- Server-side validation must verify:
  1. User is authenticated
  2. User is a member of the requested organization
  3. User has appropriate role permissions for the action
  4. Data being accessed belongs to the organization

### Organization Context
- After login, users are always in an organization context
- Organization slug is included in all URLs to maintain proper context
- If user belongs to multiple organizations, they must explicitly switch between them

### Path Structure Pattern
All protected routes follow this pattern:
```
/organizations/[organizationSlug]/...
```

This ensures:
1. Clear organization context in URLs
2. Proper API routing with organization information
3. Consistent URL structure throughout the application
4. Straightforward organization-based access control

### Responsive Design
- All screens should have responsive variants for:
  - Desktop (1200px+)
  - Laptop (992-1199px)
  - Tablet (768-991px)
  - Mobile (320-767px)

### Accessibility Requirements
- All screens must meet WCAG 2.1 AA standards
- Keyboard navigation support
- Screen reader compatibility
- Sufficient color contrast
- Focus indicators

### Loading States
- All data-dependent screens need loading states
- Skeleton loaders for content areas
- Loading indicators for actions
- Error states with retry options

### Empty States
- All list views need empty states
- Helpful guidance for users
- Clear calls-to-action
- Visual illustrations

### User Role Variations
- All screens should consider permission-based UI differences
- Hide actions not available to current user
- Show appropriate guidance based on permissions
- Consistent handling of restricted access
