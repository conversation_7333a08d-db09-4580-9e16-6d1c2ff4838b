# Notification System

## Overview

The Scene-o-matic Notification System provides a comprehensive solution for managing in-app notifications across the platform. It allows for creating, retrieving, and managing notifications for users within organizations. The system supports different notification types, priorities, and statuses, as well as user preferences for receiving notifications.

## Database Schema

### Tables

#### `notifications`

Stores individual notification records.

| Column | Type | Description |
|--------|------|-------------|
| `id` | UUID | Primary key |
| `type` | Enum | Type of notification (see Notification Types) |
| `title` | VARCHAR(255) | Notification title |
| `message` | TEXT | Notification content |
| `status` | Enum | Status of notification (unread, read, archived) |
| `priority` | Enum | Priority level (low, medium, high, urgent) |
| `recipientId` | UUID | Foreign key to users table |
| `organizationId` | UUID | Foreign key to organizations table |
| `projectId` | UUID | Optional foreign key to projects table |
| `locationId` | UUID | Optional foreign key to locations table |
| `documentId` | UUID | Optional foreign key to documents table |
| `taskId` | UUID | Optional foreign key to tasks table |
| `actorId` | UUID | Optional foreign key to users table (user who triggered the notification) |
| `metadata` | JSONB | Additional data related to the notification |
| `isRead` | BOOLEAN | Whether the notification has been read |
| `readAt` | TIMESTAMP | When the notification was read |
| `expiresAt` | TIMESTAMP | When the notification expires (optional) |
| `createdAt` | TIMESTAMP | When the notification was created |
| `updatedAt` | TIMESTAMP | When the notification was last updated |

#### `notification_preferences`

Stores user preferences for receiving notifications.

| Column | Type | Description |
|--------|------|-------------|
| `id` | UUID | Primary key |
| `userId` | UUID | Foreign key to users table |
| `organizationId` | UUID | Foreign key to organizations table |
| `notificationType` | Enum | Type of notification (see Notification Types) |
| `inApp` | BOOLEAN | Whether to show in-app notifications |
| `email` | BOOLEAN | Whether to send email notifications |
| `createdAt` | TIMESTAMP | When the preference was created |
| `updatedAt` | TIMESTAMP | When the preference was last updated |

### Enums

#### Notification Types

```typescript
export const NOTIFICATION_TYPES = [
  "project_created",
  "project_updated",
  "project_deleted",
  "location_created",
  "location_updated",
  "location_deleted",
  "location_approved",
  "document_uploaded",
  "document_updated",
  "document_deleted",
  "task_created",
  "task_updated",
  "task_completed",
  "task_assigned",
  "member_added",
  "member_removed",
  "member_role_changed",
  "subscription_created",
  "subscription_updated",
  "subscription_canceled",
  "payment_succeeded",
  "payment_failed",
  "comment_added",
  "system_notification"
] as const;
```

#### Notification Status

```typescript
export const NOTIFICATION_STATUS = ["unread", "read", "archived"] as const;
```

#### Notification Priority

```typescript
export const NOTIFICATION_PRIORITY = ["low", "medium", "high", "urgent"] as const;
```

## API Routes

### GET `/api/notifications/count`

Returns the count of notifications for the current user.

**Response:**
```json
{
  "total": 10,
  "unread": 5
}
```

### GET `/api/notifications`

Returns a paginated list of notifications for the current user.

**Query Parameters:**
- `status`: Filter by status (optional)
- `type`: Filter by notification type (optional)
- `priority`: Filter by priority (optional)
- `limit`: Number of notifications to return (default: 20)
- `offset`: Pagination offset (default: 0)

**Response:**
```json
[
  {
    "id": "uuid-string",
    "type": "system_notification",
    "title": "Notification Title",
    "message": "Notification content",
    "status": "unread",
    "priority": "medium",
    "recipientId": "user-uuid",
    "organizationId": "org-uuid",
    "projectId": null,
    "locationId": null,
    "documentId": null,
    "taskId": null,
    "actorId": null,
    "metadata": {},
    "isRead": false,
    "readAt": null,
    "expiresAt": null,
    "createdAt": "2025-05-01T17:03:38.000Z",
    "updatedAt": "2025-05-01T17:03:38.000Z",
    "recipient": {
      "id": "user-uuid",
      "name": "User Name",
      "email": "<EMAIL>",
      "avatar": null
    },
    "actor": null,
    "project": null,
    "location": null,
    "document": null,
    "task": null
  }
]
```

### PUT `/api/notifications/[id]/read`

Marks a specific notification as read.

**Response:**
```json
{
  "id": "uuid-string",
  "type": "system_notification",
  "title": "Notification Title",
  "message": "Notification content",
  "status": "read",
  "priority": "medium",
  "recipientId": "user-uuid",
  "organizationId": "org-uuid",
  "projectId": null,
  "locationId": null,
  "documentId": null,
  "taskId": null,
  "actorId": null,
  "metadata": {},
  "isRead": true,
  "readAt": "2025-05-01T17:14:36.000Z",
  "expiresAt": null,
  "createdAt": "2025-05-01T17:14:36.000Z",
  "updatedAt": "2025-05-01T17:14:36.000Z"
}
```

### PUT `/api/notifications/read-all`

Marks all notifications for the current user as read.

**Response:**
```json
{
  "count": 5
}
```

### GET `/api/notifications/preferences`

Gets the notification preferences for the current user.

**Response:**
```json
[
  {
    "id": "uuid-string",
    "userId": "user-uuid",
    "organizationId": "org-uuid",
    "notificationType": "system_notification",
    "inApp": true,
    "email": true,
    "createdAt": "2025-05-01T17:03:38.000Z",
    "updatedAt": "2025-05-01T17:03:38.000Z"
  }
]
```

### PUT `/api/notifications/preferences`

Updates notification preferences for the current user.

**Request Body:**
```json
{
  "preferences": [
    {
      "notificationType": "system_notification",
      "inApp": true,
      "email": false
    },
    {
      "notificationType": "task_assigned",
      "inApp": true,
      "email": true
    }
  ]
}
```

**Response:**
```json
[
  {
    "id": "uuid-string",
    "userId": "user-uuid",
    "organizationId": "org-uuid",
    "notificationType": "system_notification",
    "inApp": true,
    "email": false,
    "createdAt": "2025-05-01T17:03:38.000Z",
    "updatedAt": "2025-05-01T17:14:36.000Z"
  },
  {
    "id": "uuid-string",
    "userId": "user-uuid",
    "organizationId": "org-uuid",
    "notificationType": "task_assigned",
    "inApp": true,
    "email": true,
    "createdAt": "2025-05-01T17:03:38.000Z",
    "updatedAt": "2025-05-01T17:14:36.000Z"
  }
]
```

### POST `/api/notifications/preferences/initialize`

Initializes default notification preferences for the current user.

**Response:**
```json
[
  {
    "id": "uuid-string",
    "userId": "user-uuid",
    "organizationId": "org-uuid",
    "notificationType": "system_notification",
    "inApp": true,
    "email": true,
    "createdAt": "2025-05-01T17:03:38.000Z",
    "updatedAt": "2025-05-01T17:03:38.000Z"
  },
  // ... other notification types
]
```

## Service Methods

The `NotificationService` class provides the following methods:

### `createNotification(data: CreateNotification): Promise<Notification>`

Creates a new notification. If the user has disabled in-app notifications for this type, an error is thrown.

### `getNotifications(query: GetNotificationsQuery): Promise<NotificationWithRelations[]>`

Gets a paginated list of notifications for a user with optional filtering.

### `getNotificationCount(recipientId: string, organizationId: string): Promise<NotificationCount>`

Gets the total and unread notification counts for a user.

### `getNotificationById(id: string): Promise<NotificationWithRelations | null>`

Gets a notification by ID with its relations.

### `markNotificationAsRead(id: string): Promise<Notification>`

Marks a notification as read.

### `markAllNotificationsAsRead(recipientId: string, organizationId: string): Promise<number>`

Marks all notifications for a user as read and returns the number of notifications updated.

### `deleteNotification(id: string): Promise<Notification>`

Deletes a notification.

### `archiveNotification(id: string): Promise<Notification>`

Archives a notification.

### `getNotificationPreferences(userId: string, organizationId: string): Promise<NotificationPreference[]>`

Gets notification preferences for a user.

### `createOrUpdateNotificationPreference(data: CreateNotificationPreference): Promise<NotificationPreference>`

Creates or updates a notification preference.

### `bulkUpdateNotificationPreferences(data: BulkUpdateNotificationPreferences): Promise<NotificationPreference[]>`

Updates multiple notification preferences at once.

### `initializeDefaultPreferences(userId: string, organizationId: string): Promise<NotificationPreference[]>`

Initializes default notification preferences for a user.

### `deleteNotificationPreferences(userId: string, organizationId: string): Promise<number>`

Deletes all notification preferences for a user.

## Integration with Stytch Authentication

The notification system integrates with Stytch authentication to identify users and organizations. The API routes extract the Stytch user ID and organization ID from the request headers:

```typescript
const memberId = req.headers.get('X-Stytch-Member-Id')
const stytchOrgId = req.headers.get('X-Stytch-Org-Id')
```

These IDs are then converted to database IDs using utility functions:

```typescript
const userId = await getUserIdFromStytchId(memberId)
const orgId = await getOrgIdFromStytchId(stytchOrgId)
```

## RBAC Integration

The notification system integrates with the Role-Based Access Control (RBAC) system to ensure users have the appropriate permissions to perform actions. The following permissions are used:

- `read`: Required to view notifications and notification counts
- `mark_as_read`: Required to mark notifications as read
- `manage_preferences`: Required to update notification preferences

## Frontend Components

### `NotificationBell`

A bell icon that displays the number of unread notifications and opens the notification dropdown when clicked.

### `NotificationDropdown`

A dropdown that displays a list of notifications and allows users to mark them as read.

## Common Use Cases

### Creating a Notification

```typescript
await NotificationService.createNotification({
  title: "New Task Assigned",
  message: "You have been assigned a new task",
  type: "task_assigned",
  priority: "medium",
  recipientId: userId,
  organizationId: orgId,
  taskId: taskId,
  actorId: assignerId,
  metadata: {},
})
```

### Marking a Notification as Read

```typescript
await NotificationService.markNotificationAsRead(notificationId)
```

### Getting Notification Count

```typescript
const count = await NotificationService.getNotificationCount(userId, orgId)
console.log(`Total: ${count.total}, Unread: ${count.unread}`)
```

## Known Issues and Troubleshooting

### Location Relation Issue

There is a known issue with the location relation in the notification queries. The location table has a `status` column that is a varchar, while the notification table has a `status` column that is an enum. This causes a conflict when trying to join the tables.

To work around this issue, the `getNotifications` and `getNotificationById` methods exclude the location relation from the query and add a null location property to the result:

```typescript
const notificationsWithPartialRelations = await db.query.notifications.findMany({
  where: whereClause,
  with: {
    recipient: true,
    actor: true,
    project: true,
    document: true,
    task: true,
    // location relation is excluded
  },
  // ...
})

// Add empty location property to match NotificationWithRelations type
const notificationsWithRelations = notificationsWithPartialRelations.map(notification => ({
  ...notification,
  location: null
}))
```

### Stytch ID to Database ID Conversion

If you encounter issues with notifications not being associated with the correct user or organization, check that the Stytch ID to database ID conversion is working correctly. The `getUserIdFromStytchId` and `getOrgIdFromStytchId` functions in `lib/utils/userUtils.ts` are responsible for this conversion.

### Email Notifications

Email notifications are not currently implemented. The `createNotification` method checks if email notifications are enabled for the notification type, but does not send an email. This functionality will be added in a future update.

## Future Enhancements

- Implement email notifications
- Add support for push notifications
- Add support for notification templates
- Add support for notification groups
- Add support for notification expiration
- Add support for notification actions
