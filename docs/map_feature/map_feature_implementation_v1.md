# Map Feature Implementation - v1

**Date: May 1, 2025**

This document provides a comprehensive overview of the current implementation of the map feature in the Scene-o-matic application. It serves as a baseline for tracking how the feature evolves over time.


## Table of Contents

1. [Overview](#overview)
2. [Architecture](#architecture)
3. [Components](#components)
4. [State Management](#state-management)
5. [API Endpoints](#api-endpoints)
6. [Data Models](#data-models)
7. [Styling](#styling)
8. [Current Features](#current-features)
9. [Limitations and Enhancement Opportunities](#limitations-and-enhancement-opportunities)

## Overview

The map feature is a core component of the Scene-o-matic application, designed to help film and television production teams visualize and manage filming locations. It provides an interactive map interface where users can view, filter, and manage locations associated with their projects.

## Architecture

The map feature follows the application's layered architecture:

1. **UI Layer**: React components for rendering the map and related UI elements
2. **State Management Layer**: React context and hooks for managing map state
3. **API Layer**: Next.js API routes for data fetching and persistence
4. **Service Layer**: Business logic for processing map data
5. **Data Access Layer**: Database operations for retrieving and storing map-related data

The feature integrates with Mapbox GL JS for the interactive mapping capabilities and uses the application's authentication and authorization systems for security.

## Components

### Main Components

1. **FullMap** (`components/maps/full-map.tsx`)
   - Currently a placeholder component that simulates a map with markers
   - Includes search bar, map controls, and an "Add Location" button
   - Uses a static background image as a placeholder

2. **MapView** (`components/maps/map-view.tsx`)
   - The actual Mapbox GL JS implementation
   - Fetches Mapbox token from API
   - Renders locations as markers on the map
   - Supports interactive features like zooming, panning, and clicking on markers
   - Includes loading and error states

3. **MapLayers** (`components/maps/map-layers.tsx`)
   - UI for managing map layers and overlays
   - Includes checkboxes for toggling different map layers
   - Organized into categories: Base Maps, Overlays, Property Filters
   - Includes a slider for adjusting layer opacity

4. **MapLocations** (`components/maps/map-locations.tsx`)
   - Displays a list of locations with details
   - Shows location status with color-coded badges
   - Includes favorite indicators
   - Currently uses mock data

### Supporting Components

1. **Loading** (`app/(dashboard)/organizations/[organizationSlug]/map/loading.tsx`)
   - Skeleton loading state for the map page
   - Maintains the same layout as the actual page for a smooth transition

## State Management

The map feature uses React context and hooks for state management:

1. **MapProvider** (`providers/map-provider.tsx`)
   - Provides map state to child components
   - Manages locations, selected location, view settings, and filters
   - Handles fetching locations based on filters
   - Exposes methods for refreshing data and updating state

2. **Hooks**
   - `useMap` (`providers/map-provider.tsx`): Access to the map context
   - `useMapContext` (`hooks/use-map-context.ts`): Another hook for accessing map context
   - `useMapLocations` (`hooks/use-map.ts`): For fetching and filtering locations
   - `useMapViewSettings` (`hooks/use-map.ts`): For managing map view settings

## API Endpoints

1. **Map Data** (`app/api/map/route.ts`)
   - `GET`: Returns filtered locations and projects
   - Supports filtering by location types, project IDs, and search query
   - Implements RBAC checks for security

2. **Map Settings** (`app/api/map/settings/route.ts`)
   - `POST`: Saves user map settings
   - Currently logs settings but doesn't persist them

3. **Mapbox Token** (`app/api/mapbox-token/route.ts`)
   - `GET`: Returns the Mapbox token for client-side use
   - Implements authentication check

## Data Models

1. **MapViewSettings** (`modules/map/model.ts`)
   - Center coordinates (latitude, longitude)
   - Zoom level
   - Pitch
   - Bearing

2. **MapFilter** (`modules/map/model.ts`)
   - Location types
   - Project IDs
   - Search query

3. **MapState** (`modules/map/model.ts`)
   - View settings
   - Selected location ID
   - Filters

4. **ClusterSettings** (`modules/map/model.ts`)
   - Enabled flag
   - Radius
   - Maximum zoom level

5. **Location** (`modules/location/model.ts`)
   - Comprehensive model for location data
   - Includes coordinates, address, type, status, and other metadata

## Services

1. **Map Service** (`modules/map/service.ts`)
   - `getFilteredLocations`: Retrieves locations based on filters
   - `getProjectsForMap`: Retrieves projects for the organization
   - `saveUserMapSettings`: Placeholder for saving user map settings
   - `getUserMapSettings`: Placeholder for retrieving user map settings

## Styling

The map feature follows the application's futuristic neon theme:

1. **Color Palette**
   - Neon Blue (`#2323FF`): Primary accent, active states
   - Neon Green (`#16FF00`): Success states, available status
   - Neon Pink (`#F61981`): Alerts, error states, sold/cancelled status
   - Neon Yellow (`#FFED00`): Warnings, pending states

2. **UI Elements**
   - Dark backgrounds with neon accents
   - Glowing effects for interactive elements
   - Rounded corners for cards, buttons, and UI elements
   - High contrast for readability

3. **Map Styling**
   - Currently uses Mapbox's default "streets-v11" style
   - Custom markers with color coding based on location type

## Current Features

1. **Location Visualization**
   - Display locations as markers on the map
   - Color-code markers based on location type
   - Click on markers to select locations

2. **Filtering**
   - Filter locations by type
   - Filter locations by project
   - Search locations by name or description

3. **Map Controls**
   - Zoom in/out
   - Pan
   - Reset view to show all locations
   - Toggle map layers

4. **Location List**
   - View locations in a list format
   - See location status and type
   - Mark locations as favorites

5. **Map Layers**
   - Toggle different base maps (satellite, terrain, street)
   - Toggle overlays (traffic, transit, points of interest)
   - Toggle property filters (residential, commercial, industrial)
   - Adjust layer opacity

## Limitations and Enhancement Opportunities

1. **Real-time Updates**
   - The current implementation doesn't support real-time updates
   - No WebSocket or polling mechanism for live data

2. **Advanced Filtering**
   - Limited filtering options
   - No date-based or advanced attribute filtering
   - No saved filters

3. **Custom Map Views**
   - UI elements for saving views exist but aren't functional
   - No way to share custom views with team members

4. **Drawing Tools**
   - No support for drawing or annotations on the map
   - No ability to define custom boundaries or routes

5. **Clustering**
   - Model exists but not fully implemented in the UI
   - No way to control clustering behavior

6. **Geospatial Analysis**
   - No distance calculations between locations
   - No routing or travel time estimation
   - No proximity search

7. **Export Capabilities**
   - UI elements for exporting exist but aren't functional
   - No way to generate PDFs or images of the map

8. **Mobile Optimization**
   - Basic responsive design but not optimized for mobile use cases
   - No specific mobile interactions (e.g., touch gestures)

9. **Performance**
   - No optimization for handling large numbers of locations
   - No virtualization for location lists

10. **Integration with Other Features**
    - Limited integration with projects, scenes, and tasks
    - No connection to weather or lighting data

This document serves as a baseline for the map feature as of May 1, 2025. Future versions will track enhancements and changes to the feature.
