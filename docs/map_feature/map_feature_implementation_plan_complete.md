# Map Feature Implementation Plan

**Date: May 1, 2025**

This document outlines the atomic-level, step-by-step plan to implement the map feature in the Scene-o-matic application. The plan is based on the current implementation and the requirements specified by the user.

## Table of Contents

1. [Overview](#overview)
2. [Requirements](#requirements)
3. [Implementation Plan](#implementation-plan)
4. [Security Considerations](#security-considerations)
5. [Testing Strategy](#testing-strategy)
6. [Database Schema](#database-schema)
7. [API Endpoints](#api-endpoints)

## Overview

The map feature is a core component of the Scene-o-matic application, designed to serve as a visual library of ALL locations within the locations database. It allows users to visualize, filter, and interact with locations on a map, with robust filtering capabilities and the ability to share selected locations.

The map features v1 overview document is available here for reference to the map features of this app:
docs/map_feature/map_feature_implementation_v1.md

## Requirements

Based on the user's specifications, the map feature should include the following functionality:

1. **Location Visualization**
   - Display all locations from the database on the map
   - Color-code markers based on location type
   - Show location details on hover/click

2. **Interactive Elements**
   - Clicking on a location marker moves the map to center on that location
   - Clicking on a location card title opens a modal with detailed information
   - Clicking the star icon opens a modal to add the location to favorites lists
   - Full-screen mode with collapsible left panel

3. **Filtering Capabilities**
   - Robust filtering system for location managers to sort locations
   - Filter by project, location type, status, etc.

4. **Sharing and Exporting**
   - Share button generates a shareable link with selected locations
   - Export map as PDF with location details

5. **Location Management**
   - Add Location button opens the location creation modal
   - Save views for later reference

6. **Multi-tenant Security**
   - Ensure data separation between organizations
   - Implement proper RBAC for location access

## Implementation Plan

### Phase 1: Core Map Infrastructure

1. **Update MapProvider Component**
   - Connect to real data instead of mock data
   - Implement proper error handling
   - Add loading states

2. **Enhance MapView Component**
   - Implement Mapbox GL JS integration with proper styling
   - Add custom markers with type-based styling
   - Implement hover effects for markers
   - Add clustering for better performance with many locations

3. **Create Location Detail Modal**
   - Design modal based on location detail page
   - Include tabs for gallery, map, documents, notes
   - Implement role-based visibility for sensitive information

4. **Implement Favorites System**
   - Create favorites modal component
   - Design database schema for favorites lists
   - Implement API endpoints for managing favorites
   - Connect star icon to favorites functionality

### Phase 2: Filtering and UI Enhancements

5. **Implement Advanced Filtering**
   - Design filter modal component
   - Implement filter logic in MapProvider
   - Create filter UI with various options:
     - Location type
     - Project
     - Status
     - Date added
     - Custom attributes

6. **Enhance Location List Component**
   - Connect to real data
   - Implement sorting options
   - Add pagination for better performance
   - Highlight selected location

7. **Implement Full-screen Mode**
   - Add full-screen button
   - Create collapsible sidebar
   - Optimize layout for full-screen view

8. **Implement Map Controls**
   - Add zoom controls
   - Add layer toggle controls
   - Add location clustering controls
   - Add map style selector

### Phase 3: Sharing and Integration

9. **Implement View Saving**
   - Design save view modal
   - Create database schema for saved views
   - Implement API endpoints for managing saved views
   - Connect save button to functionality

10. **Implement Sharing Functionality**
    - Create shareable link generator
    - Design shared view page with limited functionality
    - Implement copy to clipboard functionality
    - Add security measures for shared links

11. **Implement Export Functionality**
    - Create PDF export service
    - Design PDF template with location details
    - Implement export button functionality
    - Add progress indicator for export process

12. **Integrate with Location Creation**
    - Connect Add Location button to location creation modal
    - Ensure proper data flow between map and location creation
    - Implement location creation success feedback

### Phase 4: Security and Performance

13. **Implement Multi-tenant Security**
    - Ensure proper organization isolation
    - Implement RBAC checks for all API endpoints
    - Add logging for security events
    - Test security measures thoroughly

14. **Optimize Performance**
    - Implement virtualization for location list
    - Optimize marker rendering for large datasets
    - Add caching for frequently accessed data
    - Implement lazy loading for location details

15. **Add Error Handling and Recovery**
    - Implement comprehensive error handling
    - Add retry mechanisms for failed API calls
    - Create user-friendly error messages
    - Add fallback UI for error states

16. **Finalize Mobile Responsiveness**
    - Test and optimize for various screen sizes
    - Implement touch-friendly controls for mobile
    - Optimize layout for small screens
    - Ensure proper performance on mobile devices

## Security Considerations

### Data Isolation

1. **Organization-Level Isolation**
   - All API endpoints must validate that the requesting user belongs to the organization
   - All database queries must include organization ID filters
   - Implement middleware to validate organization access

2. **Role-Based Access Control (RBAC)**
   - Implement permission checks for all map-related operations
   - Define granular permissions for map features:
     - `map:view` - Basic map viewing permission
     - `map:edit` - Permission to edit map settings and views
     - `map:share` - Permission to share map views
     - `map:export` - Permission to export map data
   - Enforce RBAC in both frontend and backend

3. **Shared Link Security**
   - Generate secure, non-guessable tokens for shared links
   - Implement expiration for shared links
   - Add option to password-protect shared links
   - Log all access to shared links

### API Security

1. **Input Validation**
   - Validate all API inputs using Zod schemas
   - Sanitize user inputs to prevent injection attacks
   - Implement rate limiting for API endpoints

2. **Authentication**
   - Ensure all API endpoints require authentication
   - Validate session tokens on every request
   - Implement proper token expiration and renewal

3. **Logging and Monitoring**
   - Log all security-relevant events
   - Implement audit trails for sensitive operations
   - Set up monitoring for suspicious activity

### Frontend Security

1. **State Management**
   - Implement proper state validation
   - Prevent cross-organization data leakage
   - Sanitize data before rendering

2. **UI Protection**
   - Hide sensitive UI elements based on user permissions
   - Implement proper error handling to prevent information leakage
   - Add loading states to prevent premature data display

## Testing Strategy

### Unit Testing

1. **Component Tests**
   - Test individual map components in isolation
   - Verify component rendering with different props
   - Test component state management
   - Verify event handling

2. **Hook Tests**
   - Test custom hooks for map functionality
   - Verify state management in hooks
   - Test error handling in hooks

3. **Utility Function Tests**
   - Test map utility functions
   - Verify data transformation functions
   - Test validation functions

### Integration Testing

1. **Component Integration**
   - Test interaction between map components
   - Verify data flow between components
   - Test modal interactions

2. **API Integration**
   - Test API calls from components
   - Verify error handling for API failures
   - Test loading states during API calls

3. **State Management Integration**
   - Test global state management
   - Verify context provider integration
   - Test state updates across components

### End-to-End Testing

1. **User Flow Testing**
   - Test complete user flows:
     - Viewing locations on the map
     - Filtering locations
     - Adding locations to favorites
     - Sharing map views
     - Exporting map data
   - Verify UI feedback for each action

2. **Performance Testing**
   - Test map performance with large datasets
   - Verify marker clustering performance
   - Test rendering performance on different devices

3. **Security Testing**
   - Test RBAC enforcement
   - Verify organization isolation
   - Test shared link security

### Accessibility Testing

1. **Screen Reader Testing**
   - Verify map accessibility with screen readers
   - Test keyboard navigation
   - Verify ARIA attributes

2. **Contrast Testing**
   - Verify color contrast for map elements
   - Test visibility of map markers
   - Verify readability of map controls

3. **Mobile Accessibility**
   - Test touch target sizes
   - Verify gesture support
   - Test responsive layout

## Database Schema

### Favorites Lists

```sql
CREATE TABLE favorite_lists (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  organization_id UUID NOT NULL REFERENCES organizations(id),
  name VARCHAR(255) NOT NULL,
  type VARCHAR(50) NOT NULL CHECK (type IN ('project', 'scene', 'custom')),
  project_id UUID REFERENCES projects(id),
  scene_id UUID REFERENCES scenes(id),
  created_by UUID NOT NULL REFERENCES users(id),
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
  deleted_at TIMESTAMP WITH TIME ZONE
);

CREATE TABLE favorite_list_locations (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  favorite_list_id UUID NOT NULL REFERENCES favorite_lists(id),
  location_id UUID NOT NULL REFERENCES locations(id),
  added_by UUID NOT NULL REFERENCES users(id),
  added_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
  UNIQUE(favorite_list_id, location_id)
);
```

### Saved Map Views

```sql
CREATE TABLE map_views (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  organization_id UUID NOT NULL REFERENCES organizations(id),
  name VARCHAR(255) NOT NULL,
  description TEXT,
  filters JSONB,
  view_settings JSONB,
  created_by UUID NOT NULL REFERENCES users(id),
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
  deleted_at TIMESTAMP WITH TIME ZONE
);

CREATE TABLE map_view_locations (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  map_view_id UUID NOT NULL REFERENCES map_views(id),
  location_id UUID NOT NULL REFERENCES locations(id),
  UNIQUE(map_view_id, location_id)
);
```

### Shared Map Links

```sql
CREATE TABLE shared_map_links (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  organization_id UUID NOT NULL REFERENCES organizations(id),
  token VARCHAR(255) NOT NULL UNIQUE,
  map_view_id UUID REFERENCES map_views(id),
  filters JSONB,
  view_settings JSONB,
  password_hash VARCHAR(255),
  expires_at TIMESTAMP WITH TIME ZONE,
  created_by UUID NOT NULL REFERENCES users(id),
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW()
);

CREATE TABLE shared_map_link_locations (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  shared_map_link_id UUID NOT NULL REFERENCES shared_map_links(id),
  location_id UUID NOT NULL REFERENCES locations(id),
  UNIQUE(shared_map_link_id, location_id)
);

CREATE TABLE shared_map_link_access_logs (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  shared_map_link_id UUID NOT NULL REFERENCES shared_map_links(id),
  ip_address VARCHAR(45),
  user_agent TEXT,
  accessed_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW()
);
```

## API Endpoints

### Map Data

```
GET /api/map
```

Query parameters:
- `organizationId` (required): The ID of the organization
- `locationTypes` (optional): Comma-separated list of location types
- `projectIds` (optional): Comma-separated list of project IDs
- `searchQuery` (optional): Search query for location name or description
- `status` (optional): Comma-separated list of location statuses
- `dateFrom` (optional): Start date for filtering by creation date
- `dateTo` (optional): End date for filtering by creation date

Response:
```json
{
  "locations": [
    {
      "id": "uuid",
      "name": "Location Name",
      "description": "Location description",
      "address": {
        "street": "123 Main St",
        "city": "New York",
        "state": "NY",
        "postalCode": "10001",
        "country": "USA",
        "formatted": "123 Main St, New York, NY 10001, USA"
      },
      "coordinates": {
        "latitude": 40.7128,
        "longitude": -74.0060
      },
      "type": "interior",
      "status": "approved",
      "projectId": "project-uuid",
      "organizationId": "org-uuid",
      "isActive": true,
      "permitsRequired": false,
      "createdAt": "2025-01-01T00:00:00Z",
      "updatedAt": "2025-01-01T00:00:00Z"
    }
  ],
  "projects": [
    {
      "id": "uuid",
      "name": "Project Name",
      "description": "Project description",
      "status": "active",
      "organizationId": "org-uuid",
      "isArchived": false,
      "createdAt": "2025-01-01T00:00:00Z",
      "updatedAt": "2025-01-01T00:00:00Z"
    }
  ]
}
```

### Favorites

```
GET /api/favorites
```

Query parameters:
- `organizationId` (required): The ID of the organization

Response:
```json
[
  {
    "id": "uuid",
    "name": "Favorite List Name",
    "type": "custom",
    "projectId": null,
    "sceneId": null,
    "createdBy": "user-uuid",
    "createdAt": "2025-01-01T00:00:00Z",
    "updatedAt": "2025-01-01T00:00:00Z",
    "locations": [
      {
        "id": "location-uuid",
        "name": "Location Name",
        "type": "interior",
        "status": "approved",
        "addedAt": "2025-01-01T00:00:00Z"
      }
    ]
  }
]
```

```
POST /api/favorites
```

Request body:
```json
{
  "organizationId": "org-uuid",
  "name": "Favorite List Name",
  "type": "custom",
  "projectId": null,
  "sceneId": null
}
```

Response:
```json
{
  "id": "uuid",
  "name": "Favorite List Name",
  "type": "custom",
  "projectId": null,
  "sceneId": null,
  "createdBy": "user-uuid",
  "createdAt": "2025-01-01T00:00:00Z",
  "updatedAt": "2025-01-01T00:00:00Z",
  "locations": []
}
```

```
POST /api/favorites/add
```

Request body:
```json
{
  "organizationId": "org-uuid",
  "locationId": "location-uuid",
  "listId": "list-uuid"
}
```

Response:
```json
{
  "success": true
}
```

### Map Views

```
GET /api/map/views
```

Query parameters:
- `organizationId` (required): The ID of the organization

Response:
```json
[
  {
    "id": "uuid",
    "name": "Map View Name",
    "description": "Map View description",
    "filters": {
      "locationTypes": ["interior", "exterior"],
      "projectIds": ["project-uuid"],
      "searchQuery": "search term",
      "status": ["approved", "pending"]
    },
    "viewSettings": {
      "center": {
        "latitude": 40.7128,
        "longitude": -74.0060
      },
      "zoom": 10,
      "pitch": 0,
      "bearing": 0
    },
    "createdBy": "user-uuid",
    "createdAt": "2025-01-01T00:00:00Z",
    "updatedAt": "2025-01-01T00:00:00Z"
  }
]
```

```
POST /api/map/views
```

Request body:
```json
{
  "organizationId": "org-uuid",
  "name": "Map View Name",
  "description": "Map View description",
  "filters": {
    "locationTypes": ["interior", "exterior"],
    "projectIds": ["project-uuid"],
    "searchQuery": "search term",
    "status": ["approved", "pending"]
  },
  "viewSettings": {
    "center": {
      "latitude": 40.7128,
      "longitude": -74.0060
    },
    "zoom": 10,
    "pitch": 0,
    "bearing": 0
  },
  "selectedLocationIds": ["location-uuid-1", "location-uuid-2"]
}
```

Response:
```json
{
  "id": "uuid",
  "name": "Map View Name",
  "description": "Map View description",
  "filters": {
    "locationTypes": ["interior", "exterior"],
    "projectIds": ["project-uuid"],
    "searchQuery": "search term",
    "status": ["approved", "pending"]
  },
  "viewSettings": {
    "center": {
      "latitude": 40.7128,
      "longitude": -74.0060
    },
    "zoom": 10,
    "pitch": 0,
    "bearing": 0
  },
  "createdBy": "user-uuid",
  "createdAt": "2025-01-01T00:00:00Z",
  "updatedAt": "2025-01-01T00:00:00Z"
}
```

### Shared Links

```
POST /api/map/share
```

Request body:
```json
{
  "organizationId": "org-uuid",
  "mapViewId": "map-view-uuid",
  "password": "optional-password",
  "expiresIn": 86400,
  "locationIds": ["location-uuid-1", "location-uuid-2"]
}
```

Response:
```json
{
  "id": "uuid",
  "token": "share-token",
  "url": "https://sceneomatic.com/shared/map/share-token",
  "expiresAt": "2025-01-02T00:00:00Z"
}
```

```
GET /api/map/shared/:token
```

Query parameters:
- `password` (optional): Password for protected shared links

Response:
```json
{
  "id": "uuid",
  "organizationId": "org-uuid",
  "viewSettings": {
    "center": {
      "latitude": 40.7128,
      "longitude": -74.0060
    },
    "zoom": 10,
    "pitch": 0,
    "bearing": 0
  },
  "locations": [
    {
      "id": "location-uuid",
      "name": "Location Name",
      "description": "Location description",
      "address": {
        "formatted": "123 Main St, New York, NY 10001, USA"
      },
      "coordinates": {
        "latitude": 40.7128,
        "longitude": -74.0060
      },
      "type": "interior",
      "status": "approved"
    }
  ]
}
```

### Export

```
POST /api/map/export
```

Request body:
```json
{
  "organizationId": "org-uuid",
  "format": "pdf",
  "locationIds": ["location-uuid-1", "location-uuid-2"],
  "includeDetails": true,
  "includeImages": true,
  "includeMap": true
}
```

Response:
```json
{
  "id": "export-job-uuid",
  "status": "processing",
  "estimatedCompletionTime": 30
}
```

```
GET /api/map/export/:id
```

Response:
```json
{
  "id": "export-job-uuid",
  "status": "completed",
  "downloadUrl": "https://sceneomatic.com/api/map/export/download/export-job-uuid",
  "expiresAt": "2025-01-02T00:00:00Z"
}
```

```
GET /api/map/export/download/:id
```

Response: Binary file download
