# Mapbox Integration Enhancement Plan

**Date: May 1, 2025**

This document outlines the plan for enhancing the existing Mapbox integration in the Scene-o-matic application, based on a review of the current implementation and Mapbox documentation.

## Current Implementation Overview

The application already has a well-structured Mapbox GL JS integration with:

1. **Map Component Architecture**:
   - `MapView` component that initializes and renders the Mapbox GL map
   - `MapProvider` context for state management
   - API endpoints for fetching map data and Mapbox token

2. **Core Features**:
   - Custom markers with type-based styling and hover effects
   - Marker clustering with custom styling
   - Location filtering system
   - Favorites and saved views functionality
   - RBAC security implementation

3. **Token Management**:
   - Secure token handling via `/api/mapbox-token` endpoint
   - Authentication checks before providing the token

## Enhancement Recommendations

Based on the Mapbox documentation, here are recommended enhancements to improve the map implementation:

### 1. Add Advanced Geocoding

Install the Mapbox Geocoder package:
```bash
pnpm add @mapbox/mapbox-gl-geocoder
```

Implement in the MapView component:
```javascript
// In map-view.tsx
import MapboxGeocoder from '@mapbox/mapbox-gl-geocoder';
import '@mapbox/mapbox-gl-geocoder/dist/mapbox-gl-geocoder.css';

// Add inside the map initialization effect
if (showControls) {
  // Add existing navigation control
  map.current.addControl(new mapboxgl.NavigationControl(), "top-right");
  
  // Add geocoder control
  const geocoder = new MapboxGeocoder({
    accessToken: mapboxgl.accessToken,
    mapboxgl: mapboxgl,
    marker: false,
    placeholder: 'Search for locations'
  });
  
  map.current.addControl(geocoder, 'top-left');
  
  // Handle geocoder results
  geocoder.on('result', (e) => {
    const { center, place_name } = e.result;
    console.log(`Selected location: ${place_name} at ${center}`);
    
    // You could add a custom marker or trigger a search for nearby locations
  });
}
```

### 2. Implement 3D Buildings and Terrain

Enhance the map with 3D features:
```javascript
// Add to map-view.tsx after map loads
map.current.on('load', () => {
  setMapLoaded(true);
  setIsLoading(false);

  // Add 3D terrain if supported
  if (map.current.getTerrain) { // Check if terrain is supported
    map.current.setTerrain({ source: 'mapbox-dem', exaggeration: 1.5 });
    
    // Add the DEM source
    map.current.addSource('mapbox-dem', {
      'type': 'raster-dem',
      'url': 'mapbox://mapbox.mapbox-terrain-dem-v1',
      'tileSize': 512,
      'maxzoom': 14
    });
  }
  
  // Add 3D buildings layer
  map.current.addLayer({
    'id': '3d-buildings',
    'source': 'composite',
    'source-layer': 'building',
    'filter': ['==', 'extrude', 'true'],
    'type': 'fill-extrusion',
    'minzoom': 15,
    'paint': {
      'fill-extrusion-color': '#aaa',
      'fill-extrusion-height': [
        'interpolate', ['linear'], ['zoom'],
        15, 0,
        15.05, ['get', 'height']
      ],
      'fill-extrusion-base': [
        'interpolate', ['linear'], ['zoom'],
        15, 0,
        15.05, ['get', 'min_height']
      ],
      'fill-extrusion-opacity': 0.6
    }
  });
});
```

### 3. Add Drawing Tools for Custom Boundaries

Install the Mapbox Draw package:
```bash
pnpm add @mapbox/mapbox-gl-draw @turf/turf
```

Implement drawing tools:
```javascript
// Add to map-view.tsx
import MapboxDraw from '@mapbox/mapbox-gl-draw';
import '@mapbox/mapbox-gl-draw/dist/mapbox-gl-draw.css';
import * as turf from '@turf/turf';

// Add inside the map initialization effect
const draw = new MapboxDraw({
  displayControlsDefault: false,
  controls: {
    polygon: true,
    trash: true
  },
  defaultMode: 'simple_select',
  styles: [
    // Custom styles for the drawing tools with our neon theme
    {
      'id': 'gl-draw-polygon-fill-inactive',
      'type': 'fill',
      'filter': ['all', ['==', 'active', 'false'], ['==', '$type', 'Polygon']],
      'paint': {
        'fill-color': '#2323FF',
        'fill-outline-color': '#2323FF',
        'fill-opacity': 0.1
      }
    },
    {
      'id': 'gl-draw-polygon-fill-active',
      'type': 'fill',
      'filter': ['all', ['==', 'active', 'true'], ['==', '$type', 'Polygon']],
      'paint': {
        'fill-color': '#F61981',
        'fill-outline-color': '#F61981',
        'fill-opacity': 0.1
      }
    },
    {
      'id': 'gl-draw-polygon-stroke-inactive',
      'type': 'line',
      'filter': ['all', ['==', 'active', 'false'], ['==', '$type', 'Polygon']],
      'layout': {
        'line-cap': 'round',
        'line-join': 'round'
      },
      'paint': {
        'line-color': '#2323FF',
        'line-width': 2
      }
    },
    {
      'id': 'gl-draw-polygon-stroke-active',
      'type': 'line',
      'filter': ['all', ['==', 'active', 'true'], ['==', '$type', 'Polygon']],
      'layout': {
        'line-cap': 'round',
        'line-join': 'round'
      },
      'paint': {
        'line-color': '#F61981',
        'line-dasharray': [0.2, 2],
        'line-width': 2
      }
    },
    {
      'id': 'gl-draw-polygon-midpoint',
      'type': 'circle',
      'filter': ['all', ['==', '$type', 'Point'], ['==', 'meta', 'midpoint']],
      'paint': {
        'circle-radius': 3,
        'circle-color': '#16FF00'
      }
    },
    {
      'id': 'gl-draw-polygon-and-line-vertex-inactive',
      'type': 'circle',
      'filter': ['all', ['==', 'meta', 'vertex'], ['==', '$type', 'Point']],
      'paint': {
        'circle-radius': 5,
        'circle-color': '#FFED00'
      }
    }
  ]
});

map.current.addControl(draw, 'top-right');

// Handle draw events
map.current.on('draw.create', (e) => {
  console.log('Draw created:', e.features);
  
  // Filter locations within the drawn polygon
  if (e.features.length > 0) {
    const polygon = e.features[0].geometry;
    
    // Filter locations that are within the polygon
    const locationsInPolygon = locations.filter(location => {
      const point = turf.point([location.coordinates.longitude, location.coordinates.latitude]);
      return turf.booleanPointInPolygon(point, polygon);
    });
    
    console.log(`Found ${locationsInPolygon.length} locations in the drawn area`);
    // You could update the UI to show only these locations
  }
});
```

### 4. Implement Directions for Routing

Install the Mapbox Directions package:
```bash
pnpm add @mapbox/mapbox-gl-directions
```

Add routing capabilities:
```javascript
// Add to map-view.tsx
import MapboxDirections from '@mapbox/mapbox-gl-directions/dist/mapbox-gl-directions';
import '@mapbox/mapbox-gl-directions/dist/mapbox-gl-directions.css';

// Add a state for toggling directions
const [showDirections, setShowDirections] = useState(false);
const directionsRef = useRef(null);

// Add a function to toggle directions
const toggleDirections = () => {
  if (!map.current || !mapLoaded) return;
  
  setShowDirections(!showDirections);
  
  if (!showDirections) {
    const directions = new MapboxDirections({
      accessToken: mapboxgl.accessToken,
      unit: 'metric',
      profile: 'mapbox/driving',
      alternatives: true,
      congestion: true,
      controls: {
        inputs: true,
        instructions: false
      }
    });
    
    map.current.addControl(directions, 'top-left');
    // Store the directions control in a ref for later removal
    directionsRef.current = directions;
  } else if (directionsRef.current) {
    map.current.removeControl(directionsRef.current);
    directionsRef.current = null;
  }
};

// Add a button to the UI controls
<Button
  variant="secondary"
  size="sm"
  className="shadow-md"
  onClick={toggleDirections}
>
  <Navigation className="h-4 w-4 mr-2" />
  {showDirections ? "Hide Directions" : "Show Directions"}
</Button>
```

### 5. Add Cooperative Gestures for Mobile

Improve the mobile experience:
```javascript
// In map-view.tsx, update the map initialization:
map.current = new mapboxgl.Map({
  container: mapContainer.current,
  style: "mapbox://styles/mapbox/dark-v11",
  center: [viewSettings.center.longitude, viewSettings.center.latitude],
  zoom: viewSettings.zoom,
  pitch: viewSettings.pitch,
  bearing: viewSettings.bearing,
  interactive,
  // Add cooperative gestures for better mobile experience
  cooperativeGestures: true,
});
```

### 6. Implement Custom Map Styling

Create a custom map style in Mapbox Studio and use it in your application:
```javascript
// In map-view.tsx, update the map initialization:
map.current = new mapboxgl.Map({
  container: mapContainer.current,
  // Use your custom style URL from Mapbox Studio
  style: "mapbox://styles/yourusername/your-custom-style-id",
  center: [viewSettings.center.longitude, viewSettings.center.latitude],
  zoom: viewSettings.zoom,
  pitch: viewSettings.pitch,
  bearing: viewSettings.bearing,
  interactive,
});
```

## Implementation Plan

To integrate these enhancements, we recommend the following approach:

1. **Install Required Dependencies**:
   ```bash
   pnpm add @mapbox/mapbox-gl-geocoder @mapbox/mapbox-gl-draw @mapbox/mapbox-gl-directions @turf/turf
   ```

2. **Implement Features Incrementally**:
   - Start with the geocoding feature (most useful and easiest to implement)
   - Add the drawing tools for custom boundaries
   - Implement the directions feature
   - Add 3D buildings and terrain
   - Implement cooperative gestures
   - Create and apply a custom map style

3. **Update Environment Variables**:
   - Ensure your Mapbox token has the necessary scopes for these features
   - Consider adding feature flags to enable/disable certain features

4. **Test Thoroughly**:
   - Test each feature individually
   - Test on different devices and browsers
   - Test with different data volumes

## Integration with Existing Features

These enhancements should be integrated with the existing features:

1. **Clustering**: Ensure the drawing tools and filtering work well with the existing clustering functionality
2. **Favorites**: Allow users to save drawn areas as favorites
3. **Saved Views**: Include drawn areas and routing in saved views
4. **Filtering**: Combine drawn area filtering with existing filter options

## Performance Considerations

When implementing these enhancements, consider the following performance aspects:

1. **Lazy Loading**: Load advanced features only when needed
2. **Debouncing**: Implement debouncing for search and filter operations
3. **Caching**: Cache geocoding results and directions
4. **Progressive Enhancement**: Ensure the map works well even if some features fail to load

The current Mapbox integration is already well-structured and follows good practices. These enhancements will add more advanced functionality while maintaining the clean architecture already established.
