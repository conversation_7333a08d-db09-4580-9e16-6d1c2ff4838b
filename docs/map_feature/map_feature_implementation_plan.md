# Map Feature Implementation Plan

**Date: May 1, 2025**

This document outlines the atomic-level, step-by-step plan to implement the map feature in the Scene-o-matic application. The plan is based on the current implementation and the requirements specified by the user.

BE SURE TO CONSULT THIS DOCUMENT FOR THE MAP FEATURES OVERVIEW: docs/map_feature/map_feature_implementation_v1.md

## Table of Contents

1. [Overview](#overview)
2. [Requirements](#requirements)
3. [Implementation Plan](#implementation-plan)
4. [Security Considerations](#security-considerations)
5. [Testing Strategy](#testing-strategy)

## Overview

The map feature is a core component of the Scene-o-matic application, designed to serve as a visual library of ALL locations within the locations database. It allows users to visualize, filter, and interact with locations on a map, with robust filtering capabilities and the ability to share selected locations.

## Requirements

Based on the user's specifications, the map feature should include the following functionality:

1. **Location Visualization**
   - Display all locations from the database on the map
   - Color-code markers based on location type
   - Show location details on hover/click

2. **Interactive Elements**
   - Clicking on a location marker moves the map to center on that location
   - Clicking on a location card title opens a modal with detailed information
   - Clicking the star icon opens a modal to add the location to favorites lists
   - Full-screen mode with collapsible left panel

3. **Filtering Capabilities**
   - Robust filtering system for location managers to sort locations
   - Filter by project, location type, status, etc.

4. **Sharing and Exporting**
   - Share button generates a shareable link with selected locations
   - Export map as PDF with location details

5. **Location Management**
   - Add Location button opens the location creation modal
   - Save views for later reference

6. **Multi-tenant Security**
   - Ensure data separation between organizations
   - Implement proper RBAC for location access

## Implementation Plan

### Phase 1: Core Map Infrastructure

1. **Update MapProvider Component**
   - Connect to real data instead of mock data
   - Implement proper error handling
   - Add loading states

2. **Enhance MapView Component**
   - Implement Mapbox GL JS integration with proper styling
   - Add custom markers with type-based styling
   - Implement hover effects for markers
   - Add clustering for better performance with many locations

3. **Create Location Detail Modal**
   - Design modal based on location detail page
   - Include tabs for gallery, map, documents, notes
   - Implement role-based visibility for sensitive information

4. **Implement Favorites System**
   - Create favorites modal component
   - Design database schema for favorites lists
   - Implement API endpoints for managing favorites
   - Connect star icon to favorites functionality

### Phase 2: Filtering and UI Enhancements

5. **Implement Advanced Filtering**
   - Design filter modal component
   - Implement filter logic in MapProvider
   - Create filter UI with various options:
     - Location type
     - Project
     - Status
     - Date added
     - Custom attributes

6. **Enhance Location List Component**
   - Connect to real data
   - Implement sorting options
   - Add pagination for better performance
   - Highlight selected location

7. **Implement Full-screen Mode**
   - Add full-screen button
   - Create collapsible sidebar
   - Optimize layout for full-screen view

8. **Implement Map Controls**
   - Add zoom controls
   - Add layer toggle controls
   - Add location clustering controls
   - Add map style selector

### Phase 3: Sharing and Integration

9. **Implement View Saving**
   - Design save view modal
   - Create database schema for saved views
   - Implement API endpoints for managing saved views
   - Connect save button to functionality

10. **Implement Sharing Functionality**
    - Create shareable link generator
    - Design shared view page with limited functionality
    - Implement copy to clipboard functionality
    - Add security measures for shared links

11. **Implement Export Functionality**
    - Create PDF export service
    - Design PDF template with location details
    - Implement export button functionality
    - Add progress indicator for export process

12. **Integrate with Location Creation**
    - Connect Add Location button to location creation modal
    - Ensure proper data flow between map and location creation
    - Implement location creation success feedback

### Phase 4: Security and Performance

13. **Implement Multi-tenant Security**
    - Ensure proper organization isolation
    - Implement RBAC checks for all API endpoints
    - Add logging for security events
    - Test security measures thoroughly

14. **Optimize Performance**
    - Implement virtualization for location list
    - Optimize marker rendering for large datasets
    - Add caching for frequently accessed data
    - Implement lazy loading for location details

15. **Add Error Handling and Recovery**
    - Implement comprehensive error handling
    - Add retry mechanisms for failed API calls
    - Create user-friendly error messages
    - Add fallback UI for error states

16. **Finalize Mobile Responsiveness**
    - Test and optimize for various screen sizes
    - Implement touch-friendly controls for mobile
    - Optimize layout for small screens
    - Ensure proper performance on mobile devices

## Atomic Implementation Steps

### 1. Update MapProvider Component

1.1. Modify `fetchLocations` function to use real API data:
```typescript
const fetchLocations = async () => {
  if (!organization) return;

  try {
    setIsLoading(true);
    setError(null);

    // Build query parameters
    const params = new URLSearchParams();
    params.append("organizationId", organization.id);

    if (filters.locationTypes?.length) {
      params.append("locationTypes", filters.locationTypes.join(","));
    }

    if (filters.projectIds?.length) {
      params.append("projectIds", filters.projectIds.join(","));
    }

    if (filters.searchQuery) {
      params.append("searchQuery", filters.searchQuery);
    }

    // Add additional filter parameters
    if (filters.status?.length) {
      params.append("status", filters.status.join(","));
    }

    if (filters.dateRange) {
      params.append("dateFrom", filters.dateRange.from);
      params.append("dateTo", filters.dateRange.to);
    }

    const response = await fetch(`/api/map?${params.toString()}`);

    if (!response.ok) {
      throw new Error(`Failed to fetch locations: ${response.statusText}`);
    }

    const data = await response.json();
    setLocations(data.locations || []);
  } catch (err) {
    console.error("Failed to fetch locations:", err);
    setError("Failed to load locations. Please try again.");
  } finally {
    setIsLoading(false);
  }
};
```

1.2. Add favorites state and functions:
```typescript
const [favoriteLists, setFavoriteLists] = useState<FavoriteList[]>([]);
const [selectedFavoriteList, setSelectedFavoriteList] = useState<string | null>(null);

const fetchFavoriteLists = async () => {
  if (!organization) return;
  
  try {
    const response = await fetch(`/api/favorites?organizationId=${organization.id}`);
    
    if (!response.ok) {
      throw new Error("Failed to fetch favorite lists");
    }
    
    const data = await response.json();
    setFavoriteLists(data || []);
  } catch (err) {
    console.error("Failed to fetch favorite lists:", err);
  }
};

const addLocationToFavorites = async (locationId: string, listId: string) => {
  if (!organization) return;
  
  try {
    const response = await fetch("/api/favorites/add", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        organizationId: organization.id,
        locationId,
        listId,
      }),
    });
    
    if (!response.ok) {
      throw new Error("Failed to add location to favorites");
    }
    
    // Refresh favorite lists
    fetchFavoriteLists();
    
    return true;
  } catch (err) {
    console.error("Failed to add location to favorites:", err);
    return false;
  }
};
```

1.3. Add saved views state and functions:
```typescript
const [savedViews, setSavedViews] = useState<SavedView[]>([]);

const fetchSavedViews = async () => {
  if (!organization) return;
  
  try {
    const response = await fetch(`/api/map/views?organizationId=${organization.id}`);
    
    if (!response.ok) {
      throw new Error("Failed to fetch saved views");
    }
    
    const data = await response.json();
    setSavedViews(data || []);
  } catch (err) {
    console.error("Failed to fetch saved views:", err);
  }
};

const saveCurrentView = async (name: string, description?: string) => {
  if (!organization) return;
  
  try {
    const response = await fetch("/api/map/views", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        organizationId: organization.id,
        name,
        description,
        filters,
        viewSettings,
        selectedLocationIds: selectedLocations.map(loc => loc.id),
      }),
    });
    
    if (!response.ok) {
      throw new Error("Failed to save view");
    }
    
    // Refresh saved views
    fetchSavedViews();
    
    return true;
  } catch (err) {
    console.error("Failed to save view:", err);
    return false;
  }
};
```

### 2. Enhance MapView Component

2.1. Update marker styling:
```typescript
// Create a custom marker element
const createMarkerElement = (location: Location) => {
  const markerElement = document.createElement("div");
  markerElement.className = "marker";
  markerElement.style.width = "30px";
  markerElement.style.height = "30px";
  markerElement.style.borderRadius = "50%";
  
  // Set color based on location type
  const markerColor = getMarkerColor(location.type);
  markerElement.style.backgroundColor = markerColor;
  markerElement.style.border = "2px solid white";
  markerElement.style.boxShadow = `0 0 10px ${markerColor}`;
  markerElement.style.cursor = "pointer";
  
  // Add hover effect
  markerElement.addEventListener("mouseenter", () => {
    markerElement.style.transform = "scale(1.1)";
    markerElement.style.boxShadow = `0 0 15px ${markerColor}`;
  });
  
  markerElement.addEventListener("mouseleave", () => {
    markerElement.style.transform = "scale(1)";
    markerElement.style.boxShadow = `0 0 10px ${markerColor}`;
  });
  
  return markerElement;
};
```

2.2. Implement marker clustering:
```typescript
// Add clustering to the map
useEffect(() => {
  if (!map.current || !mapLoaded) return;
  
  // Add a source for the clustered locations
  map.current.addSource("locations", {
    type: "geojson",
    data: {
      type: "FeatureCollection",
      features: locations.map(location => ({
        type: "Feature",
        geometry: {
          type: "Point",
          coordinates: [location.coordinates.longitude, location.coordinates.latitude]
        },
        properties: {
          id: location.id,
          name: location.name,
          type: location.type,
          status: location.status
        }
      }))
    },
    cluster: true,
    clusterMaxZoom: 14,
    clusterRadius: 50
  });
  
  // Add a layer for the clusters
  map.current.addLayer({
    id: "clusters",
    type: "circle",
    source: "locations",
    filter: ["has", "point_count"],
    paint: {
      "circle-color": [
        "step",
        ["get", "point_count"],
        "#2323FF", // Neon blue for small clusters
        10,
        "#16FF00", // Neon green for medium clusters
        30,
        "#F61981" // Neon pink for large clusters
      ],
      "circle-radius": [
        "step",
        ["get", "point_count"],
        20, // Size for small clusters
        10,
        25, // Size for medium clusters
        30,
        30 // Size for large clusters
      ],
      "circle-stroke-width": 2,
      "circle-stroke-color": "#FFFFFF"
    }
  });
  
  // Add a layer for the cluster counts
  map.current.addLayer({
    id: "cluster-count",
    type: "symbol",
    source: "locations",
    filter: ["has", "point_count"],
    layout: {
      "text-field": "{point_count_abbreviated}",
      "text-font": ["DIN Offc Pro Medium", "Arial Unicode MS Bold"],
      "text-size": 12
    },
    paint: {
      "text-color": "#FFFFFF"
    }
  });
  
  // Add a layer for individual points
  map.current.addLayer({
    id: "unclustered-point",
    type: "circle",
    source: "locations",
    filter: ["!", ["has", "point_count"]],
    paint: {
      "circle-color": [
        "match",
        ["get", "type"],
        "interior", "#2323FF", // Neon blue
        "exterior", "#16FF00", // Neon green
        "studio", "#F61981", // Neon pink
        "#FFED00" // Neon yellow default
      ],
      "circle-radius": 15,
      "circle-stroke-width": 2,
      "circle-stroke-color": "#FFFFFF"
    }
  });
  
  // Handle clicks on clusters
  map.current.on("click", "clusters", (e) => {
    const features = map.current.queryRenderedFeatures(e.point, { layers: ["clusters"] });
    const clusterId = features[0].properties.cluster_id;
    
    map.current.getSource("locations").getClusterExpansionZoom(clusterId, (err, zoom) => {
      if (err) return;
      
      map.current.easeTo({
        center: features[0].geometry.coordinates,
        zoom: zoom
      });
    });
  });
  
  // Handle clicks on individual points
  map.current.on("click", "unclustered-point", (e) => {
    const features = map.current.queryRenderedFeatures(e.point, { layers: ["unclustered-point"] });
    const locationId = features[0].properties.id;
    
    // Find the location in the locations array
    const location = locations.find(loc => loc.id === locationId);
    
    if (location && onLocationSelect) {
      onLocationSelect(location);
    }
  });
  
  // Change cursor on hover
  map.current.on("mouseenter", "clusters", () => {
    map.current.getCanvas().style.cursor = "pointer";
  });
  
  map.current.on("mouseleave", "clusters", () => {
    map.current.getCanvas().style.cursor = "";
  });
  
  map.current.on("mouseenter", "unclustered-point", () => {
    map.current.getCanvas().style.cursor = "pointer";
  });
  
  map.current.on("mouseleave", "unclustered-point", () => {
    map.current.getCanvas().style.cursor = "";
  });
  
  // Clean up on unmount
  return () => {
    if (map.current) {
      if (map.current.getLayer("clusters")) map.current.removeLayer("clusters");
      if (map.current.getLayer("cluster-count")) map.current.removeLayer("cluster-count");
      if (map.current.getLayer("unclustered-point")) map.current.removeLayer("unclustered-point");
      if (map.current.getSource("locations")) map.current.removeSource("locations");
    }
  };
}, [locations, mapLoaded, onLocationSelect]);
```

2.3. Add hover tooltip for markers:
```typescript
// Add a popup for hover
const popup = new mapboxgl.Popup({
  closeButton: false,
  closeOnClick: false,
  offset: 15,
  className: "location-popup"
});

map.current.on("mouseenter", "unclustered-point", (e) => {
  const features = map.current.queryRenderedFeatures(e.point, { layers: ["unclustered-point"] });
  const locationId = features[0].properties.id;
  
  // Find the location in the locations array
  const location = locations.find(loc => loc.id === locationId);
  
  if (location) {
    const coordinates = [location.coordinates.longitude, location.coordinates.latitude];
    
    // Create popup content
    const popupContent = document.createElement("div");
    popupContent.className = "p-2";
    
    const name = document.createElement("h3");
    name.className = "font-medium text-sm mb-1";
    name.textContent = location.name;
    
    const address = document.createElement("p");
    address.className = "text-xs text-muted-foreground";
    address.textContent = location.address.formatted || `${location.address.street}, ${location.address.city}`;
    
    const typeAndStatus = document.createElement("div");
    typeAndStatus.className = "flex gap-2 mt-2";
    
    const typeBadge = document.createElement("span");
    typeBadge.className = "inline-flex items-center rounded-full border-transparent bg-neon-blue/10 text-neon-blue px-2 py-0.5 text-xs font-medium";
    typeBadge.textContent = location.type;
    
    const statusBadge = document.createElement("span");
    statusBadge.className = `inline-flex items-center rounded-full border-transparent px-2 py-0.5 text-xs font-medium ${
      location.status === "approved" ? "bg-neon-green/10 text-neon-green" :
      location.status === "pending" ? "bg-neon-yellow/10 text-deep-black" :
      "bg-neon-pink/10 text-neon-pink"
    }`;
    statusBadge.textContent = location.status;
    
    typeAndStatus.appendChild(typeBadge);
    typeAndStatus.appendChild(statusBadge);
    
    popupContent.appendChild(name);
    popupContent.appendChild(address);
    popupContent.appendChild(typeAndStatus);
    
    // Set popup content and position
    popup.setLngLat(coordinates).setDOMContent(popupContent).addTo(map.current);
  }
});

map.current.on("mouseleave", "unclustered-point", () => {
  popup.remove();
});
```

### 3. Create Location Detail Modal

3.1. Create the modal component:
```tsx
import { useState } from "react"
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import {
  Calendar,
  DollarSign,
  Home,
  Lightbulb,
  MapPin,
  Parking,
  Ruler,
  Truck,
  Wifi,
  X,
} from "lucide-react"
import { LocationGallery } from "@/components/locations/location-gallery"
import { LocationMap } from "@/components/locations/location-map"
import { LocationDocuments } from "@/components/locations/location-documents"
import { LocationNotes } from "@/components/locations/location-notes"
import type { Location } from "@/modules/location/model"

interface LocationDetailModalProps {
  location: Location | null
  isOpen: boolean
  onClose: () => void
  userRole?: string
}

export function LocationDetailModal({ location, isOpen, onClose, userRole = "user" }: LocationDetailModalProps) {
  const [activeTab, setActiveTab] = useState("details")

  if (!location) return null

  return (
    <Dialog open={isOpen} onOpenChange={(open) => !open && onClose()}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader className="flex flex-row items-center justify-between">
          <div>
            <DialogTitle>{location.name}</DialogTitle>
            <p className="text-sm text-muted-foreground">{location.address.formatted}</p>
          </div>
          <div className="flex items-center gap-2">
            <Badge variant="outline">{location.type}</Badge>
            <Badge
              variant={
                location.status === "approved" ? "neonGreen" : 
                location.status === "pending" ? "neonYellow" : 
                "neonPink"
              }
            >
              {location.status}
            </Badge>
            <Button variant="ghost" size="icon" onClick={onClose}>
              <X className="h-4 w-4" />
            </Button>
          </div>
        </DialogHeader>

        <Tabs value={activeTab} onValueChange={setActiveTab} className="mt-4">
          <TabsList className="grid grid-cols-4 mb-4">
            <TabsTrigger value="details">Details</TabsTrigger>
            <TabsTrigger value="gallery">Gallery</TabsTrigger>
            <TabsTrigger value="map">Map</TabsTrigger>
            <TabsTrigger value="documents">Documents</TabsTrigger>
          </TabsList>
          
          <TabsContent value="details">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div className="md:col-span-2">
                <Card>
                  <CardHeader>
                    <CardTitle>Location Details</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      <p>{location.description}</p>

                      <div className="grid grid-cols-2 md:grid-cols-3 gap-4 pt-2">
                        {userRole === "admin" && (
                          <div className="space-y-1">
                            <div className="flex items-center text-muted-foreground">
                              <DollarSign className="h-4 w-4 mr-1" />
                              <span className="text-sm">Daily Rate</span>
                            </div>
                            <p className="font-medium">{location.dailyRate || "Not specified"}</p>
                          </div>
                        )}
                        
                        <div className="space-y-1">
                          <div className="flex items-center text-muted-foreground">
                            <Ruler className="h-4 w-4 mr-1" />
                            <span className="text-sm">Size</span>
                          </div>
                          <p className="font-medium">{location.locationSize || "Not specified"}</p>
                        </div>
                        
                        <div className="space-y-1">
                          <div className="flex items-center text-muted-foreground">
                            <Home className="h-4 w-4 mr-1" />
                            <span className="text-sm">Type</span>
                          </div>
                          <p className="font-medium">{location.type}</p>
                        </div>
                        
                        <div className="space-y-1">
                          <div className="flex items-center text-muted-foreground">
                            <Lightbulb className="h-4 w-4 mr-1" />
                            <span className="text-sm">Power</span>
                          </div>
                          <p className="font-medium">
                            {location.locationFeatures?.includes("power") ? "Available" : "Not specified"}
                          </p>
                        </div>
                        
                        <div className="space-y-1">
                          <div className="flex items-center text-muted-foreground">
                            <Wifi className="h-4 w-4 mr-1" />
                            <span className="text-sm">WiFi</span>
                          </div>
                          <p className="font-medium">
                            {location.locationFeatures?.includes("wifi") ? "Available" : "Not specified"}
                          </p>
                        </div>
                        
                        <div className="space-y-1">
                          <div className="flex items-center text-muted-foreground">
                            <Parking className="h-4 w-4 mr-1" />
                            <span className="text-sm">Parking</span>
                          </div>
                          <p className="font-medium">
                            {location.parkingInfo || (location.locationFeatures?.includes("parking") ? "Available" : "Not specified")}
                          </p>
                        </div>
                        
                        <div className="space-y-1">
                          <div className="flex items-center text-muted-foreground">
                            <Truck className="h-4 w-4 mr-1" />
                            <span className="text-sm">Access</span>
                          </div>
                          <p className="font-medium">{location.accessibility || "Not specified"}</p>
                        </div>
                        
                        <div className="space-y-1">
                          <div className="flex items-center text-muted-foreground">
                            <MapPin className="h-4 w-4 mr-1" />
                            <span className="text-sm">Permits</span>
                          </div>
                          <p className="font-medium">
                            {location.permitsRequired ? "Required" : "Not required"}
                          </p>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>
              
              <div>
                <Card>
                  <CardHeader>
                    <CardTitle>Contact Information</CardTitle>
                  </CardHeader>
                  <CardContent>
                    {userRole === "admin" ? (
                      <div className="space-y-4">
                        <div className="flex items-center gap-4">
                          <Avatar className="h-10 w-10">
                            <AvatarImage src="/placeholder.svg?height=40&width=40" alt="Contact" />
                            <AvatarFallback>{location.contactName?.substring(0, 2) || "CN"}</AvatarFallback>
                          </Avatar>
                          <div>
                            <p className="font-medium">{location.contactName || "Not specified"}</p>
                            <p className="text-sm text-muted-foreground">Property Manager</p>
                          </div>
                        </div>

                        <div className="space-y-2">
                          <p className="text-sm">{location.contactPhone || "No phone specified"}</p>
                          <p className="text-sm">{location.contactEmail || "No email specified"}</p>
                        </div>

                        <Button variant="outline" className="w-full">
                          Contact
                        </Button>
                      </div>
                    ) : (
                      <div className="space-y-4">
                        <div className="flex items-center justify-center p-4">
                          <div className="text-center">
                            <p className="text-muted-foreground mb-4">Contact information is only visible to administrators</p>
                            <Button variant="outline" className="w-full">
                              Request Contact Info
                            </Button>
                          </div>
                        </div>
                      </div>
                    )}
                  </CardContent>
                </Card>
              </div>
            </div>
          </TabsContent>
          
          <TabsContent value="gallery">
            <LocationGallery media={[]} />
          </TabsContent>
          
          <TabsContent value="map">
            <LocationMap location={location} />
          </TabsContent>
          
          <TabsContent value="documents">
            <LocationDocuments documents={[]} />
          </TabsContent>
        </Tabs>
      </DialogContent>
    </Dialog>
  )
}
```

3.2. Create the favorites modal component:
```tsx
import { useState } from "react"
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Loader2, Plus, Star } from "lucide-react"
import type { Location } from "@/modules/location/model"

interface FavoriteList {
  id: string
  name: string
  type: "project" | "scene" | "custom"
  projectId?: string
  sceneId?: string
}

interface AddToFavoritesModalProps {
  location: Location | null
  isOpen: boolean
  onClose: () => void
  favoriteLists: FavoriteList[]
  onAddToFavorites: (locationId: string, listId: string) => Promise<boolean>
  onCreateList: (name: string, type: "project" | "scene" | "custom", projectId?: string, sceneId?: string) => Promise<string | null>
}

export function AddToFavoritesModal({
  location,
  isOpen,
  onClose,
  favoriteLists,
  onAddToFavorites,
  onCreateList,
}: AddToFavoritesModalProps) {
  const [selectedListId, setSelectedListId] = useState<string>("")
  const [isCreatingList, setIsCreatingList] = useState(false)
  const [newListName, setNewListName] = useState("")
  const [newListType, setNewListType] = useState<"project" | "scene" | "custom">("custom")
  const [selectedProjectId, setSelectedProjectId] = useState<string>("")
  const [selectedSceneId, setSelectedSceneId] = useState<string>("")
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  if (!location) return null

  const handleAddToFavorites = async () => {
    if (!selectedListId) {
      setError("Please select a list or create a new one");
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      const success = await onAddToFavorites(location.id, selectedListId);
      
      if (success) {
        onClose();
      } else {
        setError("Failed to add location to favorites");
      }
    } catch (err) {
      setError("An error occurred");
    } finally {
      setIsLoading(false);
    }
  };

  const handleCreateList = async () => {
    if (!newListName) {
      setError("Please enter a list name");
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
