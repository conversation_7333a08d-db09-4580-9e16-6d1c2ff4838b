# Location Details Page Refactoring Plan

**Date: May 5, 2025**

This document outlines the step-by-step plan for refactoring the location details page to use real API data instead of mock data and to implement the sharing feature similar to the map sharing feature.

## Table of Contents

1. [Overview](#overview)
2. [Current Implementation Analysis](#current-implementation-analysis)
3. [Refactoring Goals](#refactoring-goals)
4. [Step-by-Step Implementation Plan](#step-by-step-implementation-plan)
5. [Sharing Feature Implementation](#sharing-feature-implementation)
6. [Code Organization and Best Practices](#code-organization-and-best-practices)
7. [Testing Strategy](#testing-strategy)

## Overview

The location details page currently uses mock data to display information about a location. The page needs to be refactored to use real API data from the backend and to implement a sharing feature that allows users to share a location with others without requiring them to have an account.

The refactoring will involve:
1. Connecting the page to the real API data using the `useLocationDetails` hook
2. Implementing proper loading, error, and empty states
3. Refactoring the page to handle both admin and client view modes
4. Implementing the sharing feature similar to the map sharing feature
5. Ensuring proper RBAC checks for sensitive operations

## Current Implementation Analysis

The current location details page (`app/(dashboard)/organizations/[organizationSlug]/locations/[id]/page.tsx`) has the following characteristics:

1. **Data Source**: Uses hardcoded mock data instead of real API data
2. **View Modes**: Has a toggle for admin and user views, but it's client-side only
3. **UI Components**: 
   - Main location information card
   - Tabs for gallery, map, documents, and notes
   - Sidebar with contact information, shooting schedule, and project details
4. **Missing Features**:
   - No connection to real API data
   - No sharing functionality
   - No proper loading, error, or empty states
   - No RBAC checks for sensitive operations

## Refactoring Goals

1. **Connect to Real API Data**: Use the `useLocationDetails` hook to fetch real location data
2. **Implement View Modes**: Properly implement admin and client view modes based on user permissions
3. **Add Sharing Feature**: Implement a sharing feature similar to the map sharing feature
4. **Improve UX**: Add proper loading, error, and empty states
5. **Ensure Security**: Implement proper RBAC checks for sensitive operations
6. **Optimize Performance**: Ensure efficient data fetching and rendering
7. **Maintain Code Quality**: Follow project coding standards and best practices

## Step-by-Step Implementation Plan

### 1. Refactor Page Structure

The current page is becoming too large and complex. We should refactor it into smaller, more manageable components:

1. **Create Client Component**: 
   - Create a new client component `LocationDetailsClient` that will handle the data fetching and rendering
   - Move the main rendering logic from the page component to this client component
   - The page component will simply render this client component with the necessary props

2. **Extract Sub-Components**:
   - Extract the location header into a separate component
   - Extract the location details card into a separate component
   - Extract the location sidebar into a separate component
   - Keep the existing tab components (gallery, map, documents, notes)

### 2. Connect to Real API Data

1. **Use Location Details Hook**:
   - Import and use the `useLocationDetails` hook to fetch location data
   - Pass the location ID from the URL params to the hook
   - Handle loading, error, and data states

2. **Implement Loading State**:
   - Create a skeleton loader for the location details page
   - Show the skeleton loader while data is being fetched

3. **Implement Error State**:
   - Create an error component to display when data fetching fails
   - Include a retry button to allow users to retry the data fetch

4. **Implement Empty State**:
   - Create an empty state component to display when no location is found
   - Include a link to the locations list page

### 3. Implement View Modes

1. **Determine User Role**:
   - Use the `usePermissions` hook to determine the user's role and permissions
   - Set the view mode based on the user's permissions

2. **Implement Admin View**:
   - Show all location details, including sensitive information
   - Enable editing, deletion, and approval actions
   - Show full contact information

3. **Implement Client View**:
   - Hide sensitive information (pricing, contact details, internal notes)
   - Disable editing, deletion, and approval actions
   - Show limited contact information
   - Focus on visual presentation

4. **Add View Mode Toggle**:
   - Allow users with appropriate permissions to toggle between admin and client views
   - Persist the user's preference

### 4. Implement Sharing Feature

1. **Create Share Location Modal**:
   - Create a new component `ShareLocationModal` based on the `ShareMapModal`
   - Adapt the modal to work with location data instead of map data

2. **Create Share Location API Endpoint**:
   - Create a new API endpoint `/api/locations/share` for creating shared location links
   - Implement password protection and expiration options
   - Generate a unique token for the shared link

3. **Create Shared Location View**:
   - Create a new page `/shared/location/[token]` for viewing shared locations
   - Implement password verification if the link is password protected
   - Show the location details in client view mode

4. **Add Share Button**:
   - Add a share button to the location details page
   - Open the share modal when the button is clicked

### 5. Enhance Location Actions

1. **Implement Edit Action**:
   - Connect the edit button to the location edit page
   - Pass the location ID as a parameter

2. **Implement Delete Action**:
   - Create a confirmation dialog for deletion
   - Use the `useLocationMutations` hook to delete the location
   - Redirect to the locations list page after deletion

3. **Implement Approval Workflow**:
   - Add buttons for approving, rejecting, and securing locations
   - Use the `useLocationMutations` hook for these actions
   - Show confirmation dialogs for these actions
   - Update the UI to reflect the new status

### 6. Optimize Performance

1. **Implement Data Prefetching**:
   - Use Next.js data fetching mechanisms to prefetch location data
   - Implement proper caching strategies

2. **Optimize Component Rendering**:
   - Use React.memo for pure components
   - Implement useMemo and useCallback for expensive computations and callbacks
   - Avoid unnecessary re-renders

3. **Lazy Load Components**:
   - Lazy load the tab contents to improve initial load time
   - Implement code splitting for large components

## Sharing Feature Implementation

The sharing feature will be similar to the map sharing feature but adapted for locations. Here's a detailed plan for implementing it:

### 1. Create Share Location Modal Component

```typescript
// components/locations/share-location-modal.tsx
"use client"

import { useState } from "react"
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter } from "@/components/ui/dialog"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Checkbox } from "@/components/ui/checkbox"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Loader2, Copy, Check, Link, Lock } from "lucide-react"
import { useOrganization } from "@/hooks/use-organization-data"
import { toast } from "sonner"

interface ShareLocationModalProps {
  isOpen: boolean
  onClose: () => void
  locationId: string
  locationName: string
}

export function ShareLocationModal({ isOpen, onClose, locationId, locationName }: ShareLocationModalProps) {
  const { organization } = useOrganization()
  
  const [isLoading, setIsLoading] = useState(false)
  const [shareUrl, setShareUrl] = useState<string | null>(null)
  const [copied, setCopied] = useState(false)
  const [password, setPassword] = useState("")
  const [isPasswordProtected, setIsPasswordProtected] = useState(false)
  const [expirationOption, setExpirationOption] = useState<string>("never")
  const [viewMode, setViewMode] = useState<string>("client")
  
  // Reset state when modal opens/closes
  const handleOpenChange = (open: boolean) => {
    if (!open) {
      setShareUrl(null)
      setCopied(false)
      setPassword("")
      setIsPasswordProtected(false)
      setExpirationOption("never")
      setViewMode("client")
      onClose()
    }
  }
  
  // Calculate expiration time in seconds based on selected option
  const getExpirationTime = (): number | undefined => {
    switch (expirationOption) {
      case "1hour":
        return 60 * 60 // 1 hour in seconds
      case "1day":
        return 60 * 60 * 24 // 1 day in seconds
      case "7days":
        return 60 * 60 * 24 * 7 // 7 days in seconds
      case "30days":
        return 60 * 60 * 24 * 30 // 30 days in seconds
      default:
        return undefined // No expiration
    }
  }
  
  const handleCreateShareLink = async () => {
    if (!organization) return
    
    setIsLoading(true)
    
    try {
      const payload = {
        organizationId: organization.id,
        locationId: locationId,
        viewMode: viewMode,
        expiresIn: getExpirationTime(),
        password: isPasswordProtected ? password : undefined,
      }
      
      const response = await fetch('/api/locations/share', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(payload),
      })
      
      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}))
        throw new Error(errorData.error || "Failed to create share link")
      }
      
      const data = await response.json()
      setShareUrl(data.url)
      toast.success("Share link created successfully")
    } catch (error) {
      console.error("Error creating share link:", error)
      toast.error(error instanceof Error ? error.message : "Failed to create share link")
    } finally {
      setIsLoading(false)
    }
  }
  
  const handleCopyLink = () => {
    if (!shareUrl) return
    
    navigator.clipboard.writeText(shareUrl)
      .then(() => {
        setCopied(true)
        toast.success("Link copied to clipboard")
        
        // Reset copied state after 2 seconds
        setTimeout(() => {
          setCopied(false)
        }, 2000)
      })
      .catch((error) => {
        console.error("Error copying link:", error)
        toast.error("Failed to copy link")
      })
  }

  return (
    <Dialog open={isOpen} onOpenChange={handleOpenChange}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>Share Location: {locationName}</DialogTitle>
        </DialogHeader>
        
        {!shareUrl ? (
          <div className="space-y-4 py-4">
            <div className="text-sm text-muted-foreground">
              Share this location with others. They will be able to view the location details without needing an account.
            </div>
            
            <div className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="view-mode">View Mode</Label>
                <Select value={viewMode} onValueChange={setViewMode}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select view mode" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="client">Client View (Limited Details)</SelectItem>
                    <SelectItem value="admin">Admin View (Full Details)</SelectItem>
                  </SelectContent>
                </Select>
                <p className="text-xs text-muted-foreground">
                  {viewMode === "client" 
                    ? "Client view hides sensitive information like pricing and contact details."
                    : "Admin view shows all location details including sensitive information."}
                </p>
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="expiration">Expiration</Label>
                <Select value={expirationOption} onValueChange={setExpirationOption}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select expiration time" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="never">Never</SelectItem>
                    <SelectItem value="1hour">1 Hour</SelectItem>
                    <SelectItem value="1day">1 Day</SelectItem>
                    <SelectItem value="7days">7 Days</SelectItem>
                    <SelectItem value="30days">30 Days</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              
              <div className="space-y-2">
                <div className="flex items-center space-x-2">
                  <Checkbox 
                    id="password-protection" 
                    checked={isPasswordProtected}
                    onCheckedChange={(checked) => setIsPasswordProtected(checked === true)}
                  />
                  <Label htmlFor="password-protection">Password Protection</Label>
                </div>
                
                {isPasswordProtected && (
                  <Input
                    type="password"
                    placeholder="Enter password"
                    value={password}
                    onChange={(e) => setPassword(e.target.value)}
                  />
                )}
              </div>
            </div>
          </div>
        ) : (
          <div className="py-6 space-y-4">
            <div className="text-sm text-muted-foreground">
              Your location is now shared! Use the link below to share it with others.
            </div>
            
            <div className="flex items-center space-x-2">
              <div className="relative flex-1">
                <Input
                  value={shareUrl}
                  readOnly
                  className="pr-10"
                />
                <Link className="absolute right-3 top-2.5 h-4 w-4 text-muted-foreground" />
              </div>
              <Button 
                size="icon" 
                onClick={handleCopyLink}
                disabled={copied}
              >
                {copied ? <Check className="h-4 w-4" /> : <Copy className="h-4 w-4" />}
              </Button>
            </div>
            
            {isPasswordProtected && (
              <div className="text-sm text-amber-500 flex items-center space-x-2">
                <Lock className="h-4 w-4" />
                <span>This link is password protected. Recipients will need the password to access it.</span>
              </div>
            )}
          </div>
        )}
        
        <DialogFooter>
          {!shareUrl ? (
            <>
              <Button variant="outline" onClick={onClose} disabled={isLoading}>
                Cancel
              </Button>
              <Button 
                onClick={handleCreateShareLink} 
                disabled={isLoading || (isPasswordProtected && !password.trim())}
              >
                {isLoading ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Creating...
                  </>
                ) : (
                  "Create Share Link"
                )}
              </Button>
            </>
          ) : (
            <Button onClick={onClose}>
              Done
            </Button>
          )}
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
```

### 2. Create Share Location API Endpoint

```typescript
// app/api/locations/share/route.ts
import type { NextRequest } from "next/server"
import { NextResponse } from "next/server"
import { checkPermission } from "@/lib/rbac/checkPermission"
import { getAuthParamsFromRequest } from "@/lib/utils/authUtils"
import { extractUuidFromStytchId } from "@/lib/utils/stytchUtils"
import { z } from "zod"
import { createSharedLocationLink, getSharedLocationLinksForOrganization, deleteSharedLocationLink } from "@/modules/location/service"

// Define schema for creating shared location links
const createSharedLocationLinkSchema = z.object({
  organizationId: z.string(),
  locationId: z.string().uuid(),
  viewMode: z.enum(["client", "admin"]).default("client"),
  expiresIn: z.number().optional(),
  password: z.string().optional(),
})

// POST /api/locations/share - Create a new shared location link
export async function POST(req: NextRequest) {
  try {
    // Authenticate the request
    const authParams = getAuthParamsFromRequest(req)
    const stytchOrgId = req.headers.get('X-Stytch-Org-Id')
    const memberId = req.headers.get('X-Stytch-Member-Id')

    if (!authParams || !stytchOrgId || !memberId) {
      console.warn('RBAC check failed: Missing Auth Params, Org ID, or Member ID header')
      return NextResponse.json({ error: 'Forbidden - Missing Authentication, Organization, or Member Context' }, { status: 403 })
    }

    // Check if the user has permission to share locations
    const hasPermission = await checkPermission(
      authParams,
      stytchOrgId,
      'share',
      'location'
    )

    if (!hasPermission) {
      return NextResponse.json({ error: "Forbidden" }, { status: 403 })
    }

    // Parse and validate request body
    const body = await req.json()
    const result = createSharedLocationLinkSchema.safeParse(body)
    
    if (!result.success) {
      return NextResponse.json(
        { error: "Invalid request body", details: result.error.format() },
        { status: 400 }
      )
    }
    
    // Extract UUID from Stytch organization ID
    const organizationId = extractUuidFromStytchId(result.data.organizationId)
    
    if (!organizationId) {
      return NextResponse.json({ error: "Invalid organization ID" }, { status: 400 })
    }
    
    // Extract UUID from Stytch member ID
    const userId = extractUuidFromStytchId(memberId)
    
    if (!userId) {
      return NextResponse.json({ error: "Invalid member ID" }, { status: 400 })
    }
    
    // Create shared location link
    const sharedLink = await createSharedLocationLink(
      organizationId,
      userId,
      {
        locationId: result.data.locationId,
        viewMode: result.data.viewMode,
        password: result.data.password,
        expiresIn: result.data.expiresIn,
      }
    )
    
    return NextResponse.json(sharedLink)
  } catch (error) {
    console.error("Error creating shared location link:", error)
    return NextResponse.json(
      { error: "Failed to create shared location link" },
      { status: 500 }
    )
  }
}

// GET /api/locations/share - Get all shared location links for an organization
export async function GET(req: NextRequest) {
  try {
    // Authenticate the request
    const authParams = getAuthParamsFromRequest(req)
    const stytchOrgId = req.headers.get('X-Stytch-Org-Id')

    if (!authParams || !stytchOrgId) {
      console.warn('RBAC check failed: Missing Auth Params or Stytch Org ID header')
      return NextResponse.json({ error: 'Forbidden - Missing Authentication or Organization Context' }, { status: 403 })
    }

    // Get the organization ID from the query parameters
    const { searchParams } = new URL(req.url)
    const organizationIdParam = searchParams.get("organizationId")

    if (!organizationIdParam) {
      return NextResponse.json({ error: "Organization ID is required" }, { status: 400 })
    }
    
    // Extract UUID from Stytch organization ID
    const organizationId = extractUuidFromStytchId(organizationIdParam)
    
    if (!organizationId) {
      return NextResponse.json({ error: "Invalid organization ID" }, { status: 400 })
    }

    // Check if the user has permission to view shared location links
    const hasPermission = await checkPermission(
      authParams,
      stytchOrgId,
      'view',
      'location'
    )

    if (!hasPermission) {
      return NextResponse.json({ error: "Forbidden" }, { status: 403 })
    }

    // Get shared location links
    const links = await getSharedLocationLinksForOrganization(organizationId)

    return NextResponse.json(links)
  } catch (error) {
    console.error("Error getting shared location links:", error)
    return NextResponse.json(
      { error: "Failed to get shared location links" },
      { status: 500 }
    )
  }
}

// DELETE /api/locations/share - Delete a shared location link
export async function DELETE(req: NextRequest) {
  try {
    // Authenticate the request
    const authParams = getAuthParamsFromRequest(req)
    const stytchOrgId = req.headers.get('X-Stytch-Org-Id')

    if (!authParams || !stytchOrgId) {
      console.warn('RBAC check failed: Missing Auth Params or Stytch Org ID header')
      return NextResponse.json({ error: 'Forbidden - Missing Authentication or Organization Context' }, { status: 403 })
    }

    // Check if the user has permission to delete shared location links
    const hasPermission = await checkPermission(
      authParams,
      stytchOrgId,
      'delete',
      'location'
    )

    if (!hasPermission) {
      return NextResponse.json({ error: "Forbidden" }, { status: 403 })
    }

    // Get link ID from URL
    const url = new URL(req.url)
    const id = url.searchParams.get("id")
    const organizationIdParam = url.searchParams.get("organizationId")
    
    if (!id) {
      return NextResponse.json(
        { error: "Missing link ID" },
        { status: 400 }
      )
    }
    
    if (!organizationIdParam) {
      return NextResponse.json({ error: "Organization ID is required" }, { status: 400 })
    }
    
    // Extract UUID from Stytch organization ID
    const organizationId = extractUuidFromStytchId(organizationIdParam)
    
    if (!organizationId) {
      return NextResponse.json({ error: "Invalid organization ID" }, { status: 400 })
    }
    
    // Delete shared location link
    const success = await deleteSharedLocationLink(id, organizationId)
    
    if (!success) {
      return NextResponse.json(
        { error: "Shared location link not found" },
        { status: 404 }
      )
    }
    
    return NextResponse.json({ success: true })
  } catch (error) {
    console.error("Error deleting shared location link:", error)
    return NextResponse.json(
      { error: "Failed to delete shared location link" },
      { status: 500 }
    )
  }
}
```

### 3. Create Shared Location View Page

```typescript
// app/shared/location/[token]/page.tsx
import { SharedLocationView } from "@/components/locations/shared-location-view"
import type { Metadata } from "next"

interface SharedLocationPageProps {
  params: {
    token: string
  }
}

export const metadata: Metadata = {
  title: "Shared Location | Scene-o-matic",
  description: "View a shared location from Scene-o-matic",
}

export default function SharedLocationPage({ params }: SharedLocationPageProps) {
  return <SharedLocationView token={params.token} />
}
```

### 4. Create Shared Location View Component

```typescript
// components/locations/shared-location-view.tsx
"use client"

import { useState, useEffect, useCallback } from "react"
import { useRouter } from "next/navigation"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter } from "@/components/ui/dialog"
import { Loader2, Lock, ArrowLeft } from "lucide-react"
import { toast } from "sonner"
import { LocationDetailsClient } from "@/components/locations/location-details-client"

interface SharedLocationViewProps {
  token: string
}

type SharedLocationData = {
  id: string
  organizationId: string
  location: any // Use proper type from location model
  viewMode: string
  isPasswordProtected: boolean
}

export function SharedLocationView({ token }: SharedLocationViewProps) {
  const router = useRouter()
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [locationData, setLocationData] = useState<SharedLocationData | null>(null)
  const [isPasswordModalOpen, setIsPasswordModalOpen] = useState(false)
  const [password, setPassword] = useState("")
  const [isSubmitting, setIsSubmitting] = useState(false)

  const fetchLocationData = useCallback(async (pwd?: string) => {
    try {
      setIsLoading(true)
      setError(null)
      
      const url = pwd 
        ? `/api/locations/shared/${token}?password=${encodeURIComponent(pwd)}`
        : `/api/locations/shared/${token}`
      
      const response = await fetch(url)
      
      if (!response.ok) {
        const data = await response.json()
        
        if (response.status === 401 && data.isPasswordProtected) {
          setIsPasswordModalOpen(true)
          return
        }
        
        throw new Error(data.error || "Failed to load shared location")
      }
      
      const data = await response.json()
      setLocationData(data)
    } catch (err) {
      console.error("Error loading shared location:", err)
      setError(err instanceof Error ? err.message : "Failed to load shared location")
      toast.error(err instanceof Error ? err.message : "Failed to load shared location")
    } finally {
      setIsLoading(false)
    }
  }, [token])
  
  const handlePasswordSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!password.trim()) {
      toast.error("Please enter a password")
      return
    }
    
    setIsSubmitting(true)
    
    try {
      await fetchLocationData(password)
      setIsPasswordModalOpen(false)
    } finally {
      setIsSubmitting(false)
    }
  }
  
  const handleBackToApp = () => {
    router.push("/")
  }

  useEffect(() => {
    fetchLocationData()
  }, [fetchLocationData])

  if (isLoading) {
    return (
      <div className="flex flex-col items-center justify-center min-h-screen">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
        <p className="mt-4 text-muted-foreground">Loading shared location...</p>
      </div>
    )
  }

  if (error) {
    return (
      <div className="flex flex-col items-center justify-center min-h-screen">
        <div className="text-center max-w-md mx-auto p-6 space-y-4">
          <h2 className="text-2xl font-bold">Error Loading Location</h2>
          <p className="text-muted-foreground">{error}</p>
          <Button onClick={handleBackToApp}>
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to App
          </Button>
        </div>
      </div>
    )
  }

  return (
    <>
      <div className="flex flex-col min-h-screen">
        <header className="bg-background border-b p-4 flex items-center justify-between">
          <div className="flex items-center">
            <h1 className="text-xl font-bold">Scene-o-matic Shared Location</h1>
          </div>
          <Button variant="outline" onClick={handleBackToApp}>
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to App
          </Button>
        </header>
        
        <main className="flex-1 container py-6">
          {locationData ? (
            <LocationDetailsClient 
              location={locationData.location}
              viewMode={locationData.viewMode}
              isSharedView={true}
            />
          ) : (
            <div className="flex items-center justify-center h-full">
              <p className="text-muted-foreground">No location data available</p>
            </div>
          )}
        </main>
      </div>
      
      <Dialog open={isPasswordModalOpen} onOpenChange={setIsPasswordModalOpen}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle className="flex items-center">
              <Lock className="mr-2 h-4 w-4" />
              Password Protected Location
            </DialogTitle>
          </DialogHeader>
          
          <form onSubmit={handlePasswordSubmit} className="space-y-4 py-4">
            <p className="text-sm text-muted-foreground">
              This shared location is password protected. Please enter the password to view it.
            </p>
            
            <Input
              type="password"
              placeholder="Enter password"
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              disabled={isSubmitting}
              autoFocus
            />
            
            <DialogFooter>
              <Button type="button" variant="outline" onClick={handleBackToApp} disabled={isSubmitting}>
                Cancel
              </Button>
              <Button type="submit" disabled={isSubmitting || !password.trim()}>
                {isSubmitting ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Verifying...
                  </>
                ) : (
                  "Access Location"
                )}
              </Button>
            </DialogFooter>
          </form>
        </DialogContent>
      </Dialog>
    </>
  )
}
```

### 5. Create Shared Location API Endpoint

```typescript
// app/api/locations/shared/[token]/route.ts
import type { NextRequest } from "next/server"
import { NextResponse } from "next/server"
import { z } from "zod"
import { getSharedLocationLinkByToken, logSharedLocationLinkAccess } from "@/modules/location/service"

// Define schema for accessing shared location links
const accessSharedLocationLinkSchema = z.object({
  token: z.
