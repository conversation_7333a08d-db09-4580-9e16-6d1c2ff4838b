# Locations Feature Implementation Master Guide

**Date: May 4, 2025**

This document serves as the master guide for implementing the locations feature in the Scene-o-matic application. It provides instructions for the AI coding agent to follow when working on the locations feature implementation plan.

BE SURE TO CONSULT THIS DOCUMENT FOR THE LOCATIONS FEATURES OVERVIEW: docs/locations_feature/locations_feature_implementation_v1.md

The step by step implementation guide is available here: docs/locations_feature/locations_feature_implementation_plan.md

## Table of Contents

1. [Implementation Approach](#implementation-approach)
2. [Step-by-Step Process](#step-by-step-process)
3. [Context Maintenance](#context-maintenance)
4. [Build Log Management](#build-log-management)
5. [Testing Guidelines](#testing-guidelines)
6. [Security Best Practices](#security-best-practices)
7. [Code Quality Standards](#code-quality-standards)

## Implementation Approach

The implementation of the locations feature must follow these core principles:

1. **One Step at a Time**: Work on only ONE step from the implementation plan at a time. Complete it fully before moving to the next step.

2. **Incremental Progress**: Each step should result in a working state of the application, even if the feature is not fully implemented.

3. **Thorough Testing**: Test each step thoroughly before considering it complete.

4. **Documentation**: Document all changes and decisions in the build log.

5. **Security First**: Always consider security implications of each change.

6. **Multi-tenant Awareness**: Ensure all changes respect the multi-tenant nature of the application.

## Step-by-Step Process

For each step in the implementation plan, follow this process:

### 1. Preparation

1. **Review the Current State**:
   - Read the latest build log entry to understand the current state of the implementation
   - Review the code that will be modified or extended
   - Understand the requirements for the current step

2. **Plan the Implementation**:
   - Break down the step into smaller tasks if necessary
   - Identify potential challenges or edge cases
   - Determine the testing approach

### 2. Implementation

1. **Work on ONE Step Only**:
   - Focus exclusively on the current step from the implementation plan
   - Do not attempt to implement multiple steps at once
   - If dependencies are discovered, note them but stay focused on the current step

2. **Follow Best Practices**:
   - Adhere to the project's coding standards
   - Use TypeScript types and interfaces
   - Implement proper error handling
   - Add comments for complex logic

3. **Implement Security Measures**:
   - Add RBAC checks where appropriate
   - Validate all inputs
   - Ensure organization isolation

### 3. Testing

1. **Test the Implementation**:
   - Write unit tests for new components and functions
   - Test the feature in isolation
   - Test integration with existing features
   - Verify security measures

2. **Verify Requirements**:
   - Ensure all requirements for the step are met
   - Check for edge cases
   - Verify performance

### 4. Documentation

1. **Update the Build Log**:
   - Document the completed step
   - Note any challenges or decisions made
   - Include context that will be helpful for future steps
   - Update the status of the implementation

2. **Code Documentation**:
   - Add JSDoc comments to functions and components
   - Update README files if necessary
   - Document API endpoints

## Context Maintenance

To maintain context between sessions:

1. **Always Read the Build Log First**:
   - Before starting work, read the entire build log to understand the current state
   - Pay special attention to the most recent entries

2. **Review Implementation Documents**:
   - Refer to the implementation plan documents:
     - `docs/locations_feature/locations_feature_implementation_v1.md`
     - `docs/locations_feature/locations_feature_implementation_plan.md`

3. **Understand the Codebase**:
   - Review the relevant components and files
   - Understand how they interact with each other
   - Note any changes since the last session

4. **Maintain State Awareness**:
   - Be aware of the current state of the implementation
   - Understand which steps have been completed and which are pending
   - Know the dependencies between steps

## Build Log Management

The build log is a critical tool for maintaining context. For each step:

1. **Create a New Entry**:
   - Add a new entry to the build log after completing a step
   - Use the date and step number as the heading

2. **Document What Was Done**:
   - Describe the changes made
   - List the files modified
   - Explain the implementation approach

3. **Note Challenges and Solutions**:
   - Document any challenges encountered
   - Explain how they were resolved
   - Note any workarounds or technical debt

4. **Provide Context for Next Steps**:
   - Include information that will be helpful for the next steps
   - Note any dependencies or considerations
   - Highlight any areas that need attention

5. **Update Status**:
   - Mark the step as completed
   - Update the overall progress
   - Note any changes to the plan

## Testing Guidelines

Follow these testing guidelines for each step:

1. **Unit Testing**:
   - Write unit tests for all new components and functions
   - Test edge cases and error handling
   - Ensure high test coverage

2. **Integration Testing**:
   - Test integration with existing components
   - Verify data flow between components
   - Test API endpoints

3. **Security Testing**:
   - Test RBAC enforcement
   - Verify organization isolation
   - Test input validation

4. **Performance Testing**:
   - Test with realistic data volumes
   - Verify rendering performance
   - Check for memory leaks

5. **Accessibility Testing**:
   - Ensure keyboard navigation
   - Verify screen reader compatibility
   - Check color contrast

## Security Best Practices

Adhere to these security best practices:

1. **Data Isolation**:
   - Always filter data by organization ID
   - Implement RBAC checks for all operations
   - Validate user permissions

2. **Input Validation**:
   - Validate all inputs using Zod schemas
   - Sanitize user inputs
   - Implement proper error handling

3. **Authentication and Authorization**:
   - Ensure all endpoints require authentication
   - Implement proper authorization checks
   - Use secure tokens

4. **Secure Coding**:
   - Avoid common security pitfalls
   - Follow OWASP guidelines
   - Implement proper error handling

## Code Quality Standards

Maintain high code quality standards:

1. **TypeScript Best Practices**:
   - Use proper types and interfaces
   - Avoid `any` type
   - Leverage TypeScript features

2. **React Best Practices**:
   - Use functional components
   - Implement proper state management
   - Optimize rendering

3. **Code Organization**:
   - Follow the project's folder structure
   - Organize code logically
   - Use meaningful file and component names

4. **Performance Considerations**:
   - Optimize rendering
   - Implement virtualization for large lists
   - Use memoization where appropriate

5. **Error Handling**:
   - Implement comprehensive error handling
   - Provide user-friendly error messages
   - Log errors for debugging

By following this master guide, the AI coding agent will be able to implement the locations feature in a structured, secure, and maintainable way, while maintaining context between sessions.

Once you have reviewed this guide, proceed to the next step to be completed according to the implementation plan.
