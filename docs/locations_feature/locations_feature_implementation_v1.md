# Locations Feature Implementation v1

**Date: May 4, 2025**

This document provides a comprehensive analysis of the current implementation of the locations feature in the Scene-o-matic application. It serves as a baseline for understanding the existing code structure, components, and functionality before implementing enhancements.

## Overview

The locations feature is a core component of the Scene-o-matic application, allowing users to manage filming locations for film and TV productions. The feature is partially implemented, with a robust backend but a frontend that currently uses mock data instead of connecting to the backend APIs.

## Current Implementation Analysis

### Backend Implementation

#### Database Schema (`modules/location/schema.ts`)

The locations table is well-defined with the following key fields:
- `id`: UUID primary key
- `name`: Location name (varchar)
- `description`: Text description
- `status`: Current status (e.g., 'Pending', 'Approved', 'Rejected')
- `address`: JSON object containing address details
- `coordinates`: JSON object for geospatial coordinates
- `type`: Location type (varchar)
- `projectId`: Foreign key to projects table
- `organizationId`: Foreign key to organizations table
- `creatorId`: Foreign key to users table
- `approvedBy`: Foreign key to users table (for approval workflow)
- `approvedAt`: Timestamp for approval
- `metadata`: JSON field for additional data
- `area`: Decimal field for location area
- `isActive`: Boolean flag
- Standard timestamps: `createdAt`, `updatedAt`, `deletedAt`

The schema also defines relations to:
- Projects
- Organizations
- Users (creator and approver)
- Documents
- Tasks

#### Data Model (`modules/location/model.ts`)

The location model defines TypeScript types and Zod validation schemas:

- `Location`: Main type with all location properties
- `Address`: Type for address structure
- `Coordinates`: Type for latitude/longitude
- `LocationType` and `LocationStatus`: Types for predefined values

Validation schemas include:
- `locationSchema`: Complete schema for locations
- `createLocationSchema`: Schema for creating locations
- `updateLocationSchema`: Schema for updating locations
- `locationIdSchema`: Schema for location ID validation

#### Service Layer (`modules/location/service.ts`)

The service layer provides comprehensive CRUD operations:

- `getLocations`: Fetches locations with filtering options
- `getLocationsByProject`: Fetches locations for a specific project
- `getLocation`: Fetches a single location by ID
- `createLocation`: Creates a new location
- `updateLocation`: Updates an existing location
- `deleteLocation`: Soft-deletes a location
- `approveLocation`: Approves a location
- `rejectLocation`: Rejects a location with reason
- `secureLocation`: Marks a location as secured
- `markLocationUnavailable`: Marks a location as unavailable
- `getLocationsByCoordinates`: Spatial search for locations

The service layer includes optimized database queries with:
- Proper indexing
- Efficient JSONB queries
- PostGIS spatial functions
- Pagination support

#### API Routes

1. **Main Locations API (`app/api/locations/route.ts`)**
   - `GET`: Fetches locations with filtering
   - `POST`: Creates a new location
   - Both endpoints implement RBAC checks

2. **Location Detail API (`app/api/locations/[id]/route.ts`)**
   - `GET`: Fetches a single location
   - `PATCH`: Updates a location
   - `DELETE`: Deletes a location
   - All endpoints implement RBAC checks and special permission logic

3. **Map Integration (`app/api/map/initial-data/route.ts`)**
   - Fetches locations as part of the map data
   - Combines organization, locations, views, and favorites data
   - Implements RBAC checks

### Frontend Implementation

#### Pages

1. **Locations Page (`app/(dashboard)/organizations/[organizationSlug]/locations/page.tsx`)**
   - Currently uses mock data instead of API
   - Implements UI with grid and list views
   - Has search functionality (client-side only)
   - Includes view mode toggle (grid/list)

2. **Location Detail Page**
   - Structure exists but implementation details are limited

#### Components

1. **Location Grid (`components/locations/location-grid.tsx`)**
   - Card-based grid view of locations
   - Displays location image, name, status, type, and address
   - Links to location detail page

2. **Location List (`components/locations/location-list.tsx`)**
   - Table-based list view of locations
   - Displays name, type, address, status, project, and actions
   - Links to location detail page

3. **Other Components**
   - `location-gallery.tsx`: For displaying location images
   - `enhanced-location-gallery.tsx`: Enhanced version with additional features
   - `location-map.tsx`: For displaying location on a map
   - `location-documents.tsx`: For managing location documents
   - `location-notes.tsx`: For location notes
   - `location-comparison.tsx`: For comparing locations
   - `location-client-view.tsx`: Client-side component for location view

#### View Modes

The locations feature implements two distinct view modes:

1. **Admin View**
   - Full access to all location details
   - Displays sensitive information like pricing, contact details, and internal notes
   - Includes management capabilities (edit, delete, approve, reject)
   - Shows complete metadata and administrative controls
   - Used by internal team members with appropriate permissions

2. **Client View**
   - Limited access to location details
   - Hides sensitive information (pricing details, internal notes, contact information)
   - Focuses on visual presentation with high-quality images and essential details
   - Simplified interface optimized for external sharing
   - Used when sharing locations with clients or external stakeholders

These view modes are critical when sharing location information:
- When sharing the map view with clients, users should be able to select which view mode to use
- When sharing a specific location, the system should default to client view for external sharing
- The UI should clearly indicate which view mode is active
- Users with appropriate permissions can toggle between views to preview how clients will see the content

#### Custom Hooks

1. **Locations Hook (`hooks/use-locations.ts`)**
   - Currently returns mock data
   - Has structure for organization context
   - Includes loading and error states

### Integration with Other Features

1. **Map Feature**
   - Map feature already fetches real location data from the backend
   - Uses `/api/map/initial-data` endpoint
   - Displays locations on the map

2. **Projects Feature**
   - Locations can be associated with projects via `projectId`
   - Project integration details are limited in the current implementation

## Limitations and Gaps

1. **Frontend-Backend Disconnect**
   - Frontend components use mock data instead of real API data
   - No data fetching hooks for real API integration

2. **Missing Frontend Functionality**
   - No implementation for creating/editing locations
   - Limited filtering capabilities (client-side only)
   - No pagination implementation
   - No error handling for API failures

3. **Incomplete Features**
   - Location approval workflow UI not implemented
   - Document management for locations not fully implemented
   - Advanced filtering and search not implemented

4. **Performance Considerations**
   - No client-side caching strategy for locations
   - No optimistic updates for mutations

## Technical Debt

1. **Mock Data Dependency**
   - Frontend relies on hardcoded mock data
   - Mock data structure may not match actual API response

2. **Limited Error Handling**
   - Frontend doesn't handle API errors gracefully
   - No retry mechanisms for failed requests

3. **Incomplete Type Definitions**
   - Some components use generic types instead of specific location types

## Conclusion

The locations feature has a solid backend foundation with comprehensive service functions and API routes. However, the frontend implementation is incomplete, primarily using mock data instead of connecting to the backend APIs. The next phase of development should focus on connecting the frontend to the backend, implementing proper data fetching with TanStack Query, and adding the missing functionality for location management.
