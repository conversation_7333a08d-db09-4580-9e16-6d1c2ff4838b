# Step 3: Implement Media Adapter Function

## Overview

The third step in fixing the location media and UploadThing feature is to implement the media adapter function. This function is responsible for transforming the media data from the API response into the format expected by the `LocationGallery` component. Currently, the `adaptMediaItems` function in `location-details-client.tsx` is referenced but might not be correctly implemented, leading to inconsistencies in how media is displayed.

## Current Implementation

The current implementation might look something like this in `location-details-client.tsx`:

```typescript
<LocationGallery media={adaptMediaItems(locationData?.media)} />
```

However, the `adaptMediaItems` function might not be defined or might not correctly transform the data to match the expected `MediaItem` interface.

## Required Changes

### 1. Define the `MediaItem` Interface

First, we need to define the `MediaItem` interface that the `LocationGallery` component expects:

```typescript
interface MediaItem {
  id: string;
  url: string;
  filename: string;
  type: "image" | "video" | "pdf" | "doc" | "document";
  thumbnailUrl?: string;
  uploadedBy: {
    id: string;
    name: string;
  };
  uploadedAt: string;
  fileSize?: number;
  metadata?: Record<string, any>;
}
```

### 2. Implement the `adaptMediaItems` Function

Next, implement the `adaptMediaItems` function to transform the media data from the API response to match the `MediaItem` interface:

```typescript
/**
 * Adapts the media items from the API response to the format expected by the LocationGallery component
 * @param mediaItems The media items from the API response
 * @returns The adapted media items
 */
function adaptMediaItems(mediaItems: any[] = []): MediaItem[] {
  return mediaItems.map(item => ({
    id: item.id,
    url: item.url,
    filename: item.title || 'Untitled',
    type: item.type as "image" | "video" | "pdf" | "doc" | "document",
    thumbnailUrl: item.thumbnailUrl || undefined,
    uploadedBy: {
      id: item.uploadedBy || 'unknown',
      name: item.uploadedByName || 'Unknown User'
    },
    uploadedAt: item.uploadedAt || new Date().toISOString(),
    fileSize: item.fileSize,
    metadata: item.metadata || {}
  }));
}
```

### 3. Update the `LocationGallery` Component Usage

Make sure the `LocationGallery` component is using the adapted media items:

```typescript
<LocationGallery media={adaptMediaItems(locationData?.media)} />
```

## Implementation Details

1. **Define the Interface**:
   - Define the `MediaItem` interface at the top of the `location-details-client.tsx` file or in a separate types file.
   - Make sure the interface matches the expected structure of the `LocationGallery` component.

2. **Implement the Adapter Function**:
   - Implement the `adaptMediaItems` function to transform the media data.
   - Handle missing or null values gracefully to prevent runtime errors.
   - Add appropriate type annotations to ensure type safety.

3. **Update Component Usage**:
   - Make sure the `LocationGallery` component is using the adapted media items.
   - Add error handling to handle cases where `locationData?.media` is undefined or null.

4. **Test the Changes**:
   - Test the `adaptMediaItems` function with various input data to ensure it correctly transforms the data.
   - Test the `LocationGallery` component with the adapted media items to ensure it displays correctly.

## Detailed Implementation

Here's a more detailed implementation of the `adaptMediaItems` function that handles various edge cases:

```typescript
/**
 * Adapts the media items from the API response to the format expected by the LocationGallery component
 * @param mediaItems The media items from the API response
 * @returns The adapted media items
 */
function adaptMediaItems(mediaItems: any[] = []): MediaItem[] {
  if (!mediaItems || !Array.isArray(mediaItems)) {
    console.warn('adaptMediaItems received invalid input:', mediaItems);
    return [];
  }

  return mediaItems.map(item => {
    // Ensure we have a valid item object
    if (!item || typeof item !== 'object') {
      console.warn('adaptMediaItems encountered invalid item:', item);
      return null;
    }

    // Determine the media type
    let type: "image" | "video" | "pdf" | "doc" | "document" = "document";
    if (item.type) {
      if (item.type === 'image' || item.type === 'video') {
        type = item.type as "image" | "video";
      } else if (item.type === 'pdf') {
        type = "pdf";
      } else if (item.type === 'doc') {
        type = "doc";
      }
    } else {
      // Try to infer type from URL if type is not provided
      const url = item.url || '';
      if (url.match(/\.(jpg|jpeg|png|gif|webp)$/i)) {
        type = "image";
      } else if (url.match(/\.(mp4|webm|ogg)$/i)) {
        type = "video";
      } else if (url.match(/\.pdf$/i)) {
        type = "pdf";
      } else if (url.match(/\.(doc|docx)$/i)) {
        type = "doc";
      }
    }

    // Create the adapted item
    return {
      id: item.id || `temp-${Math.random().toString(36).substring(2, 11)}`,
      url: item.url || '',
      filename: item.title || item.filename || 'Untitled',
      type,
      thumbnailUrl: item.thumbnailUrl || undefined,
      uploadedBy: {
        id: item.uploadedBy || 'unknown',
        name: item.uploadedByName || 'Unknown User'
      },
      uploadedAt: item.uploadedAt || new Date().toISOString(),
      fileSize: item.fileSize,
      metadata: item.metadata || {}
    };
  }).filter(Boolean) as MediaItem[]; // Filter out null items
}
```

## Potential Issues and Solutions

1. **Missing User Information**:
   - The API response might not include the user's name who uploaded the media.
   - Consider fetching user information separately or using a placeholder name.

2. **Inconsistent Media Types**:
   - The API response might use different media type values than what the `LocationGallery` component expects.
   - The adapter function should normalize these values to match the expected types.

3. **Missing or Invalid Data**:
   - The API response might have missing or invalid data.
   - The adapter function should handle these cases gracefully and provide default values where appropriate.

## Next Steps

After implementing the media adapter function, the next step is to [update document handling](./04_update_document_handling.md).
