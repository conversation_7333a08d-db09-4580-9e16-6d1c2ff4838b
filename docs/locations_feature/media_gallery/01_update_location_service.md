# Step 1: Update Location Service to Include Media

## Overview

The first step in fixing the location media and UploadThing feature is to update the location service to include media relations when fetching a location. Currently, the `getLocation` function in `modules/location/service.ts` doesn't include the location media in the response, which means that even if media is uploaded successfully, it won't be displayed in the location details page.

## Current Implementation

The current implementation of the `getLocation` function looks like this:

```typescript
export async function getLocation(id: string) {
  const location = await db.query.locations.findFirst({
    where: and(eq(locations.id, id), isNull(locations.deletedAt)),
    with: {
      creator: true,
      project: true,
      // Media relation is missing here
    },
  });

  return location;
}
```

## Required Changes

### 1. Update the `getLocation` Function

Modify the `getLocation` function in `modules/location/service.ts` to include the media relation:

```typescript
export async function getLocation(id: string) {
  const location = await db.query.locations.findFirst({
    where: and(eq(locations.id, id), isNull(locations.deletedAt)),
    with: {
      creator: true,
      project: true,
      media: {
        where: eq(locationMedia.isPublic, true),
        orderBy: [asc(locationMedia.ordering)],
      },
    },
  });

  return location;
}
```

This change adds a `media` relation to the query, which will include all public media associated with the location, ordered by the `ordering` field.

### 2. Update Other Location Query Functions (if necessary)

If there are other functions in the location service that fetch locations and should include media, update those as well. For example, if there's a `getLocations` function that fetches multiple locations, you might want to include media for each location.

## Implementation Details

1. **Import the `locationMedia` Table**:
   Make sure to import the `locationMedia` table at the top of the file:

   ```typescript
   import { locations, locationMedia } from "@/modules/location/schema";
   ```

2. **Import the Required Functions**:
   Make sure to import the necessary functions from Drizzle:

   ```typescript
   import { and, eq, isNull, asc } from "drizzle-orm";
   ```

3. **Update the Query**:
   Add the `media` relation to the `with` object in the query, as shown above.

4. **Test the Changes**:
   After making these changes, test the `getLocation` function to ensure it correctly includes media in the response.

## Potential Issues and Solutions

1. **Missing Schema Relations**:
   If the `locations` table doesn't have a relation to the `locationMedia` table in the schema, you'll need to update the schema first. See [Step 5: Add Media Relation to Location Model](./05_add_media_relation.md) for details.

2. **Performance Concerns**:
   If a location has a large number of media items, fetching all of them could impact performance. Consider adding pagination or limiting the number of media items returned.

3. **Error Handling**:
   Ensure that the function handles the case where no media is found for a location. The query should return an empty array for the `media` property in this case.

## Next Steps

After updating the location service to include media, the next step is to [fix the user ID format in UploadThing Core](./02_fix_user_id_format.md).
