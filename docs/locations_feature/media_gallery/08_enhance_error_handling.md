# Step 8: Enhance <PERSON>rror <PERSON>ling and User Feedback

## Overview

The eighth step in fixing the location media and UploadThing feature is to enhance error handling and user feedback. This step focuses on improving the user experience by providing clear feedback during the upload process and handling errors gracefully.

## Current Implementation

The current implementation might not provide sufficient feedback to users during the upload process, and error handling might be minimal or inconsistent. This can lead to confusion when uploads fail or when there are issues with displaying media.

## Required Changes

### 1. Add Upload Progress Feedback

First, enhance the upload component to show progress and status:

```typescript
// components/locations/location-create/location-media.tsx
import { useState } from 'react';
import { UploadDropzone } from '@/lib/utils/uploadthing';
import { toast } from '@/components/ui/use-toast';
import { Progress } from '@/components/ui/progress';
import { Loader2, CheckCircle, XCircle } from 'lucide-react';

export function LocationMediaUpload({ locationId, organizationId }: LocationMediaUploadProps) {
  const [uploading, setUploading] = useState(false);
  const [progress, setProgress] = useState(0);
  const [uploadedFiles, setUploadedFiles] = useState<UploadedFile[]>([]);
  const [errors, setErrors] = useState<string[]>([]);

  return (
    <div className="space-y-4">
      <UploadDropzone
        endpoint="locationMedia"
        onUploadBegin={() => {
          setUploading(true);
          setProgress(0);
        }}
        onUploadProgress={(progress) => {
          setProgress(progress);
        }}
        onClientUploadComplete={(res) => {
          setUploading(false);
          setProgress(100);
          setUploadedFiles((prev) => [...prev, ...res]);
          toast({
            title: 'Upload complete',
            description: `Successfully uploaded ${res.length} file(s)`,
            variant: 'default',
          });
        }}
        onUploadError={(error) => {
          setUploading(false);
          setErrors((prev) => [...prev, error.message]);
          toast({
            title: 'Upload failed',
            description: error.message,
            variant: 'destructive',
          });
        }}
        config={{
          locationId,
          organizationId,
        }}
      />

      {uploading && (
        <div className="space-y-2">
          <div className="flex items-center gap-2">
            <Loader2 className="h-4 w-4 animate-spin" />
            <span>Uploading...</span>
          </div>
          <Progress value={progress} className="h-2 w-full" />
        </div>
      )}

      {uploadedFiles.length > 0 && (
        <div className="space-y-2">
          <h3 className="text-sm font-medium">Uploaded Files</h3>
          <ul className="space-y-1">
            {uploadedFiles.map((file) => (
              <li key={file.key} className="flex items-center gap-2 text-sm">
                <CheckCircle className="h-4 w-4 text-green-500" />
                <span>{file.name}</span>
              </li>
            ))}
          </ul>
        </div>
      )}

      {errors.length > 0 && (
        <div className="space-y-2">
          <h3 className="text-sm font-medium text-destructive">Errors</h3>
          <ul className="space-y-1">
            {errors.map((error, index) => (
              <li key={index} className="flex items-center gap-2 text-sm text-destructive">
                <XCircle className="h-4 w-4" />
                <span>{error}</span>
              </li>
            ))}
          </ul>
        </div>
      )}
    </div>
  );
}
```

### 2. Enhance Error Handling in API Endpoints

Next, improve error handling in the API endpoints:

```typescript
// app/api/locations/media/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/lib/db';
import { locationMedia } from '@/modules/location/schema';
import { eq, and, isNull } from 'drizzle-orm';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { z } from 'zod';

// ... existing code ...

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await request.json();
    
    // Validate the request body
    const result = mediaSchema.safeParse(body);
    
    if (!result.success) {
      console.error('Invalid request body:', result.error.format());
      return NextResponse.json({ 
        error: 'Invalid request body', 
        details: result.error.format() 
      }, { status: 400 });
    }

    const data = result.data;

    // Check if the location exists
    const location = await db.query.locations.findFirst({
      where: and(
        eq(locations.id, data.locationId),
        isNull(locations.deletedAt)
      ),
      columns: {
        id: true,
        organizationId: true,
      }
    });

    if (!location) {
      return NextResponse.json({ error: 'Location not found' }, { status: 404 });
    }

    // Check if the user has permission to upload to this location
    if (location.organizationId !== data.organizationId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 403 });
    }

    // Create the media record
    try {
      const [media] = await db.insert(locationMedia).values({
        locationId: data.locationId,
        organizationId: data.organizationId,
        url: data.url,
        type: data.type,
        uploadedBy: data.uploadedBy,
        title: data.title || '',
        description: data.description || '',
        fileSize: data.fileSize || 0,
        isPublic: data.isPublic,
        ordering: data.ordering,
        metadata: data.metadata || {},
      }).returning();

      return NextResponse.json({ media });
    } catch (dbError) {
      console.error('Database error:', dbError);
      return NextResponse.json({ 
        error: 'Failed to save media to database', 
        details: dbError instanceof Error ? dbError.message : String(dbError) 
      }, { status: 500 });
    }
  } catch (error) {
    console.error('Error creating location media:', error);
    return NextResponse.json({ 
      error: 'Internal Server Error', 
      details: error instanceof Error ? error.message : String(error) 
    }, { status: 500 });
  }
}
```

### 3. Add Error Handling in Components

Enhance error handling in the components that display media:

```typescript
// components/locations/location-gallery-content.tsx
import { useState } from 'react';
import { AlertCircle } from 'lucide-react';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';

export function LocationGalleryContent({ media, isLoading = false }: GalleryTabProps) {
  const [loadErrors, setLoadErrors] = useState<Record<string, boolean>>({});

  const handleImageError = (id: string) => {
    setLoadErrors((prev) => ({ ...prev, [id]: true }));
  };

  if (isLoading) {
    return <div className="flex justify-center p-8">Loading media...</div>;
  }

  if (!media || media.length === 0) {
    return (
      <div className="flex flex-col items-center justify-center p-8 text-center">
        <div className="h-12 w-12 text-muted-foreground mb-4">
          <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
          </svg>
        </div>
        <h3 className="text-lg font-medium">No Images</h3>
        <p className="text-sm text-muted-foreground mt-1">
          This location doesn't have any images yet.
        </p>
      </div>
    );
  }

  // Count the number of images with load errors
  const errorCount = Object.values(loadErrors).filter(Boolean).length;

  return (
    <div className="space-y-4">
      {errorCount > 0 && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>Error</AlertTitle>
          <AlertDescription>
            Failed to load {errorCount} image(s). The images might be missing or inaccessible.
          </AlertDescription>
        </Alert>
      )}

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 p-4">
        {media.map((item) => (
          <div key={item.id} className="relative aspect-square overflow-hidden rounded-md border">
            {loadErrors[item.id] ? (
              <div className="flex h-full w-full items-center justify-center bg-muted">
                <AlertCircle className="h-8 w-8 text-muted-foreground" />
              </div>
            ) : (
              <img
                src={item.url}
                alt={item.filename}
                className="h-full w-full object-cover"
                onError={() => handleImageError(item.id)}
              />
            )}
          </div>
        ))}
      </div>
    </div>
  );
}
```

### 4. Add Error Handling in Document Viewer

Enhance error handling in the document viewer:

```typescript
// components/locations/location-documents-content.tsx
// ... existing code ...

export function LocationDocumentsContent({ documents, isLoading = false }: DocumentsTabProps) {
  const [previewUrl, setPreviewUrl] = useState<string | null>(null);
  const [previewTitle, setPreviewTitle] = useState<string>('');
  const [previewError, setPreviewError] = useState<string | null>(null);

  const handlePreview = (document: DocumentItem) => {
    if (document.previewAvailable) {
      setPreviewUrl(document.url);
      setPreviewTitle(document.filename);
      setPreviewError(null);
    } else {
      // If preview is not available, download the file instead
      window.open(document.url, '_blank');
    }
  };

  const handlePreviewError = () => {
    setPreviewError(`Failed to load preview for ${previewTitle}`);
  };

  // ... existing code ...

  return (
    <div className="space-y-4 p-4">
      {/* ... existing code ... */}

      {/* Document Preview Dialog */}
      <Dialog open={!!previewUrl} onOpenChange={(open) => !open && closePreview()}>
        <DialogContent className="max-w-4xl h-[80vh] flex flex-col">
          <DialogHeader>
            <DialogTitle>{previewTitle}</DialogTitle>
            <DialogClose />
          </DialogHeader>
          <div className="flex-1 overflow-hidden">
            {previewError ? (
              <div className="flex h-full w-full flex-col items-center justify-center gap-4 p-8 text-center">
                <AlertCircle className="h-12 w-12 text-destructive" />
                <div>
                  <h3 className="text-lg font-medium">Preview Error</h3>
                  <p className="text-sm text-muted-foreground mt-1">{previewError}</p>
                </div>
                <Button
                  variant="outline"
                  onClick={() => window.open(previewUrl!, '_blank')}
                >
                  Download Instead
                </Button>
              </div>
            ) : (
              previewUrl && (
                <iframe
                  src={previewUrl}
                  className="w-full h-full border-0"
                  title={previewTitle}
                  onError={handlePreviewError}
                />
              )
            )}
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
}
```

## Implementation Details

1. **Add Upload Progress Feedback**:
   - Enhance the upload component to show progress during uploads.
   - Add visual indicators for successful uploads and errors.
   - Use toast notifications to provide feedback to users.

2. **Enhance Error Handling in API Endpoints**:
   - Add more detailed error handling in the API endpoints.
   - Validate input data and return appropriate error responses.
   - Add checks for permissions and data integrity.

3. **Add Error Handling in Components**:
   - Add error handling for image loading in the gallery component.
   - Show appropriate error messages when images fail to load.
   - Provide fallback UI for missing or inaccessible images.

4. **Add Error Handling in Document Viewer**:
   - Add error handling for document previews.
   - Show appropriate error messages when previews fail to load.
   - Provide fallback options like downloading the document.

5. **Test the Changes**:
   - Test the upload component with various file types and sizes.
   - Test error handling by intentionally causing errors (e.g., uploading invalid files).
   - Test the gallery and document viewer with missing or inaccessible files.

## Potential Issues and Solutions

1. **Network Issues**:
   - Users might experience network issues during uploads.
   - Add retry functionality for failed uploads.
   - Provide clear feedback about network status.

2. **Large Files**:
   - Large files might take a long time to upload.
   - Add progress indicators and estimated time remaining.
   - Consider implementing chunked uploads for very large files.

3. **Browser Compatibility**:
   - Different browsers might handle file uploads and previews differently.
   - Test the implementation in multiple browsers.
   - Add browser-specific fallbacks if needed.

## Next Steps

After enhancing error handling and user feedback, the next step is to [add support for document previews](./09_add_document_previews.md).
