# Step 4: Update Document Handling

## Overview

The fourth step in fixing the location media and UploadThing feature is to update the document handling functionality. Currently, documents uploaded via UploadThing might not be properly displayed in the location detail page. This guide focuses on enhancing the document handling to ensure that documents are viewable in the same manner as images.

## Current Implementation

The current implementation might have separate handling for images and documents, or it might not properly display documents at all. The `LocationDocumentsContent` component is responsible for displaying documents, but it might not be correctly integrated with the media data from the API.

## Required Changes

### 1. Define Document-Specific Interfaces

First, we need to define interfaces specific to document handling:

```typescript
interface DocumentItem extends MediaItem {
  // Additional properties specific to documents
  fileType?: string;
  previewAvailable?: boolean;
}

interface DocumentsTabProps {
  documents: DocumentItem[];
  isLoading?: boolean;
}
```

### 2. Implement the `adaptDocuments` Function

Next, implement the `adaptDocuments` function to transform the media data into the format expected by the `LocationDocumentsContent` component:

```typescript
/**
 * Adapts the media items to document items for the LocationDocumentsContent component
 * @param mediaItems The media items from the API response
 * @returns The adapted document items
 */
function adaptDocuments(mediaItems: any[] = []): DocumentItem[] {
  if (!mediaItems || !Array.isArray(mediaItems)) {
    console.warn('adaptDocuments received invalid input:', mediaItems);
    return [];
  }

  // Filter for document types (pdf, doc, etc.)
  return mediaItems
    .filter(item => {
      const type = item.type || '';
      return type === 'pdf' || type === 'doc' || type === 'document';
    })
    .map(item => {
      // Determine file type and preview availability
      const url = item.url || '';
      let fileType = 'unknown';
      let previewAvailable = false;

      if (url.match(/\.pdf$/i)) {
        fileType = 'pdf';
        previewAvailable = true;
      } else if (url.match(/\.(doc|docx)$/i)) {
        fileType = url.match(/\.docx$/i) ? 'docx' : 'doc';
        previewAvailable = false; // Most browsers can't preview DOC/DOCX directly
      } else if (url.match(/\.(xls|xlsx)$/i)) {
        fileType = url.match(/\.xlsx$/i) ? 'xlsx' : 'xls';
        previewAvailable = false;
      } else if (url.match(/\.(ppt|pptx)$/i)) {
        fileType = url.match(/\.pptx$/i) ? 'pptx' : 'ppt';
        previewAvailable = false;
      } else if (url.match(/\.txt$/i)) {
        fileType = 'txt';
        previewAvailable = true;
      }

      // Create the adapted document item
      return {
        id: item.id || `temp-${Math.random().toString(36).substring(2, 11)}`,
        url: url,
        filename: item.title || item.filename || 'Untitled',
        type: item.type || 'document',
        thumbnailUrl: item.thumbnailUrl || undefined,
        uploadedBy: {
          id: item.uploadedBy || 'unknown',
          name: item.uploadedByName || 'Unknown User'
        },
        uploadedAt: item.uploadedAt || new Date().toISOString(),
        fileSize: item.fileSize,
        metadata: item.metadata || {},
        fileType,
        previewAvailable
      };
    });
}
```

### 3. Update the `LocationDocumentsContent` Component Usage

Make sure the `LocationDocumentsContent` component is using the adapted document items:

```typescript
<LocationDocumentsContent documents={adaptDocuments(locationData?.media)} />
```

### 4. Enhance the `LocationDocumentsContent` Component

Update the `LocationDocumentsContent` component to properly display different document types and provide appropriate actions for each type:

```typescript
// In location-documents-content.tsx
import { useState } from 'react';
import { FileText, Download, Eye, X } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogClose } from '@/components/ui/dialog';

export function LocationDocumentsContent({ documents, isLoading = false }: DocumentsTabProps) {
  const [previewUrl, setPreviewUrl] = useState<string | null>(null);
  const [previewTitle, setPreviewTitle] = useState<string>('');

  const handlePreview = (document: DocumentItem) => {
    if (document.previewAvailable) {
      setPreviewUrl(document.url);
      setPreviewTitle(document.filename);
    } else {
      // If preview is not available, download the file instead
      window.open(document.url, '_blank');
    }
  };

  const handleDownload = (url: string) => {
    window.open(url, '_blank');
  };

  const closePreview = () => {
    setPreviewUrl(null);
    setPreviewTitle('');
  };

  if (isLoading) {
    return <div className="flex justify-center p-8">Loading documents...</div>;
  }

  if (!documents || documents.length === 0) {
    return (
      <div className="flex flex-col items-center justify-center p-8 text-center">
        <FileText className="h-12 w-12 text-muted-foreground mb-4" />
        <h3 className="text-lg font-medium">No Documents</h3>
        <p className="text-sm text-muted-foreground mt-1">
          This location doesn't have any documents yet.
        </p>
      </div>
    );
  }

  return (
    <div className="space-y-4 p-4">
      <h3 className="text-lg font-medium">Documents ({documents.length})</h3>
      
      <div className="grid gap-4">
        {documents.map((doc) => (
          <div key={doc.id} className="flex items-center justify-between p-3 border rounded-md">
            <div className="flex items-center gap-3">
              <div className="h-10 w-10 flex items-center justify-center bg-muted rounded-md">
                <FileText className="h-5 w-5 text-muted-foreground" />
              </div>
              <div className="flex-1 min-w-0">
                <p className="text-sm font-medium truncate">{doc.filename}</p>
                <p className="text-xs text-muted-foreground">
                  {formatFileSize(doc.fileSize || 0)} • {doc.fileType?.toUpperCase() || doc.type.toUpperCase()}
                </p>
              </div>
            </div>
            <div className="flex items-center gap-2">
              {doc.previewAvailable && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => handlePreview(doc)}
                  title="Preview"
                >
                  <Eye className="h-4 w-4" />
                </Button>
              )}
              <Button
                variant="ghost"
                size="sm"
                onClick={() => handleDownload(doc.url)}
                title="Download"
              >
                <Download className="h-4 w-4" />
              </Button>
            </div>
          </div>
        ))}
      </div>

      {/* Document Preview Dialog */}
      <Dialog open={!!previewUrl} onOpenChange={(open) => !open && closePreview()}>
        <DialogContent className="max-w-4xl h-[80vh] flex flex-col">
          <DialogHeader>
            <DialogTitle>{previewTitle}</DialogTitle>
            <DialogClose />
          </DialogHeader>
          <div className="flex-1 overflow-hidden">
            {previewUrl && (
              <iframe
                src={previewUrl}
                className="w-full h-full border-0"
                title={previewTitle}
              />
            )}
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
}

// Helper function to format file size
function formatFileSize(bytes: number): string {
  if (bytes === 0) return "0 Bytes";
  
  const k = 1024;
  const sizes = ["Bytes", "KB", "MB", "GB"];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return `${Number.parseFloat((bytes / (k ** i)).toFixed(2))} ${sizes[i]}`;
}
```

## Implementation Details

1. **Define Document-Specific Interfaces**:
   - Define interfaces specific to document handling at the top of the `location-documents-content.tsx` file or in a separate types file.
   - Make sure the interfaces extend or are compatible with the `MediaItem` interface.

2. **Implement the Adapter Function**:
   - Implement the `adaptDocuments` function to transform the media data into document items.
   - Filter for document types and determine file type and preview availability.
   - Add appropriate type annotations to ensure type safety.

3. **Update Component Usage**:
   - Make sure the `LocationDocumentsContent` component is using the adapted document items.
   - Add error handling to handle cases where `locationData?.media` is undefined or null.

4. **Enhance the Component**:
   - Update the `LocationDocumentsContent` component to properly display different document types.
   - Add preview functionality for supported document types.
   - Add download functionality for all document types.
   - Add appropriate UI elements for each action.

5. **Test the Changes**:
   - Test the `adaptDocuments` function with various input data to ensure it correctly transforms the data.
   - Test the `LocationDocumentsContent` component with the adapted document items to ensure it displays correctly.
   - Test the preview and download functionality for different document types.

## Potential Issues and Solutions

1. **Browser Compatibility**:
   - Different browsers have different levels of support for previewing document types.
   - The `previewAvailable` flag should be set based on the document type and browser capabilities.
   - Consider using a third-party library for previewing documents that aren't natively supported by browsers.

2. **Large Documents**:
   - Large documents might take a long time to load in the preview.
   - Consider adding a loading indicator or progress bar for large documents.
   - Consider implementing lazy loading or pagination for large documents.

3. **Security Concerns**:
   - Embedding documents in an iframe might pose security risks.
   - Consider adding appropriate security headers and content security policies.
   - Consider using a sandboxed iframe for previewing documents.

## Next Steps

After updating document handling, the next step is to [add media relation to the Location model](./05_add_media_relation.md).
