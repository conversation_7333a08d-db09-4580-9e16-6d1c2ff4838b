# Step 6: Implement Media Fetching if Needed

## Overview

The sixth step in fixing the location media and UploadThing feature is to implement media fetching if needed. If the location service doesn't include media in the response, or if you need to fetch media separately for performance reasons, you'll need to implement a separate media fetching mechanism.

## Current Implementation

Currently, the location details component might expect media to be part of the location object, but it's actually fetched from a separate endpoint. There might not be any code that fetches media from the `/api/locations/media` endpoint and passes it to the `LocationGallery` component.

## Required Changes

### 1. Create a Custom Hook for Fetching Media

First, create a custom hook for fetching media for a specific location:

```typescript
// hooks/use-location-media.ts
import { useState, useEffect } from 'react';
import { LocationMedia } from '@/modules/location/schema';

export function useLocationMedia(locationId: string | undefined) {
  const [media, setMedia] = useState<LocationMedia[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<Error | null>(null);

  useEffect(() => {
    if (!locationId) {
      setMedia([]);
      return;
    }

    const fetchMedia = async () => {
      setIsLoading(true);
      setError(null);

      try {
        const response = await fetch(`/api/locations/media?locationId=${locationId}`);
        
        if (!response.ok) {
          throw new Error(`Failed to fetch media: ${response.statusText}`);
        }

        const data = await response.json();
        setMedia(data.media || []);
      } catch (err) {
        console.error('Error fetching location media:', err);
        setError(err instanceof Error ? err : new Error(String(err)));
      } finally {
        setIsLoading(false);
      }
    };

    fetchMedia();
  }, [locationId]);

  return { media, isLoading, error };
}
```

### 2. Use the Hook in the Location Details Component

Next, use the hook in the location details component to fetch media:

```typescript
// components/locations/location-details-client.tsx
import { useLocationMedia } from '@/hooks/use-location-media';

// ... existing code ...

export function LocationDetailsClient({ locationId }: LocationDetailsClientProps) {
  const { data: locationData, isLoading: isLocationLoading } = useLocationDetails(locationId);
  const { media, isLoading: isMediaLoading } = useLocationMedia(locationId);

  // Combine the location data with the media data
  const locationWithMedia = locationData ? {
    ...locationData,
    media: locationData.media || media // Use media from location if available, otherwise use fetched media
  } : null;

  // ... existing code ...

  return (
    <div>
      {/* ... existing code ... */}
      
      <Tabs defaultValue="gallery">
        <TabsList>
          <TabsTrigger value="gallery">Gallery</TabsTrigger>
          <TabsTrigger value="map">Map</TabsTrigger>
          <TabsTrigger value="documents">Documents</TabsTrigger>
          <TabsTrigger value="notes">Notes</TabsTrigger>
        </TabsList>
        <TabsContent value="gallery">
          <LocationGalleryContent 
            media={adaptMediaItems(locationWithMedia?.media)} 
            isLoading={isLocationLoading || isMediaLoading} 
          />
        </TabsContent>
        <TabsContent value="map">
          <LocationMapContent location={locationWithMedia} isLoading={isLocationLoading} />
        </TabsContent>
        <TabsContent value="documents">
          <LocationDocumentsContent 
            documents={adaptDocuments(locationWithMedia?.media)} 
            isLoading={isLocationLoading || isMediaLoading} 
          />
        </TabsContent>
        <TabsContent value="notes">
          <LocationNotesContent location={locationWithMedia} isLoading={isLocationLoading} />
        </TabsContent>
      </Tabs>
    </div>
  );
}
```

### 3. Create a Media API Endpoint (if needed)

If the `/api/locations/media` endpoint doesn't exist or doesn't work correctly, you'll need to create or update it:

```typescript
// app/api/locations/media/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/lib/db';
import { locationMedia } from '@/modules/location/schema';
import { eq, and, isNull } from 'drizzle-orm';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const locationId = searchParams.get('locationId');

    if (!locationId) {
      return NextResponse.json({ error: 'Location ID is required' }, { status: 400 });
    }

    const media = await db.query.locationMedia.findMany({
      where: and(
        eq(locationMedia.locationId, locationId),
        isNull(locationMedia.deletedAt)
      ),
      orderBy: [locationMedia.ordering],
    });

    return NextResponse.json({ media });
  } catch (error) {
    console.error('Error fetching location media:', error);
    return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 });
  }
}
```

## Implementation Details

1. **Create the Custom Hook**:
   - Create a new file `hooks/use-location-media.ts` for the custom hook.
   - Implement the hook to fetch media for a specific location.
   - Add appropriate error handling and loading states.

2. **Update the Location Details Component**:
   - Import and use the custom hook in the location details component.
   - Combine the location data with the media data.
   - Pass the combined data to the appropriate components.

3. **Create or Update the Media API Endpoint**:
   - If the `/api/locations/media` endpoint doesn't exist or doesn't work correctly, create or update it.
   - Implement the endpoint to fetch media for a specific location.
   - Add appropriate error handling and authentication.

4. **Test the Changes**:
   - Test the custom hook with various location IDs to ensure it correctly fetches media.
   - Test the location details component with the custom hook to ensure it correctly displays media.
   - Test the media API endpoint to ensure it correctly returns media for a specific location.

## Potential Issues and Solutions

1. **Performance Concerns**:
   - Fetching media separately might impact performance, especially if there are many media items.
   - Consider implementing pagination or limiting the number of media items returned.

2. **Race Conditions**:
   - If the location data and media data are fetched separately, there might be race conditions.
   - Make sure to handle loading states and errors appropriately.

3. **Authentication**:
   - Make sure the media API endpoint is properly authenticated.
   - Consider implementing authorization to ensure users can only access media for locations they have permission to view.

## Alternative Approach

If you've already updated the location service to include media in the response (as described in [Step 1](./01_update_location_service.md)), you might not need to implement separate media fetching. In that case, you can skip this step and move on to the next one.

However, if you're concerned about performance or if you need more flexibility in how media is fetched, implementing separate media fetching might still be a good idea.

## Next Steps

After implementing media fetching if needed, the next step is to [fix the location media API endpoint](./07_fix_media_api_endpoint.md).
