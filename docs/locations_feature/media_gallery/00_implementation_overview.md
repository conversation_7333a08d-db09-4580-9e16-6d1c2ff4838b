# Location Media Gallery Implementation Guide

This guide provides a comprehensive plan for fixing issues with the location media and UploadThing feature in the Scene-o-matic application. The implementation is divided into several steps, each with its own detailed guide.

## Current Issues

1. **Missing Media in Location Details**:
   - The `getLocation` function in `modules/location/service.ts` doesn't include the location media in the response.
   - The Location type in `modules/location/model.ts` doesn't include a media property.

2. **Inconsistent User ID Format**:
   - In `app/api/uploadthing/core.ts`, the `onUploadComplete` function passes the Stytch member ID as `uploadedBy` to the `/api/locations/media` endpoint.
   - The `locationMedia` schema expects `uploadedBy` to be a UUID that references the `users.id` column.

3. **Media Fetching Issue**:
   - The location details component expects media to be part of the location object, but it's actually fetched from a separate endpoint.
   - There's no code that fetches media from the `/api/locations/media` endpoint and passes it to the `LocationGallery` component.

4. **Inconsistent Media Item Structure**:
   - The `MediaItem` interface expected by the `LocationGalleryContent` component doesn't match what's returned by the API.
   - The `adaptMediaItems` function in `location-details-client.tsx` is defined but might not be correctly transforming the data.

5. **Document Handling**:
   - Documents uploaded via UploadThing should be viewable in the same manner as images in the location detail page.

## Implementation Steps

1. [Update Location Service to Include Media](./01_update_location_service.md)
2. [Fix User ID Format in UploadThing Core](./02_fix_user_id_format.md)
3. [Implement Media Adapter Function](./03_implement_media_adapter.md)
4. [Update Document Handling](./04_update_document_handling.md)
5. [Add Media Relation to Location Model](./05_add_media_relation.md)
6. [Implement Media Fetching if Needed](./06_implement_media_fetching.md)
7. [Fix Location Media API Endpoint](./07_fix_media_api_endpoint.md)
8. [Enhance Error Handling and User Feedback](./08_enhance_error_handling.md)
9. [Add Support for Document Previews](./09_add_document_previews.md)
10. [Testing Plan](./10_testing_plan.md)

## Implementation Process

To ensure a smooth and organized implementation, follow these guidelines:

1. **Work on one step at a time**: Complete each step fully before moving on to the next one. This approach helps maintain focus and makes it easier to identify and fix issues.

2. **Update the build log after each step**: After completing a step, update the [build log](./build_log.md) by:
   - Checking off the completed step (change `[ ]` to `[x]`)
   - Adding detailed notes about the implementation
   - Documenting any challenges encountered and how they were resolved
   - Recording any issues that might need further attention

3. **Test each step thoroughly**: Before marking a step as complete, test the changes to ensure they work as expected. This includes both manual testing and, where applicable, automated tests.

4. **Document issues in the build log**: If you encounter any issues during implementation, document them in the appropriate section of the build log. This will help with troubleshooting and future documentation.

5. **Review the implementation plan before starting each step**: Before beginning a new step, review the detailed guide for that step to ensure you understand what needs to be done.

6. **Commit changes after each step**: If using version control, commit your changes after each step is completed and tested. This creates a clear history of the implementation process.

## Implementation Approach

Each step in this guide is designed to be atomic and focused on a specific aspect of the problem. This allows for incremental progress and easier debugging if issues arise. The steps should be implemented in the order presented, as later steps may depend on changes made in earlier steps.

Before making any changes, it's recommended to:
1. Understand the current code structure and data flow
2. Back up any files that will be modified
3. Test each change individually before moving on to the next step

## Key Files to Modify

- `modules/location/service.ts`
- `modules/location/model.ts`
- `app/api/uploadthing/core.ts`
- `app/api/locations/media/route.ts`
- `components/locations/location-details-client.tsx`
- `components/locations/location-gallery-content.tsx`
- `components/locations/location-documents-content.tsx`
- `lib/utils/userUtils.ts` (may need to create or modify)

Follow the detailed guides for each step to implement the complete solution.
