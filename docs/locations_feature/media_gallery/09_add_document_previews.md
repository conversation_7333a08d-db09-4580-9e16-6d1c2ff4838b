# Step 9: Add Support for Document Previews

## Overview

The ninth step in fixing the location media and UploadThing feature is to add support for document previews. This step focuses on enhancing the document viewing experience by providing previews for various document types, making it easier for users to view documents without downloading them.

## Current Implementation

The current implementation might not provide previews for documents, or it might only support a limited set of document types. Users might need to download documents to view them, which can be inconvenient.

## Required Changes

### 1. Enhance the Document Adapter Function

First, enhance the document adapter function to better determine preview availability:

```typescript
// components/locations/location-details-client.tsx
/**
 * Adapts the media items to document items for the LocationDocumentsContent component
 * @param mediaItems The media items from the API response
 * @returns The adapted document items
 */
function adaptDocuments(mediaItems: any[] = []): DocumentItem[] {
  if (!mediaItems || !Array.isArray(mediaItems)) {
    console.warn('adaptDocuments received invalid input:', mediaItems);
    return [];
  }

  // Filter for document types (pdf, doc, etc.)
  return mediaItems
    .filter(item => {
      const type = item.type || '';
      return type === 'pdf' || type === 'doc' || type === 'document';
    })
    .map(item => {
      // Determine file type and preview availability
      const url = item.url || '';
      let fileType = 'unknown';
      let previewAvailable = false;
      let previewUrl = url;

      if (url.match(/\.pdf$/i)) {
        fileType = 'pdf';
        previewAvailable = true;
        // PDF can be previewed directly
      } else if (url.match(/\.(doc|docx)$/i)) {
        fileType = url.match(/\.docx$/i) ? 'docx' : 'doc';
        // Use Google Docs Viewer for Office documents
        previewAvailable = true;
        previewUrl = `https://docs.google.com/viewer?url=${encodeURIComponent(url)}&embedded=true`;
      } else if (url.match(/\.(xls|xlsx)$/i)) {
        fileType = url.match(/\.xlsx$/i) ? 'xlsx' : 'xls';
        previewAvailable = true;
        previewUrl = `https://docs.google.com/viewer?url=${encodeURIComponent(url)}&embedded=true`;
      } else if (url.match(/\.(ppt|pptx)$/i)) {
        fileType = url.match(/\.pptx$/i) ? 'pptx' : 'ppt';
        previewAvailable = true;
        previewUrl = `https://docs.google.com/viewer?url=${encodeURIComponent(url)}&embedded=true`;
      } else if (url.match(/\.txt$/i)) {
        fileType = 'txt';
        previewAvailable = true;
        // Text files can be previewed directly
      } else if (url.match(/\.(csv|tsv)$/i)) {
        fileType = url.match(/\.csv$/i) ? 'csv' : 'tsv';
        previewAvailable = true;
        previewUrl = `https://docs.google.com/viewer?url=${encodeURIComponent(url)}&embedded=true`;
      } else if (url.match(/\.(jpg|jpeg|png|gif|webp|svg)$/i)) {
        // Handle image files that might be categorized as documents
        fileType = 'image';
        previewAvailable = true;
        // Images can be previewed directly
      }

      // Create the adapted document item
      return {
        id: item.id || `temp-${Math.random().toString(36).substring(2, 11)}`,
        url: url,
        previewUrl: previewAvailable ? previewUrl : undefined,
        filename: item.title || item.filename || 'Untitled',
        type: item.type || 'document',
        thumbnailUrl: item.thumbnailUrl || undefined,
        uploadedBy: {
          id: item.uploadedBy || 'unknown',
          name: item.uploadedByName || 'Unknown User'
        },
        uploadedAt: item.uploadedAt || new Date().toISOString(),
        fileSize: item.fileSize,
        metadata: item.metadata || {},
        fileType,
        previewAvailable
      };
    });
}
```

### 2. Enhance the Document Viewer Component

Next, enhance the document viewer component to support different document types:

```typescript
// components/locations/location-documents-content.tsx
import { useState, useEffect } from 'react';
import { FileText, Download, Eye, X, ExternalLink } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogClose } from '@/components/ui/dialog';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';

export function LocationDocumentsContent({ documents, isLoading = false }: DocumentsTabProps) {
  const [previewUrl, setPreviewUrl] = useState<string | null>(null);
  const [previewTitle, setPreviewTitle] = useState<string>('');
  const [previewType, setPreviewType] = useState<string>('');
  const [previewError, setPreviewError] = useState<string | null>(null);
  const [isPreviewLoading, setIsPreviewLoading] = useState(false);

  const handlePreview = (document: DocumentItem) => {
    if (document.previewAvailable) {
      setPreviewUrl(document.previewUrl || document.url);
      setPreviewTitle(document.filename);
      setPreviewType(document.fileType || 'document');
      setPreviewError(null);
      setIsPreviewLoading(true);
    } else {
      // If preview is not available, download the file instead
      window.open(document.url, '_blank');
    }
  };

  const handlePreviewLoad = () => {
    setIsPreviewLoading(false);
  };

  const handlePreviewError = () => {
    setIsPreviewLoading(false);
    setPreviewError(`Failed to load preview for ${previewTitle}`);
  };

  const closePreview = () => {
    setPreviewUrl(null);
    setPreviewTitle('');
    setPreviewType('');
    setPreviewError(null);
    setIsPreviewLoading(false);
  };

  // ... existing code ...

  return (
    <div className="space-y-4 p-4">
      <h3 className="text-lg font-medium">Documents ({documents.length})</h3>
      
      <div className="grid gap-4">
        {documents.map((doc) => (
          <div key={doc.id} className="flex items-center justify-between p-3 border rounded-md">
            <div className="flex items-center gap-3">
              <div className="h-10 w-10 flex items-center justify-center bg-muted rounded-md">
                <FileText className="h-5 w-5 text-muted-foreground" />
              </div>
              <div className="flex-1 min-w-0">
                <p className="text-sm font-medium truncate">{doc.filename}</p>
                <p className="text-xs text-muted-foreground">
                  {formatFileSize(doc.fileSize || 0)} • {doc.fileType?.toUpperCase() || doc.type.toUpperCase()}
                </p>
              </div>
            </div>
            <div className="flex items-center gap-2">
              {doc.previewAvailable && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => handlePreview(doc)}
                  title="Preview"
                >
                  <Eye className="h-4 w-4" />
                </Button>
              )}
              <Button
                variant="ghost"
                size="sm"
                onClick={() => window.open(doc.url, '_blank')}
                title="Download"
              >
                <Download className="h-4 w-4" />
              </Button>
            </div>
          </div>
        ))}
      </div>

      {/* Document Preview Dialog */}
      <Dialog open={!!previewUrl} onOpenChange={(open) => !open && closePreview()}>
        <DialogContent className="max-w-4xl h-[80vh] flex flex-col">
          <DialogHeader className="flex flex-row items-center justify-between">
            <DialogTitle className="truncate max-w-[calc(100%-100px)]">{previewTitle}</DialogTitle>
            <div className="flex items-center gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => window.open(previewUrl!, '_blank')}
                title="Open in new tab"
              >
                <ExternalLink className="h-4 w-4 mr-1" />
                Open
              </Button>
              <DialogClose />
            </div>
          </DialogHeader>
          <div className="flex-1 overflow-hidden">
            {isPreviewLoading && (
              <div className="flex h-full w-full items-center justify-center">
                <div className="animate-spin h-8 w-8 border-2 border-primary border-t-transparent rounded-full"></div>
              </div>
            )}
            
            {previewError ? (
              <div className="flex h-full w-full flex-col items-center justify-center gap-4 p-8 text-center">
                <div className="h-12 w-12 text-destructive">
                  <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                </div>
                <div>
                  <h3 className="text-lg font-medium">Preview Error</h3>
                  <p className="text-sm text-muted-foreground mt-1">{previewError}</p>
                </div>
                <Button
                  variant="outline"
                  onClick={() => window.open(previewUrl!, '_blank')}
                >
                  Download Instead
                </Button>
              </div>
            ) : (
              previewUrl && (
                <div className={`w-full h-full ${isPreviewLoading ? 'hidden' : 'block'}`}>
                  <iframe
                    src={previewUrl}
                    className="w-full h-full border-0"
                    title={previewTitle}
                    onLoad={handlePreviewLoad}
                    onError={handlePreviewError}
                    sandbox="allow-scripts allow-same-origin allow-forms"
                  />
                </div>
              )
            )}
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
}
```

### 3. Add Document Type Icons

Add icons for different document types to make it easier to identify document types:

```typescript
// components/locations/document-type-icon.tsx
import { FileText, FileImage, FilePdf, FileSpreadsheet, FilePresentation, FileCode, File } from 'lucide-react';

interface DocumentTypeIconProps {
  fileType: string;
  className?: string;
}

export function DocumentTypeIcon({ fileType, className = "h-5 w-5" }: DocumentTypeIconProps) {
  switch (fileType.toLowerCase()) {
    case 'pdf':
      return <FilePdf className={className} />;
    case 'doc':
    case 'docx':
      return <FileText className={className} />;
    case 'xls':
    case 'xlsx':
    case 'csv':
    case 'tsv':
      return <FileSpreadsheet className={className} />;
    case 'ppt':
    case 'pptx':
      return <FilePresentation className={className} />;
    case 'jpg':
    case 'jpeg':
    case 'png':
    case 'gif':
    case 'webp':
    case 'svg':
      return <FileImage className={className} />;
    case 'html':
    case 'css':
    case 'js':
    case 'ts':
    case 'json':
    case 'xml':
      return <FileCode className={className} />;
    default:
      return <File className={className} />;
  }
}
```

### 4. Update the Document Item Component

Update the document item component to use the new icons:

```typescript
// components/locations/location-documents-content.tsx
import { DocumentTypeIcon } from './document-type-icon';

// ... existing code ...

<div key={doc.id} className="flex items-center justify-between p-3 border rounded-md">
  <div className="flex items-center gap-3">
    <div className="h-10 w-10 flex items-center justify-center bg-muted rounded-md">
      <DocumentTypeIcon fileType={doc.fileType || 'unknown'} />
    </div>
    <div className="flex-1 min-w-0">
      <p className="text-sm font-medium truncate">{doc.filename}</p>
      <p className="text-xs text-muted-foreground">
        {formatFileSize(doc.fileSize || 0)} • {doc.fileType?.toUpperCase() || doc.type.toUpperCase()}
      </p>
    </div>
  </div>
  {/* ... existing code ... */}
</div>
```

## Implementation Details

1. **Enhance the Document Adapter Function**:
   - Update the function to better determine preview availability.
   - Add support for more document types.
   - Generate preview URLs for different document types.

2. **Enhance the Document Viewer Component**:
   - Add loading indicators for previews.
   - Improve error handling for preview failures.
   - Add an option to open the document in a new tab.

3. **Add Document Type Icons**:
   - Create a component for displaying document type icons.
   - Add support for various document types.

4. **Update the Document Item Component**:
   - Use the new document type icons.
   - Improve the display of document information.

5. **Test the Changes**:
   - Test previews for different document types.
   - Test error handling for preview failures.
   - Test the UI for different document types.

## Potential Issues and Solutions

1. **Cross-Origin Resource Sharing (CORS)**:
   - Some document previews might be blocked by CORS policies.
   - Consider using a proxy server or a service like Google Docs Viewer.

2. **Browser Compatibility**:
   - Different browsers might handle document previews differently.
   - Test the implementation in multiple browsers.
   - Add browser-specific fallbacks if needed.

3. **Large Documents**:
   - Large documents might take a long time to load.
   - Add loading indicators and timeout handling.
   - Consider implementing lazy loading or pagination for large documents.

## Next Steps

After adding support for document previews, the next step is to [create a testing plan](./10_testing_plan.md).
