# Step 7: Fix Location Media API Endpoint

## Overview

The seventh step in fixing the location media and UploadThing feature is to fix the location media API endpoint. The `/api/locations/media` endpoint is responsible for handling media uploads and retrieving media for a specific location. This guide focuses on ensuring that the endpoint correctly handles both POST and GET requests.

## Current Implementation

The current implementation of the `/api/locations/media` endpoint might have issues with handling the user ID format, validating input data, or returning the correct response format. It might also not be properly integrated with the authentication system.

## Required Changes

### 1. Update the POST Handler

First, update the POST handler to correctly handle the user ID format and validate input data:

```typescript
// app/api/locations/media/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/lib/db';
import { locationMedia } from '@/modules/location/schema';
import { eq, and, isNull } from 'drizzle-orm';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { z } from 'zod';

// Define a schema for validating the request body
const mediaSchema = z.object({
  locationId: z.string().uuid(),
  organizationId: z.string(),
  url: z.string().url(),
  type: z.enum(['image', 'video', 'pdf', 'doc', 'document']),
  uploadedBy: z.string(),
  title: z.string().optional(),
  description: z.string().optional(),
  fileSize: z.number().optional(),
  isPublic: z.boolean().optional().default(true),
  ordering: z.number().optional().default(0),
  metadata: z.record(z.any()).optional(),
});

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await request.json();
    
    // Validate the request body
    const result = mediaSchema.safeParse(body);
    
    if (!result.success) {
      return NextResponse.json({ error: 'Invalid request body', details: result.error.format() }, { status: 400 });
    }

    const data = result.data;

    // Create the media record
    const [media] = await db.insert(locationMedia).values({
      locationId: data.locationId,
      organizationId: data.organizationId,
      url: data.url,
      type: data.type,
      uploadedBy: data.uploadedBy,
      title: data.title || '',
      description: data.description || '',
      fileSize: data.fileSize || 0,
      isPublic: data.isPublic,
      ordering: data.ordering,
      metadata: data.metadata || {},
    }).returning();

    return NextResponse.json({ media });
  } catch (error) {
    console.error('Error creating location media:', error);
    return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 });
  }
}
```

### 2. Update the GET Handler

Next, update the GET handler to correctly retrieve media for a specific location:

```typescript
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const locationId = searchParams.get('locationId');

    if (!locationId) {
      return NextResponse.json({ error: 'Location ID is required' }, { status: 400 });
    }

    // Fetch media for the location
    const media = await db.query.locationMedia.findMany({
      where: and(
        eq(locationMedia.locationId, locationId),
        isNull(locationMedia.deletedAt)
      ),
      orderBy: [locationMedia.ordering],
      with: {
        uploader: {
          columns: {
            id: true,
            name: true,
          }
        }
      }
    });

    return NextResponse.json({ media });
  } catch (error) {
    console.error('Error fetching location media:', error);
    return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 });
  }
}
```

### 3. Add a DELETE Handler (Optional)

If you want to support deleting media, add a DELETE handler:

```typescript
export async function DELETE(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const mediaId = searchParams.get('id');

    if (!mediaId) {
      return NextResponse.json({ error: 'Media ID is required' }, { status: 400 });
    }

    // Soft delete the media record
    await db.update(locationMedia)
      .set({ deletedAt: new Date() })
      .where(eq(locationMedia.id, mediaId));

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Error deleting location media:', error);
    return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 });
  }
}
```

## Implementation Details

1. **Update the POST Handler**:
   - Add validation for the request body using a schema validation library like Zod.
   - Make sure to handle the user ID format correctly.
   - Add appropriate error handling and authentication.

2. **Update the GET Handler**:
   - Add support for fetching media for a specific location.
   - Include the uploader information in the response.
   - Add appropriate error handling and authentication.

3. **Add a DELETE Handler (Optional)**:
   - Add support for deleting media.
   - Implement soft deletion by setting the `deletedAt` field.
   - Add appropriate error handling and authentication.

4. **Test the Changes**:
   - Test the POST handler by uploading media.
   - Test the GET handler by fetching media for a specific location.
   - Test the DELETE handler by deleting media.

## Potential Issues and Solutions

1. **Authentication**:
   - Make sure the endpoint is properly authenticated.
   - Consider implementing authorization to ensure users can only access media for locations they have permission to view.

2. **Validation**:
   - Make sure to validate all input data to prevent security issues.
   - Consider using a schema validation library like Zod for more robust validation.

3. **Error Handling**:
   - Make sure to handle all possible errors and return appropriate error responses.
   - Consider logging errors for debugging purposes.

## Next Steps

After fixing the location media API endpoint, the next step is to [enhance error handling and user feedback](./08_enhance_error_handling.md).
