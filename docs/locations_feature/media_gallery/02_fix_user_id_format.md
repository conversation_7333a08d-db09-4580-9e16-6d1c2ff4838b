# Step 2: Fix User ID Format in UploadThing Core

## Overview

The second step in fixing the location media and UploadThing feature is to address the inconsistent user ID format. Currently, in `app/api/uploadthing/core.ts`, the `onUploadComplete` function passes the Stytch member ID as `uploadedBy` to the `/api/locations/media` endpoint. However, the `locationMedia` schema expects `uploadedBy` to be a UUID that references the `users.id` column in the database.

This mismatch causes issues with referential integrity and makes it difficult to associate uploaded media with the correct user in the database.

## Current Implementation

The current implementation in `app/api/uploadthing/core.ts` looks like this:

```typescript
.onUploadComplete(async ({ metadata, file }) => {
  // This code RUNS ON YOUR SERVER after upload
  console.log("Upload complete for userId:", metadata.userId);
  console.log("Organization ID:", metadata.organizationId);
  console.log("Location ID:", metadata.locationId);
  console.log("File URL:", file.ufsUrl);
  
  try {
    // Save the file information to the database
    // This would typically be done through an API endpoint
    await fetch('/api/locations/media', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        locationId: metadata.locationId,
        organizationId: metadata.organizationId,
        url: file.ufsUrl,
        type: 'image',
        uploadedBy: metadata.userId,  // This is a Stytch member ID, not a UUID
        title: file.name,
        fileSize: file.size,
        metadata: {
          key: file.key,
          uploadedAt: metadata.uploadedAt
        }
      })
    });
  } catch (error) {
    console.error("Error saving media to database:", error);
    // Continue even if database save fails, to not block the client
  }
  
  // Return data that will be accessible on the client
  return { 
    uploadedBy: metadata.userId,
    organizationId: metadata.organizationId,
    locationId: metadata.locationId,
    url: file.ufsUrl,
    name: file.name,
    size: file.size,
    key: file.key,
    type: "image",
    uploadedAt: metadata.uploadedAt
  };
})
```

## Required Changes

### 1. Create a Utility Function to Convert Stytch Member ID to Database User ID

First, we need to create a utility function that can convert a Stytch member ID to a database user ID. This function should be added to `lib/utils/userUtils.ts`:

```typescript
/**
 * Converts a Stytch member ID to a database user ID
 * @param stytchMemberId The Stytch member ID to convert
 * @returns The database user ID (UUID) or null if not found
 */
export async function getUserIdFromStytchMemberId(stytchMemberId: string): Promise<string | null> {
  try {
    // Query the database to find the user with the given Stytch member ID
    const user = await db.query.users.findFirst({
      where: eq(users.stytchMemberId, stytchMemberId),
      columns: {
        id: true
      }
    });

    return user?.id || null;
  } catch (error) {
    console.error("Error converting Stytch member ID to user ID:", error);
    return null;
  }
}
```

### 2. Update the `onUploadComplete` Function in UploadThing Core

Next, modify the `onUploadComplete` function in `app/api/uploadthing/core.ts` to use the new utility function:

```typescript
import { getUserIdFromStytchMemberId } from "@/lib/utils/userUtils";

// ... existing code ...

.onUploadComplete(async ({ metadata, file }) => {
  // This code RUNS ON YOUR SERVER after upload
  console.log("Upload complete for userId:", metadata.userId);
  console.log("Organization ID:", metadata.organizationId);
  console.log("Location ID:", metadata.locationId);
  console.log("File URL:", file.ufsUrl);
  
  try {
    // Convert Stytch member ID to database user ID
    const databaseUserId = await getUserIdFromStytchMemberId(metadata.userId);
    
    if (!databaseUserId) {
      console.error(`Could not find database user ID for Stytch member ID: ${metadata.userId}`);
      // Continue with the upload even if we can't find the user ID
      // This ensures the upload doesn't fail for the user
    }
    
    // Save the file information to the database
    await fetch('/api/locations/media', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        locationId: metadata.locationId,
        organizationId: metadata.organizationId,
        url: file.ufsUrl,
        type: 'image',
        uploadedBy: databaseUserId || metadata.userId, // Use database ID if available, fall back to Stytch ID
        title: file.name,
        fileSize: file.size,
        metadata: {
          key: file.key,
          uploadedAt: metadata.uploadedAt,
          stytchMemberId: metadata.userId // Store the original Stytch ID for reference
        }
      })
    });
  } catch (error) {
    console.error("Error saving media to database:", error);
    // Continue even if database save fails, to not block the client
  }
  
  // Return data that will be accessible on the client
  return { 
    uploadedBy: metadata.userId,
    organizationId: metadata.organizationId,
    locationId: metadata.locationId,
    url: file.ufsUrl,
    name: file.name,
    size: file.size,
    key: file.key,
    type: "image",
    uploadedAt: metadata.uploadedAt
  };
})
```

### 3. Update the `locationDocuments` Handler

Make the same changes to the `locationDocuments` handler in `app/api/uploadthing/core.ts`:

```typescript
.onUploadComplete(async ({ metadata, file }) => {
  // This code RUNS ON YOUR SERVER after upload
  console.log("Upload complete for userId:", metadata.userId);
  console.log("Organization ID:", metadata.organizationId);
  console.log("Location ID:", metadata.locationId);
  console.log("File URL:", file.ufsUrl);
  
  // Determine file type based on file type
  let fileType = "document";
  if (file.type.includes("image")) {
    fileType = "image";
  } else if (file.type.includes("pdf")) {
    fileType = "pdf";
  } else if (
    file.type.includes("document") ||
    file.type.includes("msword")
  ) {
    fileType = "doc";
  }
  
  try {
    // Convert Stytch member ID to database user ID
    const databaseUserId = await getUserIdFromStytchMemberId(metadata.userId);
    
    if (!databaseUserId) {
      console.error(`Could not find database user ID for Stytch member ID: ${metadata.userId}`);
      // Continue with the upload even if we can't find the user ID
    }
    
    // Save the file information to the database
    await fetch('/api/locations/media', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        locationId: metadata.locationId,
        organizationId: metadata.organizationId,
        url: file.ufsUrl,
        type: fileType,
        uploadedBy: databaseUserId || metadata.userId, // Use database ID if available, fall back to Stytch ID
        title: file.name,
        fileSize: file.size,
        metadata: {
          key: file.key,
          uploadedAt: metadata.uploadedAt,
          stytchMemberId: metadata.userId // Store the original Stytch ID for reference
        }
      })
    });
  } catch (error) {
    console.error("Error saving media to database:", error);
    // Continue even if database save fails, to not block the client
  }
  
  // Return data that will be accessible on the client
  return { 
    uploadedBy: metadata.userId,
    organizationId: metadata.organizationId,
    locationId: metadata.locationId,
    url: file.ufsUrl,
    name: file.name,
    size: file.size,
    key: file.key,
    type: fileType,
    uploadedAt: metadata.uploadedAt
  };
})
```

## Implementation Details

1. **Create the Utility Function**:
   - Create or update `lib/utils/userUtils.ts` to include the `getUserIdFromStytchMemberId` function.
   - Make sure to import the necessary dependencies:
     ```typescript
     import { db } from "@/lib/db";
     import { users } from "@/modules/user/schema";
     import { eq } from "drizzle-orm";
     ```

2. **Update UploadThing Core**:
   - Import the new utility function in `app/api/uploadthing/core.ts`.
   - Update both `onUploadComplete` functions to use the utility function.
   - Add error handling to gracefully handle cases where the user ID can't be found.
   - Store the original Stytch member ID in the metadata for reference.

3. **Test the Changes**:
   - Test uploading both images and documents to ensure the correct user ID is being used.
   - Check the database to verify that the `uploadedBy` field contains a valid UUID that references the `users` table.

## Potential Issues and Solutions

1. **Missing User Records**:
   - If a user doesn't have a record in the database, the `getUserIdFromStytchMemberId` function will return `null`.
   - The code falls back to using the Stytch member ID in this case, which might cause issues with referential integrity.
   - Consider adding a fallback mechanism to create a user record if one doesn't exist.

2. **Performance Concerns**:
   - The `getUserIdFromStytchMemberId` function makes a database query for each upload, which could impact performance if there are many uploads.
   - Consider caching user IDs or using a more efficient lookup mechanism.

3. **Error Handling**:
   - The current implementation continues with the upload even if the user ID can't be found, which might lead to inconsistent data.
   - Consider adding more robust error handling or validation.

## Next Steps

After fixing the user ID format in UploadThing Core, the next step is to [implement the media adapter function](./03_implement_media_adapter.md).
