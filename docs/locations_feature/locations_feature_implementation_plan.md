# Locations Feature Implementation Plan

**Date: May 4, 2025**
**Updated: May 5, 2025**

This document outlines the atomic-level, step-by-step plan to implement the locations feature in the Scene-o-matic application. The plan is based on the current implementation and the requirements specified by the user.

BE SURE TO CONSULT THIS DOCUMENT FOR THE LOCATIONS FEATURES OVERVIEW: docs/locations_feature/locations_feature_implementation_v1.md

## Table of Contents

1. [Overview](#overview)
2. [Requirements](#requirements)
3. [Implementation Plan](#implementation-plan)
4. [Security Considerations](#security-considerations)
5. [Testing Strategy](#testing-strategy)

## Overview

The locations feature is a core component of the Scene-o-matic application, designed to serve as a comprehensive management system for filming locations. It allows users to create, view, edit, and manage locations with detailed information, media galleries, and integration with other features like projects and maps.

## Requirements

Based on the analysis of the current implementation and the application's needs, the locations feature should include the following functionality:

1. **Location Management**
   - Create, read, update, and delete locations
   - Comprehensive location details (address, coordinates, type, status, etc.)
   - Location approval workflow (pending, approved, rejected, secured)
   - Role-based access control for location operations

2. **Location Listing and Filtering**
   - Grid and list views for locations
   - Advanced filtering by type, status, project, etc.
   - Search functionality for finding locations
   - Sorting options for organizing locations

3. **Location Details**
   - Detailed view of location information
   - Photo gallery with navigation controls
   - Map view of the location
   - Document management for location-related files
   - Notes and comments for collaboration

4. **Integration with Other Features**
   - Project association for locations
   - Map integration for visualizing locations
   - Favorites system for organizing locations
   - Document management for location-related files

5. **Mobile Responsiveness**
   - Responsive design for all location components
   - Mobile-friendly location creation and editing
   - Touch-friendly controls for mobile users

6. **Sharing and Collaboration**
   - Shareable links for locations
   - Password protection for shared links
   - Expiration dates for shared links
   - View mode control (admin/client) for shared content
   - Access logging for shared links

## Implementation Plan

### Phase 1: Core Data Fetching and Display

1. **Enhance Location Data Hooks**
   - Update `use-locations.ts` to use TanStack Query for real API data
   - Create `use-location-details.ts` for fetching single location details
   - Create `use-location-mutations.ts` for CRUD operations
   - Implement proper error handling and loading states

2. **Update Locations Page**
   - Connect to real API data using enhanced hooks
   - Implement loading and error states
   - Add pagination support
   - Maintain existing grid/list view toggle

3. **Enhance Location Grid and List Components**
   - Update to handle real API data structure
   - Add loading skeletons
   - Implement proper error states
   - Ensure responsive design

4. **Implement Location Detail Page**
   - Create comprehensive location detail page
   - Implement tabs for different sections (details, gallery, map, documents, notes)
   - Add role-based access control for sensitive information
   - Connect to real API data

### Phase 2: Location Creation and Editing

5. **Implement Location Creation Form**
   - Create form with all required fields
   - Implement validation using Zod schemas
   - Add address lookup and geocoding
   - Implement file uploads for location images
   - Connect to API for creating locations

6. **Implement Location Editing Form**
   - Reuse components from creation form
   - Pre-populate form with existing location data
   - Implement optimistic updates
   - Add confirmation for sensitive changes
   - Connect to API for updating locations

7. **Implement Location Deletion**
   - Add confirmation dialog for deletion
   - Implement soft delete functionality
   - Update UI to reflect deleted state
   - Connect to API for deleting locations

8. **Implement Location Approval Workflow**
   - Add UI for approving/rejecting locations
   - Implement status change functionality
   - Add comments for rejection reasons
   - Connect to API for status changes

### Phase 3: Advanced Filtering and Search

9. **Implement Advanced Filtering**
   - Create filter modal component
   - Implement filter logic for various criteria
   - Add filter persistence
   - Connect to API with filter parameters

10. **Enhance Search Functionality**
    - Implement debounced search input
    - Add search highlighting
    - Support searching across multiple fields
    - Connect to API with search parameters

11. **Add Sorting Options**
    - Implement sorting by various criteria
    - Add sort direction toggle
    - Persist sort preferences
    - Connect to API with sort parameters

12. **Implement Filter Combinations**
    - Allow combining multiple filters
    - Create UI for managing filter combinations
    - Implement filter presets
    - Add clear all filters option

### Phase 4: Media and Document Management

13. **Enhance Location Gallery**
    - Implement media upload functionality
    - Add gallery navigation controls
    - Support multiple file types (images, videos)
    - Implement media deletion and reordering
    - Adapt gallery display based on view mode

14. **Implement Document Management**
    - Create document upload component
    - Add document categorization
    - Implement document preview
    - Add document versioning
    - Connect to API for document operations
    - Control document visibility based on view mode

15. **Add Notes and Comments**
    - Implement notes component
    - Add comment threading
    - Support rich text formatting
    - Implement mentions and notifications
    - Connect to API for notes and comments
    - Filter notes/comments based on view mode

16. **Implement Media Optimization**
    - Add image resizing and compression
    - Implement lazy loading for media
    - Add caching for frequently accessed media
    - Optimize media delivery for different devices

### Phase 5: Integration and Performance

17. **Enhance Project Integration**
    - Implement project selection in location forms
    - Add project filtering on locations page
    - Create project-specific location views
    - Connect to project API endpoints

18. **Optimize Map Integration**
    - Ensure location changes reflect in map view
    - Add ability to create/edit locations from map
    - Implement location clustering on map
    - Optimize map performance with many locations

19. **Implement Performance Optimizations**
    - Add virtualization for location lists
    - Implement data caching strategies
    - Optimize API requests with pagination and filtering
    - Add prefetching for common operations

20. **Enhance Mobile Experience**
    - Optimize layout for small screens
    - Implement touch-friendly controls
    - Add mobile-specific features (camera access, GPS)
    - Test and optimize performance on mobile devices

### Phase 6: Sharing and Collaboration

21. **Implement Location Sharing**
    - Create database schema for shared location links
    - Implement API endpoints for creating and accessing shared links
    - Create share modal component with customization options
    - Add token generation for secure sharing
    - Implement access logging for shared links

22. **Add Password Protection**
    - Implement password hashing for shared links
    - Create password input UI for accessing protected links
    - Add server-side validation for passwords
    - Implement proper error handling for invalid passwords

23. **Implement Shared View**
    - Create public view page for shared locations
    - Implement view-only version of location details
    - Add proper security checks for shared content
    - Create consistent UI between regular and shared views

24. **Add View Mode Control**
    - Implement view mode toggle (admin/client)
    - Create different information displays based on view mode
    - Add view mode selection in share modal
    - Implement permission checks for view mode access

## Atomic Implementation Steps

### 1. Enhance Location Data Hooks

1.1. Update `use-locations.ts` to use TanStack Query:
```typescript
"use client"

import { useQuery } from "@tanstack/react-query"
import { useOrganization } from "./use-organization-data"

interface LocationsQueryParams {
  status?: string;
  type?: string;
  projectId?: string;
  search?: string;
  page?: number;
  pageSize?: number;
}

export function useLocations(params: LocationsQueryParams = {}) {
  const { data: organization } = useOrganization()
  const organizationId = organization?.id

  return useQuery({
    queryKey: ['locations', organizationId, params],
    queryFn: async () => {
      // Build query string from params
      const queryParams = new URLSearchParams()
      if (params.status) queryParams.append('status', params.status)
      if (params.type) queryParams.append('type', params.type)
      if (params.projectId) queryParams.append('projectId', params.projectId)
      if (params.search) queryParams.append('search', params.search)
      if (params.page) queryParams.append('page', params.page.toString())
      if (params.pageSize) queryParams.append('pageSize', params.pageSize.toString())
      
      const queryString = queryParams.toString()
      const url = `/api/locations${queryString ? `?${queryString}` : ''}`
      
      const response = await fetch(url)
      if (!response.ok) {
        throw new Error('Failed to fetch locations')
      }
      
      return response.json()
    },
    enabled: !!organizationId,
    staleTime: 60 * 1000, // 1 minute
  })
}
```

1.2. Create `use-location-details.ts` for fetching single location details:
```typescript
"use client"

import { useQuery } from "@tanstack/react-query"

export function useLocationDetails(locationId: string) {
  return useQuery({
    queryKey: ['location', locationId],
    queryFn: async () => {
      const response = await fetch(`/api/locations/${locationId}`)
      if (!response.ok) {
        throw new Error('Failed to fetch location details')
      }
      
      return response.json()
    },
    enabled: !!locationId,
    staleTime: 60 * 1000, // 1 minute
  })
}
```

1.3. Create `use-location-mutations.ts` for CRUD operations:
```typescript
"use client"

import { useMutation, useQueryClient } from "@tanstack/react-query"
import { useOrganization } from "./use-organization-data"
import { toast } from "sonner"

export function useLocationMutations() {
  const queryClient = useQueryClient()
  const { data: organization } = useOrganization()
  const organizationId = organization?.id

  const createLocation = useMutation({
    mutationFn: async (locationData: any) => {
      const response = await fetch('/api/locations', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ...locationData,
          organizationId,
        }),
      })
      
      if (!response.ok) {
        const errorData = await response.json().catch(() => null)
        throw new Error(errorData?.error || 'Failed to create location')
      }
      
      return response.json()
    },
    onSuccess: () => {
      // Invalidate locations query to refetch data
      queryClient.invalidateQueries({ queryKey: ['locations', organizationId] })
      toast.success('Location created successfully')
    },
    onError: (error: Error) => {
      toast.error(error.message)
    }
  })

  const updateLocation = useMutation({
    mutationFn: async ({ id, data }: { id: string; data: any }) => {
      const response = await fetch(`/api/locations/${id}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      })
      
      if (!response.ok) {
        const errorData = await response.json().catch(() => null)
        throw new Error(errorData?.error || 'Failed to update location')
      }
      
      return response.json()
    },
    onSuccess: (_, variables) => {
      // Invalidate specific location query and locations list
      queryClient.invalidateQueries({ queryKey: ['location', variables.id] })
      queryClient.invalidateQueries({ queryKey: ['locations', organizationId] })
      toast.success('Location updated successfully')
    },
    onError: (error: Error) => {
      toast.error(error.message)
    }
  })

  const deleteLocation = useMutation({
    mutationFn: async (id: string) => {
      const response = await fetch(`/api/locations/${id}`, {
        method: 'DELETE',
      })
      
      if (!response.ok) {
        const errorData = await response.json().catch(() => null)
        throw new Error(errorData?.error || 'Failed to delete location')
      }
      
      return true
    },
    onSuccess: (_, id) => {
      // Invalidate specific location query and locations list
      queryClient.invalidateQueries({ queryKey: ['location', id] })
      queryClient.invalidateQueries({ queryKey: ['locations', organizationId] })
      toast.success('Location deleted successfully')
    },
    onError: (error: Error) => {
      toast.error(error.message)
    }
  })

  return {
    createLocation,
    updateLocation,
    deleteLocation,
  }
}
```

### 2. Update Locations Page

2.1. Update the locations page to use real data:
```typescript
"use client";

import { useState } from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Plus, Search, Filter, Grid3X3, List, Loader2 } from "lucide-react";
import Link from "next/link";
import { LocationGrid } from "@/components/locations/location-grid";
import { LocationList } from "@/components/locations/location-list";
import { DashboardShell } from "@/components/dashboard/dashboard-shell";
import { DashboardHeader } from "@/components/dashboard/dashboard-header";
import { useLocations } from "@/hooks/use-locations";
import { useDebounce } from "@/hooks/use-debounce";
import { Pagination } from "@/components/ui/pagination";
import { FilterModal } from "@/components/locations/filter-modal";

interface LocationsPageProps {
  params: Promise<{
    organizationSlug: string;
  }>;
}

export default function LocationsPage({ params }: LocationsPageProps) {
  const { organizationSlug } = React.use(params);
  const [searchQuery, setSearchQuery] = useState("");
  const [viewMode, setViewMode] = useState("grid");
  const [page, setPage] = useState(1);
  const [pageSize, setPageSize] = useState(12);
  const [isFilterModalOpen, setIsFilterModalOpen] = useState(false);
  const [filters, setFilters] = useState({
    status: "",
    type: "",
    projectId: "",
  });
  
  // Debounce search query to prevent excessive API calls
  const debouncedSearch = useDebounce(searchQuery, 500);
  
  // Fetch locations with filters
  const { data, isLoading, error } = useLocations({
    search: debouncedSearch,
    status: filters.status,
    type: filters.type,
    projectId: filters.projectId,
    page,
    pageSize,
  });
  
  // Extract locations and pagination info from data
  const locations = data?.locations || [];
  const totalLocations = data?.total || 0;
  const totalPages = Math.ceil(totalLocations / pageSize);

  // Handle filter changes
  const handleFilterChange = (newFilters) => {
    setFilters(newFilters);
    setPage(1); // Reset to first page when filters change
  };

  return (
    <DashboardShell>
      <DashboardHeader 
        title="Locations" 
        description="Manage and organize your filming locations"
      >
        <Link href={`/organizations/${organizationSlug}/locations/new`}>
          <Button>
            <Plus className="mr-2 h-4 w-4" />
            Add New Location
          </Button>
        </Link>
      </DashboardHeader>

      <div className="flex flex-col md:flex-row gap-4 mb-6 mt-6">
        <div className="relative flex-1">
          <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input 
            type="search" 
            placeholder="Search locations..." 
            className="w-full pl-8"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
        </div>
        <Button 
          variant="outline" 
          className="gap-1"
          onClick={() => setIsFilterModalOpen(true)}
        >
          <Filter className="h-4 w-4" />
          Filters
        </Button>
        <Tabs 
          defaultValue={viewMode} 
          value={viewMode} 
          onValueChange={setViewMode} 
          className="w-[120px]"
        >
          <TabsList>
            <TabsTrigger value="grid" className="px-3">
              <Grid3X3 className="h-4 w-4" />
            </TabsTrigger>
            <TabsTrigger value="list" className="px-3">
              <List className="h-4 w-4" />
            </TabsTrigger>
          </TabsList>
        </Tabs>
      </div>

      {isLoading ? (
        <div className="flex items-center justify-center py-10">
          <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
        </div>
      ) : error ? (
        <div className="flex flex-col items-center justify-center py-10">
          <p className="text-destructive">Error loading locations</p>
          <Button 
            variant="outline" 
            className="mt-4"
            onClick={() => window.location.reload()}
          >
            Try Again
          </Button>
        </div>
      ) : locations.length === 0 ? (
        <div className="flex flex-col items-center justify-center py-10">
          <p className="text-muted-foreground">No locations found</p>
          <Link href={`/organizations/${organizationSlug}/locations/new`}>
            <Button className="mt-4">
              <Plus className="mr-2 h-4 w-4" />
              Add New Location
            </Button>
          </Link>
        </div>
      ) : (
        <>
          <Tabs 
            defaultValue={viewMode} 
            value={viewMode} 
            onValueChange={setViewMode} 
            className="space-y-4"
          >
            <TabsContent value="grid" className="space-y-4">
              <LocationGrid locations={locations} organizationSlug={organizationSlug} />
            </TabsContent>
            <TabsContent value="list" className="space-y-4">
              <LocationList locations={locations} organizationSlug={organizationSlug} />
            </TabsContent>
          </Tabs>
          
          {totalPages > 1 && (
            <div className="flex justify-center mt-6">
              <Pagination
                currentPage={page}
                totalPages={totalPages}
                onPageChange={setPage}
              />
            </div>
          )}
        </>
      )}
      
      <FilterModal
        isOpen={isFilterModalOpen}
        onClose={() => setIsFilterModalOpen(false)}
        filters={filters}
        onFilterChange={handleFilterChange}
      />
    </DashboardShell>
  );
}
```

## Security Considerations

1. **Authentication and Authorization**
   - All API endpoints must require authentication
   - Implement proper RBAC checks for all operations
   - Ensure organization isolation for multi-tenant security
   - Validate user permissions for sensitive operations

2. **Input Validation**
   - Validate all inputs using Zod schemas
   - Sanitize user inputs to prevent injection attacks
   - Implement proper error handling for invalid inputs
   - Use TypeScript for type safety

3. **Data Protection**
   - Ensure sensitive location data is only accessible to authorized users
   - Implement proper access controls for location media
   - Use secure connections for all API requests
   - Implement proper error handling to prevent data leakage

4. **API Security**
   - Implement rate limiting for API endpoints
   - Add CSRF protection for form submissions
   - Use proper HTTP methods for different operations
   - Validate request origins and referrers

5. **Shared Content Security**
   - Use secure random tokens for shared links
   - Implement proper password hashing for protected links
   - Add expiration dates for shared links
   - Log access to shared links for auditing
   - Implement view mode control for sensitive information

## Testing Strategy

1. **Unit Testing**
   - Test custom hooks with mock fetch responses
   - Test components with mock data
   - Test validation logic
   - Test utility functions

2. **Integration Testing**
   - Test the locations page with the backend
   - Test location creation/editing flows
   - Test filtering and search functionality
   - Test pagination and sorting

3. **End-to-End Testing**
   - Test the complete user flow from listing to creating/editing locations
   - Test integration with map and projects features
   - Test mobile responsiveness
   - Test error handling and recovery

4. **Performance Testing**
   - Test with large datasets
   - Test loading times for location lists
   - Test media loading performance
   - Test filtering and search performance

5. **Security Testing**
   - Test RBAC enforcement
   - Test input validation
   - Test API endpoint security
   - Test multi-tenant isolation
   - Test shared link security (password protection, expiration)
