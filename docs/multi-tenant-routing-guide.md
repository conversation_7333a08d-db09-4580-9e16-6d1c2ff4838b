# Scene-o-matic: Multi-Tenant Routing Guide

This document explains the core routing structure used in the Scene-o-matic application to support its multi-tenant architecture, where each user operates within the context of a specific organization. It focuses on how dynamic URL segments and the Next.js App Router are used to achieve data isolation and apply shared layouts.

## Core Concept: Organization-Scoped Data

The fundamental principle is that **all application data and user activity post-authentication are scoped to a single organization**. Users must select or belong to an organization, and the application ensures they only see and interact with data belonging to that specific organization (tenant).

## Routing Pattern: Dynamic Segments

The application leverages Next.js dynamic route segments to capture the current organization context directly from the URL.

**The primary pattern for all authenticated, organization-specific pages is:**

`app/(dashboard)/organizations/[organizationSlug]/...`

Let's break this down:

1.  **`app/`**: The root directory for Next.js App Router.
2.  **`(dashboard)/`**: This is a **Route Group**.
    *   **Purpose:** It applies a shared layout (`app/(dashboard)/layout.tsx`) to all routes nested within it. This layout typically includes the main application shell (sidebar, header, etc.).
    *   **URL Impact:** Route groups **do not** affect the URL path. Files inside `(dashboard)` are accessed as if the `(dashboard)` part wasn't there.
3.  **`organizations/`**: A standard directory segment within the route group.
4.  **`[organizationSlug]`**: This is a **Dynamic Route Segment**.
    *   **Purpose:** It captures the unique identifier (slug) of the organization the user is currently accessing (e.g., `acme-corp`, `cine-global`). This slug is the key to multi-tenancy in the frontend routing.
    *   **Access:** The value captured by this segment (e.g., "acme-corp") is automatically passed as the `organizationSlug` parameter within the `params` prop to the corresponding page and layout components.
5.  **`...` (Further Nesting)**: Represents subsequent routes for specific features within that organization. These can also include dynamic segments for specific resources like projects or locations.
    *   `projects/`: Lists projects for the organization. Maps to `app/(dashboard)/organizations/[organizationSlug]/projects/page.tsx`.
    *   `projects/[projectId]/`: Shows details for a specific project. Maps to `app/(dashboard)/organizations/[organizationSlug]/projects/[projectId]/page.tsx`. The `projectId` is also captured and passed in `params`.
    *   `locations/`: Lists locations for the organization. Maps to `app/(dashboard)/organizations/[organizationSlug]/locations/page.tsx`.
    *   `locations/[locationId]/`: Shows details for a specific location. Maps to `app/(dashboard)/organizations/[organizationSlug]/locations/[locationId]/page.tsx`.

**Example URLs and File Paths:**

*   **URL:** `/organizations/acme-corp/projects`
    *   **File:** `app/(dashboard)/organizations/[organizationSlug]/projects/page.tsx`
    *   **`params`:** `{ organizationSlug: 'acme-corp' }`
*   **URL:** `/organizations/cine-global/projects/proj_123abc`
    *   **File:** `app/(dashboard)/organizations/[organizationSlug]/projects/[projectId]/page.tsx`
    *   **`params`:** `{ organizationSlug: 'cine-global', projectId: 'proj_123abc' }`
*   **URL:** `/organizations/acme-corp/settings/members`
    *   **File:** `app/(dashboard)/organizations/[organizationSlug]/settings/members/page.tsx`
    *   **`params`:** `{ organizationSlug: 'acme-corp' }`

## Shared Layout Application

By placing all organization-specific pages within the `app/(dashboard)/organizations/[organizationSlug]/...` structure, they automatically inherit the layout defined in `app/(dashboard)/layout.tsx`. This layout component also receives the `params` (including `organizationSlug`), allowing it to potentially display organization-specific branding or navigation elements consistently across all pages within that organization's context.

## Data Fetching Context

Page components (and server components in general) within this structure receive the `params` prop containing the dynamic segment values (`organizationSlug`, `projectId`, etc.).

```typescriptreact
// Example: app/(dashboard)/organizations/[organizationSlug]/projects/page.tsx

interface OrgProjectsPageProps {
  params: {
    organizationSlug: string; // e.g., 'acme-corp'
  };
  // searchParams?: { [key: string]: string | string[] | undefined }; // For query params
}

export default async function OrgProjectsPage({ params }: OrgProjectsPageProps) {
  const { organizationSlug } = params;

  // Fetch data specifically for this organization using the slug (or derived ID)
  // Example: const projects = await fetchProjectsForOrganization(organizationSlug);
  // IMPORTANT: Server-side data fetching MUST filter by organizationSlug/ID.

  return (
    <div>
      <h1>Projects for {organizationSlug}</h1>
      {/* Render projects list */}
    </div>
  );
}
```

**Crucially, all server-side data fetching logic (whether in Server Components, API routes, or Server Actions) MUST use the `organizationSlug` (or more commonly, a corresponding `organizationId` derived from the slug) to filter database queries.** This is the core mechanism for enforcing data isolation between tenants.

## API Route Structure

API routes should mirror this organization-scoped principle, typically using the organization's stable ID (`organizationId`) rather than the slug for backend operations.

**Recommended API Pattern:**

`app/api/organizations/[organizationId]/...`

*   **`app/api/organizations/[organizationId]/projects/route.ts`**: Handles requests for projects within a specific organization.
*   **`app/api/organizations/[organizationId]/locations/[locationId]/route.ts`**: Handles requests for a specific location within a specific organization.

Backend logic within these API routes must validate that the authenticated user making the request has permission to access the specified `organizationId`.

## Summary for Developers

1.  **URL is Context:** The `organizationSlug` in the URL defines the current tenant.
2.  **File Structure:** Place all organization-specific pages under `app/(dashboard)/organizations/[organizationSlug]/`.
3.  **Shared Layout:** These pages automatically use the layout from `app/(dashboard)/layout.tsx`.
4.  **Access Params:** Use the `params` prop in pages and layouts to get `organizationSlug`, `projectId`, etc.
5.  **Data Fetching:** **ALWAYS** use the `organizationSlug` or derived `organizationId` to filter data on the server-side (database queries, API calls).
6.  **API Routes:** Structure API routes under `app/api/organizations/[organizationId]/`.
7.  **Permissions:** Implement checks on the server-side to ensure the user belongs to the organization and has the necessary role/permissions for the requested action or data.

Following this structure ensures a robust, scalable, and secure multi-tenant application.
