# Location Management SaaS Project Structure Guide

This document provides an overview of the project structure for the Location Management SaaS platform, explaining the organization of files, modules, and components to help developers understand where to find and add features.

## Core Architecture Principles

The project follows a modular, domain-driven design with clear separation of concerns. Key architectural principles include:

1. **Domain-Driven Modules**: Organized by business domain in the `modules/` directory
2. **Server Actions**: Placed in domain modules as `actions.ts` 
3. **REST API Routes**: Using the Next.js App Router pattern in `app/api/`
4. **Component Organization**: Local components in `_components` folders, global in the root `components/` folder
5. **Multi-Tenant Architecture**: Enforced with Stytch for authentication and organization-based data separation

## Directory Structure Overview

### Root-Level Directories

- **`modules/`**: Domain-specific business logic, data models, and server-side functionality
- **`components/`**: Reusable UI components used across the application
- **`hooks/`**: Custom React hooks 
- **`providers/`**: Global React Context providers (e.g., Theme, potentially Auth session). Note: Server state is managed by TanStack Query and client state by <PERSON>ust<PERSON>, often initiated or configured here.
- **`lib/`**: Utility functions, configurations, and third-party integrations
- **`app/`**: Next.js App Router pages and API routes
- **`drizzle/`**: Database migration files and configurations

## Modules Directory (`modules/`)

The `modules/` directory contains domain-specific code organized by business domain. Each module encapsulates related functionality:

```
modules/
├── auth/             # Authentication logic (Stytch integration)
├── organization/     # Organization management
├── project/          # Project functionality
├── location/         # Location management
├── document/         # Document handling
├── map/              # Map functionality (Mapbox integration)
├── task/             # Task management
├── scene/            # Scene management (related to projects/locations)
├── user/             # User management
└── shared/           # Shared utilities, types, constants across modules
```
*(Note: This reflects top-level modules. Specific features like billing or AI might be integrated within these or added later)*

Each module typically contains these file types:

- **`actions.ts`**: Server actions (Next.js)
- **`domain.ts`**: Domain entities and business logic
- **`model.ts`**: Drizzle database models
- **`schema.ts`**: Zod validation schemas
- **`dtos.ts`**: Data transfer objects
- **`service.ts`**: Service layer for business logic
- **`seed.ts`**: Seed data for development/testing

### When to Add a New Module

Create a new module when:
- You're adding an entirely new domain concept
- A set of features is logically distinct from existing modules
- The functionality has its own data models and business logic

### Example: Adding a New Feature to an Existing Module

For example, to add a "comments" feature to locations:

1. Update `modules/location/model.ts` with the comment database model
2. Add comment-related schemas to `modules/location/schema.ts`
3. Create comment-related server actions in `modules/location/actions.ts`
4. Update the service layer in `modules/location/service.ts`
5. Add comment UI components in `components/locations/`
6. Create API routes in `app/api/locations/[locationId]/comments/`

## Components Directory (`components/`)

The `components/` directory contains reusable UI components organized by feature or domain:

```
components/
├── ui/               # Base UI components (shadcn/ui based)
├── auth/             # Authentication components (forms, etc.)
├── maps/             # Mapping components (Mapbox related)
├── locations/        # Location-specific components (list, grid, details)
├── projects/         # Project-specific components (list, grid, details)
├── documents/        # Document-related components
├── calendar/         # Calendar view components
├── dashboard/        # Dashboard layout and widgets
├── onboarding/       # Onboarding flow components
├── scenes/           # Scene-related components
├── settings/         # Settings page components
├── tasks/            # Task-related components
├── team/             # Team/member management components
├── debug/            # Debugging utility components
└── shared/           # Components shared across multiple features but not generic UI
```

### When to Create Component Folders

Use these guidelines for component placement:

- **Global/reusable components**: Place in the appropriate subdirectory under `components/`
- **Page-specific components**: Place in `app/path/to/_components/`
- **Feature-specific but used in multiple pages**: Place in the appropriate subdirectory under `components/`

## API Routes Organization (`app/api/`)

API routes follow RESTful conventions, organized by resource:

```
app/api/
├── auth/                          # Authentication endpoints
├── organizations/                 # Organization endpoints
│   └── [organizationId]/          # Specific organization
│       ├── members/               # Organization members
│       └── sso/                   # SSO configuration
├── projects/                      # Projects endpoints
│   └── [projectId]/               # Specific project
├── locations/                     # Locations endpoints
│   └── [locationId]/              # Specific location
├── documents/                     # Documents endpoints
├── maps/                          # Maps endpoints
├── tasks/                         # Tasks endpoints
├── media/                         # Media endpoints
├── users/                         # Users endpoints
├── billing/                       # Billing endpoints
├── ai/                            # AI endpoints
└── webhooks/                      # Webhook handlers
    ├── stytch/                    # Stytch webhooks 
    ├── polar/                     # Polar webhooks
    └── uploadthing/               # UploadThing webhooks
```

### REST API Pattern

Each resource generally has these endpoints:

- `GET /api/resource` - List resources
- `POST /api/resource` - Create a resource
- `GET /api/resource/[id]` - Get a specific resource
- `PATCH /api/resource/[id]` - Update a resource
- `DELETE /api/resource/[id]` - Delete a resource

### When to Add a New API Route

Add a new API route when:
- You need a new resource endpoint
- You're adding a specialized action that doesn't fit the server action pattern
- You need to handle webhook callbacks or third-party API interactions

## Page Organization (`app/`)

The application uses Next.js App Router with route groups:

```
app/
├── layout.tsx                     # Root layout
├── page.tsx                       # Landing page (or initial redirect)
├── globals.css                    # Global styles
├── favicon.ico                    # Favicon
├── api/                           # API routes (see API section below)
├── auth/                          # Authentication pages (login, register, callback)
├── organizations/                 # Organization-specific routes ARE HERE
│   └── [organizationSlug]/        # Dynamic segment for organization context
│       ├── layout.tsx             # Layout applying the dashboard shell
│       ├── page.tsx               # Organization dashboard overview
│       ├── projects/              # Org's projects section
│       │   └── [projectId]/       # Specific project view
│       ├── locations/             # Org's locations section
│       │   └── [locationId]/      # Specific location view
│       ├── map/                   # Org's map view
│       ├── documents/             # Org's documents
│       ├── calendar/              # Org's calendar
│       ├── team/                  # Org's team members
│       ├── settings/              # Org's settings
│       └── ...                    # Other org-specific features (tasks, scenes, etc.)
├── providers/                     # Client-side providers setup
└── (dashboard)/                   # Route Group defining the SHARED DASHBOARD LAYOUT
    ├── layout.tsx                 # The main dashboard layout (sidebar, header)
    └── _components/               # Components specific to the dashboard layout shell
```
*(Note: Marketing pages might be added later, potentially in a `(marketing)` group)*

### Route Groups

The application uses these main route groups:

- **`(marketing)`**: Public marketing pages
- **`(dashboard)`**: Authenticated application pages
- **`auth/`**: Authentication pages (not a route group, but logically separate)

### Page-Specific Components

Each section of the application can have its own components in a `_components` folder:

```
app/(dashboard)/projects/[projectId]/_components/
├── project-header.tsx
└── project-tabs.tsx
```

These components are specific to this page or section and not intended for reuse elsewhere.

## Multi-Tenant Data Access Pattern

This application follows a multi-tenant pattern where:

1. **Organization-based isolation**: Each organization has its own data
2. **Authentication with Stytch**: Using Stytch for B2B authentication
3. **Role-based access control**: Different permissions for admin, manager, scout, viewer roles

### Data Access Patterns

All data access should follow these patterns:

1. **Always filter by organization**: Database queries always include `organizationId`
2. **Check user access**: Verify the user has access to the requested organization
3. **Check permissions**: Validate the user's role has the required permissions

Example service code:

```typescript
// modules/location/service.ts
export async function getLocationsForProject(projectId: string, userId: string) {
  // Get the project to check organization
  const project = await db.query.projects.findFirst({
    where: eq(projects.id, projectId)
  });
  
  if (!project) throw new Error('Project not found');
  
  // Verify user is member of this organization
  const membership = await db.query.organizationMembers.findFirst({
    where: {
      organizationId: project.organizationId,
      userId: userId
    }
  });
  
  if (!membership) throw new Error('Access denied');
  
  // Return locations for this project with org filtering for extra security
  return db.query.projectLocations.findMany({
    where: {
      projectId: projectId,
      project: {
        organizationId: project.organizationId
      }
    },
    with: {
      location: true
    }
  });
}
```

## Adding New Features

When adding new features to the application, follow these guidelines:

### 1. For a New Domain Feature

Example: Adding a "comments" system to locations

1. Update/create models in the appropriate module (e.g., `modules/location/model.ts`)
2. Create validation schemas in `modules/location/schema.ts`
3. Implement server actions in `modules/location/actions.ts`
4. Create service methods in `modules/location/service.ts`
5. Add UI components in `components/locations/`
6. Create REST API endpoints if needed in `app/api/locations/[locationId]/comments/`
7. Add the UI to the appropriate pages in `app/(dashboard)/`

### 2. For a New Page or Route

Example: Adding a location calendar view

1. Create the page at `app/(dashboard)/projects/[projectId]/locations/calendar/page.tsx`
2. Add page-specific components to `app/(dashboard)/projects/[projectId]/locations/calendar/_components/`
3. Update navigation components to include the new route
4. Add any new server actions or API endpoints needed
5. Update permission checks to include the new route

### 3. For a New API Endpoint

Example: Adding a location export API

1. Create the API route at `app/api/locations/export/route.ts`
2. Implement the service method in `modules/location/service.ts`
3. Add required validation and permission checks
4. Update the API client in `lib/api/client.ts` if using it

## Common Development Paths

### Authentication Flow

1. `auth/` module for Stytch integration
2. `app/api/auth/` for authentication endpoints
3. `app/auth/` for login/register pages
4. `components/auth/` for authentication UI components
5. `providers/auth-provider.tsx` for auth context
6. `hooks/use-auth.ts` for authentication hooks

### Organization Management

1. `organization/` module for domain logic
2. `app/api/organizations/` for organization endpoints
3. `app/(dashboard)/organizations/` for organization pages
4. `components/organizations/` for organization UI components

### Location Features

1. `location/` module for domain logic
2. `app/api/locations/` for location endpoints
3. `app/(dashboard)/projects/[projectId]/locations/` for location pages
4. `components/locations/` for location UI components
5. `components/maps/` for mapping components

### Billing and Subscriptions

1. `billing/` module (if created) or integration within `organization/` module for Polar logic
2. `app/api/billing/` or `app/api/organizations/[organizationId]/billing` for billing endpoints
3. Billing pages likely under `app/organizations/[organizationSlug]/settings/billing/` or similar
4. `app/api/webhooks/polar/` for Polar webhooks
5. Billing UI components likely under `components/settings/` or a dedicated `components/billing/`

## Guiding Principles

When developing features, keep these principles in mind:

1. **Domain-driven**: Keep related code together in domain modules
2. **Multi-tenant**: Always apply organization-based filtering
3. **RBAC**: Apply role-based access control consistently
4. **Component Reusability**: Abstract reusable UI patterns
5. **Type Safety**: Use TypeScript, Zod, and Drizzle for type safety
6. **Server-side Validation**: Always validate on the server
7. **Authorization First**: Check permissions before accessing data
