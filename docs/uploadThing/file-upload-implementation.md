# UploadThing Implementation in Scene-o-matic

This document provides a comprehensive overview of how file uploads are implemented in the Scene-o-matic application using UploadThing.

## Architecture Overview

Our file upload system is built on UploadThing, a modern file upload solution for Next.js applications. The implementation follows a multi-tenant architecture where files are securely associated with specific organizations and locations.

### Key Components

1. **UploadThing Router**: Defines file upload endpoints with specific configurations
2. **Authentication Middleware**: Ensures only authenticated users can upload files
3. **Database Integration**: Stores file metadata with proper associations
4. **Client Components**: React components for triggering uploads
5. **Display Components**: Components for displaying uploaded files

## Server-Side Implementation

### UploadThing Router Configuration

The file router is defined in `app/api/uploadthing/core.ts` and includes two main endpoints:

1. **locationPhotos**: For image uploads
   - Max file size: 8MB
   - Max file count: 20
   - File types: Images only

2. **locationDocuments**: For document uploads
   - Max file size: 16MB
   - Max file count: 10
   - File types: Images, PDFs, Word documents

```typescript
// File router definition
export const ourFileRouter = {
  locationPhotos: f({ 
    image: {
      maxFileSize: "8MB",
      maxFileCount: 20,
    }
  })
    .middleware(async ({ req }) => {
      // Authentication and metadata extraction
      // ...
    })
    .onUploadComplete(async ({ metadata, file }) => {
      // Database storage and response handling
      // ...
    }),

  locationDocuments: f({
    image: { maxFileSize: "8MB", maxFileCount: 5 },
    pdf: { maxFileSize: "16MB", maxFileCount: 10 },
    "application/vnd.openxmlformats-officedocument.wordprocessingml.document": {
      maxFileSize: "16MB",
      maxFileCount: 10,
    },
    "application/msword": {
      maxFileSize: "16MB",
      maxFileCount: 10,
    },
  })
    .middleware(async ({ req }) => {
      // Authentication and metadata extraction
      // ...
    })
    .onUploadComplete(async ({ metadata, file }) => {
      // Database storage and response handling
      // ...
    }),
} satisfies FileRouter;
```

### Authentication and Authorization

Authentication is handled through Stytch B2B, which provides organization-level authentication:

```typescript
const auth = async (req: Request) => {
  try {
    // Get the session from the request headers
    const sessionJwt = req.headers.get("cookie")?.match(/stytch_session_jwt=([^;]+)/)?.[1];
    const sessionToken = req.headers.get("cookie")?.match(/stytch_session=([^;]+)/)?.[1];
    
    if (!sessionJwt && !sessionToken) {
      throw new UploadThingError("No session token found");
    }
    
    // Use the Stytch client to authenticate the session
    const stytchClient = await getStytchB2BClientAsync();
    let memberId: string;
    let organizationId: string;
    
    // Authentication logic...
    
    return { 
      id: memberId,
      organizationId: organizationId
    };
  } catch (error) {
    console.error("Auth error:", error);
    throw new UploadThingError("Authentication failed");
  }
};
```

### Middleware for Metadata Extraction

The middleware function extracts important metadata from the request:

```typescript
.middleware(async ({ req }) => {
  try {
    // This code runs on your server before upload
    const user = await auth(req);
    
    // Extract locationId from the request headers
    const locationId = req.headers.get("x-location-id");
    
    if (!locationId) {
      throw new UploadThingError("Location ID is required");
    }
    
    // Whatever is returned here is accessible in onUploadComplete as `metadata`
    return { 
      userId: user.id,
      organizationId: user.organizationId,
      locationId: locationId,
      uploadedAt: new Date().toISOString()
    };
  } catch (error) {
    console.error("Middleware error:", error);
    throw new UploadThingError("Failed to authenticate upload request");
  }
})
```

### Upload Completion Handler

The `onUploadComplete` function handles storing file metadata in the database:

```typescript
.onUploadComplete(async ({ metadata, file }) => {
  // This code RUNS ON YOUR SERVER after upload
  console.log("Upload complete for userId:", metadata.userId);
  console.log("Organization ID:", metadata.organizationId);
  console.log("Location ID:", metadata.locationId);
  console.log("File URL:", file.ufsUrl);
  
  try {
    // Convert Stytch member ID to database user ID
    const databaseUserId = await getUserIdFromStytchMemberId(metadata.userId);
    
    // Save the file information to the database
    await fetch('/api/locations/media', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        locationId: metadata.locationId,
        organizationId: metadata.organizationId,
        url: file.ufsUrl,
        type: 'image',
        uploadedBy: databaseUserId || metadata.userId,
        title: file.name,
        fileSize: file.size,
        metadata: {
          key: file.key,
          uploadedAt: metadata.uploadedAt,
          stytchMemberId: metadata.userId
        }
      })
    });
  } catch (error) {
    console.error("Error saving media to database:", error);
    // Continue even if database save fails, to not block the client
  }
  
  // Return data that will be accessible on the client
  return { 
    uploadedBy: metadata.userId,
    organizationId: metadata.organizationId,
    locationId: metadata.locationId,
    url: file.ufsUrl,
    name: file.name,
    size: file.size,
    key: file.key,
    type: "image",
    uploadedAt: metadata.uploadedAt
  };
})
```

### API Route Handler

The API route handler in `app/api/uploadthing/route.ts` connects the file router to the Next.js API routes:

```typescript
import { createRouteHandler } from "uploadthing/next";
import { ourFileRouter } from "./core";

// Export routes for Next App Router
export const { GET, POST } = createRouteHandler({
  router: ourFileRouter,
});
```

## Database Schema

The database schema for storing file metadata is defined in `modules/location/schema.ts`:

```typescript
// Location media table
export const locationMedia = pgTable("location_media", {
  id: uuid("id").primaryKey().defaultRandom(),
  locationId: uuid("location_id").notNull().references(() => locations.id),
  url: text("url").notNull(),
  thumbnailUrl: text("thumbnail_url"),
  type: text("type").notNull(), // image, pdf, doc, etc.
  title: text("title"),
  description: text("description"),
  uploadedBy: uuid("uploaded_by").references(() => users.id),
  uploadedAt: timestamp("uploaded_at").notNull().defaultNow(),
  metadata: jsonb("metadata"),
  fileSize: integer("file_size"),
  width: integer("width"),
  height: integer("height"),
  isPublic: boolean("is_public").notNull().default(true),
  ordering: integer("ordering").notNull().default(0),
  deletedAt: timestamp("deleted_at"),
})

// Relations for location media
export const locationMediaRelations = relations(locationMedia, ({ one }) => ({
  location: one(locations, {
    fields: [locationMedia.locationId],
    references: [locations.id],
  }),
  uploader: one(users, {
    fields: [locationMedia.uploadedBy],
    references: [users.id],
  }),
}))
```

## Media API Endpoint

The `/api/locations/media` endpoint handles CRUD operations for location media:

```typescript
// POST handler for creating new media records
export async function POST(request: NextRequest) {
  try {
    // Authentication and authorization checks
    // ...
    
    // Parse and validate the request body
    const body = await request.json();
    const validatedData = createMediaSchema.parse(body);

    // Verify that the location belongs to the organization
    // This is a security check to prevent cross-tenant access
    const locationOrganizationId = validatedData.organizationId;
    if (organization.id !== locationOrganizationId) {
      console.warn(`Organization mismatch: ${organization.id} vs ${locationOrganizationId}`);
      return NextResponse.json({ error: "Organization mismatch" }, { status: 403 });
    }

    // Insert the media record into the database
    const [media] = await db
      .insert(locationMedia)
      .values({
        locationId: validatedData.locationId,
        url: validatedData.url,
        type: validatedData.type,
        title: validatedData.title || validatedData.url.split('/').pop() || 'Untitled',
        description: validatedData.description,
        uploadedBy: validatedData.uploadedBy,
        uploadedAt: new Date(),
        fileSize: validatedData.fileSize,
        metadata: validatedData.metadata || {},
        isPublic: true,
        ordering: 0, // Default ordering
      })
      .returning();

    return NextResponse.json(media, { status: 201 });
  } catch (error) {
    // Error handling
    // ...
  }
}

// GET handler for retrieving media
export async function GET(request: NextRequest) {
  try {
    // Authentication and authorization checks
    // ...

    // Get the locationId from the query parameters
    const { searchParams } = new URL(request.url);
    const locationId = searchParams.get('locationId');

    // Query the database for media associated with the location
    const media = await db.query.locationMedia.findMany({
      where: (locationMedia, { eq, and, isNull }) => 
        and(
          eq(locationMedia.locationId, locationId),
          isNull(locationMedia.deletedAt)
        ),
      orderBy: (locationMedia, { asc }) => [asc(locationMedia.ordering)],
      with: {
        uploader: {
          columns: {
            id: true,
            name: true,
          }
        }
      }
    });

    return NextResponse.json({ media });
  } catch (error) {
    // Error handling
    // ...
  }
}
```

## Client-Side Implementation

### UploadThing Client Utilities

Client-side utilities are defined in `lib/utils/uploadthing.ts`:

```typescript
"use client";

import {
  generateUploadButton,
  generateUploadDropzone,
  generateReactHelpers,
} from "@uploadthing/react";
import type { OurFileRouter } from "@/app/api/uploadthing/core";

// Generate the upload components with the correct types
export const UploadButton = generateUploadButton<OurFileRouter>();
export const UploadDropzone = generateUploadDropzone<OurFileRouter>();

// Generate the React helpers for more advanced usage
export const { useUploadThing, uploadFiles } = generateReactHelpers<OurFileRouter>();
```

### Upload Component Implementation

The upload button is implemented in components like `LocationDocumentsContent`:

```typescript
{locationId && (
  <UploadButton
    endpoint="locationDocuments"
    content={{
      button({ ready }) {
        if (ready) {
          return (
            <div className="flex items-center gap-1">
              <UploadIcon className="h-4 w-4" />
              <span>Upload</span>
            </div>
          );
        }
        return "Loading...";
      },
    }}
    onUploadBegin={() => setIsUploading(true)}
    onClientUploadComplete={handleUploadComplete}
    onUploadError={handleUploadError}
    headers={{ "x-location-id": locationId }}
  />
)}
```

### Display Components

Files are displayed using components like `LocationGalleryContent` and `LocationDocumentsContent`:

```typescript
// Gallery component for displaying images
export function LocationGalleryContent({ media }: LocationGalleryContentProps) {
  const [activeItem, setActiveItem] = useState<MediaItem | null>(
    media.length > 0 ? media[0] : null
  );

  // Component implementation...
}

// Documents component for displaying documents
export function LocationDocumentsContent({ 
  documents, 
  locationId,
  onUploadComplete,
  isLoading = false 
}: LocationDocumentsContentProps) {
  // Component implementation...
}
```

### Data Fetching and Adaptation

The `LocationDetailsClient` component fetches and adapts media data:

```typescript
// Adapters for transforming API data to component-friendly format
function adaptMediaItemsForGallery(mediaItems: LocationMedia[] = []): GalleryMediaItem[] {
  // Implementation...
}

function adaptDocuments(mediaItems: LocationMedia[] = []): Document[] {
  // Implementation...
}

// Component that uses the adapted data
export function LocationDetailsClient({ 
  locationId, 
  organizationId,
  isSharedView = false,
  viewMode: initialViewMode,
  location: sharedLocation
}: LocationDetailsClientProps) {
  // Fetch location data including media
  const { 
    data: location, 
    isLoading, 
    isError, 
    error,
    refetch 
  } = useLocationDetails(isSharedView ? null : locationId);
  
  // Component implementation...
  
  return (
    // Render components with adapted media data
    <LocationGalleryContent 
      media={adaptMediaItemsForGallery(locationData?.media)} 
    />
    <LocationDocumentsContent 
      documents={adaptDocuments(locationData?.media)} 
    />
  );
}
```

## Security Considerations

### Multi-Tenant Data Isolation

Our implementation ensures proper multi-tenant data isolation through:

1. **Authentication**: All requests require authentication via Stytch B2B
2. **Organization Verification**: Verifies that the location belongs to the user's organization
3. **RBAC Checks**: Ensures the user has appropriate permissions

### Cross-Tenant Protection

The API endpoints include explicit checks to prevent cross-tenant access:

```typescript
// Verify that the location belongs to the organization
// This is a security check to prevent cross-tenant access
const locationOrganizationId = validatedData.organizationId;
if (organization.id !== locationOrganizationId) {
  console.warn(`Organization mismatch: ${organization.id} vs ${locationOrganizationId}`);
  return NextResponse.json({ error: "Organization mismatch" }, { status: 403 });
}
```

### Permission Checks

RBAC checks ensure users have appropriate permissions:

```typescript
const hasPermission = await checkPermission(authParams, stytchOrgId, 'create', 'location');
if (!hasPermission) {
  console.warn(`RBAC check failed: User ${memberId} in org ${stytchOrgId} does not have 'create' permission on 'location'`);
  return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
}
```

## Troubleshooting

### Common Issues

1. **Authentication Failures**
   - Check that the user is properly authenticated with Stytch B2B
   - Verify that cookies are being properly sent with the request

2. **Missing Location ID**
   - Ensure the location ID is being passed in the headers
   - Check that the location exists in the database

3. **Organization Mismatch**
   - Verify that the location belongs to the user's organization
   - Check that the organization ID is correctly set in the location record

4. **Database Errors**
   - Check for database connection issues
   - Verify that the schema matches the expected structure

### Debugging Tips

1. Check the server logs for detailed error messages
2. Use the browser's network tab to inspect request/response data
3. Verify that the correct headers are being sent with the request
4. Check the database records to ensure data is being properly stored

## Future Improvements

1. **Image Processing**: Add server-side image processing for thumbnails and optimized versions
2. **Chunked Uploads**: Implement chunked uploads for large files
3. **Progress Tracking**: Add more detailed upload progress tracking
4. **Drag-and-Drop**: Enhance the UI with drag-and-drop upload zones
5. **File Validation**: Add more sophisticated file validation (e.g., virus scanning)

## Conclusion

The UploadThing implementation in Scene-o-matic provides a secure, efficient way to handle file uploads in a multi-tenant environment. By properly associating files with organizations and locations, we ensure data isolation and enable accurate retrieval and display of files in location detail views.
