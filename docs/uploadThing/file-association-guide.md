# File Association Guide for UploadThing

This guide provides step-by-step instructions for ensuring that uploaded files are correctly associated with the appropriate organization and location in our multi-tenant application.

## Overview

In our application, files (images and documents) are uploaded using UploadThing and need to be properly associated with:

1. The correct **organization** (tenant)
2. The specific **location** within that organization
3. The **user** who uploaded the file

This ensures proper data isolation between tenants and enables accurate retrieval and display of files in location detail views.

## Current Implementation Analysis

Our current implementation has the following components:

### Server-Side (UploadThing Router)

- **Authentication**: Uses Stytch B2B for authentication, extracting the user's `memberId` and `organizationId`
- **File Routers**: Two separate routers for different file types:
  - `locationPhotos`: For image uploads (up to 8MB, max 20 files)
  - `locationDocuments`: For documents (PDF, Word, etc.) and images (up to 16MB, max 10 files)
- **Middleware**: Extracts location ID from headers and passes it along with user and organization IDs
- **onUploadComplete**: Saves file metadata to the database via the `/api/locations/media` endpoint

### Database Schema

- **locationMedia table**: Stores file metadata with proper foreign keys:
  - `locationId`: References the location
  - `uploadedBy`: References the user
  - Organization association is implicit through the location's `organizationId`

### Client-Side Components

- **UploadButton/UploadDropzone**: React components for triggering uploads
- **Location Detail Views**: Display uploaded files in gallery and document views

## Step-by-Step Guide for Proper File Association

### 1. Preparing for Upload

When implementing file uploads for a location, ensure:

```tsx
// 1. Import the necessary components
import { UploadButton } from "@/lib/utils/uploadthing";

// 2. Ensure you have the location ID available
const locationId = "your-location-id";

// 3. Use the appropriate endpoint based on file type
const endpoint = isDocument ? "locationDocuments" : "locationPhotos";
```

### 2. Implementing the Upload Component

```tsx
<UploadButton
  endpoint={endpoint}
  content={{
    button({ ready }) {
      if (ready) {
        return (
          <div className="flex items-center gap-1">
            <UploadIcon className="h-4 w-4" />
            <span>Upload</span>
          </div>
        );
      }
      return "Loading...";
    },
  }}
  onUploadBegin={() => setIsUploading(true)}
  onClientUploadComplete={(res) => {
    setIsUploading(false);
    // Handle successful upload
    console.log("Files uploaded:", res);
    // Refresh the media list if needed
    if (onUploadComplete) onUploadComplete();
  }}
  onUploadError={(error) => {
    setIsUploading(false);
    // Handle upload error
    console.error("Upload error:", error);
    toast({
      title: "Upload failed",
      description: error.message,
      variant: "destructive",
    });
  }}
  // IMPORTANT: Pass the location ID in the headers
  headers={{ "x-location-id": locationId }}
/>
```

### 3. Verifying Organization Association

The server-side implementation automatically handles organization association through:

1. Authentication middleware that extracts the user's organization ID
2. Verification that the location belongs to the authenticated organization
3. Storing the organization ID in the file metadata

This ensures that files are properly associated with the correct organization.

### 4. Retrieving Files for a Location

When displaying files for a location:

```tsx
// 1. Fetch media for the location
const fetchLocationMedia = async (locationId: string) => {
  const response = await fetch(`/api/locations/media?locationId=${locationId}`, {
    headers: {
      // Authentication headers are added automatically by the browser
    }
  });
  
  if (!response.ok) {
    throw new Error("Failed to fetch location media");
  }
  
  const data = await response.json();
  return data.media;
};

// 2. Use the fetched media in your components
const { data: mediaItems, isLoading } = useQuery({
  queryKey: ['locationMedia', locationId],
  queryFn: () => fetchLocationMedia(locationId),
});

// 3. Pass the media to your display components
return (
  <>
    <LocationGalleryContent media={adaptMediaItemsForGallery(mediaItems)} />
    <LocationDocumentsContent documents={adaptDocuments(mediaItems)} />
  </>
);
```

### 5. Security Considerations

Our implementation includes several security measures:

1. **Authentication**: All uploads require an authenticated user
2. **Organization Verification**: Verifies that the location belongs to the user's organization
3. **RBAC Checks**: Ensures the user has appropriate permissions for the operation
4. **Cross-Tenant Protection**: Prevents accessing files from other organizations

## Testing File Association

To verify that files are correctly associated:

1. **Upload Test**: Upload a file to a specific location
2. **Database Check**: Verify that the file record in the database has:
   - The correct `locationId`
   - The correct `uploadedBy` user ID
   - The location has the correct `organizationId`
3. **Retrieval Test**: Verify that the file appears in the location detail view
4. **Cross-Tenant Test**: Verify that users from other organizations cannot access the file

## Troubleshooting

### Common Issues

1. **Files not appearing in location detail view**:
   - Check that the file was successfully uploaded (check network tab)
   - Verify that the location ID was correctly passed in the headers
   - Check that the file record exists in the database with the correct location ID

2. **Permission errors during upload**:
   - Verify that the user is authenticated
   - Check that the user has appropriate permissions for the location
   - Ensure the location belongs to the user's organization

3. **Files visible to wrong organization**:
   - Check the organization ID in the location record
   - Verify that the API endpoint is correctly filtering by organization

## Best Practices

1. **Always pass location ID**: Always include the location ID in the headers when uploading
2. **Validate file types**: Use the appropriate endpoint for the file type
3. **Handle errors gracefully**: Provide user feedback for upload failures
4. **Refresh after upload**: Update the UI to show newly uploaded files
5. **Implement proper loading states**: Show loading indicators during upload

By following this guide, you can ensure that files uploaded via UploadThing are correctly associated with the appropriate organization and location, maintaining proper multi-tenant data isolation.
