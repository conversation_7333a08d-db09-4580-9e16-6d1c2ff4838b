# Media Gallery Issue Analysis

## Problem Description

Images uploaded for the "Pitot House" location (ID: e7afb8f3-90ec-461e-98e4-b5e5f3aa8aad) are not appearing in the gallery despite successful uploads to UploadThing. The issue is that while files are being uploaded to UploadThing's storage, the media entries are not being saved to the database.

## Root Cause Analysis

After investigation, we identified the following issues:

1. **Authentication Failure**: The UploadThing `onUploadComplete` handler was making a fetch request to the `/api/locations/media` endpoint, but the authentication was failing. This was causing the request to be redirected to the login page instead of receiving a proper JSON response.

2. **Silent Failure**: The error was not being properly logged or handled, making it difficult to diagnose. The fetch request was failing silently, and the error was not being propagated back to the user.

3. **Missing Authentication Validation**: The handler wasn't checking if valid authentication tokens were present before attempting to make the API request.

4. **Missing Stytch B2B Credentials**: Our debugging revealed that the Stytch B2B client initialization is failing because the required environment variables (`STYTCH_B2B_PROJECT_ID` and `STYTCH_B2B_SECRET`) are not set correctly. This is causing all authentication checks to fail, regardless of the headers provided.

## Implemented Fixes

### 1. Enhanced Authentication Validation in UploadThing Handlers

Added explicit checks for authentication tokens in both the `locationPhotos` and `locationDocuments` handlers:

```typescript
// Check if we have valid authentication tokens
if (!sessionJwt && !sessionToken) {
  console.error("No authentication tokens found in cookies");
  throw new Error("Authentication failed - No session tokens found");
}
```

### 2. Improved Logging in UploadThing Handlers

Added detailed logging to help diagnose authentication issues:

```typescript
console.log("Authentication headers:", {
  'X-Stytch-Org-Id': metadata.organizationId,
  'X-Stytch-Member-Id': metadata.userId,
  'Has Session Token': !!sessionToken,
  'Has Session JWT': !!sessionJwt
});
```

### 3. Enhanced API Endpoint Logging

Added logging in the `/api/locations/media` endpoint to track incoming authentication headers:

```typescript
// Log authentication headers for debugging
console.log("Media API - Authentication headers received:", {
  'X-Stytch-Org-Id': stytchOrgId,
  'X-Stytch-Member-Id': memberId,
  'Has Session Token': !!sessionToken,
  'Has Session JWT': !!sessionJwt
});
```

## Testing and Verification

To test the fixes, we created two test scripts:

1. **`scripts/check-pitot-house-media.ts`**: Verifies the existence of the Pitot House location and checks for associated media entries.

2. **`scripts/test-location-media-api.ts`**: Tests the API endpoint directly to identify authentication issues.

## Expected Behavior After Fixes

With these changes, we expect:

1. Better error reporting when authentication fails, making it easier to diagnose issues.
2. Proper validation of authentication tokens before attempting to make API requests.
3. Detailed logs that will help identify the exact point of failure.

## Next Steps for Verification

1. Upload an image to the Pitot House location and check the server logs for the enhanced error messages.
2. Verify that the authentication headers are being correctly passed from the client to the server.
3. Check if media entries are now being saved to the database.

## Critical Next Steps

1. **Set Up Stytch B2B Credentials**: The most critical issue is that the Stytch B2B credentials are not properly configured. The following environment variables need to be set:
   - `STYTCH_B2B_PROJECT_ID`
   - `STYTCH_B2B_SECRET`
   
   Without these credentials, all authentication will fail, and the media uploads will not be saved to the database.

2. **Verify Environment Configuration**: Ensure that the environment variables are correctly set in all environments (development, staging, production). This may involve updating the `.env` files or the deployment configuration.

## Long-term Recommendations

1. **Implement Retry Logic**: Add retry logic for failed API requests in the UploadThing handlers.
2. **Add Client-side Feedback**: Provide better feedback to users when media uploads fail to save to the database.
3. **Implement Monitoring**: Set up monitoring for failed uploads to catch issues early.
4. **Consider Using Server Actions**: For Next.js applications, consider using Server Actions instead of API routes for better error handling and type safety.
5. **Implement Environment Variable Validation**: Add validation at application startup to ensure all required environment variables are set, preventing silent failures due to missing configuration.
