# UploadThing Documentation

This folder contains documentation for the UploadThing implementation in the Scene-o-matic application.

## Available Documentation

1. [**File Upload Implementation**](./file-upload-implementation.md) - Comprehensive guide to how file uploads are implemented in the application, including architecture, server-side and client-side implementation details, database schema, security considerations, and usage examples.

2. [**File Association Guide**](./file-association-guide.md) - Step-by-step instructions for ensuring that uploaded files are correctly associated with the appropriate organization and location in our multi-tenant application.

## Key Features of Our UploadThing Implementation

- **Multi-tenant architecture** with proper organization and location associations
- **Secure authentication and authorization** using Stytch B2B
- **Support for various file types** including images, PDFs, and documents
- **Client-side components** for easy integration in React components
- **Database storage** of file metadata with proper relations
- **Cross-tenant protection** to prevent data leaks between organizations

## Quick Links

- [UploadThing Official Documentation](https://docs.uploadthing.com/)
- [Stytch B2B Documentation](https://stytch.com/docs/b2b)

## For Developers

If you're implementing file uploads in a new feature, start with the [File Association Guide](./file-association-guide.md) for a step-by-step approach. For a deeper understanding of the implementation, refer to the [File Upload Implementation](./file-upload-implementation.md) document.

## Common Issues

If you encounter issues with file uploads, check the Troubleshooting section in the [File Upload Implementation](./file-upload-implementation.md#troubleshooting) document.
