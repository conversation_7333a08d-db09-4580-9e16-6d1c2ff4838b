# Scene-o-matic Performance Optimization Techniques

This document outlines the performance optimization techniques implemented in the Scene-o-matic application, with a focus on the Map feature. These techniques can be applied to other features such as the Locations feature which uses a similar setup and system.

## Table of Contents

1. [Overview](#overview)
2. [Data Fetching Optimizations](#data-fetching-optimizations)
3. [State Management Improvements](#state-management-improvements)
4. [Database Optimizations](#database-optimizations)
5. [Component Architecture Refinements](#component-architecture-refinements)
6. [Server-Side Caching](#server-side-caching)
7. [Error Handling and Stability](#error-handling-and-stability)
8. [Verification and Monitoring](#verification-and-monitoring)
9. [Applying These Techniques to Other Features](#applying-these-techniques-to-other-features)

## Overview

The Map feature initially suffered from performance issues, with page load times exceeding 12 seconds and numerous redundant API calls. Through a phased approach, we implemented various optimizations that reduced load times to under 3 seconds and significantly improved stability.

## Data Fetching Optimizations

### 1. Combined API Endpoints

**Problem:** Multiple separate API calls created a "waterfall" effect, where each request had to wait for previous ones to complete.

**Solution:** Created consolidated API endpoints that return multiple data types in a single request.

**Implementation:**
- Created `/api/map/initial-data` endpoint that combines organization data, locations, saved views, and favorites
- Example:

```typescript
// app/api/map/initial-data/route.ts
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const organizationSlug = searchParams.get('organizationSlug');
    
    if (!organizationSlug) {
      return NextResponse.json({ error: 'Organization slug is required' }, { status: 400 });
    }
    
    // Get organization by slug
    const organization = await getOrganizationBySlug(organizationSlug);
    
    // Get locations for organization
    const locations = await getLocationsByOrganizationId(organization.id);
    
    // Get saved views for organization
    const views = await getSavedViewsByOrganizationId(organization.id);
    
    // Get favorites for organization
    const favorites = await getFavoriteLists(organization.id);
    
    // Return all data in a single response
    return NextResponse.json({
      organization,
      locations,
      views,
      favorites
    });
  } catch (error) {
    console.error("Error fetching initial map data:", error);
    return NextResponse.json({ error: 'Failed to fetch initial map data' }, { status: 500 });
  }
}
```

### 2. Custom React Query Hooks

**Problem:** Data fetching logic was scattered across components, leading to duplicate requests and inconsistent caching.

**Solution:** Created centralized custom hooks using React Query for data fetching with proper caching.

**Implementation:**
- Created hooks like `use-initial-map-data.ts` that encapsulate data fetching logic
- Configured with appropriate stale times and cache invalidation
- Example:

```typescript
// hooks/use-initial-map-data.ts
export function useInitialMapData(organizationSlug: string) {
  return useQuery<InitialMapData>({
    queryKey: ['/api/map/initial-data', organizationSlug],
    queryFn: async () => {
      const response = await fetch(`/api/map/initial-data?organizationSlug=${organizationSlug}`);
      if (!response.ok) {
        throw new Error('Failed to fetch initial map data');
      }
      return response.json();
    },
    staleTime: 60 * 1000, // 1 minute
    refetchOnWindowFocus: false
  });
}
```

### 3. Server Component Prefetching

**Problem:** Client components had to wait for the page to load before starting data fetching.

**Solution:** Utilized Next.js Server Components to prefetch data on the server before sending to the client.

**Implementation:**
- Modified server components to prefetch data
- Passed data to client components as props
- Example:

```typescript
// app/(dashboard)/organizations/[organizationSlug]/map/page.tsx
export default async function MapPage({ params }: { params: { organizationSlug: string } }) {
  // Prefetch initial data on the server
  const initialData = await getInitialMapData(params.organizationSlug);
  
  return (
    <MapPageClient 
      organizationSlug={params.organizationSlug} 
      initialData={initialData} 
    />
  );
}
```

## State Management Improvements

### 1. Context Optimization

**Problem:** Nested contexts caused re-renders and state inconsistencies.

**Solution:** Flattened context hierarchy and optimized provider structure.

**Implementation:**
- Removed nested MapProvider instances
- Used React.memo and useMemo to prevent unnecessary re-renders
- Example:

```typescript
// Before: Nested providers
return (
  <MapProvider>
    <div className="map-container">
      <MapView />
    </div>
  </MapProvider>
);

// After: Flattened structure
return (
  <div className="map-container">
    <MapView />
  </div>
);
```

### 2. Memoized Context Values

**Problem:** Context values were recreated on every render, causing child components to re-render unnecessarily.

**Solution:** Used useMemo to memoize context values.

**Implementation:**
- Wrapped context values with useMemo
- Added proper dependency arrays
- Example:

```typescript
// providers/map-provider.tsx
const contextValue = useMemo(() => ({
  locations,
  projects,
  isLoading,
  error,
  selectedLocation,
  viewSettings,
  clusterSettings,
  filters,
  mapboxToken,
  favorites,
  // ... other values
}), [
  locations, 
  projects, 
  isLoading, 
  error, 
  selectedLocation, 
  viewSettings,
  clusterSettings, 
  filters, 
  mapboxToken, 
  favorites,
  // ... other dependencies
]);

return (
  <MapContext.Provider value={contextValue}>
    {children}
  </MapContext.Provider>
);
```

### 3. Local Storage for Persistent UI State

**Problem:** User preferences and UI state were lost between sessions.

**Solution:** Implemented storage utilities for persisting state.

**Implementation:**
- Created a storage utility for map filters and view settings
- Used localStorage with proper serialization/deserialization
- Example:

```typescript
// lib/utils/storage.ts
export const mapStorage = {
  getFilters: (organizationId: string): MapFilter => {
    try {
      const stored = localStorage.getItem(`map_filters_${organizationId}`);
      return stored ? JSON.parse(stored) : {};
    } catch (e) {
      console.error('Error retrieving map filters from storage:', e);
      return {};
    }
  },
  
  setFilters: (organizationId: string, filters: MapFilter): void => {
    try {
      localStorage.setItem(`map_filters_${organizationId}`, JSON.stringify(filters));
    } catch (e) {
      console.error('Error storing map filters:', e);
    }
  },
  
  // Similar methods for view settings, etc.
};
```

## Database Optimizations

### 1. Strategic Indexing

**Problem:** Database queries were slow, especially for filtering and joining operations.

**Solution:** Added strategic indexes to frequently queried fields.

**Implementation:**
- Created migration script for adding indexes
- Focused on fields used in WHERE clauses and JOIN conditions
- Example:

```sql
-- drizzle/migrations/0008_performance_indexes.sql

-- Location indexes
CREATE INDEX IF NOT EXISTS idx_locations_organization_id ON locations (organization_id);
CREATE INDEX IF NOT EXISTS idx_locations_project_id ON locations (project_id);
CREATE INDEX IF NOT EXISTS idx_locations_deleted_at ON locations (deleted_at) WHERE deleted_at IS NULL;
CREATE INDEX IF NOT EXISTS idx_locations_status ON locations (status);
CREATE INDEX IF NOT EXISTS idx_locations_type ON locations (type);
CREATE INDEX IF NOT EXISTS idx_locations_org_deleted ON locations (organization_id, deleted_at) WHERE deleted_at IS NULL;
CREATE INDEX IF NOT EXISTS idx_locations_org_project ON locations (organization_id, project_id);

-- Create GIN index for JSONB coordinates queries
CREATE INDEX IF NOT EXISTS idx_locations_coordinates_gin ON locations USING GIN (coordinates);
```

### 2. Specialized Index Types

**Problem:** Standard B-tree indexes weren't optimal for all data types.

**Solution:** Used specialized index types for different data structures.

**Implementation:**
- Used GIN (Generalized Inverted Index) for JSONB fields
- Used partial indexes for filtered queries
- Example:

```sql
-- For JSONB data (coordinates)
CREATE INDEX IF NOT EXISTS idx_locations_coordinates_gin ON locations USING GIN (coordinates);

-- Partial index for active records
CREATE INDEX IF NOT EXISTS idx_locations_deleted_at ON locations (deleted_at) WHERE deleted_at IS NULL;
```

### 3. Composite Indexes

**Problem:** Multiple single-column indexes weren't efficient for multi-column queries.

**Solution:** Created composite indexes for common query patterns.

**Implementation:**
- Added indexes that cover multiple columns used together in queries
- Example:

```sql
-- Composite indexes for common query patterns
CREATE INDEX IF NOT EXISTS idx_locations_org_project ON locations (organization_id, project_id);
CREATE INDEX IF NOT EXISTS idx_notifications_recipient_org_status ON notifications (recipient_id, organization_id, status);
```

## Component Architecture Refinements

### 1. Component Splitting

**Problem:** Large monolithic components caused unnecessary re-renders.

**Solution:** Split components into smaller, focused pieces.

**Implementation:**
- Created separate components for map controls, layers, locations list, etc.
- Used React.memo for pure components
- Example:

```typescript
// Before: Monolithic component
function MapPage() {
  // All logic and rendering here
}

// After: Split components
function MapPage() {
  return (
    <div>
      <MapControls />
      <MapView />
      <LocationsList />
    </div>
  );
}

const MapControls = React.memo(function MapControls() {
  // Controls-specific logic
});

const LocationsList = React.memo(function LocationsList() {
  // Locations list logic
});
```

### 2. Lazy Loading

**Problem:** All components loaded upfront, increasing initial load time.

**Solution:** Implemented lazy loading for non-critical components.

**Implementation:**
- Used dynamic imports for modals and secondary features
- Added loading states for better UX
- Example:

```typescript
// Lazy loading modals
const MapFilterModal = dynamic(() => import('./map-filter-modal'), {
  loading: () => <div className="modal-loading-placeholder">Loading...</div>,
  ssr: false // Disable server-side rendering for this component
});
```

### 3. Optimized Rendering

**Problem:** Inefficient rendering of map markers caused performance issues.

**Solution:** Implemented clustering and optimized marker rendering.

**Implementation:**
- Added marker clustering for large datasets
- Used canvas rendering instead of DOM elements where appropriate
- Implemented virtualization for long lists
- Example:

```typescript
// Clustering implementation in map-view.tsx
useEffect(() => {
  if (!map.current || !mapLoaded || !mergedClusterSettings.enabled) return;
  
  // Setup clustering
  map.current.addSource('locations', {
    type: 'geojson',
    data: {
      type: 'FeatureCollection',
      features: locations.map(location => ({
        type: 'Feature',
        properties: {
          id: location.id,
          name: location.name,
          // Other properties
        },
        geometry: {
          type: 'Point',
          coordinates: [location.coordinates.longitude, location.coordinates.latitude]
        }
      }))
    },
    cluster: true,
    clusterMaxZoom: mergedClusterSettings.maxZoom,
    clusterRadius: mergedClusterSettings.radius
  });
  
  // Add cluster layers
  // ...
}, [locations, mapLoaded, mergedClusterSettings]);
```

## Server-Side Caching

### 1. In-Memory Cache

**Problem:** Repeated database queries for the same data.

**Solution:** Implemented a server-side in-memory cache.

**Implementation:**
- Created a flexible caching utility with TTL support
- Used singleton pattern for efficient memory usage
- Example:

```typescript
// lib/cache.ts
export class Cache<T = any> {
  private static instance: Cache;
  private cache: Map<string, { value: T; expires: number }> = new Map();
  private cleanupInterval: NodeJS.Timeout | null = null;

  private constructor() {
    // Start cleanup interval
    this.cleanupInterval = setInterval(() => this.cleanup(), 60000); // Cleanup every minute
  }

  public static getInstance<T>(): Cache<T> {
    if (!Cache.instance) {
      Cache.instance = new Cache<T>();
    }
    return Cache.instance as Cache<T>;
  }

  public set(key: string, value: T, ttlMs: number = 300000): void {
    const expires = Date.now() + ttlMs;
    this.cache.set(key, { value, expires });
  }

  public get(key: string): T | null {
    const item = this.cache.get(key);
    
    if (!item) return null;
    
    // Check if expired
    if (item.expires < Date.now()) {
      this.cache.delete(key);
      return null;
    }
    
    return item.value;
  }

  public delete(key: string): void {
    this.cache.delete(key);
  }

  public clear(): void {
    this.cache.clear();
  }

  private cleanup(): void {
    const now = Date.now();
    for (const [key, item] of this.cache.entries()) {
      if (item.expires < now) {
        this.cache.delete(key);
      }
    }
  }

  public dispose(): void {
    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval);
      this.cleanupInterval = null;
    }
  }
}
```

### 2. Service-Level Caching

**Problem:** Repeated processing of the same data in services.

**Solution:** Added caching to service methods.

**Implementation:**
- Integrated cache in map and notification services
- Added proper cache invalidation on updates
- Example:

```typescript
// modules/map/service.ts
import { Cache } from '@/lib/cache';

export async function getSavedViewsByOrganizationId(organizationId: string): Promise<SavedView[]> {
  const cache = Cache.getInstance<SavedView[]>();
  const cacheKey = `saved_views_${organizationId}`;
  
  // Try to get from cache first
  const cachedViews = cache.get(cacheKey);
  if (cachedViews) {
    return cachedViews;
  }
  
  // If not in cache, fetch from database
  const views = await db.query.mapViews.findMany({
    where: and(
      eq(mapViews.organizationId, organizationId),
      isNull(mapViews.deletedAt)
    ),
    orderBy: desc(mapViews.updatedAt)
  });
  
  // Cache the result (1 minute TTL)
  cache.set(cacheKey, views, 60000);
  
  return views;
}

// Invalidate cache when data changes
export async function createSavedView(data: CreateSavedViewInput): Promise<SavedView> {
  // Create the view in the database
  const [view] = await db.insert(mapViews).values({
    // ...view data
  }).returning();
  
  // Invalidate the cache
  const cache = Cache.getInstance();
  cache.delete(`saved_views_${data.organizationId}`);
  
  return view;
}
```

### 3. HTTP Caching Headers

**Problem:** Clients repeatedly requested static or semi-static resources.

**Solution:** Added proper HTTP caching headers to API responses.

**Implementation:**
- Set Cache-Control headers for appropriate endpoints
- Used ETag and conditional requests where appropriate
- Example:

```typescript
// app/api/mapbox-token/route.ts
export async function GET(request: NextRequest) {
  // Authentication check
  // ...
  
  const token = process.env.NEXT_PUBLIC_MAPBOX_ACCESS_TOKEN || "";
  
  // Add Cache-Control header to the response
  return NextResponse.json({ token }, {
    headers: {
      'Cache-Control': 'public, max-age=86400, s-maxage=86400', // Cache for 24 hours
    },
  });
}
```

## Error Handling and Stability

### 1. Robust Date Handling

**Problem:** Invalid date values caused runtime errors.

**Solution:** Added comprehensive date validation.

**Implementation:**
- Added checks for date validity before operations
- Provided fallbacks for invalid dates
- Example:

```typescript
// modules/favorites/service.ts
addedAt: loc.addedAt && loc.addedAt instanceof Date && !Number.isNaN(loc.addedAt.getTime()) 
  ? loc.addedAt.toISOString() 
  : new Date().toISOString()
```

### 2. Null and Undefined Checks

**Problem:** Accessing properties on null or undefined objects caused crashes.

**Solution:** Added comprehensive null checks throughout the codebase.

**Implementation:**
- Used optional chaining and nullish coalescing operators
- Added explicit null checks for critical operations
- Example:

```typescript
// Before
const name = user.profile.name;

// After
const name = user?.profile?.name ?? 'Unknown';
```

### 3. Error Boundaries

**Problem:** Errors in one component crashed the entire application.

**Solution:** Implemented React Error Boundaries.

**Implementation:**
- Created error boundary components
- Wrapped key sections of the application
- Example:

```typescript
// components/error-boundary.tsx
import { Component, ErrorInfo, ReactNode } from 'react';

interface ErrorBoundaryProps {
  children: ReactNode;
  fallback?: ReactNode;
}

interface ErrorBoundaryState {
  hasError: boolean;
  error?: Error;
}

export class ErrorBoundary extends Component<ErrorBoundaryProps, ErrorBoundaryState> {
  constructor(props: ErrorBoundaryProps) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error): ErrorBoundaryState {
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo): void {
    console.error('Error caught by boundary:', error, errorInfo);
    // You could also log to an error reporting service here
  }

  render(): ReactNode {
    if (this.state.hasError) {
      if (this.props.fallback) {
        return this.props.fallback;
      }
      
      return (
        <div className="error-boundary-fallback">
          <h2>Something went wrong.</h2>
          <p>{this.state.error?.message || 'Unknown error'}</p>
          <button onClick={() => this.setState({ hasError: false })}>
            Try again
          </button>
        </div>
      );
    }

    return this.props.children;
  }
}
```

## Verification and Monitoring

### 1. Database Index Verification

**Problem:** Difficult to verify if indexes were properly created.

**Solution:** Created a verification script for database indexes.

**Implementation:**
- Added script to check for the existence of performance indexes
- Provided detailed output grouped by table
- Example:

```typescript
// scripts/check-performance-indexes.ts
import postgres from "postgres"
import * as dotenv from "dotenv"

dotenv.config()

async function checkIndexes() {
  // Get connection string from environment
  const connectionString = process.env.DATABASE_URL
  if (!connectionString) {
    console.error("DATABASE_URL is not defined")
    process.exit(1)
  }

  // Create postgres client
  const sql = postgres(connectionString)

  try {
    console.log("Checking performance indexes...")
    
    // Query to get all indexes in the database
    const result = await sql`
      SELECT 
        indexname, 
        tablename, 
        indexdef 
      FROM 
        pg_indexes 
      WHERE 
        schemaname = 'public' AND
        (
          indexname LIKE 'idx_map_views%' OR
          indexname LIKE 'idx_map_view_locations%' OR
          indexname LIKE 'idx_shared_map_links%' OR
          indexname LIKE 'idx_locations%' OR
          indexname LIKE 'idx_notifications%' OR
          indexname LIKE 'idx_notification_preferences%'
        )
      ORDER BY 
        tablename, 
        indexname;
    `
    
    // Display the results
    // ...
  } catch (error) {
    console.error("Error checking performance indexes:", error)
    process.exit(1)
  }
}
```

### 2. Performance Monitoring

**Problem:** Difficult to identify performance bottlenecks.

**Solution:** Added performance logging and monitoring.

**Implementation:**
- Added timing logs for critical operations
- Created utilities for measuring performance
- Example:

```typescript
// lib/utils/performance.ts
export function measurePerformance<T>(
  fn: () => Promise<T>,
  label: string
): Promise<T> {
  const start = performance.now();
  
  return fn().then(result => {
    const end = performance.now();
    console.log(`${label}: ${Math.round(end - start)}ms`);
    return result;
  }).catch(error => {
    const end = performance.now();
    console.error(`${label} (error): ${Math.round(end - start)}ms`);
    throw error;
  });
}

// Usage
const data = await measurePerformance(
  () => fetchLocations(organizationId),
  'Fetch locations'
);
```

### 3. Error Tracking

**Problem:** Errors in production were difficult to diagnose.

**Solution:** Improved error logging and tracking.

**Implementation:**
- Added detailed error logging
- Included context information with errors
- Example:

```typescript
// lib/utils/error-logging.ts
export function logError(error: Error, context: Record<string, any> = {}): void {
  console.error('Error:', {
    message: error.message,
    stack: error.stack,
    ...context,
    timestamp: new Date().toISOString()
  });
  
  // Could also send to an error tracking service here
}

// Usage
try {
  // Some operation
} catch (error) {
  logError(error as Error, {
    component: 'MapView',
    organizationId,
    userId
  });
}
```

## Applying These Techniques to Other Features

These optimization techniques can be applied to other features in the Scene-o-matic application, such as the Locations feature. Here's how to apply them:

### 1. Identify Performance Bottlenecks

- Use browser developer tools to identify slow network requests
- Look for redundant API calls
- Check for components that re-render frequently
- Identify slow database queries

### 2. Consolidate API Endpoints

- Create combined endpoints for related data
- For the Locations feature, create a `/api/locations/initial-data` endpoint that returns locations, projects, and related data in a single request

### 3. Implement Proper Caching

- Add React Query hooks for data fetching
- Implement server-side caching for frequently accessed data
- Add HTTP caching headers to API responses

### 4. Optimize Database Queries

- Add indexes for frequently queried fields
- Use specialized index types for different data structures
- Create composite indexes for common query patterns

### 5. Refine Component Architecture

- Split large components into smaller, focused pieces
- Use React.memo for pure components
- Implement lazy loading for non-critical components

### 6. Improve Error Handling

- Add comprehensive null checks
- Implement robust date handling
- Use error boundaries to prevent cascading failures

### 7. Add Verification and Monitoring

- Create scripts to verify optimizations
- Add performance logging
- Implement error tracking

By applying these techniques systematically to each feature, you can significantly improve the overall performance and stability of the Scene-o-matic application.
