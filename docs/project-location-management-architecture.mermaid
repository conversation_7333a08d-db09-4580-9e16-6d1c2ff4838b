graph TD
    ProjectView["Project View"]
    ProjectMap["Project Map"]
    ScenesList["Scenes List"]
    LocationEval["Location Evaluation"]
    Discussion["Discussion Module"]
    ApprovalSystem["Approval System"]
    
    ProjectView --> ProjectMap
    ProjectView --> ScenesList
    ScenesList --> LocationEval
    LocationEval --> Discussion
    LocationEval --> ApprovalSystem
    
    subgraph "Data Models"
    ProjectScene["ProjectScene"]
    SceneLocation["SceneLocation"]
    SceneLocationStatus["SceneLocationStatus"]
    SceneDiscussion["SceneDiscussion"]
    end
    
    LocationEval -.-> SceneLocation
    Discussion -.-> SceneDiscussion
    ApprovalSystem -.-> SceneLocationStatus