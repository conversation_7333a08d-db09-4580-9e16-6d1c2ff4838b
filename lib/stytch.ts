import { Stytch } from "stytch"

// Initialize Stytch client for server-side operations
let stytchClient: Stytch | null = null

export function getStytchClient() {
  if (!stytchClient) {
    const projectId = process.env.STYTCH_PROJECT_ID
    const secret = process.env.STYTCH_SECRET

    if (!projectId || !secret) {
      throw new Error(
        "Missing Stytch credentials. Please set STYTCH_PROJECT_ID and STYTCH_SECRET environment variables.",
      )
    }

    stytchClient = new Stytch({
      project_id: projectId,
      secret,
    })
  }

  return stytchClient
}
