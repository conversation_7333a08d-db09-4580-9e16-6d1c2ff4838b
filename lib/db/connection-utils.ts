import { db } from './index'

interface RetryOptions {
  maxRetries?: number
  baseDelay?: number
  maxDelay?: number
}

/**
 * Execute a database operation with retry logic
 */
export async function withRetry<T>(
  operation: () => Promise<T>,
  options: RetryOptions = {}
): Promise<T> {
  const { maxRetries = 3, baseDelay = 1000, maxDelay = 10000 } = options
  
  let lastError: Error
  
  for (let attempt = 0; attempt <= maxRetries; attempt++) {
    try {
      return await operation()
    } catch (error) {
      lastError = error as Error
      
      // Don't retry on the last attempt
      if (attempt === maxRetries) {
        break
      }
      
      // Calculate delay with exponential backoff
      const delay = Math.min(baseDelay * Math.pow(2, attempt), maxDelay)
      
      console.warn(`Database operation failed (attempt ${attempt + 1}/${maxRetries + 1}):`, error)
      console.log(`Retrying in ${delay}ms...`)
      
      // Wait before retrying
      await new Promise(resolve => setTimeout(resolve, delay))
    }
  }
  
  throw lastError
}

/**
 * Execute a database query with timeout and retry logic
 */
export async function executeWithTimeout<T>(
  operation: () => Promise<T>,
  timeoutMs: number = 30000,
  retryOptions?: RetryOptions
): Promise<T> {
  return withRetry(async () => {
    return Promise.race([
      operation(),
      new Promise<never>((_, reject) => {
        setTimeout(() => {
          reject(new Error(`Database operation timed out after ${timeoutMs}ms`))
        }, timeoutMs)
      })
    ])
  }, retryOptions)
}

/**
 * Check if an error is a connection-related error that should be retried
 */
export function isRetryableError(error: Error): boolean {
  const retryableErrors = [
    'CONNECT_TIMEOUT',
    'CONNECTION_TERMINATED',
    'CONNECTION_REFUSED',
    'ECONNRESET',
    'ENOTFOUND',
    'ETIMEDOUT'
  ]
  
  return retryableErrors.some(errorCode => 
    error.message.includes(errorCode) || 
    error.code === errorCode ||
    (error as any).errno === errorCode
  )
}

/**
 * Wrapper for database queries with automatic retry on connection errors
 */
export async function safeDbQuery<T>(
  queryFn: () => Promise<T>,
  options?: RetryOptions & { timeout?: number }
): Promise<T> {
  const { timeout = 30000, ...retryOptions } = options || {}
  
  return executeWithTimeout(
    queryFn,
    timeout,
    {
      maxRetries: 3,
      baseDelay: 1000,
      maxDelay: 10000,
      ...retryOptions
    }
  )
}
