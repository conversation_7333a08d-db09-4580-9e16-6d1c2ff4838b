import { neon } from '@neondatabase/serverless'
import { drizzle } from 'drizzle-orm/neon-http'
import * as schema from './schema'

// Alternative connection using Neon's serverless driver
const connectionString = process.env.DATABASE_URL

if (!connectionString) {
  throw new Error("DATABASE_URL is not defined")
}

// Create Neon serverless client
const sql = neon(connectionString)

// Create Drizzle instance with Neon HTTP adapter
export const neonDb = drizzle(sql, { schema })

// Health check for Neon serverless
export async function checkNeonConnection() {
  try {
    console.log('Checking Neon serverless connection...')
    const result = await sql`SELECT 1 as health_check`
    console.log('Neon serverless connection successful:', result)
    return true
  } catch (error) {
    console.error('Neon serverless connection failed:', error)
    return false
  }
}
