import { drizzle } from "drizzle-orm/postgres-js"
import { migrate } from "drizzle-orm/postgres-js/migrator"
import postgres from "postgres"
import * as schema from "./schema"
import * as dotenv from "dotenv"
import * as path from "node:path"

// Load environment variables from .env file
dotenv.config({ path: path.resolve(__dirname, '../../.env') })

// For query purposes (regular usage)
const connectionString = process.env.DATABASE_URL

if (!connectionString) {
  throw new Error("DATABASE_URL is not defined")
}

// Connection for queries
const queryClient = postgres(connectionString)
export const db = drizzle(queryClient, { schema })

// Function to run migrations programmatically (if needed)
export async function runMigrations() {
  if (process.env.NODE_ENV === "production") {
    // For safety, we'll only allow this in development by default
    console.log("Skipping migrations in production. Run them manually or adjust this check.")
    return
  }

  if (!connectionString) {
    throw new Error("DATABASE_URL is not defined")
  }

  const migrationClient = postgres(connectionString, { max: 1 })
  const migrationDb = drizzle(migrationClient)

  try {
    console.log("Running migrations...")
    await migrate(migrationDb, { migrationsFolder: "drizzle/migrations" })
    console.log("Migrations completed successfully")
  } catch (error) {
    console.error("Migration failed:", error)
    throw error
  } finally {
    await migrationClient.end()
  }
}
