import { drizzle } from "drizzle-orm/postgres-js"
import { migrate } from "drizzle-orm/postgres-js/migrator"
import postgres from "postgres"
import * as schema from "./schema"
import * as dotenv from "dotenv"
import * as path from "node:path"

// Load environment variables from .env file
dotenv.config({ path: path.resolve(__dirname, '../../.env') })

// For query purposes (regular usage)
const connectionString = process.env.DATABASE_URL

if (!connectionString) {
  throw new Error("DATABASE_URL is not defined")
}

// Connection for queries with optimized settings for Neon
const queryClient = postgres(connectionString, {
  // Reduced connection pool for Neon compatibility
  max: 5, // Lower max connections to avoid overwhelming Neon
  idle_timeout: 30, // Keep connections alive longer
  connect_timeout: 15, // Longer connection timeout

  // Neon-specific optimizations
  prepare: false, // Disable prepared statements for better compatibility with Neon

  // SSL settings (Neon requires SSL in production)
  ssl: process.env.NODE_ENV === 'production' ? 'require' : 'prefer',

  // Connection retry settings
  connection: {
    application_name: 'sceneomatic_v2',
  },

  // Transform settings for better error handling
  transform: {
    undefined: null,
  },

  // Additional Neon-specific settings
  types: {
    bigint: postgres.BigInt,
  },

  // Disable connection pooling in development to avoid connection leaks
  ...(process.env.NODE_ENV === 'development' && {
    max: 1,
    idle_timeout: 0,
  }),
})

export const db = drizzle(queryClient, { schema })

// Health check function
export async function checkDatabaseConnection() {
  try {
    console.log('Checking database connection...')
    const result = await queryClient`SELECT 1 as health_check`
    console.log('Database connection successful:', result)
    return true
  } catch (error) {
    console.error('Database connection failed:', error)
    return false
  }
}

// Graceful shutdown function
export async function closeDatabaseConnection() {
  try {
    await queryClient.end()
    console.log('Database connection closed gracefully')
  } catch (error) {
    console.error('Error closing database connection:', error)
  }
}

// Function to run migrations programmatically (if needed)
export async function runMigrations() {
  if (process.env.NODE_ENV === "production") {
    // For safety, we'll only allow this in development by default
    console.log("Skipping migrations in production. Run them manually or adjust this check.")
    return
  }

  if (!connectionString) {
    throw new Error("DATABASE_URL is not defined")
  }

  const migrationClient = postgres(connectionString, { max: 1 })
  const migrationDb = drizzle(migrationClient)

  try {
    console.log("Running migrations...")
    await migrate(migrationDb, { migrationsFolder: "drizzle/migrations" })
    console.log("Migrations completed successfully")
  } catch (error) {
    console.error("Migration failed:", error)
    throw error
  } finally {
    await migrationClient.end()
  }
}
