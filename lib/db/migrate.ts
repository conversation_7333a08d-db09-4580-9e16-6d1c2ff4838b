import { drizzle } from "drizzle-orm/postgres-js"
import { migrate } from "drizzle-orm/postgres-js/migrator"
import postgres from "postgres"
import * as dotenv from "dotenv"

dotenv.config()

const connectionString = process.env.DATABASE_URL

if (!connectionString) {
  throw new Error("DATABASE_URL is not defined")
}

// For migrations
const migrationClient = postgres(connectionString, { max: 1 })

// This will run migrations on the database, creating tables if they don't exist
// based on the schema definition
async function main() {
  console.log("Running migrations...")

  const db = drizzle(migrationClient)

  await migrate(db, { migrationsFolder: "drizzle/migrations" })

  console.log("Migrations completed successfully")

  await migrationClient.end()

  process.exit(0)
}

main().catch((err) => {
  console.error("Migration failed")
  console.error(err)
  process.exit(1)
})
