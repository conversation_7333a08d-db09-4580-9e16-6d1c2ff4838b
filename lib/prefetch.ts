/**
 * Prefetch utilities for optimizing data loading
 * 
 * These functions prefetch commonly needed data when the application loads
 * to reduce perceived loading times for users.
 */

import { cache } from "@/lib/cache";

/**
 * Prefetch locations data for a specific organization
 * 
 * This function prefetches the first page of locations and common filters
 * to improve the initial loading experience when navigating to the locations page.
 */
export async function prefetchLocations(organizationSlug: string): Promise<void> {
  try {
    // Prefetch the first page of all locations
    const locationsPromise = fetch(`/api/locations?page=1&pageSize=12`, {
      headers: {
        'X-Stytch-Org-Id': organizationSlug,
      },
      next: { revalidate: 60 } // Revalidate every 60 seconds
    });
    
    // Prefetch locations metadata
    const metadataPromise = fetch(`/api/locations/metadata`, {
      headers: {
        'X-Stytch-Org-Id': organizationSlug,
      },
      next: { revalidate: 300 } // Revalidate every 5 minutes
    });
    
    // Prefetch common filter combinations
    const commonFilters = ['all', 'pending', 'approved', 'studio', 'commercial'];
    const commonFilterPromises = commonFilters.map(filter => 
      fetch(`/api/locations/common/${filter}`, {
        headers: {
          'X-Stytch-Org-Id': organizationSlug,
        },
        next: { revalidate: 60 } // Revalidate every 60 seconds
      })
    );
    
    // Wait for all prefetch requests to complete
    await Promise.all([
      locationsPromise, 
      metadataPromise, 
      ...commonFilterPromises
    ]);
    
    console.log(`Prefetched locations data for organization: ${organizationSlug}`);
  } catch (error) {
    console.error("Error prefetching locations data:", error);
    // Don't throw the error - prefetching failures shouldn't break the app
  }
}

/**
 * Prefetch common metadata used across the application
 * 
 * This function prefetches metadata that's commonly needed across
 * multiple pages to improve the overall application experience.
 */
export async function prefetchCommonMetadata(organizationSlug: string): Promise<void> {
  try {
    // Prefetch organization data
    const orgPromise = fetch(`/api/organizations/${organizationSlug}`, {
      next: { revalidate: 300 } // Revalidate every 5 minutes
    });
    
    // Prefetch projects data
    const projectsPromise = fetch(`/api/projects?organizationSlug=${organizationSlug}`, {
      headers: {
        'X-Stytch-Org-Id': organizationSlug,
      },
      next: { revalidate: 300 } // Revalidate every 5 minutes
    });
    
    // Wait for all prefetch requests to complete
    await Promise.all([orgPromise, projectsPromise]);
    
    console.log(`Prefetched common metadata for organization: ${organizationSlug}`);
  } catch (error) {
    console.error("Error prefetching common metadata:", error);
    // Don't throw the error - prefetching failures shouldn't break the app
  }
}

/**
 * Clear prefetched data from cache
 * 
 * This function can be called when data is known to be stale,
 * such as after a mutation operation.
 */
export async function clearPrefetchedData(organizationSlug: string): Promise<void> {
  try {
    // Clear locations cache
    await cache.delete(`locations:${organizationSlug}`);
    await cache.delete(`locations-metadata:${organizationSlug}`);
    
    // Clear common filters cache
    const commonFilters = ['all', 'pending', 'approved', 'studio', 'commercial'];
    for (const filter of commonFilters) {
      await cache.delete(`locations-filter:${organizationSlug}:${filter}`);
    }
    
    console.log(`Cleared prefetched data for organization: ${organizationSlug}`);
  } catch (error) {
    console.error("Error clearing prefetched data:", error);
  }
}
