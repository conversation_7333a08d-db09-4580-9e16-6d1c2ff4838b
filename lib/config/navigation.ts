import { <PERSON><PERSON>hart, FileText, Home, Map as MapIcon, Settings, Users, FolderKanban, Building, CheckSquare, Calendar } from "lucide-react" // Aliased Map to MapIcon

import type { LucideIcon } from "lucide-react";

interface NavItem {
  title: string;
  href: string;
  icon: LucideIcon;
  requiredRoles?: string[]; // Add optional requiredRoles property
}

export const dashboardNavigation: NavItem[] = [
  {
    title: "Dashboard",
    href: "",
    icon: Home,
    // No requiredRoles = accessible to all
  },
  {
    title: "Projects",
    href: "/projects",
    icon: FolderKanban,
    // No requiredRoles = accessible to all
  },
  {
    title: "Locations",
    href: "/locations",
    icon: Building,
    // No requiredRoles = accessible to all
  },
  {
    title: "Map",
    href: "/map",
    icon: MapIcon, // Use the alias
    // No requiredRoles = accessible to all
  },
  {
    title: "Tasks",
    href: "/tasks",
    icon: CheckSquare,
    requiredRoles: ['admin', 'manager', 'scout'],
  },
  {
    title: "Calendar",
    href: "/calendar",
    icon: Calendar,
    // No requiredRoles = accessible to all
  },
  {
    title: "Documents",
    href: "/documents",
    icon: FileText,
    // No requiredRoles = accessible to all
  },
  {
    title: "Team",
    href: "/team",
    icon: Users,
    requiredRoles: ['admin', 'manager'],
  },
  {
    title: "Analytics",
    href: "/analytics",
    icon: BarChart,
    requiredRoles: ['admin', 'manager'],
  },
  {
    title: "Settings",
    href: "/settings",
    icon: Settings,
    requiredRoles: ['admin'],
  },
]
