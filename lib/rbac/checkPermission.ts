import loadStytch from "@/lib/loadStytch";
import { StytchError } from "stytch";

/**
 * Checks if the user associated with the provided session token/JWT
 * is authorized to perform a specific action on a resource, based on the Stytch RBAC policy.
 *
 * @param authParams - An object containing either session_token or session_jwt.
 * @param organizationId - The organization ID from the authenticated user's request headers.
 * @param action - The action being attempted (e.g., "create", "edit").
 * @param resourceId - The resource ID being accessed (e.g., "project", "location").
 * @returns {Promise<boolean>} - True if authorized, false otherwise.
 */
export interface AuthParams { // Export the interface
  session_token?: string | null;
  session_jwt?: string | null;
}

export async function checkPermission(
  authParams: AuthParams,
  organizationId: string | undefined | null,
  action: string,
  resourceId: string
): Promise<boolean> {
  const { session_token, session_jwt } = authParams;

  if ((!session_token && !session_jwt) || !organizationId) {
    console.warn("checkPermission called without session token/JWT or organization ID.");
    return false;
  }

  const stytchClient = loadStytch();

  // Construct the authentication arguments dynamically
  const authenticateArgs: {
    session_token?: string;
    session_jwt?: string;
    authorization_check: {
      organization_id: string;
      action: string;
      resource_id: string;
    };
  } = {
    authorization_check: {
      organization_id: organizationId,
      action: action,
      resource_id: resourceId,
    },
  };

  if (session_token) {
    authenticateArgs.session_token = session_token;
  } else if (session_jwt) {
    authenticateArgs.session_jwt = session_jwt;
  } else {
     // This case should be caught by the initial check, but handle defensively
     console.error("checkPermission internal error: No token or JWT provided in authParams.");
     return false; 
  }

  try {
    // Use sessions.authenticate with authorization_check
    await stytchClient.sessions.authenticate(authenticateArgs);

    // If sessions.authenticate succeeds with the authorization check, the user is authorized.
    return true;
  } catch (error: unknown) {
    // StytchError indicates an issue like invalid token or permission denied.
    // We treat permission denied (and other auth errors) as unauthorized.
    if (error instanceof StytchError) {
        // Log specific Stytch errors for detailed debugging
        console.error(`Stytch RBAC check failed for action "${action}" on resource "${resourceId}" in org "${organizationId}". Error Type: ${error.error_type}, Message: ${error.error_message}, Code: ${error.status_code}`);
        // Log the full error object for maximum detail
        console.error("Full StytchError object:", JSON.stringify(error, null, 2)); 
        return false;
    }
     
    // Log unexpected errors
    console.error("Unexpected error during RBAC check:", error);
    return false;
  }
}
