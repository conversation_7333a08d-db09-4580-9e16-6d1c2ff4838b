import { B2BClient } from "stytch"

export default function loadStytch() {
  const projectId = process.env.STYTCH_B2B_PROJECT_ID
  const secret = process.env.STYTCH_B2B_SECRET

  if (!projectId || !secret) {
    throw new Error(
      "Missing Stytch B2B credentials. Please set STYTCH_B2B_PROJECT_ID and STYTCH_B2B_SECRET environment variables."
    )
  }

  return new B2BClient({
    project_id: projectId,
    secret,
  })
}
