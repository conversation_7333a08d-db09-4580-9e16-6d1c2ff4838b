/**
 * Simple in-memory cache implementation for server-side caching
 * 
 * This cache utility provides a way to store and retrieve data with TTL (time-to-live)
 * to improve performance by reducing database queries for frequently accessed data.
 */

interface CacheItem<T> {
  value: T;
  expiry: number | null; // Timestamp when the item expires, null for no expiry
}

interface CacheOptions {
  ttl?: number; // Time-to-live in milliseconds, undefined means no expiry
}

class Cache {
  private static instance: Cache;
  private cache: Map<string, CacheItem<unknown>>;
  private readonly defaultTTL = 5 * 60 * 1000; // 5 minutes default TTL

  private constructor() {
    this.cache = new Map();
    // Set up periodic cleanup of expired items
    setInterval(() => this.cleanup(), 60 * 1000); // Run cleanup every minute
  }

  /**
   * Get the singleton instance of the cache
   */
  public static getInstance(): Cache {
    if (!Cache.instance) {
      Cache.instance = new Cache();
    }
    return Cache.instance;
  }

  /**
   * Set a value in the cache with an optional TTL
   * @param key The cache key
   * @param value The value to cache
   * @param options Cache options including TTL
   */
  public set<T>(key: string, value: T, options?: CacheOptions): void {
    const ttl = options?.ttl ?? this.defaultTTL;
    const expiry = ttl ? Date.now() + ttl : null;
    
    this.cache.set(key, {
      value,
      expiry,
    });
  }

  /**
   * Get a value from the cache
   * @param key The cache key
   * @returns The cached value or undefined if not found or expired
   */
  public get<T>(key: string): T | undefined {
    const item = this.cache.get(key);
    
    // Return undefined if item doesn't exist
    if (!item) {
      return undefined;
    }
    
    // Check if the item has expired
    if (item.expiry && item.expiry < Date.now()) {
      this.cache.delete(key);
      return undefined;
    }
    
    return item.value as T;
  }

  /**
   * Check if a key exists in the cache and is not expired
   * @param key The cache key
   * @returns True if the key exists and is not expired
   */
  public has(key: string): boolean {
    const item = this.cache.get(key);
    
    if (!item) {
      return false;
    }
    
    if (item.expiry && item.expiry < Date.now()) {
      this.cache.delete(key);
      return false;
    }
    
    return true;
  }

  /**
   * Delete a key from the cache
   * @param key The cache key
   */
  public delete(key: string): void {
    this.cache.delete(key);
  }

  /**
   * Clear all items from the cache
   */
  public clear(): void {
    this.cache.clear();
  }

  /**
   * Get or set a value in the cache
   * If the key doesn't exist or is expired, the factory function is called to generate a new value
   * @param key The cache key
   * @param factory A function that returns a value or a promise that resolves to a value
   * @param options Cache options including TTL
   * @returns The cached value or the result of the factory function
   */
  public async getOrSet<T>(
    key: string,
    factory: () => Promise<T> | T,
    options?: CacheOptions
  ): Promise<T> {
    // Check if the value is already in the cache
    const cachedValue = this.get<T>(key);
    if (cachedValue !== undefined) {
      return cachedValue;
    }
    
    // Generate a new value
    const value = await factory();
    
    // Cache the value
    this.set(key, value, options);
    
    return value;
  }

  /**
   * Remove expired items from the cache
   */
  private cleanup(): void {
    const now = Date.now();
    
    for (const [key, item] of this.cache.entries()) {
      if (item.expiry && item.expiry < now) {
        this.cache.delete(key);
      }
    }
  }
}

// Export a singleton instance
export const cache = Cache.getInstance();
