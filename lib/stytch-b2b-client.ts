"use client"

import { createStytchB2BUIClient } from '@stytch/nextjs/b2b/ui';

// Create the real Stytch B2B client
export function createStytchB2BClient() {
  console.log("Creating client-side Stytch B2B client");
  
  if (typeof window === 'undefined') {
    // We're on the server side, so we can't create a client-side instance
    // The server-side client should be used instead from lib/stytch-b2b.ts
    console.log("Server-side environment detected, returning null");
    return null;
  }
  
  const publicToken = process.env.NEXT_PUBLIC_STYTCH_PUBLIC_TOKEN;
  console.log(`Public token: ${publicToken ? "Set" : "Not set"}`);
  
  if (!publicToken) {
    console.error("Missing Stytch public token. Please set NEXT_PUBLIC_STYTCH_PUBLIC_TOKEN environment variable.");
    return null;
  }
  
  console.log("Creating Stytch B2B UI client with token:", publicToken);
  
  // Create the client with cookie options
  const client = createStytchB2BUIClient(publicToken, {
    cookieOptions: {
      opaqueTokenCookieName: "stytch_session",
      jwtCookieName: "stytch_session_jwt",
      path: "/",
      availableToSubdomains: false,
      domain: "",
    }
  });
  
  console.log("Stytch B2B UI client created successfully");
  return client;
}

// Singleton instance
let stytchClientInstance: ReturnType<typeof createStytchB2BClient> | null = null;

// Get or create the Stytch client
export function getStytchB2BClient() {
  console.log("Getting Stytch B2B client");
  
  if (!stytchClientInstance && typeof window !== 'undefined') {
    console.log("No existing client instance, creating new one");
    stytchClientInstance = createStytchB2BClient();
  } else if (stytchClientInstance) {
    console.log("Using existing client instance");
  } else {
    console.log("Server-side environment, no client instance available");
  }
  
  return stytchClientInstance;
}
