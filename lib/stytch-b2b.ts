import * as stytch from 'stytch';

// Use a more robust singleton pattern with proper typing
let client: stytch.B2BClient | null = null;
let clientPromise: Promise<stytch.B2BClient> | null = null;

export function getStytchB2BClient(): stytch.B2BClient {
  if (!client) {
    // Use the correct env var names from the original file
    const projectId = process.env.STYTCH_B2B_PROJECT_ID;
    const secret = process.env.STYTCH_B2B_SECRET;

    console.log("Server-side Stytch B2B client initialization (Optimized)");
    console.log(`Project ID: ${projectId ? "Set" : "Not set"}`);
    console.log(`Secret: ${secret ? "Set" : "Not set"}`);

    if (!projectId || !secret) {
      console.error("Missing Stytch B2B credentials");
      throw new Error(
        "Missing Stytch B2B credentials. Please set STYTCH_B2B_PROJECT_ID and STYTCH_B2B_SECRET environment variables.",
      );
    }
    
    console.log("Creating new B2BClient instance (Optimized)");
    client = new stytch.B2BClient({
      project_id: projectId,
      secret: secret,
    });
    console.log("B2BClient instance created successfully (Optimized)");
  } else {
    console.log("Using existing B2BClient instance (Optimized)");
  }
  return client;
}

// Async version for use in async contexts
export async function getStytchB2BClientAsync(): Promise<stytch.B2BClient> {
  if (client) {
    console.log("Using existing B2BClient instance (Async - Optimized)");
    return client;
  }
  
  if (!clientPromise) {
    console.log("Creating new B2BClient promise (Async - Optimized)");
    clientPromise = Promise.resolve().then(() => {
      if (!client) {
        const projectId = process.env.STYTCH_B2B_PROJECT_ID;
        const secret = process.env.STYTCH_B2B_SECRET;

        console.log("Server-side Stytch B2B client initialization (Async - Optimized)");
        console.log(`Project ID: ${projectId ? "Set" : "Not set"}`);
        console.log(`Secret: ${secret ? "Set" : "Not set"}`);

        if (!projectId || !secret) {
          console.error("Missing Stytch B2B credentials (Async)");
          throw new Error(
            "Missing Stytch B2B credentials. Please set STYTCH_B2B_PROJECT_ID and STYTCH_B2B_SECRET environment variables.",
          );
        }
        
        console.log("Creating new B2BClient instance (Async - Optimized)");
        client = new stytch.B2BClient({
          project_id: projectId,
          secret: secret,
        });
        console.log("B2BClient instance created successfully (Async - Optimized)");
      } else {
         console.log("Using existing B2BClient instance within promise (Async - Optimized)");
      }
      return client;
    });
  } else {
     console.log("Using existing B2BClient promise (Async - Optimized)");
  }
  
  return clientPromise;
}

// Reset function for testing purposes
export function resetStytchB2BClient(): void {
  console.log("Resetting Stytch B2B client instance (Optimized)");
  client = null;
  clientPromise = null;
}
