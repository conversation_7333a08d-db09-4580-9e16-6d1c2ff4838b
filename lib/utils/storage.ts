// Utility functions for interacting with localStorage safely

export const storage = {
  /**
   * Gets an item from localStorage.
   * Returns defaultValue if the item doesn't exist, cannot be parsed, or if localStorage is unavailable.
   */
  get: <T>(key: string, defaultValue: T): T => {
    if (typeof window === 'undefined') {
      // Return default value in non-browser environments (SSR/SSG)
      return defaultValue;
    }
    
    try {
      const item = window.localStorage.getItem(key);
      // Check if item exists and is not 'undefined' or 'null' as strings
      if (item && item !== 'undefined' && item !== 'null') {
        return JSON.parse(item) as T;
      }
      return defaultValue;
    } catch (error) {
      console.error(`Error getting item ${key} from localStorage:`, error);
      return defaultValue;
    }
  },
  
  /**
   * Sets an item in localStorage.
   * Does nothing if localStorage is unavailable.
   */
  set: <T>(key: string, value: T): void => {
    if (typeof window === 'undefined') {
      return; // Do nothing in non-browser environments
    }
    
    try {
      // Ensure value is not undefined before stringifying
      if (value === undefined) {
         console.warn(`Attempted to set undefined value for key ${key} in localStorage. Removing item instead.`);
         window.localStorage.removeItem(key);
      } else {
         window.localStorage.setItem(key, JSON.stringify(value));
      }
    } catch (error) {
      console.error(`Error setting item ${key} in localStorage:`, error);
    }
  },
  
  /**
   * Removes an item from localStorage.
   * Does nothing if localStorage is unavailable.
   */
  remove: (key: string): void => {
    if (typeof window === 'undefined') {
      return; // Do nothing in non-browser environments
    }
    
    try {
      window.localStorage.removeItem(key);
    } catch (error) {
      console.error(`Error removing item ${key} from localStorage:`, error);
    }
  },
};

// Example specific storage utilities (as shown in the doc)
// These could live here or in more specific utility files (e.g., lib/utils/map-storage.ts)

// Example for map preferences
export const mapStorage = {
  getLastView: (orgSlug: string): any | null => // TODO: Define MapView type 
    storage.get(`map_last_view_${orgSlug}`, null),
  
  setLastView: (orgSlug: string, view: any): void => // TODO: Define MapView type
    storage.set(`map_last_view_${orgSlug}`, view),
  
  getFilters: (orgSlug: string): any => // TODO: Define Filter type
    storage.get(`map_filters_${orgSlug}`, {}),
  
  setFilters: (orgSlug: string, filters: any): void => // TODO: Define Filter type
    storage.set(`map_filters_${orgSlug}`, filters),
};

// Example for notification preferences (already implemented in provider, but could be extracted)
// export const notificationStorage = {
//   getPreferences: (): NotificationPreference[] | null =>
//     storage.get('notificationPreferences', null),
//   setPreferences: (prefs: NotificationPreference[]): void =>
//     storage.set('notificationPreferences', prefs),
//   getTimestamp: (): number | null =>
//     storage.get('notificationPreferencesTimestamp', null),
//   setTimestamp: (timestamp: number): void =>
//     storage.set('notificationPreferencesTimestamp', timestamp),
// };
