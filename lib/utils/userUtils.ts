import { db } from "@/lib/db"
import { users } from "@/modules/user/schema"
import { organizations } from "@/modules/organization/schema"
import { eq } from "drizzle-orm"

/**
 * Get user ID from Stytch ID
 * 
 * This function looks up a user by their Stytch ID and returns their database ID
 * 
 * @param stytchUserId The Stytch user ID to look up
 * @returns The user's database ID, or null if not found
 */
export async function getUserIdFromStytchId(stytchUserId: string | null): Promise<string | null> {
  if (!stytchUserId) return null;
  
  const user = await db.query.users.findFirst({
    where: eq(users.stytchUserId, stytchUserId),
  });
  
  return user?.id || null;
}

/**
 * Get user ID from Stytch member ID
 * 
 * This function looks up a user by their Stytch member ID and returns their database ID
 * 
 * @param stytchMemberId The Stytch member ID to look up
 * @returns The user's database ID, or null if not found
 */
export async function getUserIdFromStytchMemberId(stytchMemberId: string | null): Promise<string | null> {
  if (!stytchMemberId) return null;
  
  const user = await db.query.users.findFirst({
    where: eq(users.stytchUserId, stytchMemberId),
    columns: {
      id: true
    }
  });
  
  return user?.id || null;
}

/**
 * Get organization ID from Stytch organization ID
 * 
 * This function looks up an organization by its Stytch organization ID and returns its database ID
 * 
 * @param stytchOrgId The Stytch organization ID to look up
 * @returns The organization's database ID, or null if not found
 */
export async function getOrgIdFromStytchId(stytchOrgId: string | null): Promise<string | null> {
  if (!stytchOrgId) return null;
  
  const organization = await db.query.organizations.findFirst({
    where: eq(organizations.stytchOrganizationId, stytchOrgId),
  });
  
  return organization?.id || null;
}
