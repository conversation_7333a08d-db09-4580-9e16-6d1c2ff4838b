/**
 * Utility functions for working with Stytch IDs
 */

/**
 * Extracts the UUID part from a Stytch ID
 * Stytch IDs are in the format: "prefix-environment-uuid"
 * For example: "member-live-43401ba0-bcf8-43e6-a726-ac203de9b44b"
 * 
 * @param stytchId The Stytch ID to extract the UUID from
 * @returns The UUID part of the Stytch ID, or the original ID if it doesn't match the pattern
 */
export function extractUuidFromStytchId(stytchId: string | null): string | null {
  if (!stytchId) return null;
  
  // Stytch IDs follow the pattern: prefix-environment-uuid
  // We want to extract just the UUID part
  const parts = stytchId.split('-');
  
  // If we have at least 3 parts (prefix, environment, and the start of the UUID)
  if (parts.length >= 3) {
    // The UUID starts at the 3rd part (index 2) and continues to the end
    // We need to reconstruct it with the dashes
    return parts.slice(2).join('-');
  }
  
  // If the ID doesn't match the expected pattern, return the original
  return stytchId;
}
