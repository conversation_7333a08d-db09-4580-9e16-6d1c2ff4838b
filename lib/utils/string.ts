export function generateSlug(str: string): string {
  return str
    .toLowerCase()
    .trim()
    .replace(/[^\w\s-]/g, "")
    .replace(/[\s_-]+/g, "-")
    .replace(/^-+|-+$/g, "")
}

/**
 * Truncates a string to a specified length and adds an ellipsis if needed
 */
export function truncate(str: string, length: number): string {
  if (str.length <= length) {
    return str
  }
  return str.slice(0, length) + "..."
}

/**
 * Capitalizes the first letter of a string
 */
export function capitalize(str: string): string {
  if (!str || typeof str !== "string") return ""
  return str.charAt(0).toUpperCase() + str.slice(1)
}

/**
 * Formats a name into initials (e.g., "<PERSON>" -> "JD")
 */
export function getInitials(name: string): string {
  if (!name) return ""

  const parts = name.split(" ").filter(Boolean)

  if (parts.length === 1) {
    return parts[0].substring(0, 2).toUpperCase()
  }

  return parts
    .map((part) => part[0])
    .join("")
    .toUpperCase()
}
