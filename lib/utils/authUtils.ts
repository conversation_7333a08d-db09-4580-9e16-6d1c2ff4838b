{/* lib/utils/authUtils.ts */}
import { NextRequest } from 'next/server';
import type { AuthParams } from '@/lib/rbac/checkPermission'; // Import the type

/**
 * Extracts Stytch authentication parameters (session token or JWT)
 * from the request headers set by the middleware.
 *
 * @param request - The NextRequest object.
 * @returns An AuthParams object containing the token/JWT, or null if not found.
 */
export function getAuthParamsFromRequest(request: NextRequest): AuthParams | null {
  const sessionToken = request.headers.get('X-Stytch-Session-Token');
  const sessionJwt = request.headers.get('X-Stytch-Session-JWT');

  if (sessionToken) {
    return { session_token: sessionToken };
  }
  if (sessionJwt) {
    return { session_jwt: sessionJwt };
  }

  console.warn('No Stytch session token or JWT found in request headers.');
  return null;
}
