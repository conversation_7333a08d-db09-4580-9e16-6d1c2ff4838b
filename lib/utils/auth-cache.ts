import LRUCache from 'lru-cache';

type AuthCacheData = {
  userId: string;
  organizationId: string;
  expiresAt: number;
};

// Create a cache with a maximum of 1000 items that expire after 5 minutes
const authCache = new LRUCache<string, AuthCacheData>({
  max: 1000,
  ttl: 5 * 60 * 1000, // 5 minutes
});

export function cacheAuthData(sessionToken: string, userId: string, organizationId: string): void {
  authCache.set(sessionToken, {
    userId,
    organizationId,
    expiresAt: Date.now() + 5 * 60 * 1000, // 5 minutes from now
  });
}

export function getCachedAuthData(sessionToken: string): AuthCacheData | undefined {
  return authCache.get(sessionToken);
}

export function invalidateAuthCache(sessionToken: string): void {
  authCache.delete(sessionToken);
}
