import { randomBytes } from "crypto"

/**
 * Generates a random token for use in invitations, password resets, etc.
 */
export function generateToken(length = 32): string {
  return randomBytes(length).toString("hex")
}

/**
 * Checks if a user has the required role in an organization
 */
export function hasOrganizationRole(
  organizationId: string,
  userId: string,
  requiredRoles: string[],
  userRoles: Array<{ organizationId: string; userId: string; role: string }>,
): boolean {
  const userRole = userRoles.find((role) => role.organizationId === organizationId && role.userId === userId)

  if (!userRole) return false

  return requiredRoles.includes(userRole.role)
}

/**
 * Checks if a user has the required role in a project
 */
export function hasProjectRole(
  projectId: string,
  userId: string,
  requiredRoles: string[],
  userRoles: Array<{ projectId: string; userId: string; role: string }>,
): boolean {
  const userRole = userRoles.find((role) => role.projectId === projectId && role.userId === userId)

  if (!userRole) return false

  return requiredRoles.includes(userRole.role)
}
