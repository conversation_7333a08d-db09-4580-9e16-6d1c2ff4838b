import { format, formatDistance, isToday, isYesterday, isTomorrow } from "date-fns"

/**
 * Formats a date in a human-readable format
 */
export function formatDate(date: Date | string | number): string {
  const dateObj = new Date(date)

  if (isToday(dateObj)) {
    return `Today at ${format(dateObj, "h:mm a")}`
  }

  if (isYesterday(dateObj)) {
    return `Yesterday at ${format(dateObj, "h:mm a")}`
  }

  if (isTomorrow(dateObj)) {
    return `Tomorrow at ${format(dateObj, "h:mm a")}`
  }

  return format(dateObj, "MMM d, yyyy")
}

/**
 * Formats a date as a relative time (e.g., "2 days ago")
 */
export function formatRelativeTime(date: Date | string | number): string {
  return formatDistance(new Date(date), new Date(), { addSuffix: true })
}

/**
 * Formats a date for display in a form input
 */
export function formatForInput(date: Date | string | number): string {
  return format(new Date(date), "yyyy-MM-dd")
}

/**
 * Checks if a date is in the past
 */
export function isPast(date: Date | string | number): boolean {
  return new Date(date) < new Date()
}

/**
 * Checks if a date is in the future
 */
export function isFuture(date: Date | string | number): boolean {
  return new Date(date) > new Date()
}
