/**
 * Geocoding utility functions for converting addresses to coordinates
 * Uses Mapbox Geocoding API
 */

import { z } from "zod";

// Schema for geocoding response validation
const geocodingFeatureSchema = z.object({
  id: z.string(),
  place_name: z.string(),
  center: z.tuple([z.number(), z.number()]),
  geometry: z.object({
    type: z.literal("Point"),
    coordinates: z.tuple([z.number(), z.number()]),
  }),
  relevance: z.number(),
  properties: z.object({
    accuracy: z.string().optional(),
  }).passthrough(),
});

const geocodingResponseSchema = z.object({
  features: z.array(geocodingFeatureSchema),
});

/**
 * Geocode an address to coordinates using Mapbox Geocoding API
 * @param address The address to geocode
 * @returns Promise with latitude and longitude
 */
export async function geocodeAddress(address: string): Promise<{ latitude: number; longitude: number; confidence: number }> {
  try {
    // Get Mapbox token from API
    const tokenResponse = await fetch("/api/mapbox-token");
    if (!tokenResponse.ok) {
      throw new Error("Failed to fetch Mapbox token");
    }
    const { token } = await tokenResponse.json();

    // Format address for URL
    const formattedAddress = encodeURIComponent(address);
    
    // Call Mapbox Geocoding API
    const response = await fetch(
      `https://api.mapbox.com/geocoding/v5/mapbox.places/${formattedAddress}.json?access_token=${token}&limit=1`
    );

    if (!response.ok) {
      throw new Error(`Geocoding API error: ${response.statusText}`);
    }

    const data = await response.json();
    
    // Validate response
    const validatedData = geocodingResponseSchema.parse(data);
    
    if (validatedData.features.length === 0) {
      throw new Error("No results found for this address");
    }

    const feature = validatedData.features[0];
    const [longitude, latitude] = feature.center;
    
    // Calculate confidence based on relevance
    const confidence = feature.relevance;

    return { latitude, longitude, confidence };
  } catch (error) {
    console.error("Geocoding error:", error);
    throw error;
  }
}

/**
 * Validate an address by attempting to geocode it
 * @param address The address to validate
 * @returns Promise with validation result
 */
export async function validateAddress(address: string): Promise<{ 
  isValid: boolean; 
  formattedAddress?: string;
  coordinates?: { latitude: number; longitude: number };
  confidence?: number;
  error?: string;
}> {
  try {
    // Get Mapbox token from API
    const tokenResponse = await fetch("/api/mapbox-token");
    if (!tokenResponse.ok) {
      throw new Error("Failed to fetch Mapbox token");
    }
    const { token } = await tokenResponse.json();

    // Format address for URL
    const formattedAddress = encodeURIComponent(address);
    
    // Call Mapbox Geocoding API
    const response = await fetch(
      `https://api.mapbox.com/geocoding/v5/mapbox.places/${formattedAddress}.json?access_token=${token}&limit=1`
    );

    if (!response.ok) {
      return { 
        isValid: false, 
        error: `Geocoding API error: ${response.statusText}` 
      };
    }

    const data = await response.json();
    
    // Validate response
    const validatedData = geocodingResponseSchema.parse(data);
    
    if (validatedData.features.length === 0) {
      return { 
        isValid: false, 
        error: "No results found for this address" 
      };
    }

    const feature = validatedData.features[0];
    const [longitude, latitude] = feature.center;
    
    // Calculate confidence based on relevance
    const confidence = feature.relevance;

    // Consider an address valid if confidence is above 0.5
    const isValid = confidence > 0.5;

    return { 
      isValid,
      formattedAddress: feature.place_name,
      coordinates: { latitude, longitude },
      confidence,
      error: isValid ? undefined : "Low confidence in address match"
    };
  } catch (error) {
    console.error("Address validation error:", error);
    return { 
      isValid: false, 
      error: error instanceof Error ? error.message : "Unknown error during address validation" 
    };
  }
}

/**
 * Server-side geocoding function that uses the Mapbox API directly
 * This should be used in server-side code like API routes or scripts
 * @param address The address to geocode
 * @param mapboxToken The Mapbox API token (optional, will use env var if not provided)
 * @returns Promise with latitude and longitude
 */
export async function geocodeAddressServer(
  address: string, 
  mapboxToken?: string
): Promise<{ 
  latitude: number; 
  longitude: number; 
  formattedAddress: string;
  confidence: number 
}> {
  try {
    // Get token from parameter or environment variable
    const token = mapboxToken || process.env.NEXT_PUBLIC_MAPBOX_ACCESS_TOKEN || process.env.MAPBOX_TOKEN;
    
    if (!token) {
      throw new Error("Mapbox token is required but not provided");
    }
    
    // Format address for URL
    const formattedAddress = encodeURIComponent(address);
    
    // Call Mapbox Geocoding API
    const response = await fetch(
      `https://api.mapbox.com/geocoding/v5/mapbox.places/${formattedAddress}.json?access_token=${token}&limit=1`
    );

    if (!response.ok) {
      throw new Error(`Geocoding API error: ${response.statusText}`);
    }

    const data = await response.json();
    
    // Validate response
    const validatedData = geocodingResponseSchema.parse(data);
    
    if (validatedData.features.length === 0) {
      throw new Error("No results found for this address");
    }

    const feature = validatedData.features[0];
    const [longitude, latitude] = feature.center;
    
    // Calculate confidence based on relevance
    const confidence = feature.relevance;

    return { 
      latitude, 
      longitude, 
      formattedAddress: feature.place_name,
      confidence 
    };
  } catch (error) {
    console.error("Server geocoding error:", error);
    throw error;
  }
}
