import { cookies } from 'next/headers';
import { getStytchB2BClientAsync } from '@/lib/stytch-b2b'; // Use absolute import path
import { getCachedAuthData, cacheAuthData } from '@/lib/utils/auth-cache';

export async function authenticateSession(): Promise<{
  authenticated: boolean;
  memberId?: string;
  organizationId?: string;
  error?: string;
}> {
  // Await the cookies promise
  const cookieStore = await cookies(); 
  const sessionToken = cookieStore.get('stytch_session_jwt')?.value;
  
  if (!sessionToken) {
    console.log("Auth Utils: No session token found");
    return { authenticated: false, error: 'No session token found' };
  }
  
  try {
    // Check cache first
    const cachedData = getCachedAuthData(sessionToken);
    
    if (cachedData) {
      // Use template literal
      console.log(`Auth Utils: Using cached auth data for token: ${sessionToken.substring(0, 10)}...`);
      // Use cached authentication data
      return {
        authenticated: true,
        memberId: cachedData.userId,
        organizationId: cachedData.organizationId,
      };
    }
    
    // Use template literal
    console.log(`Auth Utils: No cached data found, authenticating with <PERSON>yt<PERSON> for token: ${sessionToken.substring(0, 10)}...`);
    // If not in cache, authenticate with Stytch
    const stytchClient = await getStytchB2BClientAsync(); // Use the async version from the updated server-side client file
    const response = await stytchClient.sessions.authenticate({ session_jwt: sessionToken });
    
    // Access the member_session from the response (based on middleware.ts implementation)
    const memberId = response.member_session.member_id;
    const organizationId = response.member_session.organization_id;
    
    console.log("Auth Utils: Stytch authentication successful. Caching data.");
    // Cache the authentication data
    cacheAuthData(sessionToken, memberId, organizationId);
    
    return {
      authenticated: true,
      memberId,
      organizationId,
    };
  } catch (error) {
    console.error('Auth Utils: Error authenticating session:', error);
    return {
      authenticated: false,
      error: error instanceof Error ? error.message : 'Authentication failed',
    };
  }
}

// Helper function to get the current user ID
export async function getCurrentUserId(): Promise<string | null> {
  console.log("Auth Utils: Getting current user ID");
  const auth = await authenticateSession();
  
  if (!auth.authenticated || !auth.memberId) {
    console.log("Auth Utils: Not authenticated or no member ID found");
    return null;
  }
  
  console.log("Auth Utils: Current user ID:", auth.memberId);
  return auth.memberId;
}

// Helper function to get the current organization ID
export async function getCurrentOrganizationId(): Promise<string | null> {
  console.log("Auth Utils: Getting current organization ID");
  const auth = await authenticateSession();
  
  if (!auth.authenticated || !auth.organizationId) {
     console.log("Auth Utils: Not authenticated or no organization ID found");
    return null;
  }
  
  console.log("Auth Utils: Current organization ID:", auth.organizationId);
  return auth.organizationId;
}
